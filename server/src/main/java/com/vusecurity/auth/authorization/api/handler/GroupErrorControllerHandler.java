package com.vusecurity.auth.authorization.api.handler;

import com.vusecurity.auth.authorization.application.exception.*;
import com.vusecurity.core.commons.ApiMessage;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@ControllerAdvice(assignableTypes = {com.vusecurity.auth.authorization.api.controller.GroupController.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class GroupErrorControllerHandler {

    @ExceptionHandler(GroupNotFoundException.class)
    public ResponseEntity<ApiMessage> handleGroupNotFoundException(GroupNotFoundException ex) {
        ApiMessage error = new ApiMessage(5500, ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }

    @ExceptionHandler(GroupNameAlreadyExistsException.class)
    public ResponseEntity<ApiMessage> handleGroupNameAlreadyExistsException(GroupNameAlreadyExistsException ex) {
        ApiMessage error = new ApiMessage(5501, ex.getMessage());
        return ResponseEntity.status(HttpStatus.CONFLICT).body(error);
    }

    @ExceptionHandler(RoleNotFoundException.class)
    public ResponseEntity<ApiMessage> handleRoleNotFoundException(RoleNotFoundException ex) {
        ApiMessage error = new ApiMessage(5502, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }

    @ExceptionHandler(AccountNotFoundException.class)
    public ResponseEntity<ApiMessage> handleAccountNotFoundException(AccountNotFoundException ex) {
        ApiMessage error = new ApiMessage(5503, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }

    @ExceptionHandler(InvalidGroupRequestException.class)
    public ResponseEntity<ApiMessage> handleInvalidGroupRequestException(InvalidGroupRequestException ex) {
        ApiMessage error = new ApiMessage(5504, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }

    @ExceptionHandler(MismatchedBusinessException.class)
    public ResponseEntity<ApiMessage> handleMismatchedBusinessException(MismatchedBusinessException ex) {
        ApiMessage error = new ApiMessage(5505, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<ApiMessage> handleDataIntegrityViolationException(DataIntegrityViolationException ex) {
        String message = ex.getMessage();
        String userMessage = "A record with the same unique value already exists.";
        int code = 5506;

        if (message != null) {
            // Example: handle known constraint for group name
            if (message.contains("uk_account_group_name")) {
                // Try to extract the duplicate value
                String duplicateName = null;
                java.util.regex.Matcher matcher = java.util.regex.Pattern
                        .compile("The duplicate key value is \\((.*?)\\)").matcher(message);
                if (matcher.find()) {
                    duplicateName = matcher.group(1);
                }
                userMessage = "Group with name '" + (duplicateName != null ? duplicateName : "") + "' already exists.";
            } else if (message.contains("unique") || message.contains("duplicate key")) {
                // Try to extract the duplicate value for generic unique constraint
                java.util.regex.Matcher matcher = java.util.regex.Pattern
                        .compile("The duplicate key value is \\((.*?)\\)").matcher(message);
                if (matcher.find()) {
                    userMessage = "A record with this value already exists: " + matcher.group(1);
                }
            }
        }

        ApiMessage error = new ApiMessage(code, userMessage);
        return ResponseEntity.status(HttpStatus.CONFLICT).body(error);
    }

    @ExceptionHandler(IllegalStateException.class)
    public ResponseEntity<ApiMessage> handleIllegalStateException(IllegalStateException ex) {
        ApiMessage error = new ApiMessage(HttpStatus.CONFLICT.value(), ex.getMessage());
        return ResponseEntity.status(HttpStatus.CONFLICT).body(error);
    }

    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiMessage> handleRuntimeException(RuntimeException ex) {
        ApiMessage error = new ApiMessage(HttpStatus.INTERNAL_SERVER_ERROR.value(), "An unexpected error occurred while processing the group operation");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
    }
} 