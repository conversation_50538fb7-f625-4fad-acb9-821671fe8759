package com.vusecurity.auth.claims.api.handler;

import com.vusecurity.auth.claims.application.exception.DuplicateClaimValueException;
import com.vusecurity.auth.claims.application.exception.ClaimValueNotFoundException;
import com.vusecurity.auth.claims.application.exception.ClaimDefinitionNotFoundException;
import com.vusecurity.auth.claims.application.exception.ClaimValueValidationException;
import com.vusecurity.core.commons.ApiMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.server.ResponseStatusException;

import java.util.NoSuchElementException;

/**
 * Exception handler for ClaimValueController.
 * Handles specific exceptions thrown by claim value operations and converts them to appropriate HTTP responses.
 */
@ControllerAdvice(assignableTypes = {com.vusecurity.auth.claims.api.controller.ClaimValueController.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ClaimValueErrorControllerHandler {

    private static final Logger logger = LoggerFactory.getLogger(ClaimValueErrorControllerHandler.class);

    /**
     * Handle DuplicateClaimValueException thrown by claim value handlers.
     * This handles database constraint violations for duplicate claim values.
     */
    @ExceptionHandler(DuplicateClaimValueException.class)
    public ResponseEntity<ApiMessage> handleDuplicateClaimValueException(DuplicateClaimValueException ex) {
        logger.debug("Handling DuplicateClaimValueException: {}", ex.getMessage());

        ApiMessage error = new ApiMessage(5000, ex.getMessage());
        return ResponseEntity.status(409).body(error);
    }

    /**
     * Handle ResponseStatusException thrown by claim value controller.
     * This ensures that the HTTP status code from the ResponseStatusException is preserved.
     */
    @ExceptionHandler(ResponseStatusException.class)
    public ResponseEntity<ApiMessage> handleResponseStatusException(ResponseStatusException ex) {
        logger.debug("Handling ResponseStatusException: {} - {}", ex.getStatusCode(), ex.getReason());
        
        // Extract the status code and reason from the ResponseStatusException
        int statusCode = ex.getStatusCode().value();
        String message = ex.getReason() != null ? ex.getReason() : ex.getMessage();
        
        // Create appropriate error code based on status
        int errorCode = switch (statusCode) {
            case 400 -> 5400; // Bad Request
            case 404 -> 5404; // Not Found
            case 500 -> 5450; // Internal Server Error
            default -> 5400; // Generic error
        };
        
        ApiMessage error = new ApiMessage(errorCode, message);
        return ResponseEntity.status(ex.getStatusCode()).body(error);
    }

    /**
     * Handle NoSuchElementException thrown by claim value services.
     * This is a fallback in case the service throws NoSuchElementException directly.
     */
    @ExceptionHandler(NoSuchElementException.class)
    public ResponseEntity<ApiMessage> handleNoSuchElementException(NoSuchElementException ex) {
        logger.debug("Handling NoSuchElementException: {}", ex.getMessage());
        
        ApiMessage error = new ApiMessage(5404, ex.getMessage());
        return ResponseEntity.status(404).body(error);
    }

    /**
     * Handle IllegalArgumentException thrown by claim value services.
     * This is a fallback in case the service throws IllegalArgumentException directly.
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiMessage> handleIllegalArgumentException(IllegalArgumentException ex) {
        logger.debug("Handling IllegalArgumentException: {}", ex.getMessage());
        
        ApiMessage error = new ApiMessage(5400, ex.getMessage());
        return ResponseEntity.status(400).body(error);
    }

    /**
     * Handle ClaimValueNotFoundException thrown by claim value handlers.
     */
    @ExceptionHandler(ClaimValueNotFoundException.class)
    public ResponseEntity<ApiMessage> handleClaimValueNotFoundException(ClaimValueNotFoundException ex) {
        logger.debug("Handling ClaimValueNotFoundException: {}", ex.getMessage());
        ApiMessage error = new ApiMessage(5404, ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }

    /**
     * Handle ClaimDefinitionNotFoundException thrown by claim value handlers.
     */
    @ExceptionHandler(ClaimDefinitionNotFoundException.class)
    public ResponseEntity<ApiMessage> handleClaimDefinitionNotFoundException(ClaimDefinitionNotFoundException ex) {
        logger.debug("Handling ClaimDefinitionNotFoundException: {}", ex.getMessage());
        ApiMessage error = new ApiMessage(5405, ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }

    /**
     * Handle ClaimValueValidationException thrown by claim value handlers.
     */
    @ExceptionHandler(ClaimValueValidationException.class)
    public ResponseEntity<ApiMessage> handleClaimValueValidationException(ClaimValueValidationException ex) {
        logger.debug("Handling ClaimValueValidationException: {}", ex.getMessage());
        ApiMessage error = new ApiMessage(5400, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }
}
