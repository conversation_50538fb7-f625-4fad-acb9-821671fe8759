package com.vusecurity.auth.identities.application.handler;

import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.identities.application.command.CreateAccountCommand;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityProviderRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityRepository;
import com.vusecurity.auth.shared.util.DatabaseConstraintUtils;
import com.vusecurity.auth.identities.application.exception.AccountAlreadyExistsException;
import com.vusecurity.auth.identities.application.exception.IdentityNotFoundException;
import com.vusecurity.auth.identities.application.exception.IdentityProviderNotFoundException;
import com.vusecurity.business.domain.repositories.BusinessRepository;
import com.vusecurity.business.usecases.exceptions.BusinessNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CreateAccountHandler {

    private final AccountRepository accountRepository;
    private final IdentityRepository identityRepository;
    private final BusinessRepository businessRepository;
    private final IdentityProviderRepository identityProviderRepository;

    @Transactional
    public AccountJpaEntity createAccount(CreateAccountCommand cmd) {

        cmd.validate();

        var business = businessRepository.findById(cmd.businessId())
                .orElseThrow(BusinessNotFoundException::new);

        var identity = identityRepository.findByIdentityId(cmd.identityId())
                .orElseThrow(() -> new IdentityNotFoundException(cmd.identityId().toString()));
        var identityProvider = identityProviderRepository.findById(cmd.identityProviderId())
                .orElseThrow(() -> new IdentityProviderNotFoundException(cmd.identityProviderId()));

        AccountJpaEntity account = new AccountJpaEntity();
        account.setBusiness(business);
        account.setIdentity(identity);
        account.setIdentityProvider(identityProvider);
        account.setAccountType(cmd.accountType());
        account.setLifecycleState(cmd.lifecycleState() != null
                ? cmd.lifecycleState()
                : AccountLifecycleState.PENDING);
        account.setMetadata(cmd.metadata());

        // Save account with DB constraint handling for unique account per identity/business/type/identityProvider
        return DatabaseConstraintUtils.executeWithConstraintHandling(
            () -> accountRepository.saveAndFlush(account),
            "uk_account_type_business_identity_provider_identity", // Unique constraint on account per identity/business/type/identityProvider
            () -> new AccountAlreadyExistsException("Account already exists for this identity, business, identityProvider and type")
        );
    }
}