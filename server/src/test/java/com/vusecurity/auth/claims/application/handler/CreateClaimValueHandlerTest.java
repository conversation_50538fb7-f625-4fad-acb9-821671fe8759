package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.CreateClaimValueCommand;
import com.vusecurity.auth.claims.application.exception.ClaimDefinitionNotFoundException;
import com.vusecurity.auth.claims.application.exception.DuplicateClaimValueException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetDefinitionMappingJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetDefinitionMappingRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.contracts.enums.LookupStrategy;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.identities.application.dto.AccountBusinessInfo;
import com.vusecurity.auth.identities.application.exception.AccountNotFoundException;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import com.vusecurity.business.domain.Business;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for CreateClaimValueHandler.
 * Tests business logic with mocked repository dependencies.
 */
@ExtendWith(MockitoExtension.class)
class CreateClaimValueHandlerTest {

    @Mock
    private ClaimValueRepository valueRepo;

    @Mock
    private ClaimDefinitionRepository defRepo;

    @Mock
    private ClaimSetDefinitionMappingRepository mappingRepo;

    @Mock
    private AccountRepository accountRepo;

    @InjectMocks
    private CreateClaimValueHandler createClaimValueHandler;

    private UUID businessId;
    private UUID accountId;
    private UUID identityId;
    private UUID claimDefinitionId;
    private UUID claimSetId;
    private Business testBusiness;
    private ClaimDefinitionJpaEntity testClaimDefinition;
    private AccountBusinessInfo testAccountBusinessInfo;
    private ClaimSetJpaEntity testClaimSet;
    private ClaimSetDefinitionMappingJpaEntity testMapping;
    private CreateClaimValueCommand identityCommand;
    private CreateClaimValueCommand accountCommand;

    @BeforeEach
    void setUp() {
        businessId = UUID.randomUUID();
        accountId = UUID.randomUUID();
        identityId = UUID.randomUUID();
        claimDefinitionId = UUID.randomUUID();
        claimSetId = UUID.randomUUID();
        testBusiness = createTestBusiness();
        testClaimDefinition = createTestClaimDefinition();
        testAccountBusinessInfo = createTestAccountBusinessInfo();
        testClaimSet = createTestClaimSet();
        testClaimSet.setId(claimSetId);
        testMapping = createTestMapping();
        identityCommand = createIdentityCommand();
        accountCommand = createAccountCommand();
        testMapping.setClaimSet(testClaimSet);
        testClaimDefinition.setClaimSetMappings(Set.of(testMapping));
    }

    @Test
    void shouldSuccessfullyCreateClaimValue_ForIdentityPath() {
        // Given
        ClaimValueJpaEntity expectedClaimValue = createTestClaimValue(OwnerType.IDENTITY, identityId);
        
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(testClaimDefinition));
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(expectedClaimValue);

        // When
        ClaimValueJpaEntity result = createClaimValueHandler.handle(identityCommand);

        // Then
        assertNotNull(result);
        assertEquals(expectedClaimValue.getId(), result.getId());
        assertEquals(OwnerType.IDENTITY, result.getOwnerType());
        assertEquals(identityId, result.getOwnerId());
        verify(defRepo).findById(claimDefinitionId);
        verify(valueRepo).saveAndFlush(any(ClaimValueJpaEntity.class));
        verifyNoInteractions(accountRepo);
        verifyNoInteractions(mappingRepo);
    }

    @Test
    void shouldSuccessfullyCreateClaimValue_ForAccountPath_WithoutUniquenessRequired() {
        // Given
        ClaimValueJpaEntity expectedClaimValue = createTestClaimValue(OwnerType.ACCOUNT, accountId);
        ClaimSetDefinitionMappingJpaEntity nonUniqueMapping = createTestMapping();
        nonUniqueMapping.getClaimSet().setIsIdentifier(false);
        nonUniqueMapping.setEnforceUniqueness(false);

        testClaimSet.setIsIdentifier(false);
        
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(testClaimDefinition));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.of(testAccountBusinessInfo));
        when(mappingRepo.findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId))
                .thenReturn(Arrays.asList(nonUniqueMapping));
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(expectedClaimValue);

        // When
        ClaimValueJpaEntity result = createClaimValueHandler.handle(accountCommand);

        // Then
        assertNotNull(result);
        assertEquals(expectedClaimValue.getId(), result.getId());
        assertEquals(OwnerType.ACCOUNT, result.getOwnerType());
        assertEquals(accountId, result.getOwnerId());
        verify(defRepo).findById(claimDefinitionId);
        verify(accountRepo).findInfoById(accountId);
        verify(mappingRepo).findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId);
        verify(valueRepo).saveAndFlush(any(ClaimValueJpaEntity.class));
        verify(valueRepo, never()).existsByOwnerTypeAndOwnerIdAndClaimDefinitionId(any(), any(), any());
        verify(valueRepo, never()).existsForAccountType(any(), any(), any(), any());
    }

    @Test
    void shouldSuccessfullyCreateClaimValue_ForAccountPath_WithUniquenessRequired() {
        // Given
        ClaimValueJpaEntity expectedClaimValue = createTestClaimValue(OwnerType.ACCOUNT, accountId);
        
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(testClaimDefinition));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.of(testAccountBusinessInfo));
        when(mappingRepo.findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId))
                .thenReturn(Arrays.asList(testMapping));
        when(valueRepo.existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId))
                .thenReturn(false);
        when(valueRepo.existsForAccountType(AccountType.WORKFORCE, claimDefinitionId, "<EMAIL>", accountId))
                .thenReturn(false);
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(expectedClaimValue);

        // When
        ClaimValueJpaEntity result = createClaimValueHandler.handle(accountCommand);

        // Then
        assertNotNull(result);
        assertEquals(expectedClaimValue.getId(), result.getId());
        verify(defRepo).findById(claimDefinitionId);
        verify(accountRepo).findInfoById(accountId);
        verify(mappingRepo).findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId);
        verify(valueRepo).existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId);
        verify(valueRepo).existsForAccountType(AccountType.WORKFORCE, claimDefinitionId, "<EMAIL>", accountId);
        verify(valueRepo).saveAndFlush(any(ClaimValueJpaEntity.class));
    }

    @Test
    void shouldThrowClaimDefinitionNotFoundException_WhenClaimDefinitionNotFound() {
        // Given
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.empty());

        // When & Then
        ClaimDefinitionNotFoundException exception = assertThrows(ClaimDefinitionNotFoundException.class,
                () -> createClaimValueHandler.handle(accountCommand));
        
        assertNotNull(exception);
        verify(defRepo).findById(claimDefinitionId);
        verifyNoInteractions(accountRepo);
        verifyNoInteractions(mappingRepo);
        verifyNoInteractions(valueRepo);
    }

    @Test
    void shouldThrowAccountNotFoundException_WhenAccountNotFound() {
        // Given
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(testClaimDefinition));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.empty());

        // When & Then
        AccountNotFoundException exception = assertThrows(AccountNotFoundException.class,
                () -> createClaimValueHandler.handle(accountCommand));
        
        assertNotNull(exception);
        verify(defRepo).findById(claimDefinitionId);
        verify(accountRepo).findInfoById(accountId);
        verifyNoInteractions(mappingRepo);
        verifyNoInteractions(valueRepo);
    }

    @Test
    void shouldThrowIllegalStateException_WhenNoApplicableMappingFound() {
        // Given
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(testClaimDefinition));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.of(testAccountBusinessInfo));
        when(mappingRepo.findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId))
                .thenReturn(Collections.emptyList());

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
                () -> createClaimValueHandler.handle(accountCommand));
        
        assertTrue(exception.getMessage().contains("No applicable mapping for claim definition"));
        verify(defRepo).findById(claimDefinitionId);
        verify(accountRepo).findInfoById(accountId);
        verify(mappingRepo).findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId);
        verifyNoInteractions(valueRepo);
    }

    @Test
    void shouldThrowDuplicateClaimValueException_WhenAccountAlreadyHasValueForClaim() {
        // Given
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(testClaimDefinition));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.of(testAccountBusinessInfo));
        when(mappingRepo.findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId))
                .thenReturn(Arrays.asList(testMapping));
        when(valueRepo.existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId))
                .thenReturn(true);

        // When & Then
        DuplicateClaimValueException exception = assertThrows(DuplicateClaimValueException.class,
                () -> createClaimValueHandler.handle(accountCommand));
        
        assertTrue(exception.getMessage().contains("Account already has a value for claim"));
        verify(defRepo).findById(claimDefinitionId);
        verify(accountRepo).findInfoById(accountId);
        verify(mappingRepo).findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId);
        verify(valueRepo).existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId);
        verify(valueRepo, never()).existsForAccountType(any(), any(), any(), any());
        verify(valueRepo, never()).saveAndFlush(any());
    }

    @Test
    void shouldThrowDuplicateClaimValueException_WhenValueAlreadyExistsForAnotherAccount() {
        // Given
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(testClaimDefinition));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.of(testAccountBusinessInfo));
        when(mappingRepo.findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId))
                .thenReturn(Arrays.asList(testMapping));
        when(valueRepo.existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId))
                .thenReturn(false);
        when(valueRepo.existsForAccountType(AccountType.WORKFORCE, claimDefinitionId, "<EMAIL>", accountId))
                .thenReturn(true);

        // When & Then
        DuplicateClaimValueException exception = assertThrows(DuplicateClaimValueException.class,
                () -> createClaimValueHandler.handle(accountCommand));
        
        assertTrue(exception.getMessage().contains("is already used by another"));
        assertTrue(exception.getMessage().contains("WORKFORCE account"));
        verify(defRepo).findById(claimDefinitionId);
        verify(accountRepo).findInfoById(accountId);
        verify(mappingRepo).findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId);
        verify(valueRepo).existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId);
        verify(valueRepo).existsForAccountType(AccountType.WORKFORCE, claimDefinitionId, "<EMAIL>", accountId);
        verify(valueRepo, never()).saveAndFlush(any());
    }

    @Test
    void shouldEnforceUniqueness_WhenMappingHasEnforceUniquenessTrue() {
        // Given
        ClaimSetDefinitionMappingJpaEntity uniqueMapping = createTestMapping();
        uniqueMapping.getClaimSet().setIsIdentifier(false);
        uniqueMapping.setEnforceUniqueness(true);
        
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(testClaimDefinition));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.of(testAccountBusinessInfo));
        when(mappingRepo.findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId))
                .thenReturn(Arrays.asList(uniqueMapping));
        when(valueRepo.existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId))
                .thenReturn(true);

        // When & Then
        DuplicateClaimValueException exception = assertThrows(DuplicateClaimValueException.class,
                () -> createClaimValueHandler.handle(accountCommand));
        
        assertTrue(exception.getMessage().contains("Account already has a value for claim"));
        verify(valueRepo).existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId);
    }

    @Test
    void shouldEnforceUniqueness_WhenClaimSetIsIdentifier() {
        // Given
        ClaimSetDefinitionMappingJpaEntity identifierMapping = createTestMapping();
        identifierMapping.getClaimSet().setIsIdentifier(true);
        identifierMapping.setEnforceUniqueness(false);
        
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(testClaimDefinition));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.of(testAccountBusinessInfo));
        when(mappingRepo.findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId))
                .thenReturn(Arrays.asList(identifierMapping));
        when(valueRepo.existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId))
                .thenReturn(true);

        // When & Then
        DuplicateClaimValueException exception = assertThrows(DuplicateClaimValueException.class,
                () -> createClaimValueHandler.handle(accountCommand));
        
        assertTrue(exception.getMessage().contains("Account already has a value for claim"));
        verify(valueRepo).existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId);
    }

    @Test
    void shouldEnforceUniqueness_WhenBothIsIdentifierAndEnforceUniquenessAreTrue() {
        // Given
        ClaimSetDefinitionMappingJpaEntity bothTrueMapping = createTestMapping();
        bothTrueMapping.getClaimSet().setIsIdentifier(true);
        bothTrueMapping.setEnforceUniqueness(true);
        
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(testClaimDefinition));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.of(testAccountBusinessInfo));
        when(mappingRepo.findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId))
                .thenReturn(Arrays.asList(bothTrueMapping));
        when(valueRepo.existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId))
                .thenReturn(true);

        // When & Then
        DuplicateClaimValueException exception = assertThrows(DuplicateClaimValueException.class,
                () -> createClaimValueHandler.handle(accountCommand));
        
        assertTrue(exception.getMessage().contains("Account already has a value for claim"));
        verify(valueRepo).existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId);
    }

    @Test
    void shouldSuccessfullyCreateClaimValue_WithMultipleMappings_NoneRequireUniqueness() {
        // Given
        ClaimValueJpaEntity expectedClaimValue = createTestClaimValue(OwnerType.ACCOUNT, accountId);
        
        ClaimSetDefinitionMappingJpaEntity mapping1 = createTestMapping();
        mapping1.getClaimSet().setIsIdentifier(false);
        mapping1.setEnforceUniqueness(false);
        
        ClaimSetDefinitionMappingJpaEntity mapping2 = createSecondTestMapping();
        mapping2.getClaimSet().setIsIdentifier(false);
        mapping2.setEnforceUniqueness(false);
        
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(testClaimDefinition));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.of(testAccountBusinessInfo));
        when(mappingRepo.findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId))
                .thenReturn(Arrays.asList(mapping1, mapping2));
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(expectedClaimValue);

        // When
        ClaimValueJpaEntity result = createClaimValueHandler.handle(accountCommand);

        // Then
        assertNotNull(result);
        assertEquals(expectedClaimValue.getId(), result.getId());
        verify(defRepo).findById(claimDefinitionId);
        verify(accountRepo).findInfoById(accountId);
        verify(mappingRepo).findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId);
        verify(valueRepo).saveAndFlush(any(ClaimValueJpaEntity.class));
        // Should not check uniqueness since no mapping requires it
        verify(valueRepo, never()).existsByOwnerTypeAndOwnerIdAndClaimDefinitionId(any(), any(), any());
        verify(valueRepo, never()).existsForAccountType(any(), any(), any(), any());
    }

    @Test
    @Disabled("TODO: Check if test is still valid.")
    void shouldEnforceUniqueness_WithMultipleMappings_WhenAtLeastOneRequiresUniqueness() {
        // Given
        ClaimSetDefinitionMappingJpaEntity nonUniqueMapping = createTestMapping();
        nonUniqueMapping.getClaimSet().setIsIdentifier(false);
        nonUniqueMapping.setEnforceUniqueness(false);
        
        ClaimSetDefinitionMappingJpaEntity uniqueMapping = createSecondTestMapping();
        uniqueMapping.getClaimSet().setIsIdentifier(false);
        uniqueMapping.setEnforceUniqueness(true);
        
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(testClaimDefinition));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.of(testAccountBusinessInfo));
        when(mappingRepo.findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId))
                .thenReturn(Arrays.asList(nonUniqueMapping, uniqueMapping));
        when(valueRepo.existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId))
                .thenReturn(true);

        // When & Then
        DuplicateClaimValueException exception = assertThrows(DuplicateClaimValueException.class,
                () -> createClaimValueHandler.handle(accountCommand));
        
        assertTrue(exception.getMessage().contains("Account already has a value for claim"));
        verify(valueRepo).existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId);
    }

    @Test
    @Disabled("TODO: Check if test is still valid.")
    void shouldEnforceUniqueness_WithMultipleMappings_WhenAtLeastOneIsIdentifier() {
        // Given
        ClaimSetDefinitionMappingJpaEntity nonIdentifierMapping = createTestMapping();
        nonIdentifierMapping.getClaimSet().setIsIdentifier(false);
        nonIdentifierMapping.setEnforceUniqueness(false);
        
        ClaimSetDefinitionMappingJpaEntity identifierMapping = createSecondTestMapping();
        identifierMapping.getClaimSet().setIsIdentifier(true);
        identifierMapping.setEnforceUniqueness(false);
        
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(testClaimDefinition));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.of(testAccountBusinessInfo));
        when(mappingRepo.findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId))
                .thenReturn(Arrays.asList(nonIdentifierMapping, identifierMapping));
        when(valueRepo.existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId))
                .thenReturn(true);

        // When & Then
        DuplicateClaimValueException exception = assertThrows(DuplicateClaimValueException.class,
                () -> createClaimValueHandler.handle(accountCommand));
        
        assertTrue(exception.getMessage().contains("Account already has a value for claim"));
        verify(valueRepo).existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId);
    }

    @Test
    @Disabled("TODO: Check if test is still valid.")
    void shouldEnforceUniqueness_WithMultipleMappings_MixedUniquenessSettings() {
        // Given - One mapping with both false, one with enforce=true, one with identifier=true
        ClaimSetDefinitionMappingJpaEntity noUniquenessMapping = createTestMapping();
        noUniquenessMapping.getClaimSet().setIsIdentifier(false);
        noUniquenessMapping.setEnforceUniqueness(false);
        
        ClaimSetDefinitionMappingJpaEntity enforceUniqueMapping = createSecondTestMapping();
        enforceUniqueMapping.getClaimSet().setIsIdentifier(false);
        enforceUniqueMapping.setEnforceUniqueness(true);
        
        ClaimSetDefinitionMappingJpaEntity identifierMapping = createThirdTestMapping();
        identifierMapping.getClaimSet().setIsIdentifier(true);
        identifierMapping.setEnforceUniqueness(false);
        
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(testClaimDefinition));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.of(testAccountBusinessInfo));
        when(mappingRepo.findApplicableMappings(businessId, AccountType.WORKFORCE, claimDefinitionId))
                .thenReturn(Arrays.asList(noUniquenessMapping, enforceUniqueMapping, identifierMapping));
        when(valueRepo.existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId))
                .thenReturn(true);

        // When & Then
        DuplicateClaimValueException exception = assertThrows(DuplicateClaimValueException.class,
                () -> createClaimValueHandler.handle(accountCommand));
        
        assertTrue(exception.getMessage().contains("Account already has a value for claim"));
        verify(valueRepo).existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType.ACCOUNT, accountId, claimDefinitionId, claimSetId);
    }

    // Helper methods
    private Business createTestBusiness() {
        Business business = new Business();
        business.setId(businessId);
        business.setName("Test Business");
        return business;
    }

    private ClaimDefinitionJpaEntity createTestClaimDefinition() {
        return new ClaimDefinitionJpaEntity(
                claimDefinitionId, "work_email", "Work Email", "Work email address",
                DataTypeEnum.STRING, "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$");
    }

    private AccountBusinessInfo createTestAccountBusinessInfo() {
        AccountBusinessInfo info = new AccountBusinessInfo();
        info.setBusinessId(businessId);
        info.setAccountType(AccountType.WORKFORCE);
        return info;
    }

    private ClaimSetJpaEntity createTestClaimSet() {
        ClaimSetJpaEntity claimSet = new ClaimSetJpaEntity(testBusiness, AccountType.WORKFORCE, true);
        claimSet.setId(UUID.randomUUID());
        claimSet.setName("Workforce Identifier Claims");
        claimSet.setLookupStrategy(LookupStrategy.ALL_CLAIMS_MUST_MATCH);
        claimSet.setIsIdentifier(true);
        return claimSet;
    }

    private ClaimSetDefinitionMappingJpaEntity createTestMapping() {
        ClaimSetDefinitionMappingJpaEntity mapping = new ClaimSetDefinitionMappingJpaEntity();
        mapping.setId(UUID.randomUUID());
        mapping.setClaimSet(testClaimSet);
        mapping.setClaimDefinition(testClaimDefinition);
        mapping.setEnforceUniqueness(false);
        return mapping;
    }

    private ClaimSetDefinitionMappingJpaEntity createSecondTestMapping() {
        ClaimSetJpaEntity secondClaimSet = new ClaimSetJpaEntity(testBusiness, AccountType.WORKFORCE, false);
        secondClaimSet.setId(UUID.randomUUID());
        secondClaimSet.setName("Second Claim Set");
        secondClaimSet.setLookupStrategy(LookupStrategy.ALL_CLAIMS_MUST_MATCH);
        
        ClaimSetDefinitionMappingJpaEntity mapping = new ClaimSetDefinitionMappingJpaEntity();
        mapping.setId(UUID.randomUUID());
        mapping.setClaimSet(secondClaimSet);
        mapping.setClaimDefinition(testClaimDefinition);
        mapping.setEnforceUniqueness(false);
        return mapping;
    }

    private ClaimSetDefinitionMappingJpaEntity createThirdTestMapping() {
        ClaimSetJpaEntity thirdClaimSet = new ClaimSetJpaEntity(testBusiness, AccountType.WORKFORCE, false);
        thirdClaimSet.setId(UUID.randomUUID());
        thirdClaimSet.setName("Third Claim Set");
        thirdClaimSet.setLookupStrategy(LookupStrategy.ALL_CLAIMS_MUST_MATCH);
        
        ClaimSetDefinitionMappingJpaEntity mapping = new ClaimSetDefinitionMappingJpaEntity();
        mapping.setId(UUID.randomUUID());
        mapping.setClaimSet(thirdClaimSet);
        mapping.setClaimDefinition(testClaimDefinition);
        mapping.setEnforceUniqueness(false);
        return mapping;
    }

    private CreateClaimValueCommand createIdentityCommand() {
        return new CreateClaimValueCommand(
                claimDefinitionId,
                identityId,
                null,
                "<EMAIL>",
                true,
                false,
                "TEST"
        );
    }

    private CreateClaimValueCommand createAccountCommand() {
        return new CreateClaimValueCommand(
                claimSetId,
                claimDefinitionId,
                null,
                accountId,
                "<EMAIL>",
                true,
                false,
                "TEST",
                null
        );
    }

    private ClaimValueJpaEntity createTestClaimValue(OwnerType ownerType, UUID ownerId) {
        ClaimValueJpaEntity claimValue = new ClaimValueJpaEntity(testClaimDefinition, ownerType, ownerId);
        claimValue.setId(claimSetId);
        claimValue.setValue("<EMAIL>");
        claimValue.setPrimary(true);
        claimValue.setComputed(false);
        claimValue.setSource("TEST");
        return claimValue;
    }
} 