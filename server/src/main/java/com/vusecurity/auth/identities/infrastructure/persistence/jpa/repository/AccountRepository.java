package com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.identities.application.dto.AccountBusinessInfo;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Spring Data JPA repository for AccountJpaEntity.
 * Infrastructure-only interface for database operations.
 */
@Repository
public interface AccountRepository extends JpaRepository<AccountJpaEntity, UUID>, JpaSpecificationExecutor<AccountJpaEntity> {

    /**
     * Find account by ID.
     * @param id the account ID
     * @return the account if found
     */
    @Query("SELECT a FROM AccountJpaEntity a WHERE a.id = :id")
    Optional<AccountJpaEntity> findById(@Param("id") UUID id);

    /**
     * Find accounts by identity ID.
     * @param identityId the identity ID
     * @param pageable pagination information
     * @return page of accounts
     */
    @Query("SELECT a FROM AccountJpaEntity a WHERE a.identity.id = :identityId")
    Page<AccountJpaEntity> findByIdentityId(@Param("identityId") UUID identityId, Pageable pageable);

    /**
     * Find accounts by business ID, account type, and matching claim values using ALL_CLAIMS_MUST_MATCH strategy.
     * This query uses a subquery to first find account IDs that match all claim criteria, then selects the full accounts.
     * This approach avoids GROUP BY issues with SQL Server when selecting entity objects.
     *
     * @param businessId the business ID
     * @param accountType the account type
     * @param claimPairs the list of claim definition ID and value pairs as concatenated strings
     * @param pairCount the number of claim definition/value pairs (for HAVING clause)
     * @return list of accounts that match all provided claims
     */
    @Query("""
        SELECT a FROM AccountJpaEntity a
        WHERE a.business.id = :businessId
        AND a.accountType = :accountType
        AND a.id IN (
            SELECT cv.ownerId FROM ClaimValueJpaEntity cv
            JOIN cv.claimDefinition cd
            WHERE cv.ownerType = com.vusecurity.auth.contracts.enums.OwnerType.ACCOUNT
            AND UPPER(CONCAT(CAST(cd.id AS string), ':', cv.value)) IN :claimPairs
            GROUP BY cv.ownerId
            HAVING COUNT(DISTINCT cd.id) = :pairCount
        )
    """)
    List<AccountJpaEntity> findAccountsByBusinessAndAccountTypeAndClaims(
            @Param("businessId") UUID businessId,
            @Param("accountType") AccountType accountType,
            @Param("claimPairs") List<String> claimPairs,
            @Param("pairCount") long pairCount
    );

    /**
     * Check if account exists by ID.
     * @param id the account ID
     * @return true if exists, false otherwise
     */
    boolean existsById(UUID id);

    /**
     * Delete account by ID.
     * @param id the account ID
     */
    void deleteById(UUID id);

    @Query("""
       SELECT new com.vusecurity.auth.identities.application.dto.AccountBusinessInfo(
                  a.business.id,
                  a.accountType)
         FROM AccountJpaEntity a
        WHERE a.id = :accountId
       """)
    Optional<AccountBusinessInfo> findInfoById(UUID accountId);
    
    /**
     * Filter UUIDs that don't belong to the provided business from a list
     * @param list List of UUIDs to check if the exist for the current business.
     * @return List of existing UUIDs
     */
    @Query("""
        SELECT a.id FROM AccountJpaEntity a
        WHERE a.business.id = :businessId
        AND a.id IN :idsToCheck
    """)
    List<UUID> filterByBusiness(UUID businessId, List<UUID> idsToCheck);
    
    /**
     * Find account by ID with roles eagerly loaded.
     * @param id the account ID
     * @return the account with roles if found
     */
    @Query("""
        SELECT a FROM AccountJpaEntity a
        LEFT JOIN FETCH a.roles
        WHERE a.id = :id
    """)
    Optional<AccountJpaEntity> findByIdWithRoles(@Param("id") UUID id);
    
    /**
     * Find account by ID with group memberships eagerly loaded.
     * @param id the account ID
     * @return the account with group memberships if found
     */
    @Query("""
        SELECT a FROM AccountJpaEntity a
        LEFT JOIN FETCH a.groupMemberships gm
        LEFT JOIN FETCH gm.group
        WHERE a.id = :id
    """)
    Optional<AccountJpaEntity> findByIdWithGroupMemberships(@Param("id") UUID id);
}
