package com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.util.UUID;

@Schema(description = "Claim that contains exactly one value")
public record ComplexClaimValueRequest(

        @Schema(description = "Unique identifier of the claim set",
            example = "123e4567-e89b-12d3-a456-************")
        UUID claimSetId,

        @NotNull
        UUID claimDefinitionId,

        @NotNull
        String value,

        String source

) implements ClaimValueRequest {
    public ComplexClaimValueRequest(
        UUID claimSetId,
        UUID claimDefinitionId,
        String value
    ){
        this(claimSetId, claimDefinitionId, value, "USER_INPUT");
    }
}
