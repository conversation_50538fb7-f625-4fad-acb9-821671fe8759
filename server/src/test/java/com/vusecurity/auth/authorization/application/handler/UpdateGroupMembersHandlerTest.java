package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.command.UpdateGroupMembersCommand;
import com.vusecurity.auth.authorization.application.exception.AccountNotFoundException;
import com.vusecurity.auth.authorization.application.exception.GroupNotFoundException;
import com.vusecurity.auth.authorization.application.exception.InvalidGroupRequestException;
import com.vusecurity.auth.authorization.domain.model.GroupMembershipRole;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.GroupMembershipJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.GroupMembershipRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountGroupRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UpdateGroupMembersHandlerTest {

    @Mock private AccountGroupRepository accountGroupRepository;
    @Mock private AccountRepository accountRepository;
    @Mock private GroupMembershipRepository groupMembershipRepository;

    @InjectMocks private UpdateGroupMembersHandler updateGroupMembersHandler;

    private UUID groupId;
    private AccountGroupJpaEntity group;
    private AccountJpaEntity account1, account2, ownerAccount;
    private GroupMembershipJpaEntity membership1, membership2;

    @BeforeEach
    void setUp() {
        groupId = UUID.randomUUID();
        group = new AccountGroupJpaEntity();
        group.setId(groupId);

        account1 = new AccountJpaEntity();
        account1.setId(UUID.randomUUID());
        account2 = new AccountJpaEntity();
        account2.setId(UUID.randomUUID());
        ownerAccount = new AccountJpaEntity();
        ownerAccount.setId(UUID.randomUUID());

        membership1 = new GroupMembershipJpaEntity(account1, group, GroupMembershipRole.MEMBER);
        membership2 = new GroupMembershipJpaEntity(account2, group, GroupMembershipRole.MEMBER);
    }

    @Test
    void shouldAddAndRemoveMembers() {
        // Given: Group has account1. Command wants account2 and ownerAccount.
        when(accountGroupRepository.findById(groupId)).thenReturn(Optional.of(group));
        when(groupMembershipRepository.findByGroupId(groupId)).thenReturn(List.of(membership1));
        when(accountRepository.findAllById(Set.of(account2.getId(), ownerAccount.getId())))
                .thenReturn(List.of(account2, ownerAccount));

        UpdateGroupMembersCommand command = new UpdateGroupMembersCommand(
                groupId,
                List.of(account2.getId(), ownerAccount.getId()),
                List.of(ownerAccount.getId())
        );

        // When
        updateGroupMembersHandler.updateMembers(command);

        // Then
        verify(groupMembershipRepository).deleteByGroupIdAndAccountIdIn(groupId, List.of(account1.getId()));

        ArgumentCaptor<List<GroupMembershipJpaEntity>> newMembersCaptor = ArgumentCaptor.forClass(List.class);
        verify(groupMembershipRepository).saveAll(newMembersCaptor.capture());
        
        List<GroupMembershipJpaEntity> newMembers = newMembersCaptor.getValue();
        assertEquals(2, newMembers.size());
        assertTrue(newMembers.stream().anyMatch(m -> m.getAccount().equals(account2) && m.getMembershipRole() == GroupMembershipRole.MEMBER));
        assertTrue(newMembers.stream().anyMatch(m -> m.getAccount().equals(ownerAccount) && m.getMembershipRole() == GroupMembershipRole.OWNER));
    }

    @Test
    void shouldThrowGroupNotFoundException_whenGroupDoesNotExist() {
        // Given
        when(accountGroupRepository.findById(groupId)).thenReturn(Optional.empty());
        UpdateGroupMembersCommand command = new UpdateGroupMembersCommand(groupId, List.of(), List.of());

        // When & Then
        assertThrows(GroupNotFoundException.class, () -> updateGroupMembersHandler.updateMembers(command));
    }

    @Test
    void shouldThrowAccountNotFoundException_whenAccountToAddIsNotFound() {
        // Given
        when(accountGroupRepository.findById(groupId)).thenReturn(Optional.of(group));
        when(groupMembershipRepository.findByGroupId(groupId)).thenReturn(Collections.emptyList());
        when(accountRepository.findAllById(any())).thenReturn(Collections.emptyList()); // Simulate account not found
        UpdateGroupMembersCommand command = new UpdateGroupMembersCommand(groupId, List.of(account1.getId()), List.of());

        // When & Then
        assertThrows(AccountNotFoundException.class, () -> updateGroupMembersHandler.updateMembers(command));
    }

    @Test
    void shouldRemoveAllMembers_whenAccountListIsNull() {
        // Given
        when(accountGroupRepository.findById(groupId)).thenReturn(Optional.of(group));
        when(groupMembershipRepository.findByGroupId(groupId)).thenReturn(List.of(membership1, membership2));
        UpdateGroupMembersCommand command = new UpdateGroupMembersCommand(groupId, null, null);

        // When
        updateGroupMembersHandler.updateMembers(command);

        // Then
        ArgumentCaptor<List<UUID>> captor = ArgumentCaptor.forClass(List.class);
        verify(groupMembershipRepository).deleteByGroupIdAndAccountIdIn(eq(groupId), captor.capture());
        
        List<UUID> deletedIds = captor.getValue();
        assertEquals(2, deletedIds.size());
        assertTrue(deletedIds.containsAll(List.of(account1.getId(), account2.getId())));
        
        verify(groupMembershipRepository, never()).saveAll(any());
    }

    @Test
    void shouldThrowException_whenCommandValidationFails() {
        // Given
        UpdateGroupMembersCommand command = new UpdateGroupMembersCommand(null, List.of(), List.of());

        // When & Then
        assertThrows(InvalidGroupRequestException.class, () -> updateGroupMembersHandler.updateMembers(command));
        verifyNoInteractions(accountGroupRepository, groupMembershipRepository, accountRepository);
    }
} 