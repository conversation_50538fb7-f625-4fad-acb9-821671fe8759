package com.vusecurity.auth.claims.api.handler;

import com.vusecurity.core.commons.ApiMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.server.ResponseStatusException;

import java.util.NoSuchElementException;

/**
 * Exception handler for BusinessClaimDefinitionController.
 * Handles specific exceptions thrown by business claim definition operations and converts them to appropriate HTTP responses.
 */
@ControllerAdvice(assignableTypes = {com.vusecurity.auth.claims.api.controller.BusinessClaimDefinitionController.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class BusinessClaimDefinitionErrorControllerHandler {

    private static final Logger logger = LoggerFactory.getLogger(BusinessClaimDefinitionErrorControllerHandler.class);

    /**
     * Handle ResponseStatusException thrown by business claim definition controller.
     * This ensures that the HTTP status code from the ResponseStatusException is preserved.
     */
    @ExceptionHandler(ResponseStatusException.class)
    public ResponseEntity<ApiMessage> handleResponseStatusException(ResponseStatusException ex) {
        logger.debug("Handling ResponseStatusException: {} - {}", ex.getStatusCode(), ex.getReason());
        
        // Extract the status code and reason from the ResponseStatusException
        int statusCode = ex.getStatusCode().value();
        String message = ex.getReason() != null ? ex.getReason() : ex.getMessage();
        
        // Create appropriate error code based on status
        int errorCode = switch (statusCode) {
            case 400 -> 5100; // Bad Request
            case 404 -> 5104; // Not Found
            case 500 -> 5150; // Internal Server Error
            default -> 5100; // Generic error
        };
        
        ApiMessage error = new ApiMessage(errorCode, message);
        return ResponseEntity.status(ex.getStatusCode()).body(error);
    }

    /**
     * Handle NoSuchElementException thrown by business claim definition services.
     * This is a fallback in case the service throws NoSuchElementException directly.
     */
    @ExceptionHandler(NoSuchElementException.class)
    public ResponseEntity<ApiMessage> handleNoSuchElementException(NoSuchElementException ex) {
        logger.debug("Handling NoSuchElementException: {}", ex.getMessage());
        
        ApiMessage error = new ApiMessage(5104, ex.getMessage());
        return ResponseEntity.status(404).body(error);
    }

    /**
     * Handle IllegalArgumentException thrown by business claim definition services.
     * This is a fallback in case the service throws IllegalArgumentException directly.
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiMessage> handleIllegalArgumentException(IllegalArgumentException ex) {
        logger.debug("Handling IllegalArgumentException: {}", ex.getMessage());

        ApiMessage error = new ApiMessage(5100, ex.getMessage());
        return ResponseEntity.status(400).body(error);
    }

    /**
     * Handle MethodArgumentTypeMismatchException thrown when path variables or request parameters have invalid format.
     * This typically happens when UUID format is invalid or enum values are not recognized.
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ApiMessage> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException ex) {
        logger.debug("Handling MethodArgumentTypeMismatchException: {}", ex.getMessage());

        String message = String.format("Invalid parameter '%s': %s", ex.getName(), ex.getMessage());
        ApiMessage error = new ApiMessage(5101, message);
        return ResponseEntity.status(400).body(error);
    }

    /**
     * Handle RuntimeException thrown by business claim definition services.
     * This is a fallback for unexpected runtime exceptions.
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiMessage> handleRuntimeException(RuntimeException ex) {
        logger.error("Handling unexpected RuntimeException: {}", ex.getMessage(), ex);

        ApiMessage error = new ApiMessage(5150, ex.getMessage());
        return ResponseEntity.status(500).body(error);
    }
}
