package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.ReplaceClaimDefinitionsInClaimSetCommand;
import com.vusecurity.auth.claims.application.exception.ClaimDefinitionNotFoundException;
import com.vusecurity.auth.claims.application.exception.ClaimSetNotFoundException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.service.ClaimSetDefinitionMappingService;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ReplaceClaimDefinitionsInClaimSetHandler {

    private static final Logger log =
            LoggerFactory.getLogger(ReplaceClaimDefinitionsInClaimSetHandler.class);

    private final EntityManager em;
    private final ClaimSetRepository claimSetRepository;
    private final ClaimDefinitionRepository claimDefinitionRepository;
    private final ClaimSetDefinitionMappingService mappingService;

    @Transactional
    public void replaceClaimDefinitionsInClaimSet(ReplaceClaimDefinitionsInClaimSetCommand cmd) {
        log.debug("Replacing claim definitions in set {} with {}",
                cmd.claimSetId(), cmd.claimDefinitions());

        cmd.validate();

        ClaimSetJpaEntity claimSet = claimSetRepository.findById(cmd.claimSetId())
                .orElseThrow(() -> new ClaimSetNotFoundException(cmd.claimSetId()));

        Set<UUID> requestedIds = new HashSet<>();
        for(ReplaceClaimDefinitionsInClaimSetCommand.ClaimDefinitionsToClaimSetCommand claimIdsDefinitions:cmd.claimDefinitions()){
            requestedIds.add(claimIdsDefinitions.claimDefinitionId());
        }
        // Get current claim definition IDs using the mapping service
        Set<UUID> currentIds = mappingService.getClaimDefinitionsForClaimSet(claimSet).stream()
                .map(ClaimDefinitionJpaEntity::getId)
                .collect(Collectors.toSet());

        if (currentIds.equals(requestedIds)) {
            log.debug("Nothing to change for claim set {}", cmd.claimSetId());
            return;
        }

        // Validate that all requested ids exist – one COUNT query
        long found = claimDefinitionRepository.countByIdIn(requestedIds);
        if (found != requestedIds.size()) {
            throw new ClaimDefinitionNotFoundException("One or more claim definitions not found");
        }

        // Clear all existing mappings
        mappingService.clearClaimDefinitionsFromClaimSet(claimSet);

        // Add new claim definitions using the mapping service
        for (ReplaceClaimDefinitionsInClaimSetCommand.ClaimDefinitionsToClaimSetCommand def : cmd.claimDefinitions()) {
            ClaimDefinitionJpaEntity claimDefinition = em.getReference(ClaimDefinitionJpaEntity.class, def.claimDefinitionId());

            Boolean requestedEnforce = def.enforceUniqueness();
            boolean enforceUniqueness;

            if (Boolean.TRUE.equals(claimSet.getIsIdentifier())) {
                if (requestedEnforce != null && !requestedEnforce) {
                    throw new IllegalArgumentException("Claim set with ID " + claimSet.getId() + " is an identifier and cannot have enforceUniqueness = false");
                }
                enforceUniqueness = true;
            } else {
                enforceUniqueness = Boolean.TRUE.equals(requestedEnforce);
            }

            mappingService.addClaimDefinitionToClaimSet(
                    claimSet,
                    claimDefinition,
                    def.claimDefinitionOrder(),
                    enforceUniqueness
            );
        }

        // No explicit save() needed – managed entity will flush automatically
        log.debug("Successfully replaced claim definitions in claim set {}", cmd.claimSetId());
    }
}
