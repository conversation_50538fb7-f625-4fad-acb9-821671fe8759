package com.vusecurity.auth.identities.application.command;

import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.*;
import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.identities.application.exception.PrimaryClaimValueOutOfBoundsException;

import java.util.List;
import java.util.Map;
import java.util.UUID;

public record CreateAccountCommand(
        UUID identityId,
        UUID businessId,
        UUID identityProviderId,
        AccountType accountType,
        AccountLifecycleState lifecycleState,
        Map<String, Object> metadata,
        List<ClaimValueRequest> claimValueList) {

    public void validate() {
        if (identityId == null)        throw new IllegalArgumentException("identityId is required");
        if (businessId == null)        throw new IllegalArgumentException("businessId is required");
        if (identityProviderId == null)throw new IllegalArgumentException("identityProviderId is required");
        if (accountType == null)       throw new IllegalArgumentException("accountType is required");

        // Validate claim values if provided
        if (claimValueList != null) {
            for (ClaimValueRequest claimValue : claimValueList) {
                if (claimValue.claimSetId() == null) {
                    throw new IllegalArgumentException("claimSetId is required for all claim values");
                }

                if (claimValue.claimDefinitionId() == null) {
                    throw new IllegalArgumentException("claimDefinitionId is required for all claim values");
                }

                switch (claimValue) {
                    case ScalarClaimValueRequest scalar -> {
                        if (scalar.value() == null || scalar.value().trim().isEmpty()) {
                            throw new IllegalArgumentException("value is required for scalar claim values");
                        }
                    }
                    case ArrayClaimValueRequest array -> {
                        if (array.values() == null) {
                            throw new IllegalArgumentException("values list cannot be null for array claim values");
                        }
                        // Allow empty arrays - they will be ignored during processing
                        if (!array.values().isEmpty()) {
                            if (array.values().stream().anyMatch(v -> v == null || v.trim().isEmpty())) {
                                throw new IllegalArgumentException("all values in array claim must be non-empty");
                            }
                            if (array.primaryIndex() == null || array.primaryIndex() < 0 || array.primaryIndex() >= array.values().size()) {
                                throw new PrimaryClaimValueOutOfBoundsException(array.primaryIndex(), array.values().size());
                            }
                        }
                    }
                    case ComplexClaimValueRequest complex -> {
                        if (complex.value() == null || complex.value().trim().isEmpty()) {
                            throw new IllegalArgumentException("value is required for scalar claim values");
                        }
                    }
                    case ArrayComplexClaimValueRequest arrayComplex -> {
                        if (arrayComplex.values() == null) {
                            throw new IllegalArgumentException("values list cannot be null for array claim values");
                        }
                        // Allow empty arrays - they will be ignored during processing
                        if (!arrayComplex.values().isEmpty()) {
                            if (arrayComplex.values().stream().anyMatch(v -> v == null || v.toString().trim().isEmpty())) {
                                throw new IllegalArgumentException("all values in array claim must be non-empty");
                            }
                            if (arrayComplex.primaryIndex() == null || arrayComplex.primaryIndex() < 0 || arrayComplex.primaryIndex() >= arrayComplex.values().size()) {
                                throw new PrimaryClaimValueOutOfBoundsException(arrayComplex.primaryIndex(), arrayComplex.values().size());
                            }
                        }
                    }
                }
            }
        }
    }
}
