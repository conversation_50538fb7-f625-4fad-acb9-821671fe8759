package com.vusecurity.auth.channel.api.handler;

import com.vusecurity.channel.usecases.exceptions.ChannelAlreadyExistException;
import com.vusecurity.channel.usecases.exceptions.ChannelNameRequiredException;
import com.vusecurity.channel.usecases.exceptions.ChannelNotFoundException;
import com.vusecurity.channel.usecases.exceptions.ChannelTypeInvalidException;
import com.vusecurity.core.commons.ApiMessage;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@ControllerAdvice(assignableTypes = {com.vusecurity.auth.channel.api.controller.ChannelController.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ChannelErrorControllerHandler {
    @ExceptionHandler(ChannelNotFoundException.class)
    public ResponseEntity<ApiMessage> handleNotFoundException(ChannelNotFoundException ex) {
        ApiMessage error = new ApiMessage(3001, ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }

    @ExceptionHandler(ChannelAlreadyExistException.class)
    public ResponseEntity<ApiMessage> handleAlreadyExistsException(ChannelAlreadyExistException ex) {
        ApiMessage error = new ApiMessage(3002, ex.getMessage());
        return ResponseEntity.status(HttpStatus.CONFLICT).body(error);
    }

    @ExceptionHandler(ChannelNameRequiredException.class)
    public ResponseEntity<ApiMessage> handleNameRequiredException(ChannelNameRequiredException ex) {
        ApiMessage error = new ApiMessage(3003, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }

    @ExceptionHandler(ChannelTypeInvalidException.class)
    public ResponseEntity<ApiMessage> handleTypeInvalidException(ChannelTypeInvalidException ex) {
        ApiMessage error = new ApiMessage(3004, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }
}
