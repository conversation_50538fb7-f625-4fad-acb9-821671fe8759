package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.command.DeleteRoleCommand;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class DeleteRoleHandler {

    private static final Logger logger = LoggerFactory.getLogger(DeleteRoleHandler.class);
    
    private final RoleRepository roleRepository;

    @Transactional
    public void deleteRole(DeleteRoleCommand command) {
        logger.debug("Deleting role: {}", command.roleId());

        try {
            // Validate command
            command.validate();

            RoleJpaEntity role = roleRepository.findById(command.roleId())
                    .orElseThrow(() -> new IllegalArgumentException("Role not found with ID: " + command.roleId()));

            // Check if role has any accounts assigned (if needed)
            // This could be added as a business rule if required

            roleRepository.delete(role);
            logger.debug("Role deleted successfully: {}", command.roleId());

        } catch (Exception e) {
            logger.error("Error deleting role: {}", e.getMessage());
            throw e;
        }
    }
}
