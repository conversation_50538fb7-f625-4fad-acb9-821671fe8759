package com.vusecurity.auth.contracts.api.v1.dto.claims.claimset;

import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.LookupStrategy;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.UUID;

@Data
@Accessors(chain = true)
@Schema(description = "Summary information about a claim set (used to avoid circular references)")
public class ClaimSetSummaryResponse {
    @Schema(description = "Unique identifier of the claim set",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    @Schema(description = "Unique identifier of the business this claim set belongs to",
            example = "987fcdeb-51a2-43d1-9c4f-123456789abc")
    private UUID businessId;

    @Schema(description = "Account type this claim set applies to",
            example = "WORKFORCE")
    private AccountType accountType;

    @Schema(description = "Whether this claim set is an identifier claim set (part of composite primary key)",
            example = "true")
    private Boolean isIdentifier;

    @Schema(description = "Name of the claim set",
            example = "Employee Basic Claims")
    private String name;

    @Schema(description = "Description of the claim set",
            example = "Basic claims for workforce employees")
    private String description;

    @Schema(description = "Strategy for resolving claim handles when multiple claim definitions are involved",
            example = "ALL_CLAIMS_MUST_MATCH")
    private LookupStrategy lookupStrategy;
}
