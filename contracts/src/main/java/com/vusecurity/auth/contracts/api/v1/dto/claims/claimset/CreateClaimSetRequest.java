package com.vusecurity.auth.contracts.api.v1.dto.claims.claimset;

import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.LookupStrategy;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import java.util.UUID;

@Data
@Schema(description = "Request to create a new claim set")
public class CreateClaimSetRequest {

    @NotNull
    @Schema(description = "Unique identifier of the business this claim set belongs to",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID businessId;

    @NotNull
    @Pattern(regexp = "[a-zA-Z0-9 _.-]*", message = "Name can only have letters, numbers, underscore, hyphen and spaces")
    @Size(min = 1, max = 255, message = "The 'name' field must be between 1 and 255 characters")
    @Schema(description = "Name of the claim set",
            example = "Employee Basic Claims")
    private String name;

    @NotNull
    @Schema(description = "Account type this claim set applies to",
            example = "WORKFORCE")
    private AccountType accountType;

    @NotNull
    @Schema(description = "Whether this claim set is an identifier claim set (part of composite primary key)",
            example = "true")
    private Boolean isIdentifier;

    @Pattern(regexp = "[a-zA-Z0-9 _.-]*", message = "Name can only have letters, numbers, underscore, hyphen and spaces")
    @Size(min = 1, max = 255, message = "The 'description' field must be between 1 and 255 characters")
    @Schema(description = "Detailed description of the claim set",
            example = "Basic claims required for all employee accounts")
    private String description;

    @Schema(description = "Strategy for resolving claim handles when multiple claim definitions are involved",
            example = "ALL_CLAIMS_MUST_MATCH")
    private LookupStrategy lookupStrategy;
}
