package com.vusecurity.auth.client.util;

import com.vusecurity.auth.client.exception.AuthServerClientException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.ResponseErrorHandler;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;

@Slf4j
public class RestTemplateErrorHandler implements ResponseErrorHandler {
    
    @Override
    public boolean hasError(ClientHttpResponse response) throws IOException {
        return response.getStatusCode().is4xxClientError() || 
               response.getStatusCode().is5xxServerError();
    }
    
    @Override
    public void handleError(ClientHttpResponse response) throws IOException {
        HttpStatus statusCode = HttpStatus.valueOf(response.getStatusCode().value());
        String responseBody = getResponseBody(response);
        
        log.error("Auth Server API error - Status: {}, Body: {}", statusCode, responseBody);
        
        String errorMessage = buildErrorMessage(statusCode, responseBody);
        
        throw new AuthServerClientException(errorMessage, statusCode);
    }
    
    private String getResponseBody(ClientHttpResponse response) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(response.getBody(), StandardCharsets.UTF_8))) {
            return reader.lines().collect(Collectors.joining("\n"));
        } catch (IOException e) {
            log.warn("Failed to read error response body", e);
            return "Unable to read response body";
        }
    }
    
    private String buildErrorMessage(HttpStatus status, String responseBody) {
        switch (status) {
            case BAD_REQUEST:
                return "Invalid request: " + responseBody;
            case UNAUTHORIZED:
                return "Authentication failed. Check your API key.";
            case FORBIDDEN:
                return "Access denied. Insufficient permissions.";
            case NOT_FOUND:
                return "Resource not found: " + responseBody;
            case CONFLICT:
                return "Conflict occurred: " + responseBody;
            case INTERNAL_SERVER_ERROR:
                return "Server error occurred: " + responseBody;
            default:
                return "Request failed with status " + status + ": " + responseBody;
        }
    }
}