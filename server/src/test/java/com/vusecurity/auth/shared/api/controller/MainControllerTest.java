package com.vusecurity.auth.shared.api.controller;

import com.vusecurity.auth.contracts.api.v1.dto.shared.SetupAdminUserResponse;
import com.vusecurity.auth.shared.infrastructure.migration.initial.SetupAdminUserService;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class MainControllerTest {

    @Test
    void initialize_returns_response_from_service() {
        SetupAdminUserService service = mock(SetupAdminUserService.class);
        SetupAdminUserResponse resp = new SetupAdminUserResponse("a", "e", "p");
        when(service.start()).thenReturn(resp);

        MainController controller = new MainController(service);
        SetupAdminUserResponse out = controller.initialize();
        assertSame(resp, out);
        verify(service).start();
    }
}

