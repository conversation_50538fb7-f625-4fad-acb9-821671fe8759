package com.vusecurity.auth.shared.api.controller;


import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.actuate.health.Status;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import com.vusecurity.auth.AppConfigProperties;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * Controller that provides a simple status endpoint for the application.
 * This endpoint can be used for health checks by load balancers, Kubernetes, etc.
 */
@RestController
@Tag(name = "Status", description = "API to check the application status")
public class StatusController {

    private static final Logger logger = LoggerFactory.getLogger(StatusController.class);
    private final AppConfigProperties appConfigProperties;

    public StatusController(AppConfigProperties appConfigProperties) {
        this.appConfigProperties = appConfigProperties;
    }

    /**
     * Returns a simple status response indicating if the application is up and running.
     * This endpoint is designed to be lightweight and fast for frequent health checks.
     *
     * @return A simple status response
     */
    @GetMapping(value = "/status", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(
            summary = "Get application status",
            description = "Returns a simple status response indicating if the application is up and running",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Application is up and running",
                            content = @Content(mediaType = "application/json")
                    ),
                    @ApiResponse(
                            responseCode = "503",
                            description = "Application is not available",
                            content = @Content(mediaType = "application/json")
                    )
            }
    )
    public ResponseEntity<Map<String, Object>> getStatus() {
        try {
            // Simple status check - if this endpoint responds, the application is running
            Map<String, Object> response = new HashMap<>();
            response.put("status", Status.UP.getCode());
            response.put("timestamp", System.currentTimeMillis());

            // Add basic version info
            response.put("version", appConfigProperties.getVersion());
            response.put("build", appConfigProperties.getBuild());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error getting application status", e);
            Map<String, Object> response = new HashMap<>();
            response.put("status", Status.DOWN.getCode());
            response.put("timestamp", System.currentTimeMillis());
            response.put("error", e.getMessage());

            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
        }
    }
}
