package com.vusecurity.auth.identities.application.exception;

import java.util.UUID;

/**
 * Exception thrown when an account with the specified identifier is not found.
 */
public class AccountNotFoundException extends AccountException {

    /**
     * Constructs a new exception with a default message including the account ID.
     *
     * @param accountId the UUID of the account that was not found
     */
    public AccountNotFoundException(UUID accountId) {
        super("Account not found: " + accountId);
    }

    /**
     * Constructs a new exception with a custom message.
     *
     * @param message the detail message
     */
    public AccountNotFoundException(String message) {
        super(message);
    }

    /**
     * Constructs a new exception with a custom message and cause.
     *
     * @param message the detail message
     * @param cause   the cause of the exception
     */
    public AccountNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * Constructs a new exception with a cause and default message.
     *
     * @param cause the cause of the exception
     */
    public AccountNotFoundException(Throwable cause) {
        super("Account not found", cause);
    }
}