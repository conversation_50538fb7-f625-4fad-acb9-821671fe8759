package com.vusecurity.auth.claims.api.controller;

import static com.vusecurity.auth.authorization.domain.PermissionNames.CLAIM_READ;

import java.util.List;
import java.util.UUID;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.vusecurity.auth.claims.application.query.GetClaimValueQuery;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.mapper.ClaimValueDtoMapper;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.OwnersResponse;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.shared.config.aspect.Oauth2Authorize;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("${app.claims.context-path}/owners")
@SecurityRequirement(name = "apikey auth")
@Tag(name = "Owners", description = "Owners management operations")
public class OwnersController {

    private final GetClaimValueQuery getClaimValueQuery;
    private final ClaimValueDtoMapper claimValueDtoMapper;

    public OwnersController(GetClaimValueQuery getClaimValueQuery,
            ClaimValueDtoMapper claimValueDtoMapper) {
        this.getClaimValueQuery = getClaimValueQuery;
        this.claimValueDtoMapper = claimValueDtoMapper;
    }

    @Operation(summary = "Get the claim values of the identity", description = "Retrieves a list of claim values associated to the identity")
    @ApiResponse(responseCode = "200", description = "List of claim values found", content = @Content(schema = @Schema(implementation = OwnersResponse.class)))
    @ApiResponse(responseCode = "401", description = "Authentication required")
    @ApiResponse(responseCode = "403", description = "Access denied")
    @GetMapping("/identity/{identityId}/claims")
    @Oauth2Authorize(permission = CLAIM_READ)
    public OwnersResponse getByIdentity(@PathVariable(value = "identityId") UUID identityId) {

        List<ClaimValueJpaEntity> claimValuesList = getClaimValueQuery
                .getClaimValuesByOwnerTypeAndOwnerId(OwnerType.IDENTITY, identityId);

        OwnersResponse response = new OwnersResponse();
        response.setClaimValues(claimValuesList.stream().map(claimValueDtoMapper::toResponse).toList());
        response.setOwnerId(identityId);
        response.setOwnerType(OwnerType.IDENTITY);

        return response;
    }

    @Operation(summary = "Get the claim values of the account", description = "Retrieves a list of claim values associated to the account")
    @ApiResponse(responseCode = "200", description = "List of claim values found", content = @Content(schema = @Schema(implementation = OwnersResponse.class)))
    @ApiResponse(responseCode = "401", description = "Authentication required")
    @ApiResponse(responseCode = "403", description = "Access denied")
    @GetMapping("/account/{accountId}/claims")
    @Oauth2Authorize(permission = CLAIM_READ)
    public OwnersResponse get(@PathVariable(value = "accountId") UUID accountId) {

        List<ClaimValueJpaEntity> claimValuesList = getClaimValueQuery
                .getClaimValuesByOwnerTypeAndOwnerId(OwnerType.ACCOUNT, accountId);

        OwnersResponse response = new OwnersResponse();
        response.setClaimValues(claimValuesList.stream().map(claimValueDtoMapper::toResponse).toList());
        response.setOwnerId(accountId);
        response.setOwnerType(OwnerType.ACCOUNT);

        return response;
    }
}
