package com.vusecurity.auth.contracts.api.v1.dto.authorization;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.Set;
import java.util.UUID;

@Data
@Schema(description = "Request to create a new role")
public class CreateRoleRequest {
    @Schema(description = "Unique identifier of the business this role belongs to",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID businessId;

    @NotNull(message = "Role name is required")
    @Size(max = 255, message = "Role name cannot exceed 255 characters")
    @Schema(description = "Name of the role",
            example = "Administrator")
    private String name;

    @Size(max = 1000, message = "Role description cannot exceed 1000 characters")
    @Schema(description = "Detailed description of the role",
            example = "Full system administrator with all permissions")
    private String description;

    @Schema(description = "Set of permission IDs to assign to this role",
            example = "[\"456e7890-a12b-34c5-d678-901234567def\", \"789abcde-f012-3456-7890-123456789abc\"]")
    private Set<UUID> permissionIds;
}
