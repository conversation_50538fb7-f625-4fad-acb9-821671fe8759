package com.vusecurity.auth.client.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "auth.server.client")
public class AuthServerClientConfiguration {
    
    private String baseUrl = "http://localhost:8082";
    
    /**
     * API Key for inter-service authentication (internal communication only)
     */
    private String apiKey;
    
    /**
     * Bearer token for user-level authentication
     */
    private String bearerToken;
    
    private String contextPath = "/api/v1";
    
    private int connectTimeout = 5000;
    
    private int readTimeout = 10000;
    
    private int maxConnections = 50;
    
    private int maxConnectionsPerRoute = 10;
    
    public String getFullBaseUrl() {
        return baseUrl + contextPath;
    }
}