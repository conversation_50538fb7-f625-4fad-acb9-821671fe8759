package com.vusecurity.auth.contracts.api.v1.dto.shared;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "Basic business information")
public class BusinessBasicInfoResponse {
    @Schema(description = "Unique identifier of the business",
            example = "123e4567-e89b-12d3-a456-426614174000")
    private String id;

    @Schema(description = "Name of the business",
            example = "Acme Corporation")
    private String name;
}
