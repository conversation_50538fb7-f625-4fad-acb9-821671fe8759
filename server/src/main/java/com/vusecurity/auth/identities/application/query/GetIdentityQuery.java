package com.vusecurity.auth.identities.application.query;

import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import org.springframework.data.domain.Page;

/**
 * Query interface for retrieving identities.
 */
public interface GetIdentityQuery {
    
    /**
     * Get an identity by its ID.
     * @param identityId the identity ID
     * @return the identity
     */
    IdentityJpaEntity getIdentityById(String identityId);
    
    /**
     * Get all identities with pagination and filtering.
     * @param query the paged query parameters
     * @return page of identities
     */
    Page<IdentityJpaEntity> getAllIdentities(GetIdentitiesPagedQuery query);
}
