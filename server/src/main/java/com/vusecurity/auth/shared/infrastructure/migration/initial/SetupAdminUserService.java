package com.vusecurity.auth.shared.infrastructure.migration.initial;

import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.RoleRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.api.v1.dto.shared.SetupAdminUserResponse;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.IdentityType;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityProviderRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityRepository;
import com.vusecurity.auth.shared.infrastructure.security.mfa.FactorServerClient;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.domain.repositories.BusinessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsProperties.Data;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

@Component
public class SetupAdminUserService {
    private static final Logger logger = LoggerFactory.getLogger(SetupAdminUserService.class);

    private final AccountRepository accountRepository;
    private final IdentityRepository identityRepository;
    private final BusinessRepository businessRepository;
    private final IdentityProviderRepository identityProviderRepository;
    private final ClaimDefinitionRepository claimDefinitionRepository;
    private final ClaimValueRepository claimValueRepository;
    private final FactorServerClient factorServerClient;
    private final RoleRepository roleRepository;

    public SetupAdminUserService(AccountRepository accountRepository, IdentityRepository identityRepository,
            BusinessRepository businessRepository, IdentityProviderRepository identityProviderRepository,
            ClaimDefinitionRepository claimDefinitionRepository, ClaimValueRepository claimValueRepository,
            FactorServerClient factorServerClient, RoleRepository roleRepository) {
        this.accountRepository = accountRepository;
        this.identityRepository = identityRepository;
        this.businessRepository = businessRepository;
        this.identityProviderRepository = identityProviderRepository;
        this.claimDefinitionRepository = claimDefinitionRepository;
        this.claimValueRepository = claimValueRepository;
        this.factorServerClient = factorServerClient;
        this.roleRepository = roleRepository;
    }

    public SetupAdminUserResponse start() {
        logger.info("SetupAdminUserService: Starting setup admin user");
        logger.trace("Entering start method.");

        IdentityProviderJpaEntity identityProvider = identityProviderRepository
                .getReferenceById(DataSeedConstants.IDENTITY_PROVIDER_ID);
        Business business = businessRepository.getReferenceById(DataSeedConstants.SYSTEM_BUSINESS_ID);
        UUID identityId = DataSeedConstants.ADMIN_IDENTITY_ID;
        UUID accountId = DataSeedConstants.ADMIN_ACCOUNT_ID;

        Optional<IdentityJpaEntity> optionalIdentity = identityRepository.findByIdentityId(identityId);

        IdentityJpaEntity identity;
        if (optionalIdentity.isEmpty()) {
            identity = new IdentityJpaEntity(
                    identityId,
                    IdentityType.PERSON,
                    "ADMIN");
            identity.activate();
            identity.setCreatedBy("SYSTEM");
            identityRepository.save(identity);
            logger.info("Identity created and saved with ID: {}", identity.getId());
        } else {
            identity = optionalIdentity.get();
            logger.info("Identity already exist in the repository. Skipping identity setup.");
        }

        AccountJpaEntity account = accountRepository.findById(accountId).orElse(null);

        if (account == null) {

            account = new AccountJpaEntity(
                    DataSeedConstants.ADMIN_ACCOUNT_ID,
                    business,
                    identity,
                    identityProvider,
                    AccountType.WORKFORCE);
            account.activate();
            account.setCreatedBy("SYSTEM");
            account = accountRepository.saveAndFlush(account);

            logger.info("Account created and saved with ID: {}", account.getId());
            logger.info("SetupAdminUserService: Admin user created successfully");
        } else {
            logger.info("Account already exist in the repository. Skipping account setup.");
        }

        RoleJpaEntity adminRole = roleRepository.findById(DataSeedConstants.ADMIN_ROLE_ID).orElse(null);

        if (adminRole != null && adminRole.getAccounts().size() == 0) {
            logger.info("Adding account to admin role");
            // NOTE: getters return defensive copies so mutating the returned Set doesn't change the entity state.
            // Build new sets, update both sides and persist using the owning side (Role) and flush to write join table.
            Set<AccountJpaEntity> updatedAccounts = adminRole.getAccounts();
            updatedAccounts.add(account);
            adminRole.setAccounts(updatedAccounts);

            Set<RoleJpaEntity> updatedRoles = account.getRoles();
            updatedRoles.add(adminRole);
            account.setRoles(updatedRoles);

            roleRepository.saveAndFlush(adminRole);
            accountRepository.saveAndFlush(account);
        } else if (adminRole == null) {
            logger.warn("Admin role with id {} not found. Skipping role assign.", DataSeedConstants.ADMIN_ROLE_ID);
        }

        String adminEmail = "<EMAIL>";
        String adminPassword = generatePassword();

        addEmailClaimValue(account, adminEmail);

        try {
            assert account != null;
            addPasswordClaimValue(account, adminPassword);
        } catch (Exception e) {
            logger.info("Error creating password. Skipping password setup.");
            return new SetupAdminUserResponse(null, null, null);
        }

        return new SetupAdminUserResponse(account.getId().toString(), adminEmail, adminPassword);
    }

    private String generatePassword() {
        String uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String lowercase = "abcdefghijklmnopqrstuvwxyz";
        String numbers = "**********";
        String specialChars = "!@#$%";
        String allChars = uppercase + lowercase + numbers + specialChars;

        SecureRandom random = new SecureRandom();
        StringBuilder password = new StringBuilder();

        // Ensure at least one character from each required category
        password.append(uppercase.charAt(random.nextInt(uppercase.length())));
        password.append(lowercase.charAt(random.nextInt(lowercase.length())));
        password.append(numbers.charAt(random.nextInt(numbers.length())));
        password.append(specialChars.charAt(random.nextInt(specialChars.length())));

        // Fill the remaining positions (12 - 4 = 8) with random characters from all
        // categories
        for (int i = 4; i < 12; i++) {
            password.append(allChars.charAt(random.nextInt(allChars.length())));
        }

        // Shuffle the password to avoid predictable patterns
        char[] passwordArray = password.toString().toCharArray();
        for (int i = passwordArray.length - 1; i > 0; i--) {
            int j = random.nextInt(i + 1);
            char temp = passwordArray[i];
            passwordArray[i] = passwordArray[j];
            passwordArray[j] = temp;
        }

        return new String(passwordArray);
    }

    private void addEmailClaimValue(AccountJpaEntity account, String email) {
        claimDefinitionRepository.findByCode("email_address")
                .ifPresent(claimDef -> {
                    ClaimValueJpaEntity emailClaim = new ClaimValueJpaEntity(
                            claimDef, OwnerType.ACCOUNT, account.getId());
                    emailClaim.setValue(email);
                    emailClaim.setPrimary(true);
                    emailClaim.setSource("USER_INPUT");
                    emailClaim.setCreatedBy("SYSTEM");
                    claimValueRepository.save(emailClaim);
                });
    }

    private void addPasswordClaimValue(AccountJpaEntity account, String password) {
        factorServerClient.createPassword(account.getId(),
                password,
                DataSeedConstants.SYSTEM_BUSINESS_ID.toString(),
                DataSeedConstants.SYSTEM_CHANNEL_ID.toString());
    }
}
