package com.vusecurity.auth.claims.api.handler;

import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.vusecurity.core.commons.ApiMessage;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.server.ResponseStatusException;

import static org.junit.jupiter.api.Assertions.*;

class ClaimDefinitionErrorControllerHandlerTest {

    private final ClaimDefinitionErrorControllerHandler handler = new ClaimDefinitionErrorControllerHandler();

    @Test
    void handleResponseStatusException_preserves_status_and_maps_code() {
        ResponseStatusException ex = new ResponseStatusException(HttpStatus.BAD_REQUEST, "bad input");
        ResponseEntity<ApiMessage> resp = handler.handleResponseStatusException(ex);
        assertEquals(HttpStatus.BAD_REQUEST, resp.getStatusCode());
        assertEquals(5200, resp.getBody().getCode());
        assertEquals("bad input", resp.getBody().getMessage());
    }

    @Test
    void handleInvalidFormatException_nonEnum_returns_generic_message() {
        InvalidFormatException ex = InvalidFormatException.from(null, "oops", "X", String.class);
        ResponseEntity<ApiMessage> resp = handler.handleInvalidFormatException(ex);
        assertEquals(400, resp.getStatusCode().value());
        assertEquals(5200, resp.getBody().getCode());
        assertEquals("Invalid format in the application.", resp.getBody().getMessage());
    }
}

