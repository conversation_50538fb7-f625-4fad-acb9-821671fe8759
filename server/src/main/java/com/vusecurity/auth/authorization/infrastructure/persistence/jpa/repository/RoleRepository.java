package com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

/**
 * Spring Data JPA repository for RoleJpaEntity.
 * Infrastructure-only interface for database operations.
 */
@Repository
public interface RoleRepository extends JpaRepository<RoleJpaEntity, UUID>, JpaSpecificationExecutor<RoleJpaEntity> {

    /**
     * Find role by ID.
     * @param id the role ID
     * @return the role if found
     */
    Optional<RoleJpaEntity> findById(UUID id);

    /**
     * Check if role exists by name.
     * @param name the role name
     * @return true if exists, false otherwise
     */
    boolean existsByName(String name);

    /**
     * Check if role exists by ID.
     * @param id the role ID
     * @return true if exists, false otherwise
     */
    boolean existsById(UUID id);

    /**
     * Delete role by ID.
     * @param id the role ID
     */
    void deleteById(UUID id);
    
    /**
     * Find roles by IDs with accounts eagerly loaded.
     * @param ids the role IDs
     * @return the roles with accounts if found
     */
    @Query("""
        SELECT r FROM RoleJpaEntity r
        LEFT JOIN FETCH r.accounts
        WHERE r.id IN :ids
    """)
    java.util.List<RoleJpaEntity> findAllByIdWithAccounts(@Param("ids") java.util.Collection<UUID> ids);
}
