package com.vusecurity.auth.authorization.api.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.PermissionJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.PermissionRepository;
import com.vusecurity.auth.shared.infrastructure.migration.initial.DataSeedConstants;
import com.vusecurity.auth.shared.test.BaseIntegrationTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.context.WebApplicationContext;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.setup.MockMvcBuilders.webAppContextSetup;

/**
 * Integration tests for PermissionsController covering all endpoints and edge cases.
 * Tests include permission retrieval with pagination, filtering, and error scenarios.
 */
@AutoConfigureWebMvc
@Testcontainers
class PermissionsControllerIT extends BaseIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;
    
    // Test permissions
    private PermissionJpaEntity testPermission1;
    private PermissionJpaEntity testPermission2;
    private PermissionJpaEntity testPermission3;

    @BeforeEach
    void setUp() {
        mockMvc = webAppContextSetup(webApplicationContext).build();
        
        // Create test permissions
        createTestPermissions();
    }

    private void createTestPermissions() {
        // Create test permissions with known data
        testPermission1 = new PermissionJpaEntity(
                DataSeedConstants.PERMISSION_IDENTITY_READ_ID,
                "sys_identity:read",
                "Allow to read identities"
        );
        testPermission1.setCreatedBy("test");
        testPermission1 = permissionRepository.save(testPermission1);

        testPermission2 = new PermissionJpaEntity(
                DataSeedConstants.PERMISSION_ACCOUNT_CREATE_ID,
                "sys_account:create",
                "Allow to create accounts entities"
        );
        testPermission2.setCreatedBy("test");
        testPermission2 = permissionRepository.save(testPermission2);

        testPermission3 = new PermissionJpaEntity(
                "test_permission:special",
                "Test permission with special characters & symbols"
        );
        testPermission3.setCreatedBy("test");
        testPermission3 = permissionRepository.save(testPermission3);
    }

    // ========================================
    // GET /api/v1/permissions Tests
    // ========================================

    @Test
    void shouldGetAllPermissions_WithDefaultPagination() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/permissions"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.currentPage").value(1))
                .andExpect(jsonPath("$.pageSize").value(10))
                .andExpect(jsonPath("$.totalElements").value(greaterThanOrEqualTo(3)))
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content", hasSize(greaterThanOrEqualTo(3))))
                .andExpect(jsonPath("$.content[0].id").exists())
                .andExpect(jsonPath("$.content[0].name").exists())
                .andExpect(jsonPath("$.content[0].description").exists())
                .andExpect(jsonPath("$.content[0]._audit").exists())
                .andExpect(jsonPath("$.content[0]._audit.createdAt").exists())
                .andExpect(jsonPath("$.content[0]._audit.createdBy").exists());
    }

    @Test
    void shouldGetAllPermissions_WithCustomPagination() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/permissions")
                        .param("page", "1")
                        .param("pageSize", "2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.currentPage").value(1))
                .andExpect(jsonPath("$.pageSize").value(2))
                .andExpect(jsonPath("$.totalElements").value(greaterThanOrEqualTo(3)))
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content", hasSize(2)));
    }

    @Test
    void shouldGetAllPermissions_WithFilter() throws Exception {
        // When & Then - Filter by "identity" should match sys_identity:read
        mockMvc.perform(get("/api/v1/permissions")
                        .param("filter", "identity"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.content[?(@.name == 'sys_identity:read')]").exists());
    }

    @Test
    void shouldGetAllPermissions_WithUrlEncodedFilter() throws Exception {
        // Given - URL encode a filter with special characters that will match our test permission
        String filterWithSpecialChars = "special";
        String encodedFilter = URLEncoder.encode(filterWithSpecialChars, StandardCharsets.UTF_8);

        // When & Then
        mockMvc.perform(get("/api/v1/permissions")
                        .param("filter", encodedFilter))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.content[?(@.description =~ /.*special.*/i)]").exists());
    }

    @Test
    void shouldGetAllPermissions_WithEmptyFilter() throws Exception {
        // When & Then - Empty filter should return all permissions
        mockMvc.perform(get("/api/v1/permissions")
                        .param("filter", ""))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content", hasSize(greaterThanOrEqualTo(3))));
    }

    @Test
    void shouldGetAllPermissions_WithFilterNoResults() throws Exception {
        // When & Then - Filter that matches no permissions
        mockMvc.perform(get("/api/v1/permissions")
                        .param("filter", "nonexistent_permission_filter"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content", hasSize(0)))
                .andExpect(jsonPath("$.totalElements").value(0));
    }

    @Test
    void shouldReturnBadRequest_WhenPageIsZero() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/permissions")
                        .param("page", "0")
                        .param("pageSize", "10"))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void shouldReturnBadRequest_WhenPageIsNegative() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/permissions")
                        .param("page", "-1")
                        .param("pageSize", "10"))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void shouldReturnBadRequest_WhenPageSizeIsZero() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/permissions")
                        .param("page", "1")
                        .param("pageSize", "0"))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void shouldReturnBadRequest_WhenPageSizeExceedsLimit() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/permissions")
                        .param("page", "1")
                        .param("pageSize", "101"))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void shouldReturnBadRequest_WhenPageIsNotNumeric() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/permissions")
                        .param("page", "invalid")
                        .param("pageSize", "10"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenPageSizeIsNotNumeric() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/permissions")
                        .param("page", "1")
                        .param("pageSize", "invalid"))
                .andExpect(status().isBadRequest());
    }

    // ========================================
    // GET /api/v1/permissions/{name} Tests
    // ========================================

    @Test
    void shouldGetPermissionByName_Successfully() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/permissions/{name}", testPermission1.getName()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testPermission1.getId().toString()))
                .andExpect(jsonPath("$.name").value(testPermission1.getName()))
                .andExpect(jsonPath("$.description").value(testPermission1.getDescription()))
                .andExpect(jsonPath("$._audit").exists())
                .andExpect(jsonPath("$._audit.createdAt").exists())
                .andExpect(jsonPath("$._audit.createdBy").value("SYSTEM")); // Audit user is set to SYSTEM in test context
    }

    @Test
    void shouldGetPermissionByName_WithSpecialCharacters() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/permissions/{name}", testPermission3.getName()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testPermission3.getId().toString()))
                .andExpect(jsonPath("$.name").value(testPermission3.getName()))
                .andExpect(jsonPath("$.description").value(testPermission3.getDescription()));
    }

    @Test
    void shouldGetPermissionByName_WithUrlEncodedName() throws Exception {
        // Given - URL encode the permission name with special characters
        String encodedName = URLEncoder.encode(testPermission3.getName(), StandardCharsets.UTF_8);

        // When & Then
        mockMvc.perform(get("/api/v1/permissions/{name}", encodedName))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testPermission3.getId().toString()))
                .andExpect(jsonPath("$.name").value(testPermission3.getName()));
    }

    @Test
    void shouldReturnNotFound_WhenPermissionNameDoesNotExist() throws Exception {
        // When & Then - The handler throws IllegalArgumentException which gets converted to 400, not 404
        mockMvc.perform(get("/api/v1/permissions/{name}", "nonexistent_permission"))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void shouldReturnBadRequest_WhenPermissionNameIsEmpty() throws Exception {
        // When & Then - Spring returns 400 for empty path variable, not 404
        mockMvc.perform(get("/api/v1/permissions/{name}", ""))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void shouldHandlePermissionName_WithSlashes() throws Exception {
        // Given - Create permission with slashes in name
        PermissionJpaEntity permissionWithSlashes = new PermissionJpaEntity(
                "sys/permission:read/write",
                "Permission with slashes in name"
        );
        permissionWithSlashes.setCreatedBy("test");
        permissionWithSlashes = permissionRepository.save(permissionWithSlashes);

        // When & Then - URL encode the name to handle slashes
        String encodedName = URLEncoder.encode(permissionWithSlashes.getName(), StandardCharsets.UTF_8);
        mockMvc.perform(get("/api/v1/permissions/{name}", encodedName))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value(permissionWithSlashes.getName()));
    }

    @Test
    void shouldGetPermissionByName_CaseInsensitive() throws Exception {
        // When & Then - Test with different case (actually finds the permission - search is case-insensitive)
        mockMvc.perform(get("/api/v1/permissions/{name}", testPermission1.getName().toUpperCase()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value(testPermission1.getName()));
    }

    @Test
    void shouldGetAllPermissions_WithLargePage() throws Exception {
        // When & Then - Test with page number beyond available data
        mockMvc.perform(get("/api/v1/permissions")
                        .param("page", "999")
                        .param("pageSize", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.currentPage").value(999))
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content", hasSize(0)));
    }

    @Test
    void shouldGetAllPermissions_WithMaxPageSize() throws Exception {
        // When & Then - Test with maximum allowed page size
        mockMvc.perform(get("/api/v1/permissions")
                        .param("page", "1")
                        .param("pageSize", "100"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.pageSize").value(100));
    }

    @Test
    void shouldHandleSpecialCharactersInPermissionName() throws Exception {
        // Given - Create permission with various special characters
        PermissionJpaEntity specialPermission = new PermissionJpaEntity(
                "test:permission-with_special.chars@domain",
                "Permission with various special characters"
        );
        specialPermission.setCreatedBy("test");
        specialPermission = permissionRepository.save(specialPermission);

        // When & Then - Should handle special characters correctly
        mockMvc.perform(get("/api/v1/permissions/{name}", specialPermission.getName()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value(specialPermission.getName()));
    }

    @Test
    void shouldReturnEmptyContent_WhenNoPermissionsExist() throws Exception {
        // Given - Clear all permissions
        permissionRepository.deleteAll();

        // When & Then
        mockMvc.perform(get("/api/v1/permissions"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content", hasSize(0)))
                .andExpect(jsonPath("$.totalElements").value(0));
    }

    @Test
    void shouldFilterPermissions_ByNameAndDescription() throws Exception {
        // When & Then - Filter by "account" should match sys_account:create
        mockMvc.perform(get("/api/v1/permissions")
                        .param("filter", "account"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.content[?(@.name =~ /.*account.*/i)]").exists());
    }

    @Test
    void shouldHandleNullFilter() throws Exception {
        // When & Then - Null filter should return all permissions
        mockMvc.perform(get("/api/v1/permissions")
                        .param("filter", (String) null))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content", hasSize(greaterThanOrEqualTo(3))));
    }

    @Test
    void shouldValidatePermissionResponse_Structure() throws Exception {
        // When & Then - Verify complete response structure
        mockMvc.perform(get("/api/v1/permissions/{name}", testPermission1.getName()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").isString())
                .andExpect(jsonPath("$.name").isString())
                .andExpect(jsonPath("$.description").isString())
                .andExpect(jsonPath("$._audit").exists())
                .andExpect(jsonPath("$._audit.createdAt").exists())
                .andExpect(jsonPath("$._audit.updatedAt").exists())
                .andExpect(jsonPath("$._audit.createdBy").exists())
                .andExpect(jsonPath("$._audit.updatedBy").exists());
    }

    @Test
    void shouldValidatePagedResponse_Structure() throws Exception {
        // When & Then - Verify complete paged response structure
        mockMvc.perform(get("/api/v1/permissions")
                        .param("page", "1")
                        .param("pageSize", "5"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.currentPage").isNumber())
                .andExpect(jsonPath("$.pageSize").isNumber())
                .andExpect(jsonPath("$.totalElements").isNumber())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].id").exists())
                .andExpect(jsonPath("$.content[0].name").exists())
                .andExpect(jsonPath("$.content[0].description").exists())
                .andExpect(jsonPath("$.content[0]._audit").exists());
    }

    @Test
    void shouldReturnBadRequest_WhenInvalidParameterTypes() throws Exception {
        // When & Then - Test with invalid parameter types
        mockMvc.perform(get("/api/v1/permissions")
                        .param("page", "not-a-number")
                        .param("pageSize", "10"))
                .andExpect(status().isBadRequest());
    }

    // ========================================
    // Performance and Load Tests
    // ========================================

    @Test
    void shouldHandleLargeFilterString() throws Exception {
        // Given - Create a very long filter string
        String longFilter = "a".repeat(1000);

        // When & Then - Should handle large filter gracefully
        mockMvc.perform(get("/api/v1/permissions")
                        .param("filter", longFilter))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());
    }
}
