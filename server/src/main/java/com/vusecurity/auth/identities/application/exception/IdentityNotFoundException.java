package com.vusecurity.auth.identities.application.exception;

/**
 * Exception thrown when an identity is not found.
 */
public class IdentityNotFoundException extends IdentityException {
    
    public IdentityNotFoundException(String identityId) {
        super("Identity not found with ID: " + identityId);
    }

    public IdentityNotFoundException(String message, Object... parameters) {
        super(String.format(message, parameters));
    }
    
    public IdentityNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
