package com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import jakarta.persistence.*;

import java.io.Serializable;
import java.time.Instant;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

@Entity
@Table(name = "permission")
public class PermissionJpaEntity extends AbstractEntity {

    @Column(unique = true)
    private String name;

    private String description;

    @ManyToMany(mappedBy = "permissions", fetch = FetchType.LAZY)
    private Set<RoleJpaEntity> roles = new HashSet<>();

    // Constructors
    public PermissionJpaEntity() {
    }

    // Constructor with ID (for migration/test scenarios)
    public PermissionJpaEntity(UUID id, String name, String description) {
        this.setId(id);
        this.name = name;
        this.description = description;
        this.roles = new HashSet<>();
    }

    // Constructor without ID (for production use - JPA will generate ID)
    public PermissionJpaEntity(String name, String description) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("name cannot be null or empty");
        }

        this.name = name.trim();
        this.description = description;
        this.roles = new HashSet<>();
    }

    // Getters
    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public Set<RoleJpaEntity> getRoles() {
        return roles != null ? new HashSet<>(roles) : new HashSet<>();
    }

    // Setters
    public void setName(String name) {
        this.name = name;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setRoles(Set<RoleJpaEntity> roles) {
        this.roles = roles != null ? new HashSet<>(roles) : new HashSet<>();
    }

    // Business behavior methods
    public void changeDescription(String newDescription) {
        this.description = newDescription;
    }

    // equals() and hashCode() using name (unique business key if it has unique index)
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        PermissionJpaEntity that = (PermissionJpaEntity) o;
        return Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), name);
    }

    @Override
    public String toString() {
        return "PermissionJpaEntity{" +
                "id=" + getId() +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
