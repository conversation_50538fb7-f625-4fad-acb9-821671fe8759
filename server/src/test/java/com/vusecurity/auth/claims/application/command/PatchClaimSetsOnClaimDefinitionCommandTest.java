package com.vusecurity.auth.claims.application.command;

import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for PatchClaimSetsOnClaimDefinitionCommand validation logic.
 * Tests pure domain logic with no external dependencies.
 */
class PatchClaimSetsOnClaimDefinitionCommandTest {

    @Test
    void shouldValidateSuccessfully_WhenAllRequiredFieldsProvided() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        List<ClaimSetAssignmentCommand> claimSetsAssignment = List.of(
                new ClaimSetAssignmentCommand(UUID.randomUUID(), true),
                new ClaimSetAssignmentCommand(UUID.randomUUID(), false)
        );

        PatchClaimSetsOnClaimDefinitionCommand command = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                claimSetsAssignment
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WhenEmptyClaimSetsAssignment() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        List<ClaimSetAssignmentCommand> claimSetsAssignment = new ArrayList<>();

        PatchClaimSetsOnClaimDefinitionCommand command = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                claimSetsAssignment
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WhenSingleClaimSetAssignment() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        List<ClaimSetAssignmentCommand> claimSetsAssignment = List.of(
                new ClaimSetAssignmentCommand(UUID.randomUUID(), true)
        );

        PatchClaimSetsOnClaimDefinitionCommand command = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                claimSetsAssignment
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithMixedEnforceUniquenessValues() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        List<ClaimSetAssignmentCommand> claimSetsAssignment = List.of(
                new ClaimSetAssignmentCommand(UUID.randomUUID(), true),
                new ClaimSetAssignmentCommand(UUID.randomUUID(), false),
                new ClaimSetAssignmentCommand(UUID.randomUUID(), true)
        );

        PatchClaimSetsOnClaimDefinitionCommand command = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                claimSetsAssignment
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldThrowException_WhenClaimDefinitionIdIsNull() {
        // Given
        List<ClaimSetAssignmentCommand> claimSetsAssignment = List.of(
                new ClaimSetAssignmentCommand(UUID.randomUUID(), true)
        );

        PatchClaimSetsOnClaimDefinitionCommand command = new PatchClaimSetsOnClaimDefinitionCommand(
                null, // claimDefinitionId is null
                claimSetsAssignment
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("claimDefinitionId is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenClaimSetsAssignmentIsNull() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();

        PatchClaimSetsOnClaimDefinitionCommand command = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                null // claimSetsAssignment is null
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("claimSets is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenClaimSetsAssignmentContainsNullValues() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        List<ClaimSetAssignmentCommand> claimSetsAssignment = new ArrayList<>();
        claimSetsAssignment.add(new ClaimSetAssignmentCommand(UUID.randomUUID(), true));
        claimSetsAssignment.add(null); // null value in the list
        claimSetsAssignment.add(new ClaimSetAssignmentCommand(UUID.randomUUID(), false));

        PatchClaimSetsOnClaimDefinitionCommand command = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                claimSetsAssignment
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("claimSets cannot contain null values", exception.getMessage());
    }

    @Test
    void shouldValidateSuccessfully_WithLargeNumberOfClaimSets() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        List<ClaimSetAssignmentCommand> claimSetsAssignment = new ArrayList<>();
        
        // Create 100 claim set assignments
        for (int i = 0; i < 100; i++) {
            claimSetsAssignment.add(new ClaimSetAssignmentCommand(UUID.randomUUID(), i % 2 == 0));
        }

        PatchClaimSetsOnClaimDefinitionCommand command = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                claimSetsAssignment
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithDuplicateClaimSetIds() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        UUID duplicateClaimSetId = UUID.randomUUID();
        List<ClaimSetAssignmentCommand> claimSetsAssignment = List.of(
                new ClaimSetAssignmentCommand(duplicateClaimSetId, true),
                new ClaimSetAssignmentCommand(duplicateClaimSetId, false) // Same ID, different enforceUniqueness
        );

        PatchClaimSetsOnClaimDefinitionCommand command = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                claimSetsAssignment
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithAllEnforceUniquenessTrue() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        List<ClaimSetAssignmentCommand> claimSetsAssignment = List.of(
                new ClaimSetAssignmentCommand(UUID.randomUUID(), true),
                new ClaimSetAssignmentCommand(UUID.randomUUID(), true),
                new ClaimSetAssignmentCommand(UUID.randomUUID(), true)
        );

        PatchClaimSetsOnClaimDefinitionCommand command = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                claimSetsAssignment
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithAllEnforceUniquenessFalse() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        List<ClaimSetAssignmentCommand> claimSetsAssignment = List.of(
                new ClaimSetAssignmentCommand(UUID.randomUUID(), false),
                new ClaimSetAssignmentCommand(UUID.randomUUID(), false),
                new ClaimSetAssignmentCommand(UUID.randomUUID(), false)
        );

        PatchClaimSetsOnClaimDefinitionCommand command = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                claimSetsAssignment
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithNullEnforceUniquenessInClaimSetAssignment() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        // Note: ClaimSetAssignmentCommand is a record, so we can't test null enforceUniqueness
        // as it's a primitive boolean. This test ensures the command validation works correctly
        List<ClaimSetAssignmentCommand> claimSetsAssignment = List.of(
                new ClaimSetAssignmentCommand(UUID.randomUUID(), false)
        );

        PatchClaimSetsOnClaimDefinitionCommand command = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                claimSetsAssignment
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithSpecialUUIDs() {
        // Given
        UUID claimDefinitionId = UUID.fromString("00000000-0000-0000-0000-000000000000");
        List<ClaimSetAssignmentCommand> claimSetsAssignment = List.of(
                new ClaimSetAssignmentCommand(UUID.fromString("ffffffff-ffff-ffff-ffff-ffffffffffff"), true),
                new ClaimSetAssignmentCommand(UUID.fromString("12345678-1234-1234-1234-123456789abc"), false)
        );

        PatchClaimSetsOnClaimDefinitionCommand command = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                claimSetsAssignment
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }
} 