package com.vusecurity.auth.identities.application.handler;

import com.vusecurity.auth.contracts.api.v1.dto.identities.ClaimIdentifierRequest;
import com.vusecurity.auth.identities.application.exception.IdentityNotFoundException;
import com.vusecurity.auth.identities.application.query.IdentifyIdentityByClaimsQuery;
import com.vusecurity.auth.identities.application.query.IdentifyIdentityQuery;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityRepository;

import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true, noRollbackFor = IdentityNotFoundException.class)
public class IdentifyIdentityHandler implements IdentifyIdentityQuery {

    private static final Logger logger = LoggerFactory.getLogger(IdentifyIdentityHandler.class);
    
    private final IdentityRepository identityRepository;

    @Override
    public IdentityJpaEntity identifyIdentityByClaims(IdentifyIdentityByClaimsQuery query) {
        logger.debug("Identifying identity by claims for claims: {}", 
                query.getClaimIdentifiers().size());
        
        // Validate query
        query.validate();
        
        // 1. Validate input claim definition IDs belong to the ClaimSet
        List<UUID> requestedClaimDefinitionIds = query.getClaimIdentifiers().stream()
                .map(ClaimIdentifierRequest::getClaimDefinitionId)
                .collect(Collectors.toList());
        
        // Check for duplicates
        Set<UUID> uniqueRequestedIds = new HashSet<>(requestedClaimDefinitionIds);
        if (uniqueRequestedIds.size() != requestedClaimDefinitionIds.size()) {
            throw new IllegalArgumentException("Duplicate claim definition IDs are not allowed");
        }
        
        // 2. Execute JPQL query with ALL_CLAIMS_MUST_MATCH strategy
        List<String> claimPairs = query.getClaimIdentifiers().stream()
                .map(claim -> claim.getClaimDefinitionId().toString().toUpperCase() + ":" + claim.getValue())
                .collect(Collectors.toList());
        
        List<IdentityJpaEntity> matchingIdentities = identityRepository.findIdentitiesByClaims(
                query.getIdentityType(), 
                claimPairs,
                query.getClaimIdentifiers().size());
        
        // 3. Handle results based on count
        if (matchingIdentities.isEmpty()) {
            logger.debug("No identity found matching the provided claims");
            //TODO check, a bit hacky
            throw new IdentityNotFoundException("No identity found matching the IdentityType %s and the provided claims", query.getIdentityType().toString());
        } else if (matchingIdentities.size() == 1) {
            IdentityJpaEntity identity = matchingIdentities.get(0);
            logger.debug("Successfully identified identity: {}", identity.getId());
            return identity;
        } else {
            logger.debug("Multiple identities ({}) found matching the provided claims", matchingIdentities.size());
            throw new IllegalStateException("Multiple identities found matching the provided claim identifiers. " +
                    "Expected exactly one identity, but found " + matchingIdentities.size());
        }
    }
}
