package com.vusecurity.auth.contracts.api.v1.dto.claims.claimset;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.UUID;

@Data
@Accessors(chain = true)
@Schema(description = "Response for claim set to claim definitions mapping operations")
public class ClaimSetClaimDefinitionsResponse {
    @Schema(description = "Unique identifier of the claim set",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID claimSetId;

    @Schema(description = "List of claim definition IDs associated with the claim set")
    private List<ClaimDefinitionsToClaimSetResponse> claimDefinitions;

    @Data
    @Schema(description = "Claim definition and related configuration")
    public static class ClaimDefinitionsToClaimSetResponse {
        @NotNull(message = "Claim definition IDs are required")
        @Schema(description = "Claim definition IDs to assign",
                example = "\"123e4567-e89b-12d3-a456-************\"")
        private UUID claimDefinitionId;
        @Schema(
                description = "Order of the claim definition in the set",
                example = "1"
        )
        private Integer claimDefinitionOrder;
        @Schema(
                description = "Whether to enforce uniqueness for this claim",
                example = "true"
        )
        private Boolean enforceUniqueness;
    }
}
