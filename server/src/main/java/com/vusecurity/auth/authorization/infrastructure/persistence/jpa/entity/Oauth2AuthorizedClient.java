package com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity;

import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Table(name = "oauth2_authorized_client")
@Entity
@IdClass(Oauth2AuthorizedClientId.class)
public class Oauth2AuthorizedClient implements Serializable{
    @Id
    @Column(name = "client_registration_id", length = 100)
    @NotBlank
    private String clientRegistrationId;

    @Id
    @Column(name = "principal_name", length = 200)
    @NotBlank
    private String principalName;

    @Column(name = "access_token_type", length = 100, nullable = false)
    @NotBlank
    private String accessTokenType;

    @Lob
    @Column(name = "access_token_value", nullable = false)
    @NotBlank
    private String accessTokenValue;

    @Column(name = "access_token_issued_at", nullable = false)
    @NotNull
    private String accessTokenIssuedAt;

    @Column(name = "access_token_expires_at", nullable = false)
    @NotNull
    private String accessTokenExpiresAt;

    @Column(name = "access_token_scopes", length = 1000)
    private String accessTokenScopes;

    @Lob
    @Column(name = "refresh_token_value")
    private String refreshTokenValue;

    @Column(name = "refresh_token_issued_at")
    private String refreshTokenIssuedAt;

    @Column(name = "created_at", nullable = false)
    @NotNull
    private String createdAt;

}
