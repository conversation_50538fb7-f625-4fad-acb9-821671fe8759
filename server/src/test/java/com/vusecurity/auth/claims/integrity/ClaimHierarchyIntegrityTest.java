package com.vusecurity.auth.claims.integrity;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetClaimValuesJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueReferencesJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetClaimValuesRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueReferencesRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.properties")
@Transactional
class ClaimHierarchyIntegrityTest {

    @Autowired
    private ClaimSetClaimValuesRepository claimSetClaimValuesRepo;
    
    @Autowired
    private ClaimValueReferencesRepository claimValueReferencesRepo;
    
    @Autowired
    private ClaimValueRepository claimValueRepo;
    
    @Autowired
    private ClaimSetRepository claimSetRepo;
    
    @Test
    void shouldMaintainIntegrity_WhenDeletingClaimValue() {
        // Given - Crear estructura jerárquica
        ClaimValueJpaEntity claimValue = createTestClaimValue();
        ClaimSetJpaEntity claimSet = createTestClaimSet();
        
        ClaimSetClaimValuesJpaEntity association = new ClaimSetClaimValuesJpaEntity(claimSet, claimValue);
        claimSetClaimValuesRepo.save(association);
        
        // When - Eliminar ClaimValue
        claimValueRepo.delete(claimValue);
        claimValueRepo.flush();
        
        // Then - Verificar que se eliminaron las asociaciones
        List<ClaimSetClaimValuesJpaEntity> remainingAssociations =
            claimSetClaimValuesRepo.findByClaimValueId(claimValue.getId());
        
        assertTrue(remainingAssociations.isEmpty());
    }
    
    @Test
    void shouldMaintainIntegrity_WhenDeletingClaimSet() {
        // Given
        ClaimSetJpaEntity claimSet = createTestClaimSet();
        ClaimValueJpaEntity claimValue = createTestClaimValue();
        
        ClaimSetClaimValuesJpaEntity association = new ClaimSetClaimValuesJpaEntity(claimSet, claimValue);
        claimSetClaimValuesRepo.save(association);
        
        // When
        claimSetRepo.delete(claimSet);
        claimSetRepo.flush();
        
        // Then
        List<ClaimSetClaimValuesJpaEntity> remainingAssociations = 
            claimSetClaimValuesRepo.findByClaimSetId(claimSet.getId());
        
        assertTrue(remainingAssociations.isEmpty());
    }
    
    @Test
    void shouldPreventOrphanedReferences() {
        // Given
        ClaimValueJpaEntity sourceValue = createTestClaimValue();
        ClaimSetJpaEntity referencedClaimSet = createTestClaimSet();
        
        ClaimValueReferencesJpaEntity reference = new ClaimValueReferencesJpaEntity();
        reference.setSourceClaimValue(sourceValue);
        reference.setReferencedClaimSet(referencedClaimSet);
        claimValueReferencesRepo.save(reference);
        
        // When - Intentar eliminar ClaimSet referenciado
        claimSetRepo.delete(referencedClaimSet);
        
        // Then - Debe eliminar las referencias automáticamente
        assertDoesNotThrow(() -> claimSetRepo.flush());
        
        List<ClaimValueReferencesJpaEntity> remainingReferences = 
            claimValueReferencesRepo.findByReferencedClaimSetId(referencedClaimSet.getId());
        
        assertTrue(remainingReferences.isEmpty());
    }
    
    private ClaimValueJpaEntity createTestClaimValue() {
        // Implementación helper
        return new ClaimValueJpaEntity(); // placeholder
    }
    
    private ClaimSetJpaEntity createTestClaimSet() {
        // Implementación helper
        return new ClaimSetJpaEntity(); // placeholder
    }
}