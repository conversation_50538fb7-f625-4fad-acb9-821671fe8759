package com.vusecurity.auth.shared.health;

import com.vusecurity.auth.AppConfigProperties;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

/**
 * A simple health indicator that always returns UP status.
 * This ensures that the application has at least one working health indicator.
 */
@Component
public class ApplicationHealthIndicator implements HealthIndicator {

    private final AppConfigProperties appConfigProperties;

    public ApplicationHealthIndicator(AppConfigProperties appConfigProperties) {
        this.appConfigProperties = appConfigProperties;
    }

    @Override
    public Health health() {
        return Health.up()
                .withDetail("application", "VU One Auth Server")
                .withDetail("version", appConfigProperties.getVersion())
                .withDetail("build", appConfigProperties.getBuild())
                .build();
    }
}
