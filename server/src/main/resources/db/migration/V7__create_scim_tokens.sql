/*
 * Flyway Migration: V7_1_1_0__create_scim_tokens.sql
 * Purpose: Create SCIM tokens table with required constraints and indexes
 * Notes:
 *  - Matches ScimTokenJpaEntity mapping
 *  - SQL Server syntax with GO separators
 */

/*******************************************************
SCIM TOKENS
*******************************************************/

IF OBJECT_ID('scim_tokens', 'U') IS NULL
BEGIN
    CREATE TABLE [scim_tokens](
        [created_at] [datetimeoffset](6) NOT NULL,
        [updated_at] [datetimeoffset](6) NULL,
        [business_id] [uniqueidentifier] NOT NULL,
        [identity_provider_id] [uniqueidentifier] NOT NULL,
        [id] [uniqueidentifier] NOT NULL,
        [name] [varchar](255) NOT NULL,
        [description] [varchar](1024) NULL,
        [token] [varchar](255) NOT NULL,
        [created_by] [varchar](255) NOT NULL,
        [status] [varchar](255) NOT NULL,
        [updated_by] [varchar](255) NULL,
     CONSTRAINT [pk_scim_tokens_id] PRIMARY KEY CLUSTERED
    (
        [id] ASC
    )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
END
GO

-- Unique constraints / indexes
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'uk_scim_tokens_id' AND object_id = OBJECT_ID('scim_tokens'))
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX [uk_scim_tokens_id]
    ON [scim_tokens]([id] ASC)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY];
END
GO

IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'uk_scim_tokens_name' AND object_id = OBJECT_ID('scim_tokens'))
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX [uk_scim_tokens_name]
    ON [scim_tokens]([name] ASC)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY];
END
GO

IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'uk_scim_tokens_business_id' AND object_id = OBJECT_ID('scim_tokens'))
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX [uk_scim_tokens_business_id]
    ON [scim_tokens]([business_id] ASC, [identity_provider_id] ASC, [token] ASC)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY];
END
GO

-- Remove old constraint if exists
DECLARE @scimTokensStatusDefault varchar(max);
SET @scimTokensStatusDefault = (SELECT dc.name FROM sys.default_constraints dc JOIN sys.columns c ON c.default_object_id = dc.object_id
WHERE c.object_id = OBJECT_ID('scim_tokens') AND c.name = 'status' AND dc.name != 'df_scim_tokens_status');

IF (@scimTokensStatusDefault IS NOT NULL)
BEGIN
    DECLARE @dropContraintQuery nvarchar(max);
    SET @dropContraintQuery = 'ALTER TABLE [scim_tokens] DROP CONSTRAINT ' + QUOTENAME(@scimTokensStatusDefault);
	EXEC sp_executesql @dropContraintQuery;
END

-- Defaults
IF NOT EXISTS (
    SELECT 1 FROM sys.default_constraints dc
    JOIN sys.columns c ON c.default_object_id = dc.object_id
    WHERE dc.name = 'df_scim_tokens_status' AND c.object_id = OBJECT_ID('scim_tokens') AND c.name = 'status')
BEGIN
    ALTER TABLE [scim_tokens] ADD CONSTRAINT [df_scim_tokens_status] DEFAULT ('ACTIVE') FOR [status];
END
GO

-- Foreign Keys
IF NOT EXISTS (
    SELECT 1 FROM sys.foreign_keys WHERE name = 'fk_scim_tokens_business' AND parent_object_id = OBJECT_ID('scim_tokens'))
BEGIN
    ALTER TABLE [scim_tokens]  WITH CHECK ADD CONSTRAINT [fk_scim_tokens_business] FOREIGN KEY([business_id])
    REFERENCES [business] ([id]);
    ALTER TABLE [scim_tokens] CHECK CONSTRAINT [fk_scim_tokens_business];
END
GO

IF NOT EXISTS (
    SELECT 1 FROM sys.foreign_keys WHERE name = 'fk_scim_tokens_identity_provider' AND parent_object_id = OBJECT_ID('scim_tokens'))
BEGIN
    ALTER TABLE [scim_tokens]  WITH CHECK ADD CONSTRAINT [fk_scim_tokens_identity_provider] FOREIGN KEY([identity_provider_id])
    REFERENCES [identity_provider] ([id]);
    ALTER TABLE [scim_tokens] CHECK CONSTRAINT [fk_scim_tokens_identity_provider];
END
GO

-- Drops all contraints over scim_tokens.status
DECLARE @dropCheckedConstraintsScimTokenStatus NVARCHAR(MAX) = N'';
SELECT @dropCheckedConstraintsScimTokenStatus = @dropCheckedConstraintsScimTokenStatus + N'ALTER TABLE [scim_tokens] DROP CONSTRAINT ' + QUOTENAME(cc.name) + N';'
FROM sys.check_constraints cc JOIN sys.tables st ON st.object_id = cc.parent_object_id
JOIN sys.columns sc ON st.object_id = sc.object_id WHERE st.object_id = OBJECT_ID('scim_tokens') and sc.name = 'status' and cc.name != 'ck_scim_tokens_status';
EXEC sp_executesql @dropCheckedConstraintsScimTokenStatus;

-- Check constraint for status values
IF NOT EXISTS (
    SELECT 1 FROM sys.check_constraints WHERE name = 'ck_scim_tokens_status' AND parent_object_id = OBJECT_ID('scim_tokens'))
BEGIN
    ALTER TABLE [scim_tokens]  WITH CHECK ADD constraint [ck_scim_tokens_status] CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'));
END
GO

