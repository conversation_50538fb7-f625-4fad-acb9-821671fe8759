package com.vusecurity.auth.contracts.api.v1.dto.identities;

import com.vusecurity.auth.contracts.enums.IdentityLifecycleState;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Data
@Schema(description = "Request to update an existing identity")
public class UpdateIdentityRequest {
    @Schema(description = "New name for the identity",
            example = "Jane Doe")
    private String name;

    @Schema(description = "Additional metadata for the identity as key-value pairs",
            example = "{\"email\": \"<EMAIL>\", \"phone\": \"+1987654321\"}")
    private Map<String, Object> metadata;

    @Schema(description = "New lifecycle state of the identity",
            example = "ACTIVE")
    private IdentityLifecycleState lifecycleState;
}