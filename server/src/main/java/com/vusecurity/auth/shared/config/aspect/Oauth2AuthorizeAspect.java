package com.vusecurity.auth.shared.config.aspect;

import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.Locale;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.lang.annotation.Annotation;

import jakarta.servlet.http.HttpServletRequest;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;

import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;

@Component
@Aspect
class Oauth2AuthorizeAspect {
    @Autowired
    private AccountRepository accountRepository;
    @Autowired(required = false)
    private HttpServletRequest httpServletRequest;

    @Around(value = "@annotation(Oauth2Authorize)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        final Logger logger = getLogger(joinPoint);
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        Oauth2Authorize authorize = method.getAnnotation(Oauth2Authorize.class);

        // Self-access shortcut (GET or POST on own accountId) before permission evaluation
        if (isSelfAccountAccess(joinPoint)) {
            logger.trace("Bypassing permission check: self account access detected for method {}", method.getName());
            return joinPoint.proceed();
        }

        String requiredPermission = Optional.ofNullable(authorize.permission()).orElse("").trim();
        if (requiredPermission.isEmpty()) {
            logger.trace("No permission required for method {}", method.getName());
            return joinPoint.proceed();
        }

        String normalizedRequired = requiredPermission.toUpperCase(Locale.ROOT);
        String treePart = normalizedRequired.split(":")[0];

        Set<String> validPermissions = new HashSet<>();
        validPermissions.add("ROLE_ADMIN"); // hardcoded: ROLE_ADMIN has all permissions
        validPermissions.add("ROLE_ADMIN"); // hardcoded: ROLE_ADMIN has all permissions
        validPermissions.add(normalizedRequired);
        validPermissions.add(treePart + ":ALL");

        logger.debug("Permission required='{}', valid permissions={}", normalizedRequired, validPermissions);

        var securityContext = SecurityContextHolder.getContext();
        if (securityContext == null || securityContext.getAuthentication() == null) {
            logger.debug("No authentication present in security context");
            throw new AccessDeniedException("Permission required: " + requiredPermission);
        }

        var authentication = securityContext.getAuthentication();

        // Fast path: if authentication contains direct granted authority matching
        for (GrantedAuthority authority : authentication.getAuthorities()) {
            String auth = Optional.ofNullable(authority.getAuthority()).orElse("").toUpperCase(Locale.ROOT);
            if(auth.startsWith("SCOPE_"))
                auth = auth.replace("SCOPE_", "");
            if (validPermissions.contains(auth)) {
                logger.trace("Access granted by granted-authority: {}", authority.getAuthority());
                return joinPoint.proceed();
            }
        }

        // Special case: single SCOPE_openid authority -> need to load full account and inspect roles/groups
        boolean onlyOpenIdScope = authentication.getAuthorities().size() == 1
                && authentication.getAuthorities().stream()
                        .anyMatch(a -> "SCOPE_openid".equalsIgnoreCase(a.getAuthority()));

        if (onlyOpenIdScope) {
            if (!(authentication instanceof JwtAuthenticationToken)) {
                logger.debug("Authentication is not JwtAuthenticationToken despite having SCOPE_openid");
                throw new AccessDeniedException("Permission required: " + requiredPermission);
            }
            String subject = ((JwtAuthenticationToken) authentication).getName();
            logger.trace("Loading account for subject {} to evaluate permissions", subject);
            Optional<AccountJpaEntity> accountOpt = accountRepository.findById(UUID.fromString(subject));
            if (accountOpt.isEmpty()) {
                logger.debug("Account not found for subject {}", subject);
                throw new AccessDeniedException("Permission required: " + requiredPermission);
            }
            AccountJpaEntity account = accountOpt.get();
            if (hasPermissionFromAccount(account, validPermissions, logger)) {
                logger.trace("Access granted by account roles/groups for subject {}", subject);
                return joinPoint.proceed();
            }
        }

        logger.debug("Access denied. Required permission: {}", requiredPermission);
        throw new AccessDeniedException("Permission required: " + requiredPermission);
    }

    private boolean isSelfAccountAccess(ProceedingJoinPoint joinPoint) {
        try {
            if (httpServletRequest == null) return false;
            String method = httpServletRequest.getMethod();
            if (!("GET".equalsIgnoreCase(method) || "POST".equalsIgnoreCase(method))) return false;

            var securityContext = SecurityContextHolder.getContext();
            if (securityContext == null || securityContext.getAuthentication() == null) return false;
            var authentication = securityContext.getAuthentication();
            if (!(authentication instanceof JwtAuthenticationToken jwtAuth)) return false;

            String subject = jwtAuth.getName(); // sub claim
            if (subject == null || subject.isBlank()) return false;

            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method methodRef = signature.getMethod();
            Object[] args = joinPoint.getArgs();
            Annotation[][] paramAnnotations = methodRef.getParameterAnnotations();
            String[] parameterNames = signature.getParameterNames(); // may be null if not compiled with -parameters

            for (int i = 0; i < args.length; i++) {
                String pathVarName = null;
                for (Annotation a : paramAnnotations[i]) {
                    if (a instanceof org.springframework.web.bind.annotation.PathVariable pv) {
                        pathVarName = pv.name();
                        if (pathVarName == null || pathVarName.isEmpty()) pathVarName = pv.value();
                    }
                }

                // Fallback to parameter name if annotation missing or empty
                if ((pathVarName == null || pathVarName.isEmpty()) && parameterNames != null && i < parameterNames.length) {
                    pathVarName = parameterNames[i];
                }

                if (pathVarName == null) continue;
                String lower = pathVarName.toLowerCase(Locale.ROOT);
                if (!lower.equals("accountid") && !lower.endsWith("accountid")) continue;

                Object val = args[i];
                if (val == null) continue;
                String accountIdValue;
                if (val instanceof UUID uuid) accountIdValue = uuid.toString();
                else accountIdValue = val.toString();

                if (accountIdValue.equalsIgnoreCase(subject)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            // Fail-safe: never allow on error
            return false;
        }
    }

    private boolean hasPermissionFromAccount(AccountJpaEntity account, Set<String> validPermissions, Logger logger) {
        // Check account direct roles and their permissions
        for (var role : account.getRoles()) {
            String roleName = Optional.ofNullable(role.getName()).orElse("").toUpperCase(Locale.ROOT);
            if (validPermissions.contains(roleName)) {
                logger.trace("Permission matched on account role: {}", role.getName());
                return true;
            }
            for (var permission : role.getPermissions()) {
                String p = Optional.ofNullable(permission.getName()).orElse("").toUpperCase(Locale.ROOT);
                if (validPermissions.contains(p)) {
                    logger.trace("Permission matched on role permission: {} (role={})", permission.getName(), role.getName());
                    return true;
                }
            }
        }

        // Check group memberships -> group roles -> permissions
        for (var membership : account.getGroupMemberships()) {
            var group = membership.getGroup();
            if (group == null) continue;
            for (var role : group.getRoles()) {
                String roleName = Optional.ofNullable(role.getName()).orElse("").toUpperCase(Locale.ROOT);
                if (validPermissions.contains(roleName)) {
                    logger.trace("Permission matched on group role: {} (group={})", role.getName(), group.getName());
                    return true;
                }
                for (var permission : role.getPermissions()) {
                    String p = Optional.ofNullable(permission.getName()).orElse("").toUpperCase(Locale.ROOT);
                    if (validPermissions.contains(p)) {
                        logger.trace("Permission matched on group role permission: {} (role={} group={})", permission.getName(), role.getName(), group.getName());
                        return true;
                    }
                }
            }
        }

        return false;
    }

    private Logger getLogger(JoinPoint joinPoint) {
        return LoggerFactory.getLogger(joinPoint.getSignature().getDeclaringType().getName());
    }
}
