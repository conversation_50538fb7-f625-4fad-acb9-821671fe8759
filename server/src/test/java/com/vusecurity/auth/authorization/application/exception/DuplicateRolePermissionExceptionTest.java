package com.vusecurity.auth.authorization.application.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class DuplicateRolePermissionExceptionTest {

    @Test
    void constructor_with_message() {
        DuplicateRolePermissionException ex = new DuplicateRolePermissionException("duplicate");
        assertEquals("duplicate", ex.getMessage());
        assertNull(ex.getCause());
    }

    @Test
    void constructor_with_message_and_cause() {
        RuntimeException cause = new RuntimeException("root");
        DuplicateRolePermissionException ex = new DuplicateRolePermissionException("duplicate", cause);
        assertEquals("duplicate", ex.getMessage());
        assertSame(cause, ex.getCause());
    }
}
