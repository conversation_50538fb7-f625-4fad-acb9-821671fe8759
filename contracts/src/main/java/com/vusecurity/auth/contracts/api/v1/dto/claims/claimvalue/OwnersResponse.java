package com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue;

import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import com.vusecurity.auth.contracts.enums.OwnerType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "Ownsers information response")
public class OwnersResponse extends BaseResponse {

    @Schema(description = "Type of owner",
            example = "IDENTITY")
    private OwnerType ownerType;

    @Schema(description = "Unique identifier of the owner (identity or account) this claim value belongs to",
            example = "456e7890-a12b-34c5-d678-901234567def")
    private UUID ownerId;

    @Schema(description = "List of ClaimValues associated with the owner.",
            ref = "#/components/schemas/ClaimValueResponse")
    List<ClaimValueResponse> claimValues;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        OwnersResponse that = (OwnersResponse) o;
        return ownerType == that.ownerType &&
               Objects.equals(ownerId, that.ownerId) &&
               Objects.equals(claimValues, that.claimValues);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), ownerType, ownerId, claimValues);
    }
}
