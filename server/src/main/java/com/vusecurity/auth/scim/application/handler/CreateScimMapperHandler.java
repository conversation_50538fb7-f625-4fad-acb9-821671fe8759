package com.vusecurity.auth.scim.application.handler;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.contracts.api.v1.dto.scimConfig.ScimFilterMapperRequest;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.scim.application.command.CreateScimMapperCommand;
import com.vusecurity.auth.scim.application.exception.InvalidScimMapperRequest;
import com.vusecurity.auth.scim.application.exception.ScimMapperAlreadyExistsException;
import com.vusecurity.auth.scim.application.service.ScimToClaimMappingService;
import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimToClaimMappingJpaEntity;
import com.vusecurity.auth.shared.util.DatabaseConstraintUtils;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.domain.services.BusinessService;
import com.vusecurity.business.usecases.exceptions.BusinessNotFoundException;

import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CreateScimMapperHandler {

    private static final Logger logger = LoggerFactory.getLogger(CreateScimTokenHandler.class);

    private final ScimToClaimMappingService scimToClaimMappingService;
    private final BusinessService businessService;
    private final ClaimSetRepository claimSetRepository;

    @Transactional
    public List<ScimToClaimMappingJpaEntity> createMapper(CreateScimMapperCommand command) {
        logger.debug("Creating SCIM mapper for business: {}", command.businessId());

        // Validate command
        command.validate();

        if (!scimToClaimMappingService.getByBusinessId(command.businessId()).isEmpty()){
            throw new ScimMapperAlreadyExistsException("A Mapper exists for the business " + command.businessId());
        }

        Business business = businessService.getById(command.businessId()).orElseThrow(() -> new BusinessNotFoundException());
        List<ClaimSetJpaEntity> businessClaimSets = claimSetRepository.findByBusinessIdAndAccountType(business.getId(), AccountType.FEDERATED);

        List<ScimToClaimMappingJpaEntity> newEntities = new ArrayList<>();

        for (ScimFilterMapperRequest filter : command.filters()) {
            //TODO: Until complex are implemented, set the same claimSet as Origin.
            filter.setClaimSetIdReferenced(filter.getClaimSetIdOrigin());
            //If there's no referenced claimSetId, then we use the required claimSetIdOrigin.
            /*
            if (filter.getClaimSetIdReferenced() == null){
                filter.setClaimSetIdReferenced(filter.getClaimSetIdOrigin());
            }
            */
            //If there's no sequence number, set it to 0.
            if (filter.getSequenceOrder() == null){
                filter.setSequenceOrder(0);
            }
            ScimToClaimMappingJpaEntity newMapper = new ScimToClaimMappingJpaEntity(
                business,
                filter.getFilter(),
                filter.getClaimSetIdOrigin(),
                filter.getClaimSetIdReferenced(),
                filter.getClaimDefinitionId(),
                filter.getSequenceOrder(),
                filter.getDescription()
            );

            //There must exist a valid path associated to the business and the claimSet / claimDefinition.
            businessClaimSets.stream().filter(cs ->{
                cs.getClaimDefinitionMappings().stream().filter(cd -> cd.getClaimDefinition().getId().equals(filter.getClaimDefinitionId()))
                    .findAny().orElseThrow(() -> new InvalidScimMapperRequest("No valid path found the provided business, claimSet and claimDefinition"));
                return cs.getId().equals(filter.getClaimSetIdOrigin()) || cs.getId().equals(filter.getClaimSetIdReferenced());
            }).findAny()
                .orElseThrow(() -> new InvalidScimMapperRequest("No valid path found the provided business, claimSet and claimDefinition"));

            newEntities.add(newMapper);
        }

        // Save token with constraint handling for unique token name
        List<ScimToClaimMappingJpaEntity> savedEntites = DatabaseConstraintUtils.executeWithConstraintHandling(
                () -> scimToClaimMappingService.saveAll(newEntities),
                "uk_scim_to_claim_mapping_business_id_claim_set_origin_claim_definition_referenced", // Unique constraint on mapper for business_id, claim_set_referenced and claim_definition_referenced
                () -> new ScimMapperAlreadyExistsException()
        );
        logger.debug("SCIM mapper created successfully for business: {}", command.businessId());

        return savedEntites;
    }
} 