package com.vusecurity.auth.identities.application.query;

import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;

import java.util.List;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

public interface GetAccountQuery {
    AccountJpaEntity getAccountById(String accountId);
    Page<AccountJpaEntity> getAllAccounts(GetAccountsPagedQuery query);
    Page<AccountJpaEntity> getAccountsByIdentity(GetAccountsByIdentityQuery query);
    /**
     * Filter UUIDs that don't belong to the provided business.
     * @param businessId UUID of the business
     * @param idsToCheck List of business to verify.
     * @return a list of account UUIDs that belong to the business.
     */
    List<UUID> filterUUIDsByBusiness(UUID businessId, List<UUID> idsToCheck);

    /**
     * Similar to the findAll of the account repository, but with the account belonging to the provided business and provider.
     * The specification can be null, the Pageable cannot be null.
     * @param businessId the business id.
     * @param providerId the provider id.
     * @param spec a {@link Specification}
     * @param page the {@link Pageable} options
     * @return Page of accounts that belongs to the corresponding business and provider, also, if provided, the specification.
     */
    Page<AccountJpaEntity> getAllAccountsByBusinessidAndProviderId(UUID businessId, UUID providerId, Specification<AccountJpaEntity> spec, Pageable page);

    /**
     * Similar to the {@link #getAllAccountsByBusinessidAndProviderId}
     * The specification can be null, the specification can contain references to {@link com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity}
     * The Pageable cannot be null.
     * @param businessId the business id.
     * @param providerId the provider id.
     * @param spec a {@link Specification}
     * @param page the {@link Pageable} options
     * @return Page of accounts that belongs to the corresponding business and provider, also, if provided, the specification.
     */
    Page<AccountJpaEntity> getAllAccountsBySpecs(UUID businessId, UUID providerId, Specification<AccountJpaEntity> dynamicSpec, Pageable page);
}
