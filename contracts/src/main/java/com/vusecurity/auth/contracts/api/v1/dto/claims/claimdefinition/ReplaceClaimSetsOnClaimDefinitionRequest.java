package com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "Request to replace claim sets to a claim definition")
public class ReplaceClaimSetsOnClaimDefinitionRequest {
    @NotEmpty(message = "Claim sets must not be empty")
    private List<ClaimSetToClaimDefinition> claimSets;
}
