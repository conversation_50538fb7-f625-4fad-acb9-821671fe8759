package com.vusecurity.auth.contracts.api.v1.dto.authorization;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.UUID;

@Data
@Accessors(chain = true)
@Schema(description = "Summary information about a role")
public class RoleSummary {
    
    @Schema(description = "Unique identifier of the role",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    @Schema(description = "Name of the role",
            example = "ROLE_ADMIN")
    private String name;

    @Schema(description = "Description of the role",
            example = "Administrator role with full access")
    private String description;
} 