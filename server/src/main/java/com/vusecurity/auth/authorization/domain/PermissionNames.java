package com.vusecurity.auth.authorization.domain;

/**
 * Compile-time String constants for permission names, safe to use inside annotations.
 * Keep in sync with PermissionEnum.
 */
public final class PermissionNames {
    private PermissionNames() {}

    // Identity
    public static final String IDENTITY_CREATE = "sys_identity:create";
    public static final String IDENTITY_READ = "sys_identity:read";
    public static final String IDENTITY_UPDATE = "sys_identity:update";
    public static final String IDENTITY_DELETE = "sys_identity:delete";
    public static final String IDENTITY_ALL = "sys_identity:all";

    // Account
    public static final String ACCOUNT_CREATE = "sys_account:create";
    public static final String ACCOUNT_READ = "sys_account:read";
    public static final String ACCOUNT_UPDATE = "sys_account:update";
    public static final String ACCOUNT_DELETE = "sys_account:delete";
    public static final String ACCOUNT_ALL = "sys_account:all";

    // Business
    public static final String BUSINESS_CREATE = "sys_business:create";
    public static final String BUSINESS_READ = "sys_business:read";
    public static final String BUSINESS_UPDATE = "sys_business:update";
    public static final String BUSINESS_DELETE = "sys_business:delete";
    public static final String BUSINESS_ALL = "sys_business:all";

    // Claim
    public static final String CLAIM_CREATE = "sys_claim:create";
    public static final String CLAIM_READ = "sys_claim:read";
    public static final String CLAIM_UPDATE = "sys_claim:update";
    public static final String CLAIM_DELETE = "sys_claim:delete";
    public static final String CLAIM_ALL = "sys_claim:all";

    // Consent
    public static final String CONSENT_CREATE = "sys_consent:create";
    public static final String CONSENT_READ = "sys_consent:read";
    public static final String CONSENT_UPDATE = "sys_consent:update";
    public static final String CONSENT_DELETE = "sys_consent:delete";
    public static final String CONSENT_ALL = "sys_consent:all";

    // Role
    public static final String ROLE_CREATE = "sys_role:create";
    public static final String ROLE_READ = "sys_role:read";
    public static final String ROLE_UPDATE = "sys_role:update";
    public static final String ROLE_DELETE = "sys_role:delete";
    public static final String ROLE_ALL = "sys_role:all";

    // Permission
    public static final String PERMISSION_CREATE = "sys_permission:create";
    public static final String PERMISSION_READ = "sys_permission:read";
    public static final String PERMISSION_UPDATE = "sys_permission:update";
    public static final String PERMISSION_DELETE = "sys_permission:delete";
    public static final String PERMISSION_ALL = "sys_permission:all";

    // Group
    public static final String GROUP_CREATE = "sys_group:create";
    public static final String GROUP_READ = "sys_group:read";
    public static final String GROUP_UPDATE = "sys_group:update";
    public static final String GROUP_DELETE = "sys_group:delete";
    public static final String GROUP_ALL = "sys_group:all";

    // Channel
    public static final String CHANNEL_CREATE = "sys_channel:create";
    public static final String CHANNEL_READ = "sys_channel:read";
    public static final String CHANNEL_UPDATE = "sys_channel:update";
    public static final String CHANNEL_DELETE = "sys_channel:delete";
    public static final String CHANNEL_ALL = "sys_channel:all";

    // Factor TOTP
    public static final String FACTOR_TOTP_CREATE = "sys_factor_totp:create";
    public static final String FACTOR_TOTP_READ = "sys_factor_totp:read";
    public static final String FACTOR_TOTP_UPDATE = "sys_factor_totp:update";
    public static final String FACTOR_TOTP_DELETE = "sys_factor_totp:delete";
    public static final String FACTOR_TOTP_ALL = "sys_factor_totp:all";

    // Factor SMS
    public static final String FACTOR_SMS_CREATE = "sys_factor_sms:create";
    public static final String FACTOR_SMS_READ = "sys_factor_sms:read";
    public static final String FACTOR_SMS_UPDATE = "sys_factor_sms:update";
    public static final String FACTOR_SMS_DELETE = "sys_factor_sms:delete";
    public static final String FACTOR_SMS_ALL = "sys_factor_sms:all";

    // Factor Email
    public static final String FACTOR_EMAIL_CREATE = "sys_factor_email:create";
    public static final String FACTOR_EMAIL_READ = "sys_factor_email:read";
    public static final String FACTOR_EMAIL_UPDATE = "sys_factor_email:update";
    public static final String FACTOR_EMAIL_DELETE = "sys_factor_email:delete";
    public static final String FACTOR_EMAIL_ALL = "sys_factor_email:all";

    // Factor Password
    public static final String FACTOR_PASSWORD_CREATE = "sys_factor_password:create";
    public static final String FACTOR_PASSWORD_READ = "sys_factor_password:read";
    public static final String FACTOR_PASSWORD_UPDATE = "sys_factor_password:update";
    public static final String FACTOR_PASSWORD_DELETE = "sys_factor_password:delete";
    public static final String FACTOR_PASSWORD_ALL = "sys_factor_password:all";

    // Factor Policies
    public static final String FACTOR_POLICIES_CREATE = "sys_factor_policies:create";
    public static final String FACTOR_POLICIES_READ = "sys_factor_policies:read";
    public static final String FACTOR_POLICIES_UPDATE = "sys_factor_policies:update";
    public static final String FACTOR_POLICIES_DELETE = "sys_factor_policies:delete";
    public static final String FACTOR_POLICIES_ALL = "sys_factor_policies:all";
}