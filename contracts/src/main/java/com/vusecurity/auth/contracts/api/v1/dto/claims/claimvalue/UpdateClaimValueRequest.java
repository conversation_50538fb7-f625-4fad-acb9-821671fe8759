package com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.UUID;

@Data
@Schema(description = "Request to update an existing claim value")
public class UpdateClaimValueRequest {

    @Schema(description = "Unique identifier of the claim set this value belongs to, required if accountId is present",
            example = "********-51a2-43d1-9c4f-abc9987fcdeb")
    private UUID claimSetId;

    @Schema(description = "New claim definition ID",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID claimDefinitionId;

    @Schema(description = "New identity ID",
            example = "987fcdeb-51a2-43d1-9c4f-********9abc")
    private UUID identityId;

    @Schema(description = "New account ID",
            example = "456e7890-a12b-34c5-d678-901234567def")
    private UUID accountId;

    @Schema(description = "New claim value",
            example = "<EMAIL>")
    private String value;

    @Schema(description = "Whether this should be the primary value",
            example = "true")
    private boolean isPrimary;

    @Schema(description = "Whether this value should be computed automatically",
            example = "false")
    private boolean isComputed;

    @Schema(description = "New source of the claim value",
            example = "USER_UPDATE")
    private String source;

    @Schema(description = "New primary index for the claim value",
            example = "1")
    private Integer primaryIndex;
}
