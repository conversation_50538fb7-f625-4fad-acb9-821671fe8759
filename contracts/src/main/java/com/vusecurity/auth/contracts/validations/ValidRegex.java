package com.vusecurity.auth.contracts.validations;

import jakarta.validation.Constraint;

import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = ValidRegexValidator.class)
@Target({ ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidRegex {
    String message() default "Invalid regular expression";
    Class<?>[] groups() default {};
    Class<?>[] payload() default {};
}
