package com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.shared.enums.ResultType;

import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import jakarta.persistence.*;

import java.time.Instant;

@Entity
@Table(name = "claim_verification",
        indexes = {
                @Index(name = "uk_claim_verif_id",           columnList = "id",              unique = true),
                @Index(name = "idx_claim_verif_value",       columnList = "claim_value_id"),
                @Index(name = "idx_claim_verif_type",        columnList = "verification_type"),
                @Index(name = "idx_claim_verif_result",      columnList = "result")
        })
@SuppressWarnings("LombokGetterMayBeUsed, LombokSetterMayBeUsed")
public class ClaimVerificationJpaEntity extends AbstractEntity {

    /**
     * The ClaimValue to which this verification belongs.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "claim_value_id",  // the column in claim_verification
            referencedColumnName = "id" // FK to ClaimValueJpaEntity.id
    )
    private ClaimValueJpaEntity claimValue;

    @Column(nullable = false)
    private String verificationType = "undefined";

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private ResultType result = ResultType.NONE;

    private Instant verifiedAt;

    // Constructors
    public ClaimVerificationJpaEntity() {
    }

    public ClaimVerificationJpaEntity(ClaimValueJpaEntity claimValue) {
        this.claimValue = claimValue;
    }

    public ClaimVerificationJpaEntity(ClaimValueJpaEntity claimValue, String verificationType, ResultType result) {
        this.claimValue = claimValue;
        this.verificationType = verificationType;
        this.result = result;
    }

    // Getters
    public ClaimValueJpaEntity getClaimValue() {
        return claimValue;
    }

    public String getVerificationType() {
        return verificationType;
    }

    public ResultType getResult() {
        return result;
    }

    public Instant getVerifiedAt() {
        return verifiedAt;
    }

    // Setters
    public void setClaimValue(ClaimValueJpaEntity claimValue) {
        this.claimValue = claimValue;
    }

    public void setVerificationType(String verificationType) {
        this.verificationType = verificationType;
    }

    public void setResult(ResultType result) {
        this.result = result;
    }

    public void setVerifiedAt(Instant verifiedAt) {
        this.verifiedAt = verifiedAt;
    }
}