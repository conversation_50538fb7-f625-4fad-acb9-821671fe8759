package com.vusecurity.auth.shared.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vusecurity.auth.shared.infrastructure.security.mfa.MultiFactorAuthenticationToken;
import org.junit.jupiter.api.Test;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify Jackson serialization/deserialization of MultiFactorAuthenticationToken.
 * This test ensures that our Jackson configuration properly handles the custom authentication token.
 */
class JacksonSecurityConfigTest {

    @Test
    void testMultiFactorAuthenticationTokenSerialization() throws Exception {
        // Arrange
        JacksonSecurityConfig config = new JacksonSecurityConfig();
        ObjectMapper objectMapper = config.securityObjectMapper();
        
        UUID accountId = UUID.randomUUID();
        String totp = "123456";
        String password = "testPassword";
        
        MultiFactorAuthenticationToken originalToken = new MultiFactorAuthenticationToken(
                accountId, totp, password);
        
        // Act - Serialize
        String json = objectMapper.writeValueAsString(originalToken);
        assertNotNull(json);
        assertFalse(json.isEmpty());
        
        // Act - Deserialize
        MultiFactorAuthenticationToken deserializedToken = objectMapper.readValue(
                json, MultiFactorAuthenticationToken.class);
        
        // Assert
        assertNotNull(deserializedToken);
        assertEquals(originalToken.getAccountId(), deserializedToken.getAccountId());
        assertEquals(originalToken.getTotp(), deserializedToken.getTotp());
        assertEquals(originalToken.getPassword(), deserializedToken.getPassword());
        assertEquals(originalToken.isAuthenticated(), deserializedToken.isAuthenticated());
    }


    /**
     * Mock implementation that mimics AccountUserDetails for testing purposes.
     * This class provides the getAccountId() method that MultiFactorAuthenticationToken expects.
     */
    private static class MockAccountUserDetails {
        private final UUID accountId;

        public MockAccountUserDetails(UUID accountId) {
            this.accountId = accountId;
        }

        public UUID getAccountId() {
            return accountId;
        }
    }
}
