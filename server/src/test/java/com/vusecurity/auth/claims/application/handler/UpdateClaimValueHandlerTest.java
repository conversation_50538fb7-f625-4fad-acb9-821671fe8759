package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.UpdateClaimValueCommand;
import com.vusecurity.auth.claims.application.exception.UpdateClaimTypeSystemException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.enums.ClaimType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UpdateClaimValueHandlerTest {

    @Mock
    private ClaimValueRepository claimValueRepository;
    
    @Mock
    private ClaimDefinitionRepository claimDefinitionRepository;

    @InjectMocks
    private UpdateClaimValueHandler handler;

    @Test
    void should_not_fail_when_updating_definition_of_claimType_user() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                null,
                UUID.randomUUID(),
                null,
                null, // null value should skip validation
                true,
                null,
                null,
                null,
                null
        );

        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.NUMERIC, null, ClaimType.USER_DEFINED);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        assertDoesNotThrow(() -> handler.updateClaimValue(cmd));
    }

    @Test
    void should_fail_when_updating_definition_of_claimType_system() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                null,
                null,
                null,
                null, // null value should skip validation
                true,
                null,
                null,
                null,
                null
        );

        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.NUMERIC, null, ClaimType.SYSTEM_DEFINED);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        assertThrows(UpdateClaimTypeSystemException.class, () -> handler.updateClaimValue(cmd));
    }

    private ClaimValueJpaEntity createClaimValue(UUID id, UUID claimDefinitionId) {
        ClaimValueJpaEntity value = new ClaimValueJpaEntity();
        value.setId(id);
        
        ClaimDefinitionJpaEntity definition = new ClaimDefinitionJpaEntity();
        definition.setId(claimDefinitionId);
        value.setClaimDefinition(definition);
        
        return value;
    }

    private ClaimDefinitionJpaEntity createClaimDefinition(UUID id, DataTypeEnum dataType, String dataFormat, ClaimType claimType) {
        ClaimDefinitionJpaEntity definition = new ClaimDefinitionJpaEntity();
        definition.setId(id);
        definition.setCode("test-code");
        definition.setName("Test Claim");
        definition.setDataType(dataType);
        definition.setDataFormat(dataFormat);
        definition.setClaimType(claimType);
        return definition;
    }
}