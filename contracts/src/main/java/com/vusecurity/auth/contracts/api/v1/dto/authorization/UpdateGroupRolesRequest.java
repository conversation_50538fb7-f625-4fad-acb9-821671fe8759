package com.vusecurity.auth.contracts.api.v1.dto.authorization;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "Request to update the roles of a group")
public class UpdateGroupRolesRequest {

    @Schema(description = "List of role IDs that should be assigned to the group",
            example = "[\"123e4567-e89b-12d3-a456-************\", \"987fcdeb-51a2-43d1-9c4f-123456789abc\"]")
    private List<UUID> roleIds;
} 