package com.vusecurity.auth.identities.application.handler;

import com.vusecurity.auth.contracts.enums.IdentityType;
import com.vusecurity.auth.identities.application.exception.IdentityNotFoundException;
import com.vusecurity.auth.identities.application.query.GetIdentitiesPagedQuery;
import com.vusecurity.auth.identities.application.query.GetIdentityQuery;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class GetIdentityHandler implements GetIdentityQuery {

    private static final Logger logger = LoggerFactory.getLogger(GetIdentityHandler.class);
    
    private final IdentityRepository identityRepository;

    @Override
    public IdentityJpaEntity getIdentityById(String identityId) {
        logger.trace("Getting identity by ID: {}", identityId);
        
        try {
            UUID uuid = UUID.fromString(identityId);
            return identityRepository.findByIdentityId(uuid)
                    .orElseThrow(() -> new IdentityNotFoundException(identityId));
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid identity ID dataFormat: " + identityId);
        }
    }

    @Override
    public Page<IdentityJpaEntity> getAllIdentities(GetIdentitiesPagedQuery query) {
        logger.trace("Getting all identities - page: {}, size: {}", query.getPage(), query.getPageSize());

        int page = Math.max(0, query.getPage() - 1); // Convert to 0-based
        int size = Math.min(100, Math.max(1, query.getPageSize())); // Limit size between 1-100

        PageRequest pageRequest = PageRequest.of(page, size);

        // Apply filters
        if (query.getName() != null && !query.getName().trim().isEmpty()) {
            return identityRepository.findByNameContaining(query.getName().trim(), pageRequest);
        }

        if (query.getIdentityType() != null && !query.getIdentityType().trim().isEmpty()) {
            try {
                IdentityType identityType = IdentityType.valueOf(query.getIdentityType().trim().toUpperCase());
                return identityRepository.findByIdentityType(identityType, pageRequest);
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("Invalid identity type: " + query.getIdentityType());
            }
        }

        if (query.getLifecycleState() != null && !query.getLifecycleState().trim().isEmpty()) {
            return identityRepository.findByLifecycleState(query.getLifecycleState().trim(), pageRequest);
        }

        return identityRepository.findAll(pageRequest);
    }
}
