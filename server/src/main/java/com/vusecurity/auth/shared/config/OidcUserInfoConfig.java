package com.vusecurity.auth.shared.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Configuration properties for OIDC UserInfo endpoint customization.
 * This allows configuring claim mappings and filtering for the UserInfo response.
 */
@Configuration
@ConfigurationProperties(prefix = "app.oidc.userinfo")
@Data
@Validated
public class OidcUserInfoConfig {

    /**
     * Custom claim mappings from internal claim codes to OIDC standard claim names.
     * Example: {"internal_email": "email", "internal_phone": "phone_number"}
     */
    private Map<String, String> claimMappings = new HashMap<>();

    /**
     * Set of claim codes to exclude from UserInfo response.
     * Useful for filtering sensitive or internal-only claims.
     */
    private Set<String> excludedClaims = Set.of();

    /**
     * Set of metadata keys to include in UserInfo response.
     * By default, only non-sensitive metadata is included.
     */
    private Set<String> includedMetadataKeys = Set.of();

    /**
     * Whether to include custom VU Security claims (business_id, identity_id, etc.)
     * in the UserInfo response. Default: true
     */
    private boolean includeCustomClaims = true;

    /**
     * Whether to include account metadata in the UserInfo response.
     * Default: true (but filtered for sensitive keys)
     */
    private boolean includeMetadata = true;

    /**
     * Whether to include address claim as a structured object.
     * Default: true
     */
    private boolean includeAddress = true;

    /**
     * Fallback display name format when first_name and last_name are not available.
     * Supported placeholders: {identity_name}, {username}, {email}, {account_id}
     * Default: "{identity_name}"
     */
    private String fallbackDisplayNameFormat = "{identity_name}";

    /**
     * Default values for standard OIDC claims when not available from account claims.
     */
    private Map<String, Object> defaultClaimValues = new HashMap<>();

    /**
     * Gets the OIDC claim name for an internal claim code.
     * Returns the original code if no mapping is configured.
     */
    public String getOidcClaimName(String internalClaimCode) {
        return claimMappings.getOrDefault(internalClaimCode, internalClaimCode);
    }

    /**
     * Checks if a claim should be excluded from UserInfo response.
     */
    public boolean isClaimExcluded(String claimCode) {
        return excludedClaims.contains(claimCode);
    }

    /**
     * Checks if a metadata key should be included in UserInfo response.
     */
    public boolean isMetadataKeyIncluded(String metadataKey) {
        if (!includeMetadata) {
            return false;
        }
        
        // If specific keys are configured, only include those
        if (!includedMetadataKeys.isEmpty()) {
            return includedMetadataKeys.contains(metadataKey);
        }
        
        // Otherwise, exclude sensitive keys by default
        return !isSensitiveMetadataKey(metadataKey);
    }

    /**
     * Determines if a metadata key contains sensitive information.
     */
    private boolean isSensitiveMetadataKey(String key) {
        if (key == null) return true;
        
        String lowerKey = key.toLowerCase();
        return lowerKey.contains("password") || 
               lowerKey.contains("secret") || 
               lowerKey.contains("token") || 
               lowerKey.contains("key") ||
               lowerKey.contains("credential") ||
               lowerKey.contains("hash") ||
               lowerKey.contains("salt");
    }

    /**
     * Gets default value for a claim if configured.
     */
    public Object getDefaultClaimValue(String claimName) {
        return defaultClaimValues.get(claimName);
    }

    /**
     * Configuration for standard claim mappings used in VU Security.
     * This provides sensible defaults for common internal claim codes.
     */
    public static OidcUserInfoConfig createDefaultConfig() {
        OidcUserInfoConfig config = new OidcUserInfoConfig();
        
        // Standard claim mappings
        Map<String, String> mappings = new HashMap<>();
        mappings.put("email_address", "email");
        mappings.put("phone_number", "phone_number");
        mappings.put("first_name", "given_name");
        mappings.put("last_name", "family_name");
        mappings.put("middle_name", "middle_name");
        mappings.put("birth_date", "birthdate");
        mappings.put("profile_url", "profile");
        mappings.put("picture_url", "picture");
        mappings.put("website_url", "website");
        config.setClaimMappings(mappings);
        
        // Exclude sensitive claims by default
        config.setExcludedClaims(Set.of(
            "password_hash",
            "password_salt", 
            "totp_secret",
            "recovery_codes",
            "api_keys",
            "internal_id"
        ));
        
        // Default claim values
        Map<String, Object> defaults = new HashMap<>();
        defaults.put("email_verified", false);
        defaults.put("phone_number_verified", false);
        config.setDefaultClaimValues(defaults);
        
        return config;
    }
}
