package com.vusecurity.auth.contracts.api.v1.dto.identities;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
@Schema(description = "Request to create a new account")
public class CreateAccountRequest {
    @NotNull(message = "Business ID is required")
    @Schema(description = "Unique identifier of the business this account belongs to",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID businessId;

    @NotNull(message = "Identity ID is required")
    @Schema(description = "Unique identifier of the identity this account is associated with",
            example = "987fcdeb-51a2-43d1-9c4f-123456789abc")
    private UUID identityId;

    @NotNull(message = "Identity provider ID is required")
    @Schema(description = "Unique identifier of the identity provider",
            example = "456e7890-a12b-34c5-d678-901234567def")
    private UUID identityProviderId;

    @NotNull(message = "Account type is required")
    @Schema(description = "Type of account being created",
            example = "WORKFORCE")
    private AccountType accountType;

    @Schema(description = "Additional metadata for the account as key-value pairs",
            example = "{\"department\": \"Engineering\", \"role\": \"Developer\"}")
    private Map<String, Object> metadata;

    @Schema(description = "Current lifecycle state of the account",
            example = "ACTIVE")
    private AccountLifecycleState lifecycleState;

    @Valid
    @Schema(
            description = "List of claim values to create with the account",
            oneOf = {ScalarClaimValueRequest.class, ArrayClaimValueRequest.class}
    )
    @JsonProperty("claimValues")
    List<AccountClaimValueRequest> claimValues;
}
