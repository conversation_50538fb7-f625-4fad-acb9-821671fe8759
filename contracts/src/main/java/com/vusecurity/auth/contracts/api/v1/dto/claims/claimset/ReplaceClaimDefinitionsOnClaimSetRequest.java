package com.vusecurity.auth.contracts.api.v1.dto.claims.claimset;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "Request to replace all claim definitions in a claim set")
public class ReplaceClaimDefinitionsOnClaimSetRequest {
    @NotNull(message = "Claim definition are required")
    @NotEmpty(message = "At least one claim definition must be provided")
    @Schema(description = "List of claim definitions to assign")
    private List<ClaimDefinitionsToClaimSetRequest> claimDefinitions;

    @Data
    @Schema(description = "Claim definition and related configuration")
    public static class ClaimDefinitionsToClaimSetRequest {
        @NotNull(message = "Claim definition ID is required")
        @Schema(description = "Claim definition IDs to assign",
                example = "\"123e4567-e89b-12d3-a456-************\"")
        private UUID claimDefinitionId;
        @Schema(
                description = "Order of the claim definition in the set",
                example = "1"
        )
        private Integer claimDefinitionOrder;
        @Schema(
                description = "Whether to enforce uniqueness for this claim",
                example = "true"
        )
        private Boolean enforceUniqueness;
    }
}
