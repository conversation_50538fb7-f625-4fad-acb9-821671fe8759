package com.vusecurity.auth.shared.infrastructure.security.mfa;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.vusecurity.auth.shared.infrastructure.security.oidc.AccountUserDetails;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;
import java.util.UUID;

/**
 * Custom authentication token for multi-factor authentication.
 * This token holds accountId, TOTP, and password for validation against the
 * factor server.
 */
public class MultiFactorAuthenticationToken extends AbstractAuthenticationToken {

    private final UUID accountId;
    private final String totp;
    private final String password;

    @JsonIgnore
    private final Object principal;

    /**
     * Constructor for unauthenticated token (before validation)
     */
    public MultiFactorAuthenticationToken(UUID accountId, String totp, String password) {
        super(null);
        this.accountId = accountId;
        this.totp = totp != null ? totp.trim() : null;
        this.password = password;
        this.principal = accountId != null ? accountId.toString() : "unknown";
        setAuthenticated(false);
    }



    /**
     * Constructor for authenticated token (after successful validation)
     */
    public MultiFactorAuthenticationToken(Object principal, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = ((AccountUserDetails) principal).getAccountId();
        this.accountId = ((AccountUserDetails) principal).getAccountId();
        this.totp = null;
        this.password = null;
        setAuthenticated(true);
    }

    /**
     * Constructor for Jackson deserialization.
     * This constructor can handle both authenticated and unauthenticated tokens.
     */
    public MultiFactorAuthenticationToken(
            UUID accountId,
            String totp,
            String password,
            Collection<? extends GrantedAuthority> authorities,
            boolean authenticated) {
        super(authorities);
        this.accountId = accountId;
        this.totp = totp;
        this.password = password;
        this.principal = accountId != null ? accountId.toString() : "unknown";
        setAuthenticated(authenticated);
    }

    @Override
    public Object getCredentials() {
        return password;
    }

    @Override
    public Object getPrincipal() {
        return principal;
    }

    public UUID getAccountId() {
        return accountId;
    }

    public String getTotp() {
        return totp;
    }

    public String getPassword() {
        return password;
    }

    @Override
    public void eraseCredentials() {
        super.eraseCredentials();
        // Note: We can't modify final fields, but Spring Security will handle this
        // appropriately
    }
}
