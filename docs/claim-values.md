# Claim Values

## owner = account_id o identity_id

| # | Business rule (what must be guaranteed) | Applies when … | Enforce in … | KEY columns / filter | Example that is ALLOWED | Example that is BLOCKED |
|---|-----------------------------------------|----------------|---------------|------------------------|---------------------------|--------------------------|
| 1 | The same owner cannot store the **same value** for the same CD twice | every insert | DB – UNIQUE indexes | `(account_id, CD, lower(value))` – filter: `account_id IS NOT NULL`<br>`(identity_id, CD, lower(value))` – filter: `identity_id IS NOT NULL` | A1 / email = alice@x, bob@x | A1 / email = alice@x (inserted twice) |
| 2 | Cardinality-1 for _identifier_ claims: the owner may have **at most one value** for that CD (value can be anything) | `mapping.claimSet.isIdentifier = true`<br>or `mapping.enforceUniqueness = true` | Application (handler) | Check `existsByOwnerAndCd(owner, CD)` | A1 email = alice@x (first) | A1 already has any email, tries to add another (identifier claim) |
| 3 | “Primary” flag: at most **one** row per (owner, CD) may have `is_primary = true` (used for ARRAY claims) | only rows with `is_primary = true` | DB – UNIQUE indexes | `(account_id, CD)` WHERE `is_primary = 1 AND account_id IS NOT NULL`<br>`(identity_id, CD)` WHERE `is_primary = 1 AND identity_id IS NOT NULL` | A1 phones: ****** (primary), ****** (not primary) | Two rows for A1 / phones both flagged primary |
| 4 | Cross-account duplicate blocking: two **different accounts of the same AT** cannot share the same value when rule-2 applies | Same condition as rule-2 **and** handler invoked with `accountId` | Application (handler) | Finder: `existsForType(AT, CD, lower(value))` | CUSTOMER-Alice SSN = 123-…, WORKFORCE-Bob SSN = 123-… | CUSTOMER-Alice SSN = 123-…, CUSTOMER-Carol SSN = 123-… (duplicate across CUSTOMER accounts) |
| 5 | Identity path bypasses rules 2 & 4 – uniqueness across accounts is **not** checked when the call is done with `identityId` (`account_id = NULL`) | Handler invoked with `identityId` | Short-circuit in handler | — | Identity I1 email = alex@x even if a CUSTOMER already has alex@x | — (rule is deliberately skipped) |

### Regla 2. Si se puede repetir si es en distintos Domains. Que pasa si defino (owner, CD) en global?
Identity es siempre global
Account -> siempre voy a estar en un negocio y acccount type

### Regla 4 - Si y solo si Is_Identifier

### Regla 5 - Por el momento para una identidad voy a validar solo regla 1.