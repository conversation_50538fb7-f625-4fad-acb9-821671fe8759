package com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import com.vusecurity.business.domain.Business;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Entity
@Table(name = "scim_tokens",
        indexes = {
                @Index(name = "uk_scim_tokens_id", columnList = "id", unique = true),
                @Index(name = "uk_scim_tokens_business_id", columnList = "business_id, identity_provider_id, token", unique = true)
        },
        uniqueConstraints = @UniqueConstraint(name ="uk_scim_tokens_name", columnNames = "name")
    )
@NoArgsConstructor
public class ScimTokenJpaEntity extends AbstractEntity {

    @Column(nullable = false)
    @Size(max = 255)
    private String name;

    @Column(length = 1024)
    @Size(max = 1024)
    private String description;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(
            name = "business_id",           // column in table
            foreignKey = @ForeignKey(name = "fk_scim_tokens_business"),
            referencedColumnName = "id"     // PK of Business
    )
    private Business business;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(
            name = "identity_provider_id",  // column in account table
            foreignKey = @ForeignKey(name = "fk_scim_tokens_identity_provider"),
            referencedColumnName = "id"     // PK of IdentityProviderJpaEntity
    )
    private IdentityProviderJpaEntity identityProvider;

    @Column(nullable = false)
    private String token;

    // Constructor without ID (for production use - JPA will generate ID)
    public ScimTokenJpaEntity(Business business, IdentityProviderJpaEntity identityProvider, String token) {
        if (business == null) {
            throw new IllegalArgumentException("business cannot be null");
        }
        if (identityProvider == null ) {
            throw new IllegalArgumentException("identityProvider cannot be null");
        }
        if (token == null || token.isEmpty()) {
            throw new IllegalArgumentException("token cannot be null or be empty");
        }
        
        this.business = business;
        this.identityProvider = identityProvider;
        this.token = token;
    }

    // Constructor with ID (for migration scenarios)
    public ScimTokenJpaEntity(UUID primaryId, Business business, IdentityProviderJpaEntity identityProvider, String token) {
        this(business, identityProvider, token);
        this.setId(primaryId);
    }

    public Business getBusiness() {
        return business;
    }

    public void setBusiness(Business business) {
        this.business = business;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public IdentityProviderJpaEntity getIdentityProvider() {
        return identityProvider;
    }

    public void setIdentityProvider(IdentityProviderJpaEntity identityProvider) {
        this.identityProvider = identityProvider;
    }    

    public void delete(){
        this.setStatus(Status.DELETED);
    }

    public void deactivate(){
        if (this.getStatus().equals(Status.ACTIVE)){
            this.setStatus(Status.INACTIVE);
        }
    }

    public void activate(){
        if (this.getStatus().equals(Status.INACTIVE)){
            this.setStatus(Status.ACTIVE);
        }
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = super.hashCode();
        result = prime * result + ((name == null) ? 0 : name.hashCode());
        result = prime * result + ((description == null) ? 0 : description.hashCode());
        result = prime * result + ((business == null) ? 0 : business.hashCode());
        result = prime * result + ((identityProvider == null) ? 0 : identityProvider.hashCode());
        result = prime * result + ((token == null) ? 0 : token.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (!super.equals(obj))
            return false;
        if (getClass() != obj.getClass())
            return false;
        ScimTokenJpaEntity other = (ScimTokenJpaEntity) obj;
        if (name == null) {
            if (other.name != null)
                return false;
        } else if (!name.equals(other.name))
            return false;
        if (description == null) {
            if (other.description != null)
                return false;
        } else if (!description.equals(other.description))
            return false;
        if (business == null) {
            if (other.business != null)
                return false;
        } else if (!business.equals(other.business))
            return false;
        if (identityProvider == null) {
            if (other.identityProvider != null)
                return false;
        } else if (!identityProvider.equals(other.identityProvider))
            return false;
        if (token == null) {
            if (other.token != null)
                return false;
        } else if (!token.equals(other.token))
            return false;
        return true;
    }

    @Override
    public String toString() {
        return "BusinessScimToClaimMappingJpaEntity{" +
            "id=" + getId() +
            ", name=" + getName() +
            ", description=" + getDescription() +
            ", business=" + getBusiness() +
            ", identityProvider=" + getIdentityProvider() +
            ", token=" + getToken() +
            '}';
    }
}
