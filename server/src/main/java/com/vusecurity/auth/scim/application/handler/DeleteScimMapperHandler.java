package com.vusecurity.auth.scim.application.handler;

import com.vusecurity.auth.scim.application.exception.InvalidScimMapperRequest;
import com.vusecurity.auth.scim.application.service.ScimToClaimMappingService;
import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimToClaimMappingJpaEntity;

import lombok.RequiredArgsConstructor;

import java.util.Optional;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DeleteScimMapperHandler {

    private static final Logger logger = LoggerFactory.getLogger(DeleteScimMapperHandler.class);

    private final ScimToClaimMappingService scimToClaimMappingService;
    
    public void deleteMapper(UUID businessId) {
        logger.debug("deleting SCIM Mapper: {}", businessId.toString());

        scimToClaimMappingService.deleteByBusinessId(businessId);

        logger.debug("deleted SCIM Mapper: {}", businessId.toString());
    }

    public void deleteFilter(UUID filterId) {
        logger.debug("deleting filter: {}", filterId.toString());

        Optional<ScimToClaimMappingJpaEntity> filter = scimToClaimMappingService.findById(filterId);
        if (filter.isPresent()){
            String mapping = filter.get().getFilterMapping();
            logger.debug("Found filter with id: {} and mapping: {}", filterId.toString(), mapping);

            if (mapping.equals("userName") || mapping.equals("externalId")){
                logger.debug("Refusing to delete filter as maps a required value");
                throw new InvalidScimMapperRequest("Filters that map `userName` or `externalId` cannot be deleted as they are required.");
            }

            scimToClaimMappingService.deleteById(filterId);
        }

        logger.debug("deleted SCIM Mapper: {}", filterId.toString());
    }
} 