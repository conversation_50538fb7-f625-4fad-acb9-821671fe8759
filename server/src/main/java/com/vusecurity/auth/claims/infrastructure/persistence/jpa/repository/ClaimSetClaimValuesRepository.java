package com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetClaimValueId;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetClaimValuesJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface ClaimSetClaimValuesRepository extends JpaRepository<ClaimSetClaimValuesJpaEntity, ClaimSetClaimValueId> {
    List<ClaimSetClaimValuesJpaEntity> findByClaimSetId(UUID claimSetId);

    List<ClaimSetClaimValuesJpaEntity> findByClaimValueId(UUID claimValueId);

    boolean existsByClaimSetIdAndClaimValueId(UUID claimSetId, UUID claimValueId);

    void deleteByClaimSetId(UUID claimSetId);

    void deleteByClaimValueId(UUID claimValueId);
}