package com.vusecurity.auth.authorization.mapper;

import com.vusecurity.auth.authorization.dto.ClientSettingsDto;
import com.vusecurity.auth.authorization.dto.TokenSettingsDto;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.Oauth2RegisteredClient;
import org.junit.jupiter.api.Test;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;

import java.time.Duration;
import java.time.Instant;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class OauthRegisteredClientMapperTest {

    private RegisteredClient buildRegisteredClient() {
        return RegisteredClient.withId(UUID.randomUUID().toString())
                .clientId("cid")
                .clientIdIssuedAt(Instant.now())
                .clientSecret("secret")
                .clientSecretExpiresAt(Instant.now().plusSeconds(3600))
                .clientName("name")
                .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
                .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS)
                .redirectUri("https://a.example/cb")
                .postLogoutRedirectUri("https://a.example/pl")
                .scope("read")
                .scope("write")
                .clientSettings(ClientSettings.builder()
                        .requireAuthorizationConsent(true)
                        .requireProofKey(true)
                        .jwkSetUrl("https://keys.example/jwks")
                        .x509CertificateSubjectDN("CN=client")
                        .build())
                .tokenSettings(TokenSettings.builder()
                        .accessTokenTimeToLive(Duration.ofSeconds(300))
                        .reuseRefreshTokens(true)
                        .refreshTokenTimeToLive(Duration.ofSeconds(3600))
                        .x509CertificateBoundAccessTokens(false)
                        .authorizationCodeTimeToLive(Duration.ofSeconds(300))
                        .deviceCodeTimeToLive(Duration.ofSeconds(600))
                        .build())
                .build();
    }

    @Test
    void toEntity_and_back_preserves_core_fields_and_settings() {
        RegisteredClient rc = buildRegisteredClient();

        Oauth2RegisteredClient entity = OauthRegisteredClientMapper.toEntity(rc);
        assertNotNull(entity);
        assertEquals(rc.getId(), entity.getId());
        assertEquals(rc.getClientId(), entity.getClientId());
        assertEquals(rc.getClientName(), entity.getClientName());
        assertTrue(entity.getScopes().contains("read"));
        assertTrue(entity.getScopes().contains("write"));
        ClientSettingsDto cs = entity.getClientSettings();
        assertEquals(Boolean.TRUE, cs.getRequireAuthorizationConsent());
        assertEquals(Boolean.TRUE, cs.getRequireProofKey());
        assertEquals("https://keys.example/jwks", cs.getJwkSetUrl());
        TokenSettingsDto ts = entity.getTokenSettings();
        assertEquals(300, ts.getAccessTokenTimeToLive());
        assertEquals(3600, ts.getRefreshTokenTimeToLive());

        RegisteredClient back = OauthRegisteredClientMapper.toRegisteredClient(entity);
        assertEquals(rc.getClientId(), back.getClientId());
        assertEquals(rc.getClientName(), back.getClientName());
        assertTrue(back.getScopes().contains("read"));
        assertTrue(back.getScopes().contains("write"));
    }
}

