package com.vusecurity.auth.contracts.api.v1.dto.scimConfig;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import com.vusecurity.core.commons.Auditable.Status;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "SCIM authentication token response")
public class ScimTokenResponse extends BaseResponse {

    @Schema(description = "Identification of the token",
        example = "6e63a8f0-193b-4268-9afa-274bc44ddf40")
    private UUID id;

    @Schema(description = "Plain token value",
        example = "f5f96c04-a6fc-54a7-02e5-85b55ace")
    @JsonInclude(Include.NON_NULL)
    private String token;

    @Schema(description = "Name of the account group",
        example = "Engineering Team")
    private String name;

    @Schema(description = "Description of the account group",
        example = "Engineering team members in Argentina office")
    private String description;

    @Schema(description = "Id of the associated business",
        example = "f9fc0a64-56fc-4477-87a7-8e525505abce")
    private UUID businessId;

    @Schema(description = "Name of the associated business",
        example = "My Business")
    private String businesName;

    @Schema(description = "Id of the associated Identity Provider",
        example = "f5250a64-5f6c-4433-84a7-9fc8e505abce")
    private UUID identityProviderId;

    @Schema(description = "Name of the associated Identity Provider",
        example = "SCIM provider")
    private String identityProviderName;

    @Schema(description = "The status of the token",
            example = "ACTIVE")
    private Status status;
} 