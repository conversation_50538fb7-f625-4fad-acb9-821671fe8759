package com.vusecurity.auth.shared.infrastructure.security.scim;

import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimTokenJpaEntity;
import com.vusecurity.core.commons.Auditable.Status;

import org.springframework.security.core.CredentialsContainer;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.*;

/**
 * Custom implementation of UserDetails and CredentialsContainer based on
 * TokenDetails.
 */
public class TokenDetails implements UserDetails, CredentialsContainer {

    private final ScimTokenJpaEntity token;
    private final Collection<GrantedAuthority> authorities;

    public TokenDetails(ScimTokenJpaEntity token) {
        this.token = token;
        this.authorities = buildAuthorities();
    }

    private Collection<GrantedAuthority> buildAuthorities() {
        Set<GrantedAuthority> authorities = new HashSet<>();

        //! TODO: do we need authorities for SCIM
        // Add role-based authorities
        /*
        if (account.getRoles() != null) {
            authorities.addAll(
                    account.getRoles().stream()
                            .map(role -> new SimpleGrantedAuthority("ROLE_" + role.getName().toUpperCase()))
                            .collect(Collectors.toSet()));
        }

        // Add account type as authority
        if (account.getAccountType() != null) {
            authorities.add(new SimpleGrantedAuthority("ACCOUNT_TYPE_" + account.getAccountType().name()));
        }
        */

        return authorities;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }

    @Override
    public String getPassword() {
        //noOp
        //return token.getToken();
        return null;
    }

    @Override
    public String getUsername() {
        // Use token ID
        return token.getName();
    }

    @Override
    public boolean isAccountNonExpired() {
        // token is active
        return this.isEnabled();
    }

    @Override
    public boolean isAccountNonLocked() {
        // token is active
        return this.isEnabled();
    }

    @Override
    public boolean isCredentialsNonExpired() {
        // For now, credentials don't expire
        return true;
    }

    @Override
    public boolean isEnabled() {
        // Account is enabled if it's ACTIVE
        return Status.ACTIVE.equals(token.getStatus());
    }

    @Override
    public void eraseCredentials() {
        //noOP
        //this.token.setToken(null);
    }

    // Additional methods to access account information

    public ScimTokenJpaEntity getToken() {
        return token;
    }

    public UUID getTokenId() {
        return token.getId();
    }

    public UUID getBusinessId() {
        return token.getBusiness().getId();
    }

    public UUID getIdentityProviderId() {
        return token.getIdentityProvider().getId();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        TokenDetails other = (TokenDetails) obj;
        if (token == null) {
            if (other.token != null)
                return false;
        } else if (!token.equals(other.token))
            return false;
        if (authorities == null) {
            if (other.authorities != null)
                return false;
        } else if (!authorities.equals(other.authorities))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((token == null) ? 0 : token.hashCode());
        result = prime * result + ((authorities == null) ? 0 : authorities.hashCode());
        return result;
    }

    @Override
    public String toString() {
        return "ScimTokenJpaEntity{" +
                "tokenId=" + token.getId() +
                ", businessId=" + getBusinessId() +
                ", identityProviderId=" + getIdentityProviderId() +
                ", Status=" + token.getStatus() +
                ", authorities=" + authorities.size() +
                '}';
    }
}
