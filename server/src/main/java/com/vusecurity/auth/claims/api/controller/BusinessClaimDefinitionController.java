package com.vusecurity.auth.claims.api.controller;

import static com.vusecurity.auth.authorization.domain.PermissionNames.CLAIM_READ;

import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.vusecurity.auth.claims.application.service.ClaimDefinitionLookupService;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.BusinessIdentifierClaimSetResponse;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.shared.config.aspect.Oauth2Authorize;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;

/**
 * REST controller for business-specific claim definition operations.
 * Provides endpoints for retrieving claim definitions scoped to a business.
 */
@RestController
@RequiredArgsConstructor
@SecurityRequirement(name = "oauth2 client credentials")
@Tag(name = "Business Claim Definitions", description = "Business-scoped claim definition operations")
public class BusinessClaimDefinitionController {

        private static final Logger logger = LoggerFactory.getLogger(BusinessClaimDefinitionController.class);

        private final ClaimDefinitionLookupService claimDefinitionLookupService;

        /**
         * Get the identifier claim set with its claim definitions for a business and
         * account type.
         *
         * This endpoint retrieves the identifier claim set where:
         * - claim_set.business_id = businessId
         * - claim_set.account_type = accountType
         * - claim_set.is_identifier = true
         *
         * Since there can be at most one identifier claim set per business/account type
         * combination,
         * this returns a single response containing the claim set info and its claim
         * definitions.
         *
         * @param businessId  the business ID
         * @param accountType the account type
         * @return business identifier claim set response with claim set info and claim
         *         definitions
         */
        @Operation(summary = "Get identifier claim set for a business and account type", description = "Retrieves the identifier claim set for the specified business and account type, including both the claim set information and its associated claim definitions. "
                        +
                        "Returns HTTP 200 with the claim set and its claim definitions when found, or HTTP 404 when no identifier claim set exists.")
        @ApiResponses({
                        @ApiResponse(responseCode = "200", description = "Identifier claim set found", content = @Content(mediaType = "application/json", schema = @Schema(implementation = BusinessIdentifierClaimSetResponse.class))),
                        @ApiResponse(responseCode = "404", description = "No identifier claim set found for the business and account type"),
                        @ApiResponse(responseCode = "400", description = "Invalid business ID format or unsupported account type"),
                        @ApiResponse(responseCode = "401", description = "Authentication required"),
                        @ApiResponse(responseCode = "403", description = "Access denied")
        })
        @GetMapping("${app.business.context-path}/businesses/{businessId}/identifier-claim-definitions")
        @Oauth2Authorize(permission = CLAIM_READ)
        public BusinessIdentifierClaimSetResponse getIdentifierClaimDefinitions(
                        @PathVariable("businessId") @NotNull UUID businessId,
                        @RequestParam("accountType") AccountType accountType) {

                logger.debug("Getting identifier claim set for business: {} and account type: {}", businessId,
                                accountType);

                BusinessIdentifierClaimSetResponse response = claimDefinitionLookupService
                                .getIdentifierClaimSet(businessId, accountType);
                logger.debug("Found identifier claim set '{}' with {} claim definitions for business: {} and account type: {}",
                                response.getClaimSet().getName(), response.getClaimDefinitions().size(), businessId,
                                accountType);
                return response;
        }
}
