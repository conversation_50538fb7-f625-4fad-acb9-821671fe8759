package com.vusecurity.auth.contracts.api.v1.dto.authorization;

import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "Basic permission information")
public class PermissionBasicInfoResponse extends BaseResponse {
    @Schema(description = "Unique identifier of the permission",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    @Schema(description = "Name of the permission",
            example = "READ_USERS")
    private String name;
}