package com.vusecurity.auth.identities.application.handler;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.service.ClaimSetDefinitionMappingService;
import com.vusecurity.auth.contracts.api.v1.dto.identities.ClaimIdentifierRequest;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.identities.application.exception.AccountNotFoundException;
import com.vusecurity.auth.identities.application.query.IdentifyAccountByClaimsQuery;
import com.vusecurity.auth.identities.application.query.IdentifyAccountQuery;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class IdentifyAccountHandler implements IdentifyAccountQuery {

    private static final Logger logger = LoggerFactory.getLogger(IdentifyAccountHandler.class);
    
    private final AccountRepository accountRepository;
    private final ClaimSetRepository claimSetRepository;
    private final ClaimSetDefinitionMappingService mappingService;

    @Override
    public AccountJpaEntity identifyAccountByClaims(IdentifyAccountByClaimsQuery query) {
        logger.debug("Identifying account by claims for business: {}, accountType: {}, claims: {}", 
                query.getBusinessId(), query.getAccountType(), query.getClaimIdentifiers().size());
        
        // Validate query
        query.validate();
        
        // 1. Retrieve and validate ClaimSet for business+accountType+isIdentifier=true
        ClaimSetJpaEntity claimSet = claimSetRepository.findByBusinessIdAndAccountTypeAndIsIdentifier(
                query.getBusinessId(), query.getAccountType(), true)
                .orElseThrow(() -> {
                    logger.debug("No identifier ClaimSet found for business: {} and account type: {}", 
                            query.getBusinessId(), query.getAccountType());
                    return new NoSuchElementException("No identifier ClaimSet found for business: " + 
                            query.getBusinessId() + " and account type: " + query.getAccountType());
                });
        
        // 2. Get claim definitions for this claim set
        List<ClaimDefinitionJpaEntity> validClaimDefinitions = mappingService.getClaimDefinitionsForClaimSet(claimSet);
        Set<UUID> validClaimDefinitionIds = validClaimDefinitions.stream()
                .map(ClaimDefinitionJpaEntity::getId)
                .collect(Collectors.toSet());
        
        // 3. Validate input claim definition IDs belong to the ClaimSet
        List<UUID> requestedClaimDefinitionIds = query.getClaimIdentifiers().stream()
                .map(ClaimIdentifierRequest::getClaimDefinitionId)
                .collect(Collectors.toList());
        
        // Check for duplicates
        Set<UUID> uniqueRequestedIds = new HashSet<>(requestedClaimDefinitionIds);
        if (uniqueRequestedIds.size() != requestedClaimDefinitionIds.size()) {
            throw new IllegalArgumentException("Duplicate claim definition IDs are not allowed");
        }
        
        // Check if all requested claim definition IDs belong to the claim set
        for (UUID requestedId : requestedClaimDefinitionIds) {
            if (!validClaimDefinitionIds.contains(requestedId)) {
                throw new IllegalArgumentException("Claim definition ID " + requestedId + 
                        " does not belong to the identifier claim set for this business and account type");
            }
        }
        
        // 4. Execute JPQL query with ALL_CLAIMS_MUST_MATCH strategy
        List<String> claimPairs = query.getClaimIdentifiers().stream()
                .map(claim -> claim.getClaimDefinitionId().toString().toUpperCase() + ":" + claim.getValue())
                .collect(Collectors.toList());
        
        List<AccountJpaEntity> matchingAccounts = accountRepository.findAccountsByBusinessAndAccountTypeAndClaims(
                query.getBusinessId(),
                query.getAccountType(),
                claimPairs,
                query.getClaimIdentifiers().size()
        );
        
        // 5. Handle results based on count
        if (matchingAccounts.isEmpty()) {
            logger.debug("No accounts found matching the provided claims");
            throw new AccountNotFoundException("No account found matching the provided claim identifiers");
        } else if (matchingAccounts.size() == 1) {
            AccountJpaEntity account = matchingAccounts.get(0);
            logger.debug("Successfully identified account: {}", account.getId());
            return account;
        } else {
            logger.debug("Multiple accounts ({}) found matching the provided claims", matchingAccounts.size());
            throw new IllegalStateException("Multiple accounts found matching the provided claim identifiers. " +
                    "Expected exactly one account, but found " + matchingAccounts.size());
        }
    }
}
