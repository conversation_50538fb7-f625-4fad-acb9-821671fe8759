package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.CreateClaimValueCommand;
import com.vusecurity.auth.claims.application.exception.ClaimDefinitionNotFoundException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetDefinitionMappingJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetDefinitionMappingRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.identities.application.dto.AccountBusinessInfo;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import com.vusecurity.auth.contracts.enums.AccountType;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CreateClaimValueHandlerValidationTest {

    @Mock
    private ClaimValueRepository valueRepo;
    
    @Mock
    private ClaimDefinitionRepository defRepo;
    
    @Mock
    private ClaimSetDefinitionMappingRepository mappingRepo;
    
    @Mock
    private AccountRepository accountRepo;

    @InjectMocks
    private CreateClaimValueHandler handler;

    @Test
    void should_throw_exception_when_claim_definition_not_found() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "test-value",
                true,
                false,
                "USER_INPUT"
        );

        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ClaimDefinitionNotFoundException.class, () -> handler.handle(cmd));
    }

    @Test
    void should_validate_numeric_data_type_successfully() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "123.45",
                true,
                false,
                "USER_INPUT"
        );

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.NUMERIC, null);
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(new ClaimValueJpaEntity());

        // When & Then
        assertDoesNotThrow(() -> handler.handle(cmd));
        verify(valueRepo).saveAndFlush(any(ClaimValueJpaEntity.class));
    }

    @Test
    void should_reject_invalid_numeric_data_type() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "not-a-number",
                true,
                false,
                "USER_INPUT"
        );

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.NUMERIC, null);
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> handler.handle(cmd)
        );
        assertEquals("Value does not match data type: NUMERIC", exception.getMessage());
    }

    @Test
    void should_validate_boolean_data_type_successfully() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "true",
                true,
                false,
                "USER_INPUT"
        );

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.BOOL, null);
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(new ClaimValueJpaEntity());

        // When & Then
        assertDoesNotThrow(() -> handler.handle(cmd));
        verify(valueRepo).saveAndFlush(any(ClaimValueJpaEntity.class));
    }

    @Test
    void should_reject_invalid_boolean_data_type() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "maybe",
                true,
                false,
                "USER_INPUT"
        );

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.BOOL, null);
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> handler.handle(cmd)
        );
        assertEquals("Value does not match data type: BOOL", exception.getMessage());
    }

    @Test
    void should_validate_date_data_type_successfully() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "2023-12-25",
                true,
                false,
                "USER_INPUT"
        );

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.DATE, null);
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(new ClaimValueJpaEntity());

        // When & Then
        assertDoesNotThrow(() -> handler.handle(cmd));
        verify(valueRepo).saveAndFlush(any(ClaimValueJpaEntity.class));
    }

    @Test
    void should_reject_invalid_date_data_type() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "2023-13-01", // Invalid month
                true,
                false,
                "USER_INPUT"
        );

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.DATE, null);
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> handler.handle(cmd)
        );
        assertEquals("Value does not match data type: DATE", exception.getMessage());
    }

    @Test
    void should_validate_datetime_data_type_successfully() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "2023-12-25T10:30:00Z",
                true,
                false,
                "USER_INPUT"
        );

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.DATETIME, null);
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(new ClaimValueJpaEntity());

        // When & Then
        assertDoesNotThrow(() -> handler.handle(cmd));
        verify(valueRepo).saveAndFlush(any(ClaimValueJpaEntity.class));
    }

    @Test
    void should_reject_invalid_datetime_data_type() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "2023-12-25 10:30:00", // Invalid format (no T, no timezone)
                true,
                false,
                "USER_INPUT"
        );

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.DATETIME, null);
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> handler.handle(cmd)
        );
        assertEquals("Value does not match data type: DATETIME", exception.getMessage());
    }

    @Test
    void should_validate_image_data_type_successfully() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
                true,
                false,
                "USER_INPUT"
        );

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.IMAGE, null);
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(new ClaimValueJpaEntity());

        // When & Then
        assertDoesNotThrow(() -> handler.handle(cmd));
        verify(valueRepo).saveAndFlush(any(ClaimValueJpaEntity.class));
    }

    @Test
    void should_reject_invalid_image_data_type() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "not-an-image",
                true,
                false,
                "USER_INPUT"
        );

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.IMAGE, null);
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> handler.handle(cmd)
        );
        assertEquals("Value does not match data type: IMAGE", exception.getMessage());
    }

    @Test
    void should_validate_array_data_type_successfully() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "[\"item1\", \"item2\"]",
                true,
                false,
                "USER_INPUT"
        );

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.ARRAY, null);
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(new ClaimValueJpaEntity());

        // When & Then
        assertDoesNotThrow(() -> handler.handle(cmd));
        verify(valueRepo).saveAndFlush(any(ClaimValueJpaEntity.class));
    }

    @Test
    void should_reject_invalid_array_data_type() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "single-item-no-separator",
                true,
                false,
                "USER_INPUT"
        );

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.ARRAY, null);
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> handler.handle(cmd)
        );
        assertEquals("Value does not match data type: ARRAY", exception.getMessage());
    }

    @Test
    void should_validate_regex_pattern_successfully() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "<EMAIL>",
                true,
                false,
                "USER_INPUT"
        );

        String emailRegex = "^[\\w-.]+@([\\w-]+\\.)+[\\w-]{2,4}$";
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.STRING, emailRegex);
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(new ClaimValueJpaEntity());

        // When & Then
        assertDoesNotThrow(() -> handler.handle(cmd));
        verify(valueRepo).saveAndFlush(any(ClaimValueJpaEntity.class));
    }

    @Test
    void should_reject_invalid_regex_pattern() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "not-an-email",
                true,
                false,
                "USER_INPUT"
        );

        String emailRegex = "^[\\w-.]+@([\\w-]+\\.)+[\\w-]{2,4}$";
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.STRING, emailRegex);
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> handler.handle(cmd)
        );
        assertEquals("value does not match the required pattern", exception.getMessage());
    }

    @Test
    void should_normalize_boolean_values() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "TRUE", // Should be normalized to "true"
                true,
                false,
                "USER_INPUT"
        );

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.BOOL, null);
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        
        ClaimValueJpaEntity savedEntity = new ClaimValueJpaEntity();
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenAnswer(invocation -> {
            ClaimValueJpaEntity entity = invocation.getArgument(0);
            savedEntity.setValue(entity.getValue());
            return savedEntity;
        });

        // When
        handler.handle(cmd);

        // Then
        verify(valueRepo).saveAndFlush(argThat(entity -> "true".equals(entity.getValue())));
    }

    @Test
    void should_normalize_numeric_values() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimDefinitionId,
                UUID.randomUUID(),
                null,
                "123.00", // Should be normalized to "123"
                true,
                false,
                "USER_INPUT"
        );

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.NUMERIC, null);
        when(defRepo.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        
        ClaimValueJpaEntity savedEntity = new ClaimValueJpaEntity();
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenAnswer(invocation -> {
            ClaimValueJpaEntity entity = invocation.getArgument(0);
            savedEntity.setValue(entity.getValue());
            return savedEntity;
        });

        // When
        handler.handle(cmd);

        // Then
        verify(valueRepo).saveAndFlush(argThat(entity -> "123".equals(entity.getValue())));
    }

    private ClaimDefinitionJpaEntity createClaimDefinition(UUID id, DataTypeEnum dataType, String dataFormat) {
        ClaimDefinitionJpaEntity definition = new ClaimDefinitionJpaEntity();
        definition.setId(id);
        definition.setCode("test-code");
        definition.setName("Test Claim");
        definition.setDataType(dataType);
        definition.setDataFormat(dataFormat);
        return definition;
    }
}