package com.vusecurity.auth.shared.test;

import com.vusecurity.multitenant.test.TestTenantContextManager;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;

import java.util.List;

/**
 * Base class for integration tests that provides:
 * - Shared TestContainers SQL Server setup
 * - Tenant context management
 * - Common test configuration
 * - Single datasource mode for simplified testing
 * - Automatic database cleanup between tests
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@ExtendWith(TestTenantContextManager.class)
public abstract class BaseIntegrationTest {

    @Autowired
    private TestDatabaseCleaner databaseCleaner;

    @BeforeAll
    static void setupContainerLogging() {
        // Enable container logging for debugging
        SharedTestContainers.enableContainerLogging();
    }

    @BeforeEach
    void cleanupDatabase() {
        // Clean database before each test for isolation
        databaseCleaner.smartCleanDatabase();
    }

    @DynamicPropertySource
    static void configureTestProperties(DynamicPropertyRegistry registry) {
        // Use shared container configuration
        SharedTestContainers.configureProperties(registry);
    }

    /**
     * Helper method to execute code within a specific tenant context
     */
    protected void withTenantContext(String tenantId, Runnable action) {
        TestTenantContextManager.withTenantContext(tenantId, action);
    }

    /**
     * Helper method to get current tenant
     */
    protected String getCurrentTenant() {
        return TestTenantContextManager.getCurrentTenant();
    }

    /**
     * Helper method to set tenant context
     */
    protected void setTenantContext(String tenantId) {
        TestTenantContextManager.setTenantContext(tenantId);
    }

    @BeforeEach
    void setupSecurityContext() {
        SecurityContextHolder.clearContext();
        SecurityContextHolder.getContext().setAuthentication(
                new UsernamePasswordAuthenticationToken(
                        "SYSTEM",
                        null,
                        List.of(
                                new SimpleGrantedAuthority("sys_factor_policies:all"),
                                new SimpleGrantedAuthority("sys_factor_sms:update"),
                                new SimpleGrantedAuthority("sys_identity:all"),
                                new SimpleGrantedAuthority("sys_factor_policies:delete"),
                                new SimpleGrantedAuthority("sys_role:delete"),
                                new SimpleGrantedAuthority("sys_consent:delete"),
                                new SimpleGrantedAuthority("sys_identity:delete"),
                                new SimpleGrantedAuthority("sys_factor_sms:read"),
                                new SimpleGrantedAuthority("sys_channel:delete"),
                                new SimpleGrantedAuthority("sys_identity:create"),
                                new SimpleGrantedAuthority("sys_group:create"),
                                new SimpleGrantedAuthority("sys_consent:create"),
                                new SimpleGrantedAuthority("sys_claim:update"),
                                new SimpleGrantedAuthority("sys_account:update"),
                                new SimpleGrantedAuthority("sys_account:read"),
                                new SimpleGrantedAuthority("sys_group:read"),
                                new SimpleGrantedAuthority("sys_factor_totp:delete"),
                                new SimpleGrantedAuthority("sys_claim:all"),
                                new SimpleGrantedAuthority("sys_factor_sms:delete"),
                                new SimpleGrantedAuthority("sys_permission:update"),
                                new SimpleGrantedAuthority("sys_group:update"),
                                new SimpleGrantedAuthority("sys_factor_email:delete"),
                                new SimpleGrantedAuthority("sys_factor_policies:update"),
                                new SimpleGrantedAuthority("sys_business:all"),
                                new SimpleGrantedAuthority("sys_factor_sms:create"),
                                new SimpleGrantedAuthority("sys_group:all"),
                                new SimpleGrantedAuthority("sys_factor_totp:all"),
                                new SimpleGrantedAuthority("sys_group:delete"),
                                new SimpleGrantedAuthority("sys_factor_email:update"),
                                new SimpleGrantedAuthority("sys_business:delete"),
                                new SimpleGrantedAuthority("sys_identity:read"),
                                new SimpleGrantedAuthority("sys_factor_sms:all"),
                                new SimpleGrantedAuthority("sys_factor_totp:read"),
                                new SimpleGrantedAuthority("sys_factor_policies:read"),
                                new SimpleGrantedAuthority("sys_permission:read"),
                                new SimpleGrantedAuthority("sys_business:update"),
                                new SimpleGrantedAuthority("sys_factor_email:create"),
                                new SimpleGrantedAuthority("sys_channel:all"),
                                new SimpleGrantedAuthority("sys_factor_password:update"),
                                new SimpleGrantedAuthority("sys_consent:update"),
                                new SimpleGrantedAuthority("sys_factor_totp:create"),
                                new SimpleGrantedAuthority("sys_factor_password:create"),
                                new SimpleGrantedAuthority("sys_claim:create"),
                                new SimpleGrantedAuthority("sys_factor_password:read"),
                                new SimpleGrantedAuthority("sys_role:create"),
                                new SimpleGrantedAuthority("sys_channel:update"),
                                new SimpleGrantedAuthority("sys_factor_email:all"),
                                new SimpleGrantedAuthority("sys_permission:delete"),
                                new SimpleGrantedAuthority("sys_channel:read"),
                                new SimpleGrantedAuthority("sys_role:update"),
                                new SimpleGrantedAuthority("sys_permission:create"),
                                new SimpleGrantedAuthority("sys_identity:update"),
                                new SimpleGrantedAuthority("sys_consent:all"),
                                new SimpleGrantedAuthority("sys_account:all"),
                                new SimpleGrantedAuthority("sys_business:read"),
                                new SimpleGrantedAuthority("sys_factor_email:read"),
                                new SimpleGrantedAuthority("sys_consent:read"),
                                new SimpleGrantedAuthority("sys_role:all"),
                                new SimpleGrantedAuthority("sys_permission:all"),
                                new SimpleGrantedAuthority("sys_factor_policies:create"),
                                new SimpleGrantedAuthority("sys_role:read"),
                                new SimpleGrantedAuthority("sys_account:delete"),
                                new SimpleGrantedAuthority("sys_channel:create"),
                                new SimpleGrantedAuthority("sys_factor_password:all"),
                                new SimpleGrantedAuthority("sys_factor_totp:update"),
                                new SimpleGrantedAuthority("sys_account:create"),
                                new SimpleGrantedAuthority("sys_claim:read"),
                                new SimpleGrantedAuthority("sys_business:create"),
                                new SimpleGrantedAuthority("sys_factor_password:delete"),
                                new SimpleGrantedAuthority("sys_claim:delete")
                                )
                )
        );
    }

    @AfterEach
    void clearSecurityContext() {
        SecurityContextHolder.clearContext();
    }
}
