package com.vusecurity.auth.authorization.application.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;

import com.vusecurity.auth.authorization.application.exception.RegisteredClientNotFoundException;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.Oauth2RegisteredClient;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.Oauth2RegisteredClientRepository;
import com.vusecurity.auth.authorization.mapper.OauthRegisteredClientMapper;

public class OAuth2RegisteredClientService implements RegisteredClientRepository {

    private static final Logger logger = LoggerFactory.getLogger(OAuth2RegisteredClientService.class);

    private final Oauth2RegisteredClientRepository repository;

    public OAuth2RegisteredClientService(Oauth2RegisteredClientRepository repository) {
        this.repository = repository;

    }

    @Override
    public RegisteredClient findById(String id) {
        logger.debug("Fetching RegisteredClient by id: {}", id);

        try {
            Oauth2RegisteredClient registeredClient = this.repository.findById(id)
                    .orElseThrow(
                            () -> new RegisteredClientNotFoundException("RegisteredClient not found with id: " + id));
            return OauthRegisteredClientMapper.toRegisteredClient(registeredClient);
        } catch (RegisteredClientNotFoundException ex) {
            logger.warn("RegisteredClient not found by id: {}", id);
            throw ex;
        }
    }

    @Override
    public RegisteredClient findByClientId(String clientId) {
        logger.debug("Fetching RegisteredClient by clientId: {}", clientId);

        try {
            Oauth2RegisteredClient registeredClient = this.repository.findByClientId(clientId)
                    .orElseThrow(() -> new RegisteredClientNotFoundException(
                            "RegisteredClient not found with clientId: " + clientId));
            return OauthRegisteredClientMapper.toRegisteredClient(registeredClient);
        } catch (RegisteredClientNotFoundException ex) {
            logger.warn("RegisteredClient not found by clientId: {}", clientId);
            throw ex;
        }
    }

    @Override
    public void save(RegisteredClient registeredClient) {
        logger.debug("Saving RegisteredClient with id: {}", registeredClient.getId());

        Oauth2RegisteredClient entity = OauthRegisteredClientMapper.toEntity(registeredClient);
        
        // Set audit fields explicitly
        java.time.Instant now = java.time.Instant.now();
        entity.setCreatedAt(now);
        entity.setUpdatedAt(now);
        entity.setCreatedBy(SecurityContextHolder.getContext().getAuthentication() != null
                ? SecurityContextHolder.getContext().getAuthentication().getName()
                : "SYSTEM");
        
        // Set the required status field
        entity.setStatus(com.vusecurity.core.commons.Auditable.Status.ACTIVE);
        
        repository.save(entity);
        logger.debug("RegisteredClient saved successfully: {}", registeredClient.getId());
    }
}