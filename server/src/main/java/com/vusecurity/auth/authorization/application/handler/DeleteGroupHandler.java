package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.command.DeleteGroupCommand;
import com.vusecurity.auth.authorization.application.exception.GroupNotFoundException;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.GroupMembershipRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountGroupRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class DeleteGroupHandler {

    private static final Logger logger = LoggerFactory.getLogger(DeleteGroupHandler.class);

    private final AccountGroupRepository accountGroupRepository;
    private final GroupMembershipRepository groupMembershipRepository;

    @Transactional
    public void deleteGroup(DeleteGroupCommand command) {
        logger.debug("Deleting group: {}", command.groupId());

        command.validate();

        if (!accountGroupRepository.existsById(command.groupId())) {
            throw new GroupNotFoundException("Group not found with ID: " + command.groupId());
        }

        // Delete all memberships for this group first
        groupMembershipRepository.deleteByGroupId(command.groupId());
        logger.debug("Deleted memberships for group: {}", command.groupId());

        accountGroupRepository.deleteById(command.groupId());
        logger.debug("Group {} deleted permanently", command.groupId());
    }
} 