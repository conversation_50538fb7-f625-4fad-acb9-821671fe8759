package com.vusecurity.auth.claims.application.command;

import java.util.List;
import java.util.UUID;

/**
 * Command for partially updating claim sets associations in a claim definition.
 * This allows partial updates without affecting existing associations that are not mentioned.
 */
public record PatchClaimSetsOnClaimDefinitionCommand(
    UUID claimDefinitionId,
    List<ClaimSetAssignmentCommand> claimSetsAssignment) {

    public void validate() {
        if (claimDefinitionId == null) {
            throw new IllegalArgumentException("claimDefinitionId is required");
        }
        if (claimSetsAssignment == null) {
            throw new IllegalArgumentException("claimSets is required");
        }

        for (ClaimSetAssignmentCommand claimSet : claimSetsAssignment) {
            if (claimSet == null) {
                throw new IllegalArgumentException("claimSets cannot contain null values");
            }
        }
    }
} 