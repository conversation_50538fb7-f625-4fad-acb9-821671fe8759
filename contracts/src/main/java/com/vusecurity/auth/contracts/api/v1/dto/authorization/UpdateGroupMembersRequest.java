package com.vusecurity.auth.contracts.api.v1.dto.authorization;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "Request to update the members of a group")
public class UpdateGroupMembersRequest {

    @Schema(description = "List of account IDs that should be in the group",
            example = "[\"456e7890-e89b-12d3-a456-************\", \"789fcdeb-51a2-43d1-9c4f-123456789def\"]")
    private List<UUID> accountIds;

    @Schema(description = "List of account IDs that should be owners of the group (optional, must be subset of accountIds)",
            example = "[\"456e7890-e89b-12d3-a456-************\"]")
    private List<UUID> ownerAccountIds;
} 