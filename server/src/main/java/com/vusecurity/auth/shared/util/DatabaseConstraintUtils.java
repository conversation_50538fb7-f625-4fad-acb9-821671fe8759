package com.vusecurity.auth.shared.util;

import org.hibernate.exception.ConstraintViolationException;
import org.springframework.dao.DataIntegrityViolationException;

import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * Utility class for handling database constraint violations and translating them into business exceptions.
 * 
 * This utility provides methods to:
 * - Find specific throwable types in exception chains
 * - Detect specific constraint violations
 * - Translate constraint violations into business exceptions
 */
public final class DatabaseConstraintUtils {

    private DatabaseConstraintUtils() {
        // Utility class - prevent instantiation
    }

    /**
     * Finds a specific throwable type in the exception chain.
     * Replaces the deprecated DataAccessUtils.findThrowableOfType() method.
     *
     * @param ex   the root exception to search from
     * @param type the type of throwable to find
     * @param <T>  the throwable type
     * @return the found throwable or null if not found
     */
    @SuppressWarnings("unchecked")
    public static <T extends Throwable> T findThrowableOfType(Throwable ex, Class<T> type) {
        if (ex == null) {
            return null;
        }
        if (type.isInstance(ex)) {
            return (T) ex;
        }
        return findThrowableOfType(ex.getCause(), type);
    }

    /**
     * Finds a Hibernate ConstraintViolationException in the exception chain.
     *
     * @param ex the root exception to search from
     * @return Optional containing the ConstraintViolationException if found
     */
    public static Optional<ConstraintViolationException> findConstraintViolationException(Throwable ex) {
        return Optional.ofNullable(findThrowableOfType(ex, ConstraintViolationException.class));
    }

    /**
     * Checks if a DataIntegrityViolationException contains a specific constraint violation.
     *
     * @param ex             the DataIntegrityViolationException to check
     * @param constraintName the name of the constraint to look for
     * @return true if the exception contains the specified constraint violation
     */
    public static boolean isConstraintViolation(DataIntegrityViolationException ex, String constraintName) {
        return findConstraintViolationException(ex)
                .map(ConstraintViolationException::getConstraintName)
                .filter(name -> constraintName.equalsIgnoreCase(name))
                .isPresent();
    }

    /**
     * Handles a DataIntegrityViolationException by checking for a specific constraint violation
     * and throwing a business exception if found.
     *
     * @param ex                    the DataIntegrityViolationException to handle
     * @param constraintName        the name of the constraint to check for
     * @param businessExceptionSupplier supplier that creates the business exception to throw
     * @param <E>                   the type of business exception
     * @throws E if the constraint violation matches
     * @throws DataIntegrityViolationException if the constraint doesn't match (re-throws original)
     */
    public static <E extends RuntimeException> void handleConstraintViolation(
            DataIntegrityViolationException ex,
            String constraintName,
            Supplier<E> businessExceptionSupplier) throws E {
        
        if (isConstraintViolation(ex, constraintName)) {
            throw businessExceptionSupplier.get();
        }
        throw ex;
    }

    /**
     * Handles a DataIntegrityViolationException by checking for a specific constraint violation
     * and throwing a business exception created from the constraint details if found.
     *
     * @param ex                           the DataIntegrityViolationException to handle
     * @param constraintName               the name of the constraint to check for
     * @param businessExceptionFactory     function that creates the business exception from constraint details
     * @param <E>                          the type of business exception
     * @throws E if the constraint violation matches
     * @throws DataIntegrityViolationException if the constraint doesn't match (re-throws original)
     */
    public static <E extends RuntimeException> void handleConstraintViolation(
            DataIntegrityViolationException ex,
            String constraintName,
            Function<ConstraintViolationException, E> businessExceptionFactory) throws E {
        
        findConstraintViolationException(ex)
                .filter(cve -> constraintName.equalsIgnoreCase(cve.getConstraintName()))
                .ifPresentOrElse(
                        cve -> { throw businessExceptionFactory.apply(cve); },
                        () -> { throw ex; }
                );
    }

    /**
     * Executes a database operation and handles constraint violations by translating them
     * into business exceptions.
     *
     * @param operation                database operation to execute
     * @param constraintName           the name of the constraint to check for
     * @param businessExceptionSupplier supplier that creates the business exception to throw
     * @param <T>                      the return type of the operation
     * @param <E>                      the type of business exception
     * @return the result of the operation
     * @throws E if the specified constraint violation occurs
     * @throws DataIntegrityViolationException if a different constraint violation occurs
     */
    public static <T, E extends RuntimeException> T executeWithConstraintHandling(
            Supplier<T> operation,
            String constraintName,
            Supplier<E> businessExceptionSupplier) throws E {
        
        try {
            return operation.get();
        } catch (DataIntegrityViolationException ex) {
            handleConstraintViolation(ex, constraintName, businessExceptionSupplier);
            return null; // Never reached due to exception throwing
        }
    }

    /**
     * Executes a database operation and handles constraint violations by translating them
     * into business exceptions created from constraint details.
     *
     * @param operation                    database operation to execute
     * @param constraintName               the name of the constraint to check for
     * @param businessExceptionFactory     function that creates the business exception from constraint details
     * @param <T>                          the return type of the operation
     * @param <E>                          the type of business exception
     * @return the result of the operation
     * @throws E if the specified constraint violation occurs
     * @throws DataIntegrityViolationException if a different constraint violation occurs
     */
    public static <T, E extends RuntimeException> T executeWithConstraintHandling(
            Supplier<T> operation,
            String constraintName,
            Function<ConstraintViolationException, E> businessExceptionFactory) throws E {
        
        try {
            return operation.get();
        } catch (DataIntegrityViolationException ex) {
            handleConstraintViolation(ex, constraintName, businessExceptionFactory);
            return null; // Never reached due to exception throwing
        }
    }
}
