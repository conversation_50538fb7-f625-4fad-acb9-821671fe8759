package com.vusecurity.auth.authorization.api.controller;

import static com.vusecurity.auth.authorization.domain.PermissionNames.PERMISSION_READ;

import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.vusecurity.auth.authorization.application.query.GetPermissionQuery;
import com.vusecurity.auth.authorization.application.query.GetPermissionsPagedQuery;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.PermissionJpaEntity;
import com.vusecurity.auth.authorization.mapper.PermissionDtoMapper;
import com.vusecurity.auth.contracts.api.v1.dto.authorization.PermissionResponse;
import com.vusecurity.auth.shared.config.aspect.Oauth2Authorize;
import com.vusecurity.auth.shared.util.UrlParameterUtils;
import com.vusecurity.core.commons.models.PageableResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@SecurityRequirement(name = "oauth2 client credentials")
@Tag(name = "Permissions", description = "Permission management operations")
public class PermissionsController {

        private final GetPermissionQuery getPermissionQuery;
        private final PermissionDtoMapper permissionDtoMapper;

        public PermissionsController(GetPermissionQuery getPermissionQuery,
                        PermissionDtoMapper permissionDtoMapper) {
                this.getPermissionQuery = getPermissionQuery;
                this.permissionDtoMapper = permissionDtoMapper;
        }

        @Operation(summary = "Get all permissions", description = "Retrieves a paginated list of permissions with optional filtering. Supports filtering by permission name and description.")
        @ApiResponses({
                        @ApiResponse(responseCode = "200", description = "Successful retrieval", content = @Content(schema = @Schema(implementation = PageableResponse.class))),
                        @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
                        @ApiResponse(responseCode = "401", description = "Authentication required"),
                        @ApiResponse(responseCode = "403", description = "Access denied")
        })
        @GetMapping("${app.authorization.context-path}/permissions")
        @Oauth2Authorize(permission = PERMISSION_READ)
        public PageableResponse<PermissionResponse> get(@RequestParam(name = "page", defaultValue = "1") int page,
                        @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                        @RequestParam(name = "filter", required = false) String filter) {

                // Decode URL-encoded parameter to handle special characters properly
                String decodedFilter = UrlParameterUtils.decodeAndSanitizeParameter(filter);

                GetPermissionsPagedQuery query = GetPermissionsPagedQuery.builder()
                                .page(page)
                                .pageSize(pageSize)
                                .filter(decodedFilter)
                                .build();

                Page<PermissionJpaEntity> permissions = getPermissionQuery.getAllPermissions(query);
                return new PageableResponse<>(permissions.getNumber() + 1, permissions.getSize(),
                                permissions.getTotalElements(),
                                permissions.get().map(permissionDtoMapper::toResponse).toList());
        }

        @Operation(summary = "Get permission by name", description = "Retrieves a specific permission by its unique name identifier.")
        @ApiResponses({
                        @ApiResponse(responseCode = "200", description = "Permission found", content = @Content(schema = @Schema(implementation = PermissionResponse.class))),
                        @ApiResponse(responseCode = "404", description = "Permission not found"),
                        @ApiResponse(responseCode = "401", description = "Authentication required"),
                        @ApiResponse(responseCode = "403", description = "Access denied")
        })
        @GetMapping("${app.authorization.context-path}/permissions/{name}")
        @Oauth2Authorize(permission = PERMISSION_READ)
        public PermissionResponse get(@PathVariable(value = "name") String name) {

                // Decode URL-encoded path variable to handle special characters properly
                String decodedName = UrlParameterUtils.decodeAndSanitizeParameter(name);

                PermissionJpaEntity permission = getPermissionQuery.getPermissionByName(decodedName);
                return permissionDtoMapper.toResponse(permission);
        }
}
