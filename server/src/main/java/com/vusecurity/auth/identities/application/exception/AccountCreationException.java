package com.vusecurity.auth.identities.application.exception;

import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

/**
 * Exception thrown when an error occurs during account creation.
 */
@Getter
@Setter
public class AccountCreationException extends AccountException {

    private final UUID businessId;
    private final UUID identityId;

    public AccountCreationException(UUID businessId, UUID identityId, String message, Throwable cause) {
        super(String.format("%s (businessId=%s, identityId=%s)", message, businessId, identityId), cause);
        this.businessId = businessId;
        this.identityId = identityId;
    }
}