package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.exception.ClaimValueNotFoundException;
import com.vusecurity.auth.claims.application.query.GetClaimValueQuery;
import com.vusecurity.auth.claims.application.query.GetClaimValuesPagedQuery;
import com.vusecurity.auth.claims.application.query.GetClaimValuesByValueQuery;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.enums.OwnerType;

import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class GetClaimValueHandler implements GetClaimValueQuery {

    private static final Logger logger = LoggerFactory.getLogger(GetClaimValueHandler.class);
    
    private final ClaimValueRepository claimValueRepository;

    @Override
    public ClaimValueJpaEntity getClaimValueById(UUID claimValueId) {
        logger.debug("Getting claim value by ID: {}", claimValueId);
        
        return claimValueRepository.findById(claimValueId)
                .orElseThrow(() -> new ClaimValueNotFoundException(claimValueId));
    }



    @Override
    public Page<ClaimValueJpaEntity> getAllClaimValues(GetClaimValuesPagedQuery query) {
        logger.debug("Getting all claim values with query: {}", query);
        
        // Convert 1-based page to 0-based for Spring Data
        int pageNumber = Math.max(0, query.getPage() - 1);
        Pageable pageable = PageRequest.of(pageNumber, query.getPageSize());
        
        // For now, we'll return all claim values. In the future, we can add filtering based on query.getFilter()
        Page<ClaimValueJpaEntity> claimValues = claimValueRepository.findAll(pageable);
        
        logger.debug("Found {} claim values", claimValues.getTotalElements());
        return claimValues;
    }

    @Override
    public Page<ClaimValueJpaEntity> getClaimValuesByValue(GetClaimValuesByValueQuery query) {
        logger.debug("Getting claim values by value: {}", query.getValue());
        
        // Convert 1-based page to 0-based for Spring Data
        int pageNumber = Math.max(0, query.getPage() - 1);
        Pageable pageable = PageRequest.of(pageNumber, query.getPageSize());
        
        Page<ClaimValueJpaEntity> claimValues;
        if (query.getValue() != null && !query.getValue().trim().isEmpty()) {
            // Search by exact value or containing pattern
            claimValues = claimValueRepository.findByValueContaining(query.getValue(), pageable);
        } else {
            // Return all if no value specified
            claimValues = claimValueRepository.findAll(pageable);
        }
        
        logger.debug("Found {} claim values for value search", claimValues.getTotalElements());
        return claimValues;
    }

    @Override
    public List<ClaimValueJpaEntity> getClaimValuesByOwnerTypeAndOwnerId(OwnerType ownerType, UUID ownerId){
        logger.debug("Getting claim value by Owner Type and Id: {} - {}", ownerType, ownerId);

        return claimValueRepository.findAllByOwnerTypeAndOwnerId(ownerType, ownerId);
    }

    @Override
    public List<ClaimValueJpaEntity> getRelatedClaimValuesByValue(OwnerType ownerType, UUID ownerId, String searchValue) {
        logger.debug("Getting related claim values by value: {} for owner: {} - {}", searchValue, ownerType, ownerId);

        List<ClaimValueJpaEntity> relatedValues = claimValueRepository.findRelatedClaimValuesByValue(ownerType, ownerId, searchValue);

        logger.debug("Found {} related claim values for search value: {}", relatedValues.size(), searchValue);
        return relatedValues;
    }
}
