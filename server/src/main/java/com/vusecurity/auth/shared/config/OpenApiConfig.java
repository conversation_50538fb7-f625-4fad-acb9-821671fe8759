package com.vusecurity.auth.shared.config;

import io.swagger.v3.oas.models.security.Scopes;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.HandlerMethod;

import com.vusecurity.multitenant.MultitenantConfiguration;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.parameters.Parameter;
import io.swagger.v3.oas.models.security.SecurityScheme;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class OpenApiConfig {

    private final MultitenantConfiguration multitenantConfiguration;
    
    /**
     * Context path del servidor, inyectado desde la configuración.
     * Se utiliza para construir URLs dinámicas en la documentación de OpenAPI.
     * Valor por defecto: cadena vacía si no se define CONTEXT_PATH
     */

    @Value("${server.servlet.context-path}")
    private String contextPath;

    @Value("${swagger.oauth.token-url-base}")
    private String baseTokenUrl;


    public OpenApiConfig(MultitenantConfiguration multitenantConfiguration) {
        this.multitenantConfiguration = multitenantConfiguration;
    }

    @Bean
    public OpenAPI customOpenAPI() {
        // Construir la URL del token dinámicamente con el context-path
        String tokenUrl =  baseTokenUrl +((contextPath != null && !contextPath.isEmpty())
            ? contextPath + "/oauth2/token"
            : "/oauth2/token");

        Scopes scopes = new Scopes();
        scopes.put("role_admin", "Role to Client Administrator");
            
        return new OpenAPI()
                .components(new Components()
                        .addSecuritySchemes("oauth2 client credentials",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.OAUTH2)
                                        .description("OAuth2 Client Credentials Flow")
                                        .flows(new io.swagger.v3.oas.models.security.OAuthFlows()
                                                .clientCredentials(new io.swagger.v3.oas.models.security.OAuthFlow()
                                                        .tokenUrl(tokenUrl)
                                                        .scopes(scopes)
                                                )
                                        )
                        )
                )
                .addTagsItem(new io.swagger.v3.oas.models.tags.Tag()
                        .name("Status")
                        .description("Endpoints for checking application status and health"))
                .addTagsItem(new io.swagger.v3.oas.models.tags.Tag()
                        .name("Version")
                        .description("Endpoints for retrieving application version information"));
    }

    @Bean
    public OperationCustomizer operationCustomizer() {
        return (Operation operation, HandlerMethod handlerMethod) -> {


            Parameter originalHostParameter = new Parameter()
                    .in("header")
                    .name(multitenantConfiguration.getIdHeader())
                    .description("Original Host Header for multi-tenant identification")
                    .required(false)
                    .schema(new io.swagger.v3.oas.models.media.StringSchema());

            operation.addParametersItem(originalHostParameter);
            return operation;
        };
    }
}
