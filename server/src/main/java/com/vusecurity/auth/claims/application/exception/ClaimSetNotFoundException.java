package com.vusecurity.auth.claims.application.exception;

import java.util.UUID;

public class ClaimSetNotFoundException extends RuntimeException {
    public ClaimSetNotFoundException(UUID claimSetId) {
        super("Claim set not found with ID: " + claimSetId);
    }

    public ClaimSetNotFoundException(String message) {
        super(message);
    }

    public ClaimSetNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}