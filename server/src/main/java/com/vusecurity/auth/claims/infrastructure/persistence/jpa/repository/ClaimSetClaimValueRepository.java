package com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetClaimValueJpaEntity;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Spring Data JPA repository for ClaimSetClaimValueJpaEntity.
 * Infrastructure-only interface for database operations.
 */
@Repository
public interface ClaimSetClaimValueRepository extends JpaRepository<ClaimSetClaimValueJpaEntity, UUID>, JpaSpecificationExecutor<ClaimSetClaimValueJpaEntity> {

    List<ClaimSetClaimValueJpaEntity> findAllByClaimSet_Id(UUID claimSetId);

    Optional<ClaimSetClaimValueJpaEntity> findByClaimValue_Id(UUID claimValueId);

}
