package com.vusecurity.auth.claims.application.command;

import java.util.UUID;

public record CreateClaimValueCommand(
        UUID claimSetId,
        UUID claimDefinitionId,
        UUID identityId,
        UUID accountId,
        Object value,
        boolean isPrimary,
        boolean isComputed,
        String source,
        Integer primaryIndex) {

        public CreateClaimValueCommand(
            UUID claimDefinitionId,
            UUID identityId,
            UUID accountId,
            Object value,
            boolean isPrimary,
            boolean isComputed,
            String source){
                this(null, claimDefinitionId, identityId, accountId, value, isPrimary, isComputed, source, null);
        }

    public void validate() {
        if (claimDefinitionId == null) {
            throw new IllegalArgumentException("claimDefinitionId is required");
        }
        if (identityId == null && accountId == null) {
            throw new IllegalArgumentException("Either identityId or accountId is required");
        }
        if (identityId != null && accountId != null) {
            throw new IllegalArgumentException("Cannot specify both identityId and accountId");
        }
        if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
            throw new IllegalArgumentException("value is required");
        }
        if (claimSetId == null && accountId != null) {
            throw new IllegalArgumentException("claimSetId is required for accounts");
        }
    }

    public void validateWithPattern(String pattern) {
        if (pattern != null && !pattern.isBlank()) {
            String stringValue = convertValueToString();
            if (!stringValue.matches(pattern)) {
                throw new IllegalArgumentException("value does not match the required pattern");
            }
        }
    }

    /**
     * Converts the Object value to String for validation and persistence purposes
     */
    public String convertValueToString() {
        if (value == null) {
            return null;
        }
        if (value instanceof String) {
            return (String) value;
        }
        // For complex objects, convert to JSON string
        try {
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            return mapper.writeValueAsString(value);
        } catch (Exception e) {
            // Fallback to toString if JSON serialization fails
            return value.toString();
        }
    }
}
