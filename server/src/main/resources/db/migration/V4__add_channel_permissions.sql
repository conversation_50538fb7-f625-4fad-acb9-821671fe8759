/*
 * Flyway Migration: V3__add_channel_permissions.sql
 * Purpose: Introduce channel permissions and assign them to existing roles.
 * Idempotent: Uses IF NOT EXISTS checks for SQL Server.
 */

-- Channel permissions
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '8f52f06e-e52c-47e0-81fd-d8e568ed34cb') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('8f52f06e-e52c-47e0-81fd-d8e568ed34cb','sys_channel:create','Allow to create channels','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '359ee229-e09c-46b1-8c55-90ac0ae1a9e8') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('359ee229-e09c-46b1-8c55-90ac0ae1a9e8','sys_channel:read','Allow to read channels','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '3b1f7511-bd15-47b8-9053-7d9bf4e99155') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('3b1f7511-bd15-47b8-9053-7d9bf4e99155','sys_channel:update','Allow to update channels','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = 'b55af4fd-f1c0-4a08-9d02-0c70859f0e68') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('b55af4fd-f1c0-4a08-9d02-0c70859f0e68','sys_channel:delete','Allow to delete channels','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '50835799-dad9-4bf2-8b41-5cd922c0d04d') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('50835799-dad9-4bf2-8b41-5cd922c0d04d','sys_channel:all','Allow all channel operations','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());

-- Assign to ADMIN (all)
INSERT INTO role_permission (role_id, permission_name)
SELECT '9fa7a7f6-b8d4-4db4-a8c7-1642a80e863a', p.id FROM permission p
WHERE p.name LIKE 'sys_channel:%'
AND NOT EXISTS (
    SELECT 1 FROM role_permission rp WHERE rp.role_id = '9fa7a7f6-b8d4-4db4-a8c7-1642a80e863a' AND rp.permission_name = p.id
);

-- Assign only read to OPERATOR
IF NOT EXISTS (SELECT 1 FROM role_permission WHERE role_id = '779f520a-bb5f-45ef-95b8-3f2ea204023d' AND permission_name = '359ee229-e09c-46b1-8c55-90ac0ae1a9e8')
    INSERT INTO role_permission (role_id, permission_name) VALUES ('779f520a-bb5f-45ef-95b8-3f2ea204023d','359ee229-e09c-46b1-8c55-90ac0ae1a9e8');
