package com.vusecurity.auth.authorization.mapper;

import com.vusecurity.auth.authorization.application.handler.GetBusinessHandler;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.PermissionJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.contracts.api.v1.dto.authorization.PermissionBasicInfoResponse;
import com.vusecurity.auth.contracts.api.v1.dto.authorization.RoleResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RoleDtoMapper {

    private final GetBusinessHandler getBusinessHandler;

    public RoleResponse toResponse(RoleJpaEntity entity) {
        return toResponse(entity, false);
    }

    public RoleResponse toResponseWithBusiness(RoleJpaEntity entity) {
        return toResponse(entity, true);
    }

    public RoleResponse toResponse(RoleJpaEntity entity, boolean includeBusiness) {
        RoleResponse response = new RoleResponse();
        response.setId(entity.getId());
        response.setName(entity.getName());
        response.setDescription(entity.getDescription());
        response.setPermissions(entity.getPermissions().stream()
                .map(permission -> {
                    PermissionBasicInfoResponse dto = new PermissionBasicInfoResponse();
                    dto.setId(permission.getId());
                    dto.setName(permission.getName());
                    return dto;
                })
                .toList());

        // Set audit information
        response.setAudit(new BaseResponse.AuditInfo()
                .setCreatedAt(entity.getCreatedAt())
                .setUpdatedAt(entity.getUpdatedAt())
                .setCreatedBy(entity.getCreatedBy())
                .setUpdatedBy(entity.getUpdatedBy()));

        if (includeBusiness && entity.getBusinessId() != null) {
           response.setBusiness(getBusinessHandler.retrieveBusinessBasicInfo(entity.getBusinessId()));
        }

        return response;
    }
}
