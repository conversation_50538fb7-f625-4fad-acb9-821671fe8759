package com.vusecurity.auth.authorization.dto;

import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for OAuth2 TokenSettings to be stored as structured data instead of JSON string.
 * Based on Spring OAuth2 TokenSettings properties.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Embeddable
public class TokenSettingsDto {

    /**
     * The time-to-live for an access token.
     * Default: 5 minutes (300 seconds)
     */
    private Integer accessTokenTimeToLive;

    /**
     * The token format for access tokens.
     * Values: "self-contained" (JWT) or "reference" (opaque)
     * Default: "self-contained"
     */
    private String accessTokenFormat;

    /**
     * Whether to reuse refresh tokens.
     * Default: true
     */
    private Boolean reuseRefreshTokens;

    /**
     * The time-to-live for a refresh token.
     * Default: 60 minutes (3600 seconds)
     */
    private Integer refreshTokenTimeToLive;

    /**
     * The signing algorithm used for signing JWT access tokens.
     * Common values: RS256, ES256, PS256
     * Default: RS256
     */
    private String idTokenSignatureAlgorithm;

    /**
     * Whether to bind access tokens to the client's X.509 certificate.
     * Used for mutual TLS proof-of-possession.
     * Default: false
     */
    private Boolean x509CertificateBoundAccessTokens;

    /**
     * The time-to-live for an authorization code.
     * Default: 5 minutes (300 seconds)
     */
    private Integer authorizationCodeTimeToLive;

    /**
     * The time-to-live for a device code.
     * Default: 5 minutes (300 seconds)
     */
    private Integer deviceCodeTimeToLive;

    /**
     * Creates a TokenSettingsDto with default values.
     */
    public static TokenSettingsDto withDefaults() {
        TokenSettingsDto dto = new TokenSettingsDto();
        dto.setAccessTokenTimeToLive(300); // 5 minutes in seconds
        dto.setAccessTokenFormat("self-contained");
        dto.setReuseRefreshTokens(true);
        dto.setRefreshTokenTimeToLive(3600); // 60 minutes in seconds
        dto.setIdTokenSignatureAlgorithm("RS256");
        dto.setX509CertificateBoundAccessTokens(false);
        dto.setAuthorizationCodeTimeToLive(300); // 5 minutes in seconds
        dto.setDeviceCodeTimeToLive(300); // 5 minutes in seconds
        return dto;
    }
}
