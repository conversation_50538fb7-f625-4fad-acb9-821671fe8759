package com.vusecurity.auth.shared.api.controller;

import com.vusecurity.auth.AppConfigProperties;
import org.junit.jupiter.api.Test;
import org.springframework.http.ResponseEntity;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class StatusControllerTest {

    @Test
    void getStatus_returns_up_with_version_build() {
        AppConfigProperties props = new AppConfigProperties();
        props.setVersion("1.2.3");
        props.setBuild("7");
        StatusController controller = new StatusController(props);

        ResponseEntity<Map<String, Object>> resp = controller.getStatus();
        assertEquals(200, resp.getStatusCode().value());
        assertEquals("UP", resp.getBody().get("status"));
        assertEquals("1.2.3", resp.getBody().get("version"));
        assertEquals("7", resp.getBody().get("build"));
        assertTrue(((Number) resp.getBody().get("timestamp")).longValue() > 0);
    }
}

