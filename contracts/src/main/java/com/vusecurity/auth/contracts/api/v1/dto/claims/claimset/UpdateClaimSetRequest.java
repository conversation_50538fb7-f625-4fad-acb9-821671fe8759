package com.vusecurity.auth.contracts.api.v1.dto.claims.claimset;

import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.LookupStrategy;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.UUID;

@Data
@Schema(description = "Request to update an existing claim set")
public class UpdateClaimSetRequest {

    @Schema(description = "New business identifier for the claim set",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID businessId;

    @Pattern(regexp = "[a-zA-Z0-9 _.-]*", message = "Name can only have letters, numbers, underscore, hyphen and spaces")
    @Size(min = 1, max = 255, message = "The 'name' field must be between 1 and 255 characters")
    @Schema(description = "New name for the claim set",
            example = "Updated Employee Claims")
    private String name;

    @Schema(description = "New account type this claim set applies to",
            example = "WORKFORCE")
    private AccountType accountType;

    @Schema(description = "Whether this claim set is an identifier claim set (part of composite primary key)",
            example = "true")
    private Boolean isIdentifier;

    @Pattern(regexp = "[a-zA-Z0-9 _.-]*", message = "Name can only have letters, numbers, underscore, hyphen and spaces")
    @Size(min = 1, max = 255, message = "The 'description' field must be between 1 and 255 characters")
    @Schema(description = "New description for the claim set",
            example = "Updated claims required for all employee accounts")
    private String description;

    @Schema(description = "Strategy for resolving claim handles when multiple claim definitions are involved",
            example = "ALL_CLAIMS_MUST_MATCH")
    private LookupStrategy lookupStrategy;
}
