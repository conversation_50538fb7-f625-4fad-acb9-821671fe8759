package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.DeleteClaimDefinitionCommand;
import com.vusecurity.auth.claims.application.exception.DeleteClaimTypeSystemException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.contracts.enums.ClaimType;

import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Transactional
public class DeleteClaimDefinitionHandler {

    private static final Logger logger = LoggerFactory.getLogger(DeleteClaimDefinitionHandler.class);
    
    private final ClaimDefinitionRepository claimDefinitionRepository;

    public void deleteClaimDefinition(DeleteClaimDefinitionCommand command) {
        this.deleteClaimDefinition(command, false);
    }

    public void deleteClaimDefinition(DeleteClaimDefinitionCommand command, boolean allowToDeleteSystemClaim) {
        logger.debug("Deleting claim definition: {}", command.claimDefinitionId());

        try {
            // Validate command
            command.validate();

            // Check if claim definition exists
            ClaimDefinitionJpaEntity claimDefinition = claimDefinitionRepository.findById(command.claimDefinitionId())
                    .orElseThrow(() -> new IllegalArgumentException("Claim definition not found with ID: " + command.claimDefinitionId()));

            if (claimDefinition.getClaimType().equals(ClaimType.SYSTEM_DEFINED) && Boolean.FALSE.equals(allowToDeleteSystemClaim)){
                throw new DeleteClaimTypeSystemException();
            }

            // Check if claim definition is being used by any claim sets
            if (!claimDefinition.getClaimSetMappings().isEmpty()) {
                throw new IllegalStateException("Cannot delete claim definition that is being used by claim sets");
            }

            // Delete the claim definition
            claimDefinitionRepository.deleteById(command.claimDefinitionId());
            
            logger.debug("Claim definition deleted successfully: {}", command.claimDefinitionId());

        } catch (Exception e) {
            logger.error("Error deleting claim definition {}: {}", command.claimDefinitionId(), e.getMessage(), e);
            throw e;
        }
    }
}
