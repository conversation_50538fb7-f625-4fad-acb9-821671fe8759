# SCIM Documentation

## General Rules
- As the database is dynamic and for now we will only support standard extensions for SCIM, the mapping will be done only for the attributes that are mapped silently ignoring others.
- Complext types are not supported at the time, so if the mapping ends up beeing a complex object it will be stored as it is (e.g.: `"[Email(type=work, value=<EMAIL>, display=null, primary=true)]"`)
- If the Attribute definition in the schema is multivalued for the current mapped value it has to be added as array while setting the corresponding primary value.
- The validations of the ClaimValues are done while saving them, only the required ones should be added as null or empty if the resulting mapping has no data.
- In the case of a complext type filter the query will be best effort possible but it's not ensured.
- ClaimDefinitions of Type.Array are not supported for mapping ScimFields.
- SCIM multivalued attributes can be filtered by the boolena primary subAttribute, but because VU One requires at least one primary element to an array of ClaimValues means that at least one is going to be saved a `primary eq true` even if the filter indicates that primary must be true. two rules are applied.
    - If an attribute contains `primary = true`, then that value will be the primary.
    - If no attribute contain `primary = true` then, the first value will be the primary.


### TBD
- Filtering over multiple data sets (e.g.: `Primary Email (<EMAIL>) claim and array of emails([<EMAIL>, <EMAIL>, <EMAIL>])`). ✔️
- ~~Support for ClaimDefinitions of Type.Array~~ ❌
- Support for Complet Types
- Support for filtering Complex Types
- Support for nested or indirect group members

## [0.0.1] - Unreleased

### Add
- Added Apache Scimple library and Springboot started. They work by creating an Application with a Jersey Server deployed, exposing the SCIM API.