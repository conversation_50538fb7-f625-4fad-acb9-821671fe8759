package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.command.UpdateGroupMembersCommand;
import com.vusecurity.auth.authorization.application.exception.AccountNotFoundException;
import com.vusecurity.auth.authorization.application.exception.GroupNotFoundException;
import com.vusecurity.auth.authorization.domain.model.GroupMembershipRole;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.GroupMembershipJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.GroupMembershipRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountGroupRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class UpdateGroupMembersHandler {

    private final AccountGroupRepository accountGroupRepository;
    private final AccountRepository accountRepository;
    private final GroupMembershipRepository groupMembershipRepository;

    @Transactional
    public void updateMembers(UpdateGroupMembersCommand command) {
        command.validate();

        AccountGroupJpaEntity group = accountGroupRepository.findById(command.groupId())
                .orElseThrow(() -> new GroupNotFoundException("Group not found with ID: " + command.groupId()));

        // Fetch existing memberships once and map by account ID for efficient lookup
        Map<UUID, GroupMembershipJpaEntity> currentMemberships = groupMembershipRepository.findByGroupId(group.getId())
                .stream()
                .collect(Collectors.toMap(m -> m.getAccount().getId(), m -> m));

        Set<UUID> currentMemberIds = currentMemberships.keySet();
        Set<UUID> desiredMemberIds = command.accountIds() == null ? Collections.emptySet() : new HashSet<>(command.accountIds());
        Set<UUID> ownerIds = command.ownerAccountIds() == null ? Collections.emptySet() : new HashSet<>(command.ownerAccountIds());

        // 1. Members to REMOVE
        Set<UUID> membersToRemove = new HashSet<>(currentMemberIds);
        membersToRemove.removeAll(desiredMemberIds);
        if (!membersToRemove.isEmpty()) {
            groupMembershipRepository.deleteByGroupIdAndAccountIdIn(group.getId(), membersToRemove.stream().toList());
        }

        // 2. Members to ADD
        Set<UUID> membersToAdd = new HashSet<>(desiredMemberIds);
        membersToAdd.removeAll(currentMemberIds);
        if (!membersToAdd.isEmpty()) {
            List<AccountJpaEntity> accountsToAdd = accountRepository.findAllById(membersToAdd);
            if (accountsToAdd.size() != membersToAdd.size()) {
                throw new AccountNotFoundException("One or more accounts to add were not found.");
            }
            List<GroupMembershipJpaEntity> newMemberships = accountsToAdd.stream()
                    .map(account -> {
                        GroupMembershipRole role = ownerIds.contains(account.getId())
                                ? GroupMembershipRole.OWNER
                                : GroupMembershipRole.MEMBER;
                        return new GroupMembershipJpaEntity(account, group, role);
                    })
                    .toList();
            groupMembershipRepository.saveAll(newMemberships);
        }

        // 3. Members to UPDATE (those who remain in the group)
        Set<UUID> retainedMemberIds = new HashSet<>(currentMemberIds);
        retainedMemberIds.retainAll(desiredMemberIds);

        List<GroupMembershipJpaEntity> membershipsToUpdate = new ArrayList<>();
        for (UUID accountId : retainedMemberIds) {
            GroupMembershipJpaEntity membership = currentMemberships.get(accountId);
            GroupMembershipRole desiredRole = ownerIds.contains(accountId) ? GroupMembershipRole.OWNER : GroupMembershipRole.MEMBER;

            if (membership.getMembershipRole() != desiredRole) {
                membership.setMembershipRole(desiredRole);
                membershipsToUpdate.add(membership);
            }
        }

        if (!membershipsToUpdate.isEmpty()) {
            groupMembershipRepository.saveAll(membershipsToUpdate);
        }
    }
} 