package com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetDefinitionMappingJpaEntity;
import com.vusecurity.auth.contracts.enums.AccountType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;
import java.util.UUID;

@Repository
public interface ClaimSetDefinitionMappingRepository extends JpaRepository<ClaimSetDefinitionMappingJpaEntity, UUID> {

    /**
     * Find all mappings for a specific claim set
     */
    @Query("SELECT m FROM ClaimSetDefinitionMappingJpaEntity m " +
           "WHERE m.claimSet.id = :claimSetId " +
           "ORDER BY m.claimDefinitionOrder ASC NULLS LAST")
    List<ClaimSetDefinitionMappingJpaEntity> findByClaimSetId(@Param("claimSetId") UUID claimSetId);

    /**
     * Find a specific mapping
     */
    @Query("SELECT m FROM ClaimSetDefinitionMappingJpaEntity m " +
           "WHERE m.claimSet.id = :claimSetId " +
           "AND m.claimDefinition.id = :claimDefinitionId")
    ClaimSetDefinitionMappingJpaEntity findByClaimSetIdAndClaimDefinitionId(
            @Param("claimSetId") UUID claimSetId,
            @Param("claimDefinitionId") UUID claimDefinitionId);

    /**
     * Delete all mappings for a specific claim set
     */
    @Modifying
    @Query("DELETE FROM ClaimSetDefinitionMappingJpaEntity m " +
           "WHERE m.claimSet.id = :claimSetId")
    void deleteByClaimSetId(@Param("claimSetId") UUID claimSetId);

    /**
     * Delete all mappings for a specific claim definitions
     */
    @Modifying
    @Query("DELETE FROM ClaimSetDefinitionMappingJpaEntity m " +
            "WHERE m.claimDefinition.id = :claimDefinitionId")
    void deleteByClaimDefinitionId(@Param("claimDefinitionId") UUID claimSetId);

    /**
     * Delete a specific mapping
     */
    @Modifying
    @Query("DELETE FROM ClaimSetDefinitionMappingJpaEntity m " +
           "WHERE m.claimSet.id = :claimSetId " +
           "AND m.claimDefinition.id = :claimDefinitionId")
    void deleteByClaimSetIdAndClaimDefinitionId(
            @Param("claimSetId") UUID claimSetId,
            @Param("claimDefinitionId") UUID claimDefinitionId);


    @Query("""
           SELECT m
             FROM ClaimSetDefinitionMappingJpaEntity m
             JOIN m.claimSet cs
            WHERE cs.business.id        = :businessId
              AND cs.accountType        = :accountType
              AND m.claimDefinition.id  = :definitionId
           """)
    List<ClaimSetDefinitionMappingJpaEntity> findApplicableMappings(UUID businessId, AccountType accountType, UUID definitionId);

    /**
     * Find mappings by claim definition ID and claim set IDs
     */
    @Query("SELECT m FROM ClaimSetDefinitionMappingJpaEntity m " +
           "WHERE m.claimDefinition.id = :claimDefinitionId " +
           "AND m.claimSet.id IN :claimSetIds")
    List<ClaimSetDefinitionMappingJpaEntity> findByClaimDefinitionIdAndClaimSetIdIn(
            @Param("claimDefinitionId") UUID claimDefinitionId,
            @Param("claimSetIds") Set<UUID> claimSetIds);

    /**
     * Find mappings by claim set ID and claim definition IDs
     */
    @Query("SELECT m FROM ClaimSetDefinitionMappingJpaEntity m " +
           "WHERE m.claimSet.id = :claimSetId " +
           "AND m.claimDefinition.id IN :claimDefinitionIds")
    List<ClaimSetDefinitionMappingJpaEntity> findByClaimSetIdAndClaimDefinitionIdIn(
            @Param("claimSetId") UUID claimSetId,
            @Param("claimDefinitionIds") Set<UUID> claimDefinitionIds);
}
