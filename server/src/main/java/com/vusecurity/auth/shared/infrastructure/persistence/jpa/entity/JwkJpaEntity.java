package com.vusecurity.auth.shared.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import jakarta.persistence.*;

import java.util.Objects;
import java.util.UUID;

@Entity
@Table(name = "jwk")
public class JwkJpaEntity extends AbstractEntity {

    @Column(name = "key_id", unique = true, nullable = false)
    private String keyId;

    @Column(name = "public_key", nullable = false, columnDefinition = "TEXT")
    private String publicKey;

    @Column(name = "private_key", nullable = false, columnDefinition = "TEXT")
    private String privateKey;

    @Column(name = "algorithm", nullable = false)
    private String algorithm;

    @Column(name = "key_use", nullable = false)
    private String keyUse;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    public JwkJpaEntity() {
    }

    public JwkJpaEntity(String keyId, String publicKey, String privateKey, String algorithm, String keyUse) {
        this.keyId = keyId;
        this.publicKey = publicKey;
        this.privateKey = privateKey;
        this.algorithm = algorithm;
        this.keyUse = keyUse;
        this.isActive = true;
    }

    public JwkJpaEntity(UUID id, String keyId, String publicKey, String privateKey, String algorithm, String keyUse) {
        super(id);
        this.keyId = keyId;
        this.publicKey = publicKey;
        this.privateKey = privateKey;
        this.algorithm = algorithm;
        this.keyUse = keyUse;
        this.isActive = true;
    }

    public String getKeyId() {
        return keyId;
    }

    public void setKeyId(String keyId) {
        this.keyId = keyId;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getAlgorithm() {
        return algorithm;
    }

    public void setAlgorithm(String algorithm) {
        this.algorithm = algorithm;
    }

    public String getKeyUse() {
        return keyUse;
    }

    public void setKeyUse(String keyUse) {
        this.keyUse = keyUse;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        JwkJpaEntity that = (JwkJpaEntity) o;
        return Objects.equals(keyId, that.keyId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), keyId);
    }

    @Override
    public String toString() {
        return "JwkJpaEntity{" +
                "keyId='" + keyId + '\'' +
                ", algorithm='" + algorithm + '\'' +
                ", keyUse='" + keyUse + '\'' +
                ", isActive=" + isActive +
                ", id=" + getId() +
                '}';
    }
}