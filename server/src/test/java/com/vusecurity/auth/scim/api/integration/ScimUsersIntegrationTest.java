package com.vusecurity.auth.scim.api.integration;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.vusecurity.auth.scim.application.command.CreateScimTokenCommand;
import com.vusecurity.auth.scim.application.handler.CreateScimTokenHandler;
import com.vusecurity.auth.scim.application.service.ScimTokensService;
import com.vusecurity.auth.shared.infrastructure.migration.initial.DataSeedConstants;
import com.vusecurity.auth.shared.test.BaseIntegrationTest;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.*;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.NoSuchElementException;
import java.util.UUID;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@Disabled("TODO: Enable after Flyway/Hibernate are stable and works without issues.")
class ScimUsersIntegrationTest extends BaseIntegrationTest {

    private final static String USERS_ENDPOINT = "/scim/v2/Users";
    private final static String GROUPS_ENDPOINT = "/scim/v2/Groups";

    private final ObjectMapper objectMapper = new ObjectMapper();



    @Autowired
    private ScimTokensService scimTokensService;

    @Autowired
    private CreateScimTokenHandler createScimTokenHandler;

    @Autowired
    private TestRestTemplate restTemplate;
        
    @LocalServerPort
    int port;

    private final static String AUTH_TOKEN = "6e63a8f0-193b-4268-9afa-274bc44ddf40";
    private String authToken = "Bearer ".concat(AUTH_TOKEN);
    HttpHeaders requestHeaders = new HttpHeaders();

    @BeforeEach
    void setUp() {
        try {
            scimTokensService.findByPlainToken(AUTH_TOKEN);
        } catch (NoSuchElementException e){
            CreateScimTokenCommand command = new CreateScimTokenCommand("Test Token",
            "Token for tests",
            DataSeedConstants.SYSTEM_BUSINESS_ID,
            DataSeedConstants.SCIM_PROVIDER_ID,
            AUTH_TOKEN);

            createScimTokenHandler.createToken(command);

        }
        
        requestHeaders.add("Authorization", authToken);
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    }

    @Test
    void shouldCreateUser() throws Exception {
        // Given
        String userJson = """
            {
                "schemas": ["urn:ietf:params:scim:schemas:core:2.0:User"],
                "userName": "<EMAIL>",
                "name": {
                    "givenName": "John",
                    "familyName": "Doe"
                },
                "emails": [{
                    "value": "<EMAIL>",
                    "type": "work",
                    "primary": true
                }],
                "active": true
            }
            """;

        HttpEntity<String> request = new HttpEntity<>(userJson, requestHeaders);

        // When
        ResponseEntity<String> response = restTemplate.postForEntity("/scim/v2/Users", request, String.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        
        JsonNode responseBody = objectMapper.readTree(response.getBody());
        assertThat(responseBody.get("id")).isNotNull();
        assertThat(responseBody.get("userName").asText()).isEqualTo("<EMAIL>");
        assertThat(responseBody.get("name").get("givenName").asText()).isEqualTo("John");
        assertThat(responseBody.get("name").get("familyName").asText()).isEqualTo("Doe");
        assertThat(responseBody.get("active").asBoolean()).isTrue();
        assertThat(responseBody.get("meta").get("resourceType").asText()).isEqualTo("User");
        assertThat(responseBody.get("meta").get("created")).isNotNull();
        assertThat(responseBody.get("meta").get("lastModified")).isNotNull();
    }

    @Test
    void shouldGetUserById() throws Exception {
        // Given - Create a user first
        String userJson = """
            {
                "schemas": ["urn:ietf:params:scim:schemas:core:2.0:User"],
                "userName": "<EMAIL>",
                "name": {
                    "givenName": "Jane",
                    "familyName": "Smith"
                },
                "active": true
            }
            """;

        HttpEntity<String> createRequest = new HttpEntity<>(userJson, requestHeaders);
        ResponseEntity<String> createResponse = restTemplate.postForEntity(
                USERS_ENDPOINT, createRequest, String.class);
        
        String userId = objectMapper.readTree(createResponse.getBody()).get("id").asText();

        // When
        HttpEntity<Void> getRequest = new HttpEntity<>(requestHeaders);
        ResponseEntity<String> response = restTemplate.exchange(
                USERS_ENDPOINT + "/" + userId, 
                HttpMethod.GET, 
                getRequest, 
                String.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        JsonNode responseBody = objectMapper.readTree(response.getBody());
        assertThat(responseBody.get("id").asText()).isEqualTo(userId);
        assertThat(responseBody.get("userName").asText()).isEqualTo("<EMAIL>");
        assertThat(responseBody.get("name").get("givenName").asText()).isEqualTo("Jane");
    }

    @Test
    @Disabled("TODO: Enable after UPDATE / PUT is implemented")
    void shouldUpdateUser() throws Exception {
        // Given - Create a user first
        String userJson = """
            {
                "schemas": ["urn:ietf:params:scim:schemas:core:2.0:User"],
                "userName": "<EMAIL>",
                "name": {
                    "givenName": "Update",
                    "familyName": "Test"
                },
                "active": true
            }
            """;

        HttpEntity<String> createRequest = new HttpEntity<>(userJson, requestHeaders);
        ResponseEntity<String> createResponse = restTemplate.postForEntity(
                USERS_ENDPOINT, createRequest, String.class);
        
        JsonNode createdUser = objectMapper.readTree(createResponse.getBody());
        String userId = createdUser.get("id").asText();

        // When - Update the user
        String updateJson = """
            {
                "schemas": ["urn:ietf:params:scim:schemas:core:2.0:User"],
                "id": "%s",
                "userName": "<EMAIL>",
                "name": {
                    "givenName": "Updated",
                    "familyName": "User"
                },
                "active": false
            }
            """.formatted(userId);

        HttpEntity<String> updateRequest = new HttpEntity<>(updateJson, requestHeaders);
        ResponseEntity<String> response = restTemplate.exchange(
                USERS_ENDPOINT + "/" + userId,
                HttpMethod.PUT,
                updateRequest,
                String.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        JsonNode responseBody = objectMapper.readTree(response.getBody());
        assertThat(responseBody.get("name").get("givenName").asText()).isEqualTo("Updated");
        assertThat(responseBody.get("name").get("familyName").asText()).isEqualTo("User");
        assertThat(responseBody.get("active").asBoolean()).isFalse();
    }

    @Test
    @Disabled("TODO: Enable after UPDATE / PUT is implemented")
    void shouldPatchUser() throws Exception {
        // Given - Create a user first
        String userJson = """
            {
                "schemas": ["urn:ietf:params:scim:schemas:core:2.0:User"],
                "userName": "<EMAIL>",
                "name": {
                    "givenName": "Patch",
                    "familyName": "Test"
                },
                "active": true
            }
            """;

        HttpEntity<String> createRequest = new HttpEntity<>(userJson, requestHeaders);
        ResponseEntity<String> createResponse = restTemplate.postForEntity(
                USERS_ENDPOINT, createRequest, String.class);
        
        String userId = objectMapper.readTree(createResponse.getBody()).get("id").asText();

        // When - Patch the user
        String patchJson = """
            {
                "schemas": ["urn:ietf:params:scim:api:messages:2.0:PatchOp"],
                "Operations": [
                    {
                        "op": "replace",
                        "path": "active",
                        "value": false
                    },
                    {
                        "op": "replace",
                        "path": "name.givenName",
                        "value": "Patched"
                    }
                ]
            }
            """;

        HttpEntity<String> patchRequest = new HttpEntity<>(patchJson, requestHeaders);
        ResponseEntity<String> response = restTemplate.exchange(
                USERS_ENDPOINT + "/" + userId,
                HttpMethod.PATCH,
                patchRequest,
                String.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        JsonNode responseBody = objectMapper.readTree(response.getBody());
        assertThat(responseBody.get("name").get("givenName").asText()).isEqualTo("Patched");
        assertThat(responseBody.get("active").asBoolean()).isFalse();
    }

    @Test
    void shouldDeleteUser() throws Exception {
        // Given - Create a user first
        String userJson = """
            {
                "schemas": ["urn:ietf:params:scim:schemas:core:2.0:User"],
                "userName": "<EMAIL>",
                "name": {
                    "givenName": "Delete",
                    "familyName": "Test"
                }
            }
            """;

        HttpEntity<String> createRequest = new HttpEntity<>(userJson, requestHeaders);
        ResponseEntity<String> createResponse = restTemplate.postForEntity(
                USERS_ENDPOINT, createRequest, String.class);
        
        String userId = objectMapper.readTree(createResponse.getBody()).get("id").asText();

        // When
        HttpEntity<Void> deleteRequest = new HttpEntity<>(requestHeaders);
        ResponseEntity<Void> response = restTemplate.exchange(
                USERS_ENDPOINT + "/" + userId,
                HttpMethod.DELETE,
                deleteRequest,
                Void.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NO_CONTENT);

        // Verify group is deleted
        ResponseEntity<String> getResponse = restTemplate.exchange(
                USERS_ENDPOINT + "/" + userId,
                HttpMethod.GET,
                new HttpEntity<>(requestHeaders),
                String.class);
        //For now there's no delete implemented, so check if the users is inactive.
        assertThat(getResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        JsonNode responseBody = objectMapper.readTree(getResponse.getBody());
        assertThat(responseBody.get("active").asBoolean()).isFalse();
        //TODO: uncomment next line and remove previous block when delete is properly implemented.
        //assertThat(getResponse.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
    }

    @Test
    void shouldListGroups() throws Exception {
        // Given - Create multiple groups
        String group1Json = """
            {
                "schemas": ["urn:ietf:params:scim:schemas:core:2.0:Group"],
                "displayName": "List Group One"
            }
            """;
        
        String group2Json = """
            {
                "schemas": ["urn:ietf:params:scim:schemas:core:2.0:Group"],
                "displayName": "List Group Two"
            }
            """;

        restTemplate.postForEntity(GROUPS_ENDPOINT, new HttpEntity<>(group1Json, requestHeaders), String.class);
        restTemplate.postForEntity(GROUPS_ENDPOINT, new HttpEntity<>(group2Json, requestHeaders), String.class);

        // When
        HttpEntity<Void> entityRequest = new HttpEntity<>(requestHeaders);
        ResponseEntity<String> response = restTemplate.exchange(GROUPS_ENDPOINT, HttpMethod.GET, entityRequest, String.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        JsonNode responseBody = objectMapper.readTree(response.getBody());
        assertThat(responseBody.get("schemas").get(0).asText())
                .isEqualTo("urn:ietf:params:scim:api:messages:2.0:ListResponse");
        assertThat(responseBody.get("totalResults").asInt()).isGreaterThanOrEqualTo(2);
        assertThat(responseBody.get("Resources")).isInstanceOfAny(ArrayNode.class);
        assertThat(responseBody.get("Resources").size()).isGreaterThanOrEqualTo(2);
    }

    @Test
    void shouldFilterGroups() throws Exception {
        // Given - Create groups with different names
        String engineeringGroupJson = """
            {
                "schemas": ["urn:ietf:params:scim:schemas:core:2.0:Group"],
                "displayName": "Engineering"
            }
            """;
        
        String marketingGroupJson = """
            {
                "schemas": ["urn:ietf:params:scim:schemas:core:2.0:Group"],
                "displayName": "Marketing"
            }
            """;

        restTemplate.postForEntity(GROUPS_ENDPOINT, new HttpEntity<>(engineeringGroupJson, requestHeaders), String.class);
        restTemplate.postForEntity(GROUPS_ENDPOINT, new HttpEntity<>(marketingGroupJson, requestHeaders), String.class);

        // When - Filter for Engineering groups
        String filterUrl = GROUPS_ENDPOINT + "?filter=displayName sw \"Engineering\"";
        HttpEntity<Void> request = new HttpEntity<>(requestHeaders);
        ResponseEntity<String> response = restTemplate.exchange(
                filterUrl, HttpMethod.GET, request, String.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        JsonNode responseBody = objectMapper.readTree(response.getBody());
        JsonNode resources = responseBody.get("Resources");
        
        for (JsonNode resource : resources) {
            assertThat(resource.get("displayName").asText()).startsWith("Engineering");
        }
    }

    @Test
    void shouldHandleInvalidGroupCreation() throws Exception {
        // Given - Invalid group JSON (missing required displayName)
        String invalidGroupJson = """
            {
                "schemas": ["urn:ietf:params:scim:schemas:core:2.0:Group"],
                "members": []
            }
            """;

        HttpEntity<String> request = new HttpEntity<>(invalidGroupJson, requestHeaders);

        // When
        ResponseEntity<String> response = restTemplate.postForEntity(
                GROUPS_ENDPOINT, request, String.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        
        JsonNode responseBody = objectMapper.readTree(response.getBody());
        assertThat(responseBody.get("schemas").get(0).asText())
                .isEqualTo("urn:ietf:params:scim:api:messages:2.0:Error");
        assertThat(responseBody.get("status").asText()).isEqualTo("400");
    }

    @Test
    void shouldHandleGroupNotFound() throws Exception {
        // Given
        String nonExistentId = UUID.randomUUID().toString();

        // When
        HttpEntity<Void> request = new HttpEntity<>(requestHeaders);
        ResponseEntity<String> response = restTemplate.exchange(
                GROUPS_ENDPOINT + "/" + nonExistentId,
                HttpMethod.GET,
                request,
                String.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
        
        JsonNode responseBody = objectMapper.readTree(response.getBody());
        assertThat(responseBody.get("schemas").get(0).asText())
                .isEqualTo("urn:ietf:params:scim:api:messages:2.0:Error");
        assertThat(responseBody.get("status").asText()).isEqualTo("404");
    }

    @Test
    void shouldHandlePaginationForGroups() throws Exception {
        // Given - Create multiple groups to test pagination
        for (int i = 1; i <= 15; i++) {
            String groupJson = """
                {
                    "schemas": ["urn:ietf:params:scim:schemas:core:2.0:Group"],
                    "displayName": "Pagination Group %d"
                }
                """.formatted(i);
            restTemplate.postForEntity(GROUPS_ENDPOINT, new HttpEntity<>(groupJson, requestHeaders), String.class);
        }

        // When - Request first page with count=5
        String paginationUrl = GROUPS_ENDPOINT + "?startIndex=1&count=5";
        HttpEntity<Void> request = new HttpEntity<>(requestHeaders);
        ResponseEntity<String> response = restTemplate.exchange(
                paginationUrl, HttpMethod.GET, request, String.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        JsonNode responseBody = objectMapper.readTree(response.getBody());
        assertThat(responseBody.get("startIndex").asInt()).isEqualTo(1);
        assertThat(responseBody.get("itemsPerPage").asInt()).isEqualTo(5);
        assertThat(responseBody.get("Resources").size()).isLessThanOrEqualTo(5);
        assertThat(responseBody.get("totalResults").asInt()).isGreaterThanOrEqualTo(15);
    }
}
