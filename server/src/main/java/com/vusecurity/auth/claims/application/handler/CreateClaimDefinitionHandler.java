package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.CreateClaimDefinitionCommand;
import com.vusecurity.auth.claims.application.exception.DuplicateClaimDefinitionException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.shared.util.DatabaseConstraintUtils;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Transactional
public class CreateClaimDefinitionHandler {

    private static final Logger logger = LoggerFactory.getLogger(CreateClaimDefinitionHandler.class);
    
    private final ClaimDefinitionRepository claimDefinitionRepository;

    public ClaimDefinitionJpaEntity createClaimDefinition(CreateClaimDefinitionCommand command) {
        logger.debug("Creating claim definition: {}", command);

        // Validate command
        command.validate();

        // Create new claim definition entity
        ClaimDefinitionJpaEntity claimDefinition = new ClaimDefinitionJpaEntity(
                command.code(),
                command.name(),
                command.description(),
                command.claimType(),
                command.dataType(),
                command.dataFormat()
        );

        // Save the entity with constraint handling for unique code
        ClaimDefinitionJpaEntity savedClaimDefinition = DatabaseConstraintUtils.executeWithConstraintHandling(
            () -> claimDefinitionRepository.saveAndFlush(claimDefinition),
            "uk_claim_def_code",
            () -> new DuplicateClaimDefinitionException(
                String.format("Claim definition with code '%s' already exists", command.code())
            )
        );

        logger.debug("Claim definition created successfully with ID: {}", savedClaimDefinition.getId());
        return savedClaimDefinition;
    }
}
