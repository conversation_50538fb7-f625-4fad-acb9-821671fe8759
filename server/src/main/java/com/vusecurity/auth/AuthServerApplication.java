package com.vusecurity.auth;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;

@SpringBootApplication
@ComponentScan(basePackages = {
                "com.vusecurity.auth.**",
                "com.vusecurity.multitenant.**", // multitenant
                "com.vusecurity.business.**", // business-core
                "com.vusecurity.channel.**", // business-core
                "com.vusecurity.core.**" // core
})
@EnableJpaRepositories(basePackages = { "com.vusecurity.**" })
@EnableAspectJAutoProxy(exposeProxy = true)
@OpenAPIDefinition(info = @Info(title = "VU One Authorization Server API's", version = "1", description = "Documentation Authorization Server APIs v1.0"))
public class AuthServerApplication {
        private static final Logger logger = LoggerFactory.getLogger(AuthServerApplication.class);

        public static void main(String[] args) {
                try {
                        SpringApplication.run(AuthServerApplication.class, args);
                } catch (Exception e) {
                        logger.error(e.toString());
                }
        }
}
