package com.vusecurity.auth.shared.infrastructure.security.oidc;

import java.util.Map;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import com.vusecurity.auth.identities.application.query.GetAccountClaimsQuery;
import com.vusecurity.auth.identities.application.query.GetAccountQuery;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;

/**
 * Custom UserDetailsService implementation that loads user details from AccountJpaEntity
 * and includes claim values and definitions.
 */
public class AccountDetailService implements UserDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(AccountDetailService.class);

    private final GetAccountQuery getAccountQuery;
    private final GetAccountClaimsQuery getAccountClaimsQuery;

    public AccountDetailService(GetAccountQuery getAccountQuery, GetAccountClaimsQuery getAccountClaimsQuery) {
        this.getAccountQuery = getAccountQuery;
        this.getAccountClaimsQuery = getAccountClaimsQuery;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        logger.debug("Loading user details for username: {}", username);

        try {
            // First, try to find account by ID if username is a UUID
            AccountJpaEntity account = null;
            try {
                UUID accountId = UUID.fromString(username);
                account = getAccountQuery.getAccountById(accountId.toString());
            } catch (IllegalArgumentException e) {
                // Username is not a UUID, try to find by claim values
                throw new IllegalArgumentException("AccountId is not a valid UUID: " + username);
            }

            if (account == null) {
                throw new UsernameNotFoundException("Account not found for username: " + username);
            }

            // Load claim values and definitions
            Map<String, Object> claimValues = getAccountClaimsQuery.getClaimValuesByAccountId(account.getId());

            // Create and return AccountUserDetails
            AccountUserDetails userDetails = new AccountUserDetails(account, claimValues);

            logger.debug("Successfully loaded user details for account: {} ({})", account.getId(), userDetails.getUsername());
            return userDetails;

        } catch (IllegalArgumentException e) {
            logger.error("Invalid username format for: {}", username, e);
            throw new UsernameNotFoundException("Invalid username format: " + username, e);
        } catch (RuntimeException e) {
            logger.error("Runtime error loading user details for username: {}", username, e);
            throw new UsernameNotFoundException("Failed to load user details due to runtime error for username: " + username, e);
        } catch (Exception e) {
            logger.error("Unexpected error loading user details for username: {}", username, e);
            throw new UsernameNotFoundException("Unexpected error occurred while loading user details for username: " + username, e);
        }
    }


}
