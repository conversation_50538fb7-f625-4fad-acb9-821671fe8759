package com.vusecurity.auth.shared.infrastructure.migration.initial;

import java.time.Duration;
import java.time.Instant;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.core.oidc.OidcScopes;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;
import org.springframework.stereotype.Service;

import com.vusecurity.auth.authorization.application.service.OAuth2RegisteredClientService;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.Oauth2RegisteredClientRepository;
import com.vusecurity.multitenant.MultitenantService;
import com.vusecurity.multitenant.TenantContext;
import com.vusecurity.multitenant.model.props.PropertiesConfig;

@Service
public class OAuth2RegisteredClientSetupService {
        private final Logger logger = LoggerFactory.getLogger(OAuth2RegisteredClientSetupService.class);
        private final MultitenantService multitenantService;
        private final OAuth2RegisteredClientService registeredClientService;
        private final Oauth2RegisteredClientRepository oauth2RegisteredClientRepository;
        private final PasswordEncoder passwordEncoder;

        public OAuth2RegisteredClientSetupService(MultitenantService multitenantService,
                        OAuth2RegisteredClientService registeredClientService,
                        Oauth2RegisteredClientRepository oauth2RegisteredClientRepository,
                        PasswordEncoder passwordEncoder) {
                this.multitenantService = multitenantService;
                this.registeredClientService = registeredClientService;
                this.oauth2RegisteredClientRepository = oauth2RegisteredClientRepository;
                this.passwordEncoder = passwordEncoder;
        }

        public void start() {
                logger.debug("OAuth2RegisteredClientSetupService: Starting OAuth2 registered client initialization...");

                if (oauth2RegisteredClientRepository.count() == 0) {
                        PropertiesConfig properties = multitenantService
                                        .getProperties(TenantContext.getCurrentTenant());
                        String clientId = properties.getProperties().stream()
                                        .filter(p -> p.getKey().equalsIgnoreCase("oauth2.client-id"))
                                        .findFirst()
                                        .map(p -> p.getValue())
                                        .orElse(null);
                        String clientSecret = properties.getProperties().stream()
                                        .filter(p -> p.getKey().equalsIgnoreCase("oauth2.client-secret"))
                                        .findFirst()
                                        .map(p -> p.getValue())
                                        .orElse(null);
                        String clientCallback = properties.getProperties().stream()
                                        .filter(p -> p.getKey().equalsIgnoreCase("oauth2.callback"))
                                        .findFirst()
                                        .map(p -> p.getValue())
                                        .orElse(null);
                        if (clientId != null && clientSecret != null) {
                                RegisteredClient client = RegisteredClient.withId(UUID.randomUUID().toString())
                                                .clientId(clientId)
                                                .clientSecret(passwordEncoder.encode(clientSecret))
                                                .clientAuthenticationMethod(
                                                                ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
                                                .clientAuthenticationMethod(
                                                                ClientAuthenticationMethod.CLIENT_SECRET_POST)
                                                .authorizationGrantType(
                                                                AuthorizationGrantType.AUTHORIZATION_CODE)
                                                .authorizationGrantType(
                                                                AuthorizationGrantType.CLIENT_CREDENTIALS)
                                                .authorizationGrantType(
                                                                AuthorizationGrantType.REFRESH_TOKEN)
                                                .redirectUri(clientCallback != null ? clientCallback
                                                                : "https://" + TenantContext.getCurrentTenant()
                                                                                + "/callback")
                                                .scope(OidcScopes.OPENID)
                                                .scope("read")
                                                .scope("write")                                        
                                                .scope("role_admin")                                        
                                                .clientSettings(ClientSettings.builder()
                                                                .requireAuthorizationConsent(false)
                                                                .build())
                                                .tokenSettings(TokenSettings.builder()
                                                                .accessTokenTimeToLive(Duration.ofHours(1))
                                                                .build())
                                                .clientIdIssuedAt(Instant.now()).build();
                                registeredClientService.save(client);
                                logger.info("OAuth2 client created and saved with ID: {}", client.getId());
                        } else {
                                logger.warn("OAuth2 client not created. Client ID and/or client secret not found in properties.");
                        }
                }
        else {
                
        }
        }
}
