package com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import com.vusecurity.core.commons.utils.converters.MapToJsonConverter;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@Entity
@Table(name = "identity_provider",
        indexes = {
                @Index(name = "uk_identity_provider_id",  columnList = "id",          unique = true),
                @Index(name = "uk_identity_provider_name",columnList = "name",        unique = true)
        })
@SuppressWarnings("LombokGetterMayBeUsed, LombokSetterMayBeUsed")
public class IdentityProviderJpaEntity extends AbstractEntity {

    @Column(nullable = false, unique = true)
    @Size(max = 255)
    private String name;

    @Column(length = 1024)
    @Size(max = 1024)
    private String description;

    @Convert(converter = MapToJsonConverter.class)
    private Map<String, Object> metadata = new HashMap<>();

    // Constructors
    public IdentityProviderJpaEntity() {
    }

    public IdentityProviderJpaEntity(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("name cannot be null or empty");
        }
        this.name = name.trim();
        this.metadata = new HashMap<>();
    }

    public IdentityProviderJpaEntity(UUID id, String name) {
        this(name);
        this.setId(id);
    }

    // Getters
    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public Map<String, Object> getMetadata() {
        return metadata != null ? new HashMap<>(metadata) : new HashMap<>();
    }

    // Setters
    public void setName(String name) {
        this.name = name;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata != null ? new HashMap<>(metadata) : new HashMap<>();
    }

    // equals() and hashCode() using name (unique business key)
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        IdentityProviderJpaEntity that = (IdentityProviderJpaEntity) o;
        return Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), name);
    }

    @Override
    public String toString() {
        return "IdentityProviderJpaEntity{" +
                "id=" + getId() +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                '}';
    }

    // Simple activate method for compatibility (no status field for now)
    public void activate() {
        // For now, this is a no-op since there's no status field
        // This method exists for compatibility with existing code
    }

    // TODO: Status-related methods commented out due to missing Status enum
    // TODO: Implement status-related methods when Status enum is available
}
