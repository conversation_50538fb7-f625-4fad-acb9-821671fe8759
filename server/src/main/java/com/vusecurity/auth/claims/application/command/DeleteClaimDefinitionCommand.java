package com.vusecurity.auth.claims.application.command;

import java.util.UUID;

/**
 * Command for deleting a claim definition.
 */
public record DeleteClaimDefinitionCommand(
        UUID claimDefinitionId
) {
    
    /**
     * Validates the command parameters.
     * @throws IllegalArgumentException if validation fails
     */
    public void validate() {
        if (claimDefinitionId == null) {
            throw new IllegalArgumentException("Claim definition ID cannot be null");
        }
    }
}
