package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.command.CreateGroupCommand;
import com.vusecurity.auth.authorization.application.exception.*;
import com.vusecurity.auth.authorization.domain.model.GroupMembershipRole;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.GroupMembershipJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.GroupMembershipRepository;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.RoleRepository;
import com.vusecurity.auth.identities.application.dto.AccountBusinessInfo;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountGroupRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import com.vusecurity.auth.shared.util.DatabaseConstraintUtils;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CreateGroupHandler {

    private static final Logger logger = LoggerFactory.getLogger(CreateGroupHandler.class);

    private final AccountGroupRepository accountGroupRepository;
    private final GroupMembershipRepository groupMembershipRepository;
    private final RoleRepository roleRepository;
    private final AccountRepository accountRepository;

    @Transactional
    public AccountGroupJpaEntity createGroup(CreateGroupCommand command) {
        logger.debug("Creating group: {}", command.name());

        // Validate command
        command.validate();

        // Validate business consistency and get business ID
        UUID businessId = null;
        if (!command.accountIds().isEmpty() || !command.roleIds().isEmpty()){
            businessId = validateBusinessConsistency(command.accountIds(), command.roleIds());
        }

        // Create group entity
        AccountGroupJpaEntity group = new AccountGroupJpaEntity(command.name());
        group.setDescription(command.description());
        if (command.metadata() != null) {
            group.setMetadata(command.metadata());
        }

        // Get and validate roles
        List<RoleJpaEntity> foundRoles = roleRepository.findAllById(command.roleIds());

        // Validate all roles were found
        if (foundRoles.size() != command.roleIds().size()) {
            Set<UUID> foundIds = foundRoles.stream()
                    .map(RoleJpaEntity::getId)
                    .collect(Collectors.toSet());
            Set<UUID> notFound = command.roleIds().stream()
                    .filter(id -> !foundIds.contains(id))
                    .collect(Collectors.toSet());
            throw new RoleNotFoundException("Roles not found with IDs: " + notFound);
        }

        // Validate all roles belong to same business
        if (businessId != null){
            for (RoleJpaEntity role : foundRoles) {
                if (!businessId.equals(role.getBusinessId())) {
                    throw new MismatchedBusinessException("Role " + role.getName() + " does not belong to the same business as the accounts");
                }
            }
        }

        group.setRoles(new HashSet<>(foundRoles));

        // Save group first with constraint handling for unique role name
        AccountGroupJpaEntity savedGroup = DatabaseConstraintUtils.executeWithConstraintHandling(
                () -> accountGroupRepository.saveAndFlush(group),
                "uk_account_group_name", // Unique constraint on group name
                () -> new GroupNameAlreadyExistsException("Group with name '" + command.name() + "' already exists")
        );
        logger.debug("Group created successfully: {}", savedGroup.getId());

        // Create memberships for accounts (only if accounts are provided)
        if (command.accountIds() != null && !command.accountIds().isEmpty()) {

            List<AccountJpaEntity> accounts = accountRepository.findAllById(command.accountIds());

            // Validate all accounts were found
            if (accounts.size() != command.accountIds().size()) {
                Set<UUID> foundIds = accounts.stream()
                        .map(AccountJpaEntity::getId)
                        .collect(Collectors.toSet());
                Set<UUID> notFound = command.accountIds().stream()
                        .filter(id -> !foundIds.contains(id))
                        .collect(Collectors.toSet());
                throw new AccountNotFoundException("Accounts not found with IDs: " + notFound);
            }

            List<GroupMembershipJpaEntity> memberships = accounts.stream()
                    .map(account -> {
                        GroupMembershipRole role = command.ownerAccountIds().contains(account.getId())
                                ? GroupMembershipRole.OWNER
                                : GroupMembershipRole.MEMBER;
                        return new GroupMembershipJpaEntity(account, savedGroup, role);
                    })
                    .toList();

            groupMembershipRepository.saveAll(memberships);

            logger.debug("Created {} memberships for group: {}", memberships.size(), savedGroup.getId());
        } else {
            logger.debug("No accounts provided - group created without memberships: {}", savedGroup.getId());
        }

        return savedGroup;
    }

    private UUID validateBusinessConsistency(List<UUID> accountIds, List<UUID> roleIds) {
        UUID businessId;

        // If accounts are provided, get business ID from first account
        if (accountIds != null && !accountIds.isEmpty()) {
            UUID firstAccountId = accountIds.getFirst();
            AccountBusinessInfo firstAccountInfo = accountRepository.findInfoById(firstAccountId)
                    .orElseThrow(() -> new AccountNotFoundException("Account not found with ID: " + firstAccountId));

            businessId = firstAccountInfo.getBusinessId();

            // Validate all accounts belong to same business
            for (UUID accountId : accountIds) {
                AccountBusinessInfo accountInfo = accountRepository.findInfoById(accountId)
                        .orElseThrow(() -> new AccountNotFoundException("Account not found with ID: " + accountId));

                if (!businessId.equals(accountInfo.getBusinessId())) {
                    throw new MismatchedBusinessException("All accounts must belong to the same business");
                }
            }
        } else {
            // No accounts provided, get business ID from first role
            UUID firstRoleId = roleIds.getFirst();
            RoleJpaEntity firstRole = roleRepository.findById(firstRoleId)
                    .orElseThrow(() -> new RoleNotFoundException("Role not found with ID: " + firstRoleId));

            businessId = firstRole.getBusinessId();
        }

        return businessId;
    }
} 