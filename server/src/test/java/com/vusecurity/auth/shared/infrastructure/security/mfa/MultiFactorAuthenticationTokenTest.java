package com.vusecurity.auth.shared.infrastructure.security.mfa;

import static org.junit.jupiter.api.Assertions.*;

import java.util.*;

import org.junit.jupiter.api.Test;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.auth.shared.infrastructure.security.oidc.AccountUserDetails;
import com.vusecurity.business.domain.Business;

/**
 * Unit tests for MultiFactorAuthenticationToken.
 * Tests the authentication token functionality for multi-factor authentication.
 */
class MultiFactorAuthenticationTokenTest {

    @Test
    void shouldCreateUnauthenticatedTokenWithValidData() {
        // Given
        UUID accountId = UUID.randomUUID();
        String totp = "123456";
        String password = "testPassword";

        // When
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(accountId, totp, password);

        // Then
        assertNotNull(token);
        assertEquals(accountId, token.getAccountId());
        assertEquals(totp, token.getTotp());
        assertEquals(password, token.getPassword());
        assertEquals(accountId.toString(), token.getPrincipal());
        assertEquals(password, token.getCredentials());
        assertFalse(token.isAuthenticated());
        assertTrue(token.getAuthorities().isEmpty());
    }

    @Test
    void shouldCreateUnauthenticatedTokenWithNullTotp() {
        // Given
        UUID accountId = UUID.randomUUID();
        String password = "testPassword";

        // When
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(accountId, null, password);

        // Then
        assertNotNull(token);
        assertEquals(accountId, token.getAccountId());
        assertNull(token.getTotp());
        assertEquals(password, token.getPassword());
        assertFalse(token.isAuthenticated());
    }

    @Test
    void shouldCreateUnauthenticatedTokenWithNullPassword() {
        // Given
        UUID accountId = UUID.randomUUID();
        String totp = "123456";

        // When
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(accountId, totp, null);

        // Then
        assertNotNull(token);
        assertEquals(accountId, token.getAccountId());
        assertEquals(totp, token.getTotp());
        assertNull(token.getPassword());
        assertFalse(token.isAuthenticated());
    }

    @Test
    void shouldTrimTotpWhenCreatingToken() {
        // Given
        UUID accountId = UUID.randomUUID();
        String totpWithSpaces = "  123456  ";
        String password = "testPassword";

        // When
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(accountId, totpWithSpaces, password);

        // Then
        assertEquals("123456", token.getTotp());
    }

    @Test
    void shouldHandleNullAccountIdInUnauthenticatedToken() {
        // Given
        String totp = "123456";
        String password = "testPassword";

        // When
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(null, totp, password);

        // Then
        assertNull(token.getAccountId());
        assertEquals("unknown", token.getPrincipal());
        assertFalse(token.isAuthenticated());
    }

    @Test
    void shouldCreateAuthenticatedTokenWithUserDetails() {
        // Given
        AccountUserDetails userDetails = createTestUserDetails();
        Collection<GrantedAuthority> authorities = Arrays.asList(
            new SimpleGrantedAuthority("ROLE_USER"),
            new SimpleGrantedAuthority("ROLE_ADMIN")
        );

        // When
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(userDetails, authorities);

        // Then
        assertNotNull(token);
        assertEquals(userDetails.getAccountId(), token.getAccountId());
        assertEquals(userDetails.getAccountId(), token.getPrincipal());
        assertNull(token.getTotp());
        assertNull(token.getPassword());
        assertNull(token.getCredentials());
        assertTrue(token.isAuthenticated());
        assertEquals(authorities, token.getAuthorities());
    }

    @Test
    void shouldCreateTokenWithJacksonConstructor() {
        // Given
        UUID accountId = UUID.randomUUID();
        String totp = "123456";
        String password = "testPassword";
        Collection<GrantedAuthority> authorities = Arrays.asList(new SimpleGrantedAuthority("ROLE_USER"));
        boolean authenticated = true;

        // When
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(
            accountId, totp, password, authorities, authenticated);

        // Then
        assertNotNull(token);
        assertEquals(accountId, token.getAccountId());
        assertEquals(totp, token.getTotp());
        assertEquals(password, token.getPassword());
        assertEquals(accountId.toString(), token.getPrincipal());
        assertEquals(password, token.getCredentials());
        assertEquals(authenticated, token.isAuthenticated());
        assertEquals(authorities, token.getAuthorities());
    }

    @Test
    void shouldCreateUnauthenticatedTokenWithJacksonConstructor() {
        // Given
        UUID accountId = UUID.randomUUID();
        String totp = "123456";
        String password = "testPassword";
        Collection<GrantedAuthority> authorities = Arrays.asList(new SimpleGrantedAuthority("ROLE_USER"));
        boolean authenticated = false;

        // When
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(
            accountId, totp, password, authorities, authenticated);

        // Then
        assertNotNull(token);
        assertEquals(accountId, token.getAccountId());
        assertEquals(totp, token.getTotp());
        assertEquals(password, token.getPassword());
        assertFalse(token.isAuthenticated());
        assertEquals(authorities, token.getAuthorities());
    }

    @Test
    void shouldHandleNullAccountIdInJacksonConstructor() {
        // Given
        String totp = "123456";
        String password = "testPassword";
        Collection<GrantedAuthority> authorities = Arrays.asList(new SimpleGrantedAuthority("ROLE_USER"));

        // When
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(
            null, totp, password, authorities, true);

        // Then
        assertNull(token.getAccountId());
        assertEquals("unknown", token.getPrincipal());
    }

    @Test
    void shouldCallEraseCredentials() {
        // Given
        UUID accountId = UUID.randomUUID();
        String totp = "123456";
        String password = "testPassword";
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(accountId, totp, password);

        // When
        token.eraseCredentials();

        // Then
        // Note: Since fields are final, they can't be erased, but the method should not throw
        assertDoesNotThrow(() -> token.eraseCredentials());
        // The password should still be accessible since it's final
        assertEquals(password, token.getPassword());
    }

    @Test
    void shouldHandleNullAuthorities() {
        // Given
        UUID accountId = UUID.randomUUID();
        String totp = "123456";
        String password = "testPassword";

        // When
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(
            accountId, totp, password, null, false);

        // Then
        assertTrue(token.getAuthorities().isEmpty());
    }

    @Test
    void shouldHandleEmptyAuthorities() {
        // Given
        UUID accountId = UUID.randomUUID();
        String totp = "123456";
        String password = "testPassword";
        Collection<GrantedAuthority> emptyAuthorities = Collections.emptyList();

        // When
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(
            accountId, totp, password, emptyAuthorities, true);

        // Then
        assertEquals(emptyAuthorities, token.getAuthorities());
        assertTrue(token.getAuthorities().isEmpty());
    }

    // Helper method to create test user details
    private AccountUserDetails createTestUserDetails() {
        Business business = new Business();
        business.setId(UUID.randomUUID());
        business.setName("Test Business");

        IdentityJpaEntity identity = new IdentityJpaEntity();
        identity.setId(UUID.randomUUID());
        identity.setName("John Doe");

        IdentityProviderJpaEntity identityProvider = new IdentityProviderJpaEntity();
        identityProvider.setId(UUID.randomUUID());
        identityProvider.setName("Test Provider");

        AccountJpaEntity account = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        account.setId(UUID.randomUUID());
        account.setLifecycleState(AccountLifecycleState.ACTIVE);

        Map<String, Object> claimValues = new HashMap<>();
        return new AccountUserDetails(account, claimValues);
    }
}
