package com.vusecurity.auth.authorization.application.query;

import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import org.springframework.data.domain.Page;

import java.util.Optional;
import java.util.UUID;

public interface GetGroupQuery {
    AccountGroupJpaEntity getGroupById(UUID groupId);

    AccountGroupJpaEntity getActiveGroupById(UUID groupId);

    Page<AccountGroupJpaEntity> getAllGroups(GetGroupsPagedQuery query);

    Optional<AccountGroupJpaEntity> findGroupByName(String name);
} 