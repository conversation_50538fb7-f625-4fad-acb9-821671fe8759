package com.vusecurity.auth.contracts.validations;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ValidRegexValidatorTest {

    private ValidRegexValidator validator;

    @BeforeEach
    void setUp() {
        validator = new ValidRegexValidator();
    }

    @Test
    void should_accept_valid_regex_patterns() {
        // Given
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        String phoneRegex = "^\\+?[1-9]\\d{1,14}$";
        String simpleRegex = "\\d{3}-\\d{3}-\\d{4}";

        // When & Then
        assertTrue(validator.isValid(emailRegex, null));
        assertTrue(validator.isValid(phoneRegex, null));
        assertTrue(validator.isValid(simpleRegex, null));
    }

    @Test
    void should_accept_valid_uuid_formats() {
        // Given
        String uuid1 = "2784994d-dc95-491b-8843-f5664dece55e";
        String uuid2 = "beb4da69-6685-481b-a940-87823788b02a";
        String uuidUpperCase = "2784994D-DC95-491B-8843-F5664DECE55E";
        String uuidMixed = "2784994d-DC95-491b-8843-F5664dece55e";

        // When & Then
        assertTrue(validator.isValid(uuid1, null));
        assertTrue(validator.isValid(uuid2, null));
        assertTrue(validator.isValid(uuidUpperCase, null));
        assertTrue(validator.isValid(uuidMixed, null));
    }

    @Test
    void should_accept_uuid_with_whitespace() {
        // Given
        String uuidWithSpaces = "  2784994d-dc95-491b-8843-f5664dece55e  ";
        String uuidWithTabs = "\t2784994d-dc95-491b-8843-f5664dece55e\t";

        // When & Then
        assertTrue(validator.isValid(uuidWithSpaces, null));
        assertTrue(validator.isValid(uuidWithTabs, null));
    }

    @Test
    void should_reject_invalid_regex_patterns() {
        // Given
        String invalidRegex1 = "[unclosed bracket";
        String invalidRegex2 = "(?invalid group";
        String invalidRegex3 = "*invalid quantifier";

        // When & Then
        assertFalse(validator.isValid(invalidRegex1, null));
        assertFalse(validator.isValid(invalidRegex2, null));
        assertFalse(validator.isValid(invalidRegex3, null));
    }

    @Test
    void should_handle_invalid_uuid_formats_that_are_valid_regex() {
        // Given - These are invalid UUIDs but valid regex patterns
        String shortUuid = "2784994d-dc95-491b-8843-f5664dece5";
        String longUuid = "2784994d-dc95-491b-8843-f5664dece55ee";
        String invalidChars = "2784994g-dc95-491b-8843-f5664dece55e";
        String missingDashes = "2784994ddc95491b8843f5664dece55e";
        String extraDashes = "2784994d-dc95-491b-8843-f5664de-ce55e";

        // When & Then - These should be accepted as valid regex patterns
        assertTrue(validator.isValid(shortUuid, null));
        assertTrue(validator.isValid(longUuid, null));
        assertTrue(validator.isValid(invalidChars, null));
        assertTrue(validator.isValid(missingDashes, null));
        assertTrue(validator.isValid(extraDashes, null));
    }

    @Test
    void should_accept_null_and_empty_values() {
        // Given & When & Then
        assertTrue(validator.isValid(null, null));
        assertTrue(validator.isValid("", null));
        assertTrue(validator.isValid("   ", null)); // Whitespace-only
    }

    @Test
    void should_prioritize_uuid_validation_over_regex() {
        // Given - This is a valid UUID but would be an invalid regex due to braces
        String validUuid = "2784994d-dc95-491b-8843-f5664dece55e";

        // When & Then - Should be valid because it's recognized as UUID first
        assertTrue(validator.isValid(validUuid, null));
    }

    @Test
    void should_fallback_to_regex_validation_for_non_uuid_strings() {
        // Given - Valid regex pattern that's not a UUID
        String validRegexNotUuid = "^[a-zA-Z]+$";

        // When & Then
        assertTrue(validator.isValid(validRegexNotUuid, null));
    }

    @Test
    void should_handle_mixed_scenarios() {
        // Given
        String[] validInputs = {
            "2784994d-dc95-491b-8843-f5664dece55e", // Valid UUID
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", // Valid regex
            "", // Empty string
            null, // Null
            "  BEB4DA69-6685-481B-A940-87823788B02A  ", // UUID with whitespace
            "2784994g-dc95-491b-8843-f5664dece55e", // Invalid UUID but valid regex
            "invalid-uuid-format" // Valid regex pattern
        };

        String[] invalidInputs = {
            "[unclosed bracket", // Invalid regex
            "*invalid", // Invalid regex quantifier
            "(?invalid group" // Invalid regex group
        };

        // When & Then
        for (String validInput : validInputs) {
            assertTrue(validator.isValid(validInput, null), 
                "Expected '" + validInput + "' to be valid");
        }

        for (String invalidInput : invalidInputs) {
            assertFalse(validator.isValid(invalidInput, null), 
                "Expected '" + invalidInput + "' to be invalid");
        }
    }
}