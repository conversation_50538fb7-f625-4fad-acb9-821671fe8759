/*
 * Flyway Migration: V9_1_1_0__add_jwk_storage
 * Purpose: Create jwk storage table with required constraints and indexes
 * Notes:
 *  - Matches JwkJpaEntity mapping
 *  - SQL Server syntax with GO separators
 */

-- Create table for storing JSON Web Keys (JWK)
IF OBJECT_ID('jwk', 'U') IS NULL
BEGIN
    CREATE TABLE jwk (
        id [uniqueidentifier] NOT NULL DEFAULT NEWID(),
        key_id VARCHAR(255) NOT NULL,
        public_key TEXT NOT NULL,
        private_key TEXT NOT NULL,
        algorithm VARCHAR(50) NOT NULL DEFAULT 'RS256',
        key_use VARCHAR(10) NOT NULL DEFAULT 'sig',
        is_active bit NOT NULL DEFAULT 1,
        created_at [datetimeoffset](6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at [datetimeoffset](6) NULL,
        created_by <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL DEFAULT 'SYSTEM',
        updated_by VARCHAR(255) NULL,
        status [varchar](255) NOT NULL,
        
        CONSTRAINT pk_jwk PRIMARY KEY (id),
        CONSTRAINT uk_jwk_key_id UNIQUE (key_id)
    );
END
GO

IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'idx_jwk_key_id' AND object_id = OBJECT_ID('jwk'))
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX [idx_jwk_key_id] ON jwk(key_id);
END
GO

IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'idx_jwk_is_active' AND object_id = OBJECT_ID('jwk'))
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX [idx_jwk_is_active] ON jwk(is_active) WHERE is_active = 1;
END
GO

IF NOT EXISTS (SELECT 1 FROM sys.check_constraints WHERE name = 'ck_jwk_status' AND parent_object_id = OBJECT_ID('jwk'))
BEGIN
    ALTER TABLE [jwk]  WITH CHECK ADD constraint [ck_jwk_status] CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'));
END
GO