package com.vusecurity.auth.contracts.api.v1.dto.shared;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Instant;

@Data
@Accessors(chain = true)
@Schema(description = "Base response with audit information")
public abstract class BaseResponse {
    @JsonProperty("_audit")
    @Schema(description = "Audit information for the entity")
    private AuditInfo audit;

    @Data
    @Accessors(chain = true)
    @Schema(description = "Audit information containing creation and modification details")
    public static class AuditInfo {
        @Schema(description = "Timestamp when the entity was created",
                example = "2024-01-15T10:30:00Z")
        private Instant createdAt;

        @Schema(description = "User who created the entity",
                example = "<EMAIL>")
        private String createdBy;

        @Schema(description = "Timestamp when the entity was last updated",
                example = "2024-01-15T14:45:00Z")
        private Instant updatedAt;

        @Schema(description = "User who last updated the entity",
                example = "<EMAIL>")
        private String updatedBy;
    }
}