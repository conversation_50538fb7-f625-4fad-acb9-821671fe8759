package com.vusecurity.auth.shared.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class UrlParameterUtilsTest {

    @Test
    void decodeParameter_handles_null_and_empty() {
        assertNull(UrlParameterUtils.decodeParameter(null));
        assertEquals("", UrlParameterUtils.decodeParameter("   "));
    }

    @Test
    void decodeParameter_decodes_url_encoded() {
        assertEquals("a b+c", UrlParameterUtils.decodeParameter("a%20b%2Bc"));
    }

    @Test
    void decodeParameter_returns_original_when_fails() {
        // Provide an invalid percent-encoding to trigger exception. "%ZZ" is invalid
        String input = "%ZZ";
        String out = UrlParameterUtils.decodeParameter(input);
        // It should return the trimmed original string on failure
        assertEquals(input, out);
    }

    @Test
    void decodeAndSanitizeParameter_removes_control_chars() {
        String withCtrl = "abc\u0007def"; // bell character
        String result = UrlParameterUtils.decodeAndSanitizeParameter(withCtrl);
        assertEquals("abcdef", result);
    }
}

