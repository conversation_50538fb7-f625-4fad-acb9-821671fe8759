package com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity;

import jakarta.persistence.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "claim_set_claim_values")
public class ClaimSetClaimValuesJpaEntity {

    @EmbeddedId
    private ClaimSetClaimValueId id;

    @MapsId("claimSet")
    @ManyToOne
    @JoinColumn(name = "claim_set_id", nullable = false)
    private ClaimSetJpaEntity claimSet;

    @MapsId("claimValue")
    @ManyToOne
    @JoinColumn(name = "claim_value_id", nullable = false)
    private ClaimValueJpaEntity claimValue;

    @Column(name = "created_date")
    private LocalDateTime createdDate = LocalDateTime.now();

    public ClaimSetClaimValuesJpaEntity() {}

    public ClaimSetClaimValuesJpaEntity(ClaimSetJpaEntity claimSet, ClaimValueJpaEntity claimValue) {
        this.claimSet = claimSet;
        this.claimValue = claimValue;
        this.id = new ClaimSetClaimValueId(claimSet.getId(), claimValue.getId());
        this.createdDate = LocalDateTime.now();
    }

    public ClaimSetClaimValueId getId() {
        return id;
    }

    public void setId(ClaimSetClaimValueId id) {
        this.id = id;
    }

    public ClaimSetJpaEntity getClaimSet() {
        return claimSet;
    }

    public void setClaimSet(ClaimSetJpaEntity claimSet) {
        this.claimSet = claimSet;
    }

    public ClaimValueJpaEntity getClaimValue() {
        return claimValue;
    }

    public void setClaimValue(ClaimValueJpaEntity claimValue) {
        this.claimValue = claimValue;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }
}