package com.vusecurity.auth.scim.api.controller;

import static com.vusecurity.auth.authorization.domain.PermissionNames.BUSINESS_CREATE;
import static com.vusecurity.auth.authorization.domain.PermissionNames.BUSINESS_DELETE;
import static com.vusecurity.auth.authorization.domain.PermissionNames.BUSINESS_READ;
import static com.vusecurity.auth.authorization.domain.PermissionNames.BUSINESS_UPDATE;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.vusecurity.auth.contracts.api.v1.dto.scimConfig.CreateScimMapperRequest;
import com.vusecurity.auth.contracts.api.v1.dto.scimConfig.ScimFilterMapperRequest;
import com.vusecurity.auth.contracts.api.v1.dto.scimConfig.ScimMapperResponse;
import com.vusecurity.auth.scim.application.command.CreateScimMapperCommand;
import com.vusecurity.auth.scim.application.command.UpdateScimMapperCommand;
import com.vusecurity.auth.scim.application.command.UpdateScimMapperFilterCommand;
import com.vusecurity.auth.scim.application.handler.CreateScimMapperHandler;
import com.vusecurity.auth.scim.application.handler.DeleteScimMapperHandler;
import com.vusecurity.auth.scim.application.handler.GetScimMapperHandler;
import com.vusecurity.auth.scim.application.handler.UpdateScimMapperHandler;
import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimToClaimMappingJpaEntity;
import com.vusecurity.auth.scim.mapper.ScimMapperDtoMapper;
import com.vusecurity.auth.shared.config.aspect.Oauth2Authorize;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

import java.util.List;
import java.util.UUID;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;



@RestController
@RequestMapping("${app.scim.context-path}/scim/mappers")
@Tag(name = "Scim Mappers", description = "Scim mappers management operations")
public class ScimMappersController {

    private final CreateScimMapperHandler createScimMapperHandler;
    private final UpdateScimMapperHandler updateScimMapperHandler;
    private final GetScimMapperHandler getScimMapperHandler;
    private final DeleteScimMapperHandler deleteScimMapperHandler;

    public ScimMappersController (CreateScimMapperHandler createScimMapperHandler, UpdateScimMapperHandler updateScimMapperHandler,
            GetScimMapperHandler getScimMapperHandler, DeleteScimMapperHandler deleteScimMapperHandler){
        this.createScimMapperHandler = createScimMapperHandler;
        this.updateScimMapperHandler = updateScimMapperHandler;
        this.getScimMapperHandler = getScimMapperHandler;
        this.deleteScimMapperHandler = deleteScimMapperHandler;
    }

    @Operation(summary = "Create SCIM mapping for a business",
            description = "Creates SCIM mapping for a business and the SCIM User resource.")
    @ApiResponse(responseCode = "201", description = "Mapper created successfully", content = @Content(schema = @Schema(implementation = ScimMapperResponse.class)))
    @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors")
    @ApiResponse(responseCode = "401", description = "Authentication required")
    @ApiResponse(responseCode = "403", description = "Access denied")
    @ApiResponse(responseCode = "409", description = "Mapper for the same business, claimSet and claimDefinition already exists")
    @PostMapping({""})
    @ResponseStatus(HttpStatus.CREATED)
    @Oauth2Authorize(permission = BUSINESS_CREATE)
    public ScimMapperResponse createMapper(@Valid @RequestBody CreateScimMapperRequest request) {

        CreateScimMapperCommand createCommand = new CreateScimMapperCommand(
            request.getBusinessId(),
            request.getFilters()
        );
        
        List<ScimToClaimMappingJpaEntity> scimMapper = createScimMapperHandler.createMapper(createCommand);
        ScimMapperResponse response = ScimMapperDtoMapper.toResponse(scimMapper);
        
        return response;
    }

    @Operation(summary = "Replaces a SCIM Mapper",
            description = "Replaces all filters for SCIM mapper by business id")
    @ApiResponse(responseCode = "200", description = "Mapper updated successfully", content = @Content(schema = @Schema(implementation = ScimMapperResponse.class)))
    @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors")
    @ApiResponse(responseCode = "401", description = "Authentication required")
    @ApiResponse(responseCode = "403", description = "Access denied")
    @ApiResponse(responseCode = "409", description = "Filter with same business, claimSet and claimDefinition already exists")
    @PutMapping("/business/{id}")
    @Oauth2Authorize(permission = BUSINESS_UPDATE)
    public ScimMapperResponse replaceMapper(@PathVariable(value = "id") UUID businessId, @Valid @RequestBody List<ScimFilterMapperRequest> filters) {
        UpdateScimMapperCommand replaceCommand = new UpdateScimMapperCommand(businessId, filters);

        List<ScimToClaimMappingJpaEntity> scimMapper = updateScimMapperHandler.updateMapper(replaceCommand);

        ScimMapperResponse response = ScimMapperDtoMapper.toResponse(scimMapper);
        
        return response;
    }

    @Operation(summary = "Replaces a filter",
            description = "Replaces a filter for SCIM mapper by its own id")
    @ApiResponse(responseCode = "200", description = "Mapper updated successfully", content = @Content(schema = @Schema(implementation = ScimMapperResponse.class)))
    @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors")
    @ApiResponse(responseCode = "401", description = "Authentication required")
    @ApiResponse(responseCode = "403", description = "Access denied")
    @ApiResponse(responseCode = "409", description = "Filter with same business, claimSet and claimDefinition already exists")
    @PutMapping("/filter/{id}")
    @Oauth2Authorize(permission = BUSINESS_UPDATE)
    public ScimMapperResponse updateByFilterId(@PathVariable(value = "id") UUID filterId,@Valid @RequestBody ScimFilterMapperRequest definition) {
        UpdateScimMapperFilterCommand updateCommand = new UpdateScimMapperFilterCommand(filterId, definition);

        ScimToClaimMappingJpaEntity scimMapper = updateScimMapperHandler.updateFilter(updateCommand);

        ScimMapperResponse response = ScimMapperDtoMapper.toResponse(scimMapper);
        
        return response;
    }


    @Operation(
            summary = "Get a SCIM Mapper",
            description = "Retrieves the SCIM Mapper of a business with it's filters."
    )
    @ApiResponse(responseCode = "200", description = "Successful retrieval", content = @Content(schema = @Schema(implementation = ScimMapperResponse.class)))
    @ApiResponse(responseCode = "400", description = "Invalid request parameters")
    @ApiResponse(responseCode = "401", description = "Authentication required")
    @ApiResponse(responseCode = "403", description = "Access denied")
    @GetMapping({"/business/{id}"})
    @Oauth2Authorize(permission = BUSINESS_READ)
    public ScimMapperResponse getByBusinessId(@PathVariable(value = "id") UUID businessId) {

        List<ScimToClaimMappingJpaEntity> scimMapper = getScimMapperHandler.getMapper(businessId);

        ScimMapperResponse response = ScimMapperDtoMapper.toResponse(scimMapper);

        return response;
    }

    @Operation(
            summary = "Get a filter",
            description = "Retrieves the filter of a SCIM Mapper based on it's id."
    )
    @ApiResponse(responseCode = "200", description = "Successful retrieval", content = @Content(schema = @Schema(implementation = ScimMapperResponse.class)))
    @ApiResponse(responseCode = "400", description = "Invalid request parameters")
    @ApiResponse(responseCode = "401", description = "Authentication required")
    @ApiResponse(responseCode = "403", description = "Access denied")
    @GetMapping({"/filter/{id}"})
    @Oauth2Authorize(permission = BUSINESS_READ)
    public ScimMapperResponse getByFilterId(@PathVariable(value = "id") UUID filterId) {

        ScimToClaimMappingJpaEntity scimMapper = getScimMapperHandler.getFilter(filterId);

        ScimMapperResponse response = ScimMapperDtoMapper.toResponse(scimMapper);

        return response;
    }
    
    @Operation(
            summary = "Delete Scim Mapper",
            description = "Deletes a Scim Mapper with all it's filters based on the business id."
    )
    @ApiResponse(responseCode = "204", description = "Delete executed")
    @ApiResponse(responseCode = "400", description = "Invalid request parameters")
    @ApiResponse(responseCode = "401", description = "Authentication required")
    @ApiResponse(responseCode = "403", description = "Access denied")
    @DeleteMapping({"/business/{businessId}"})
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Oauth2Authorize(permission = BUSINESS_DELETE)
    public void deleteByBusinessId(@PathVariable(value = "businessId") UUID businessId) {
        deleteScimMapperHandler.deleteMapper(businessId);
    }

    @Operation(
            summary = "Delete filter",
            description = "Deletes a filter based on it's id."
    )
    @ApiResponse(responseCode = "204", description = "Delete executed")
    @ApiResponse(responseCode = "400", description = "Invalid request parameters")
    @ApiResponse(responseCode = "401", description = "Authentication required")
    @ApiResponse(responseCode = "403", description = "Access denied")
    @DeleteMapping({"/filter/{filterId}"})
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Oauth2Authorize(permission = BUSINESS_DELETE)
    public void deleteByFilterId(@PathVariable(value = "filterId") UUID filterId) {
        deleteScimMapperHandler.deleteFilter(filterId);
    }

}
