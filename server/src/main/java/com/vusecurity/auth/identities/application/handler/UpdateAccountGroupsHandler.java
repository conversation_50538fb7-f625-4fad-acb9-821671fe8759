package com.vusecurity.auth.identities.application.handler;

import com.vusecurity.auth.authorization.application.exception.GroupNotFoundException;
import com.vusecurity.auth.authorization.domain.model.GroupMembershipRole;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.GroupMembershipJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.GroupMembershipRepository;
import com.vusecurity.auth.identities.application.command.UpdateAccountGroupsCommand;
import com.vusecurity.auth.identities.application.exception.AccountNotFoundException;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountGroupRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class UpdateAccountGroupsHandler {

    private final AccountRepository accountRepository;
    private final AccountGroupRepository accountGroupRepository;
    private final GroupMembershipRepository groupMembershipRepository;

    @Transactional
    public void updateGroups(UpdateAccountGroupsCommand command) {
        command.validate();

        AccountJpaEntity account = accountRepository.findByIdWithGroupMemberships(command.accountId())
                .orElseThrow(() -> new AccountNotFoundException("Account not found with ID: " + command.accountId()));

        Set<UUID> desiredGroupIds = (command.groupIds() == null) ? Collections.emptySet() : new HashSet<>(command.groupIds());

        List<GroupMembershipJpaEntity> currentMemberships = account.getGroupMemberships().stream()
                .filter(GroupMembershipJpaEntity::isActive)
                .collect(Collectors.toList());

        Set<UUID> currentGroupIds = currentMemberships.stream()
                .map(membership -> membership.getGroup().getId())
                .collect(Collectors.toSet());

        List<GroupMembershipJpaEntity> membershipsToDeactivate = currentMemberships.stream()
                .filter(membership -> !desiredGroupIds.contains(membership.getGroup().getId()))
                .collect(Collectors.toList());

        for (GroupMembershipJpaEntity membership : membershipsToDeactivate) {
            membership.setActive(false);
        }

        Set<UUID> groupIdsToAdd = new HashSet<>(desiredGroupIds);
        groupIdsToAdd.removeAll(currentGroupIds);

        if (!groupIdsToAdd.isEmpty()) {
            List<AccountGroupJpaEntity> groupsToAdd = accountGroupRepository.findAllById(groupIdsToAdd);
            if (groupsToAdd.size() != groupIdsToAdd.size()) {
                throw new GroupNotFoundException("One or more groups to add were not found.");
            }

            for (AccountGroupJpaEntity group : groupsToAdd) {
                Optional<GroupMembershipJpaEntity> existingMembership = groupMembershipRepository
                        .findByAccountIdAndGroupId(account.getId(), group.getId());

                if (existingMembership.isPresent()) {
                    existingMembership.get().setActive(true);
                    groupMembershipRepository.save(existingMembership.get());
                } else {

                    boolean hasActiveMembers = groupMembershipRepository.existsByGroupIdAndIsActive(group.getId());
                    GroupMembershipRole role = hasActiveMembers ? GroupMembershipRole.MEMBER : GroupMembershipRole.OWNER;
                    
                    GroupMembershipJpaEntity newMembership = new GroupMembershipJpaEntity();
                    newMembership.setAccount(account);
                    newMembership.setGroup(group);
                    newMembership.setMembershipRole(role);
                    newMembership.setActive(true);
                    
                    groupMembershipRepository.save(newMembership);
                }
            }
        }

        if (!membershipsToDeactivate.isEmpty()) {
            groupMembershipRepository.saveAll(membershipsToDeactivate);
        }

        accountRepository.save(account);
    }
}