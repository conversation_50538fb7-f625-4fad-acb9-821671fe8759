package com.vusecurity.auth.authorization.application.command;

import java.util.Set;
import java.util.UUID;

public record ManageRolePermissionsCommand(
        UUID roleId,
        Set<UUID> permissionIds,
        PermissionOperation operation
) {
    public enum PermissionOperation {
        ADD, REMOVE, REPLACE
    }

    public void validate() {
        if (roleId == null) {
            throw new IllegalArgumentException("roleId is required");
        }
        if (operation == null) {
            throw new IllegalArgumentException("operation is required");
        }
        if (permissionIds == null || permissionIds.isEmpty()) {
            throw new IllegalArgumentException("permissionIds cannot be null or empty");
        }
    }
}
