package com.vusecurity.auth.identities.application.exception;

import java.util.UUID;

public class IdentityProviderNotFoundException extends RuntimeException {
    public IdentityProviderNotFoundException(UUID identityProviderId) {
        super("Identity provider not found: " + identityProviderId);
    }
    public IdentityProviderNotFoundException(String message) {
        super(message);
    }
    public IdentityProviderNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
} 