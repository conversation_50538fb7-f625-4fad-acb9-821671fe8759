{"schemas": ["urn:sap:params:scim:schemas:extension:sac:2.0:user-custom-parameters", "urn:ietf:params:scim:schemas:core:2.0:User", "urn:ietf:params:scim:schemas:extension:enterprise:2.0:User"], "userName": "TEST", "name": {"familyName": "test", "givenName": "test", "formatted": "test"}, "displayName": "test", "preferredLanguage": "en", "active": true, "emails": [{"value": "<EMAIL>", "type": "work", "primary": true}], "groups": [], "roles": [{"value": "PROFILE:sap.epm:Admin", "display": "Admin", "primary": true}], "urn:sap:params:scim:schemas:extension:sac:2.0:user-custom-parameters": {"dataAccessLanguage": "en", "systemNotificationsEmailOptIn": false, "numberFormatting": "1,234.56", "timeFormatting": "H:mm:ss", "isConcurrent": false, "dateFormatting": "MMM d, yyyy", "marketingEmailOptIn": false, "cleanUpNotificationsNumberOfDays": 0, "idpUserId": "<EMAIL>"}, "urn:ietf:params:scim:schemas:extension:enterprise:2.0:User": {"manager": {"value": "", "$ref": "/api/v1/scim2/Users/"}}}