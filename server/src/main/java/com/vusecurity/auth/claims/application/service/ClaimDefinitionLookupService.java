package com.vusecurity.auth.claims.application.service;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.service.ClaimSetDefinitionMappingService;
import com.vusecurity.auth.claims.mapper.ClaimDefinitionDtoMapper;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.BusinessIdentifierClaimSetResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import com.vusecurity.auth.contracts.enums.AccountType;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.UUID;

/**
 * Service for looking up claim definitions.
 * Provides read-only operations for claim definition retrieval.
 */
@Service
@RequiredArgsConstructor
public class ClaimDefinitionLookupService {

    private static final Logger logger = LoggerFactory.getLogger(ClaimDefinitionLookupService.class);

    private final ClaimSetRepository claimSetRepository;
    private final ClaimSetDefinitionMappingService mappingService;
    private final ClaimDefinitionDtoMapper claimDefinitionDtoMapper;

    /**
     * Get the identifier claim set with its claim definitions for a business and account type.
     *
     * This method finds the claim set where:
     * - claim_set.business_id = businessId
     * - claim_set.account_type = accountType
     * - claim_set.is_identifier = true
     *
     * Since there can be at most one identifier claim set per business/account type combination,
     * this returns a single response containing the claim set info and its claim definitions.
     *
     * @param businessId the business ID
     * @param accountType the account type
     * @return business identifier claim set response with claim set info and claim definitions
     * @throws NoSuchElementException if no identifier claim set is found for the business and account type
     * @throws IllegalArgumentException if accountType is not valid
     */
    @Transactional(readOnly = true)
    public BusinessIdentifierClaimSetResponse getIdentifierClaimSet(UUID businessId, AccountType accountType) {
        logger.debug("Getting identifier claim set for business: {} and account type: {}", businessId, accountType);

        if (businessId == null) {
            throw new IllegalArgumentException("businessId cannot be null");
        }

        if (accountType == null) {
            throw new IllegalArgumentException("accountType cannot be null");
        }

        // Find the identifier claim set
        ClaimSetJpaEntity claimSet = claimSetRepository.findByBusinessIdAndAccountTypeAndIsIdentifier(
                businessId, accountType, true)
                .orElseThrow(() -> {
                    logger.debug("No identifier ClaimSet found for business: {} and account type: {}", businessId, accountType);
                    return new NoSuchElementException("No identifier ClaimSet found for business: " + businessId + " and account type: " + accountType);
                });

        // Get claim definitions for this claim set (ordered)
        List<ClaimDefinitionJpaEntity> claimDefinitions = mappingService.getClaimDefinitionsForClaimSet(claimSet);

        // Build the response
        BusinessIdentifierClaimSetResponse response = new BusinessIdentifierClaimSetResponse();

        // Set claim set info (without claim definitions to avoid circular references)
        BusinessIdentifierClaimSetResponse.ClaimSetInfo claimSetInfo = new BusinessIdentifierClaimSetResponse.ClaimSetInfo()
                .setId(claimSet.getId())
                .setBusinessId(claimSet.getBusiness().getId())
                .setAccountType(claimSet.getAccountType())
                .setIsIdentifier(claimSet.getIsIdentifier())
                .setName(claimSet.getName())
                .setDescription(claimSet.getDescription())
                .setLookupStrategy(claimSet.getLookupStrategy());

        response.setAudit(new BaseResponse.AuditInfo()
                .setCreatedAt(claimSet.getCreatedAt())
                .setUpdatedAt(claimSet.getUpdatedAt())
                .setCreatedBy(claimSet.getCreatedBy())
                .setUpdatedBy(claimSet.getUpdatedBy()));

        response.setClaimSet(claimSetInfo);

        // Set claim definitions (using summary response to avoid circular references)
        List<com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition.ClaimDefinitionSummaryResponse> claimDefinitionSummaries =
            claimDefinitions.stream()
                .map(claimDefinitionDtoMapper::toSummaryResponse)
                .toList();

        response.setClaimDefinitions(claimDefinitionSummaries);

        logger.debug("Found identifier claim set '{}' with {} claim definitions for business: {} and account type: {}",
                claimSet.getName(), claimDefinitions.size(), businessId, accountType);
        return response;
    }

    @Transactional(readOnly = true)
    public List<BusinessIdentifierClaimSetResponse> getBusinessAndAccountTypeClaimSet(UUID businessId, AccountType accountType) {
        logger.debug("Getting identifier claim set for business: {} and account type: {}", businessId, accountType);

        if (businessId == null) {
            throw new IllegalArgumentException("businessId cannot be null");
        }

        if (accountType == null) {
            throw new IllegalArgumentException("accountType cannot be null");
        }

        // Find the identifier claim set
        List<ClaimSetJpaEntity> claimSetList = claimSetRepository.findByBusinessIdAndAccountType(businessId, accountType);
        if (claimSetList.isEmpty()){
            logger.debug("No identifier ClaimSet found for business: {} and account type: {}", businessId, accountType);
            throw new NoSuchElementException("No identifier ClaimSet found for business: " + businessId + " and account type: " + accountType);
        }

        List<BusinessIdentifierClaimSetResponse> responseList = new ArrayList<>();
        // Get claim definitions for this claim set (ordered)
        for (ClaimSetJpaEntity claimSet : claimSetList) {
            List<ClaimDefinitionJpaEntity> claimDefinitions = mappingService.getClaimDefinitionsForClaimSet(claimSet);

            // Build the response
            BusinessIdentifierClaimSetResponse response = new BusinessIdentifierClaimSetResponse();

            // Set claim set info (without claim definitions to avoid circular references)
            BusinessIdentifierClaimSetResponse.ClaimSetInfo claimSetInfo = new BusinessIdentifierClaimSetResponse.ClaimSetInfo()
                    .setId(claimSet.getId())
                    .setBusinessId(claimSet.getBusiness().getId())
                    .setAccountType(claimSet.getAccountType())
                    .setIsIdentifier(claimSet.getIsIdentifier())
                    .setName(claimSet.getName())
                    .setDescription(claimSet.getDescription())
                    .setLookupStrategy(claimSet.getLookupStrategy());

            response.setAudit(new BaseResponse.AuditInfo()
                    .setCreatedAt(claimSet.getCreatedAt())
                    .setUpdatedAt(claimSet.getUpdatedAt())
                    .setCreatedBy(claimSet.getCreatedBy())
                    .setUpdatedBy(claimSet.getUpdatedBy()));

            response.setClaimSet(claimSetInfo);

            // Set claim definitions (using summary response to avoid circular references)
            List<com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition.ClaimDefinitionSummaryResponse> claimDefinitionSummaries =
                claimDefinitions.stream()
                    .map(claimDefinitionDtoMapper::toSummaryResponse)
                    .toList();

            response.setClaimDefinitions(claimDefinitionSummaries);

            logger.debug("Found identifier claim set '{}' with {} claim definitions for business: {} and account type: {}",
                claimSet.getName(), claimDefinitions.size(), businessId, accountType);

            responseList.add(response);
        }
        
        return responseList;
    }
}
