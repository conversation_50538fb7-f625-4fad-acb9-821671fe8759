package com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition;

import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.ClaimSetSummaryResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import com.vusecurity.auth.contracts.enums.ClaimType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "Claim definition information response")
public class ClaimDefinitionResponse extends BaseResponse {
    @Schema(description = "Unique identifier of the claim definition",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    @Schema(description = "Unique code identifier for the claim definition",
            example = "email_address")
    private String code;

    @Schema(description = "Human-readable name for the claim definition",
            example = "Email Address")
    private String name;

    @Schema(description = "Detailed description of what this claim represents",
            example = "Primary email address of the user")
    private String description;

    @Schema(description = "Type of the claim",
            example = "USER_DEFINED | SYSTEM_DEFINED")
    private ClaimType claimType;

    @Schema(description = "Data type of the claim value",
            example = "STRING")
    private DataTypeEnum dataType;

    @Schema(description = "Regular expression or format specification for validation",
            example = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
    private String dataFormat;

    @Schema(description = "Reference to another claim definition if this is a list type",
            example = "f9fc0a64-56fc-4477-87a7-8e525505abce")
    private UUID isAListOf;

    @Schema(description = "List of claim sets that include this claim definition")
    private List<ClaimSetSummaryResponse> claimSets;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        ClaimDefinitionResponse that = (ClaimDefinitionResponse) o;
        return Objects.equals(id, that.id) &&
               Objects.equals(code, that.code) &&
               Objects.equals(name, that.name) &&
               Objects.equals(description, that.description) &&
               claimType == that.claimType &&
               dataType == that.dataType &&
               Objects.equals(dataFormat, that.dataFormat) &&
               Objects.equals(isAListOf, that.isAListOf) &&
               Objects.equals(claimSets, that.claimSets);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), id, code, name, description, claimType, dataType, dataFormat, isAListOf, claimSets);
    }
}
