package com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import jakarta.persistence.*;

@Entity
@Table(name = "claim_value_references")
public class ClaimValueReferencesJpaEntity extends AbstractEntity {

    @ManyToOne
    @JoinColumn(name = "source_claim_value_id")
    private ClaimValueJpaEntity sourceClaimValue;
    
    @ManyToOne
    @JoinColumn(name = "referenced_claim_set_id")
    private ClaimSetJpaEntity referencedClaimSet;
    
    @ManyToOne
    @JoinColumn(name = "referenced_claim_definition_id")
    private ClaimDefinitionJpaEntity referencedClaimDefinition;
    
    @Column(name = "sequence_order")
    private Integer sequenceOrder;

    // Constructors
    public ClaimValueReferencesJpaEntity() {}
    
    public ClaimValueReferencesJpaEntity(ClaimValueJpaEntity sourceClaimValue,
                                       ClaimSetJpaEntity referencedClaimSet,
                                       ClaimDefinitionJpaEntity referencedClaimDefinition,
                                       Integer sequenceOrder) {
        this.sourceClaimValue = sourceClaimValue;
        this.referencedClaimSet = referencedClaimSet;
        this.referencedClaimDefinition = referencedClaimDefinition;
        this.sequenceOrder = sequenceOrder;
    }
    
    // Getters and setters

    public ClaimValueJpaEntity getSourceClaimValue() { return sourceClaimValue; }
    public void setSourceClaimValue(ClaimValueJpaEntity sourceClaimValue) { this.sourceClaimValue = sourceClaimValue; }
    
    public ClaimSetJpaEntity getReferencedClaimSet() { return referencedClaimSet; }
    public void setReferencedClaimSet(ClaimSetJpaEntity referencedClaimSet) { this.referencedClaimSet = referencedClaimSet; }
    
    public ClaimDefinitionJpaEntity getReferencedClaimDefinition() { return referencedClaimDefinition; }
    public void setReferencedClaimDefinition(ClaimDefinitionJpaEntity referencedClaimDefinition) { this.referencedClaimDefinition = referencedClaimDefinition; }
    
    public Integer getSequenceOrder() { return sequenceOrder; }
    public void setSequenceOrder(Integer sequenceOrder) { this.sequenceOrder = sequenceOrder; }
}