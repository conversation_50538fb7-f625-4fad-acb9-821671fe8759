# Auth Server Client SDK

This SDK provides a Java client for interacting with the Auth Server's Account management APIs.

## Features

- Account CRUD operations
- Account identification by claims
- **API key authentication for inter-service communication (internal services only)**
- **Bearer token authentication for user-level requests**
- Spring Boot auto-configuration support
- Comprehensive error handling

## Installation

Add the following dependency to your `pom.xml`:

```xml
<dependency>
    <groupId>com.vusecurity</groupId>
    <artifactId>auth-client</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

## Configuration

### Authentication Methods

The SDK supports two authentication methods:

1. **API Key Authentication** - For inter-service communication (internal services only)
2. **Bearer Token Authentication** - For user-level requests

### Spring Boot Applications

#### Configuration for API Key Authentication (Inter-service Communication)
Add the following configuration to your `application.yml`:

```yaml
auth:
  server:
    client:
      base-url: http://localhost:8082
      api-key: your-api-key-here  # For inter-service communication only
      context-path: /api/v1
      connect-timeout: 5000
      read-timeout: 10000
```

#### Configuration for Bearer Token Authentication (User Requests)
Add the following configuration to your `application.yml`:

```yaml
auth:
  server:
    client:
      base-url: http://localhost:8082
      bearer-token: your-bearer-token-here  # For user-level requests
      context-path: /api/v1
      connect-timeout: 5000
      read-timeout: 10000
```

The SDK will be auto-configured and you can inject `AccountsClient` directly:

```java
@Autowired
private AccountsClient accountsClient;
```

### Manual Configuration

For non-Spring Boot applications or manual configuration:

```java
// Using configuration object with API key (inter-service communication)
AuthServerClientConfiguration config = new AuthServerClientConfiguration();
config.setBaseUrl("http://localhost:8082");
config.setApiKey("your-api-key");  // For inter-service communication only
config.setContextPath("/api/v1");
AccountsClient client = new AccountsClient(config);

// Using configuration object with bearer token (user requests)
AuthServerClientConfiguration config = new AuthServerClientConfiguration();
config.setBaseUrl("http://localhost:8082");
config.setBearerToken("your-bearer-token");  // For user-level requests
config.setContextPath("/api/v1");
AccountsClient client = new AccountsClient(config);

// Factory methods for easy creation (recommended)
AccountsClient apiKeyClient = AccountsClient.withApiKey("http://localhost:8082", "your-api-key");
AccountsClient bearerTokenClient = AccountsClient.withBearerToken("http://localhost:8082", "your-bearer-token");

// Constructor with explicit authentication type
AccountsClient apiKeyClient = new AccountsClient("http://localhost:8082", "your-api-key", AuthenticationType.API_KEY);
AccountsClient bearerTokenClient = new AccountsClient("http://localhost:8082", "your-bearer-token", AuthenticationType.BEARER_TOKEN);

// Legacy constructor (API key authentication for inter-service communication)
AccountsClient client = new AccountsClient("http://localhost:8082", "your-api-key");
```

## Usage Examples

### Get All Accounts

```java
// Returns JSON string response with pagination data
String accountsJson = accountsClient.getAllAccounts(1, 10, null);
```

### Create Account

```java
CreateAccountRequest request = new CreateAccountRequest();
request.setIdentityId(identityId);
request.setBusinessId(businessId);
request.setIdentityProviderId(providerId);
request.setAccountType(AccountType.WORKFORCE);
request.setLifecycleState(AccountLifecycleState.ACTIVE);

AccountResponse account = accountsClient.createAccount(request);
```

### Get Account by ID

```java
AccountResponse account = accountsClient.getAccountById(accountId);
```

### Update Account

```java
UpdateAccountRequest request = new UpdateAccountRequest();
request.setLifecycleState(AccountLifecycleState.SUSPENDED);
request.setMetadata(Map.of("department", "Engineering"));

accountsClient.updateAccount(accountId, request);
```

### Get Accounts by Identity

```java
// Returns JSON string response with pagination data
String accountsJson = 
    accountsClient.getAccountsByIdentity(identityId, 1, 10);
```

### Identify Account by Claims

```java
List<ClaimIdentifierRequest> claims = Arrays.asList(
    new ClaimIdentifierRequest("email", "<EMAIL>"),
    new ClaimIdentifierRequest("phone", "+**********")
);

AccountResponse account = accountsClient.identifyAccount(
    businessId, 
    AccountType.CUSTOMER, 
    claims
);
```

### Get Account with Claims

```java
AccountWithClaimsResponse accountWithClaims = 
    accountsClient.getAccountWithClaims(accountId);
```

## Error Handling

The SDK throws `AuthServerClientException` for all API errors with appropriate HTTP status codes:

```java
try {
    AccountResponse account = accountsClient.getAccountById(accountId);
} catch (AuthServerClientException e) {
    HttpStatus status = e.getHttpStatus();
    String message = e.getMessage();
    // Handle error
}
```

## Authentication Methods

### API Key Authentication (Inter-service Communication)
**Important**: API keys should ONLY be used for internal inter-service communication within your infrastructure.

The SDK automatically adds the API key to all requests using the `x-apikey` header. Ensure your API key has the necessary permissions for the operations you want to perform.

Example for internal service:
```java
// For internal service-to-service communication only
AccountsClient internalClient = AccountsClient.withApiKey("http://localhost:8082", "internal-api-key");
```

### Bearer Token Authentication (User Requests)
For user-level requests, use bearer token authentication:

The SDK automatically adds the bearer token to all requests using the `Authorization: Bearer <token>` header.

Example for user requests:
```java
// For user-level requests
AccountsClient userClient = AccountsClient.withBearerToken("http://localhost:8082", "user-jwt-token");
```

**Security Notice**: Never expose API keys in client-side applications or public repositories. API keys are intended for secure server-to-server communication only.