package com.vusecurity.auth.claims.application.command;

import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class AddClaimDefinitionsToClaimSetCommandTest {

    @Test
    void validate_happy_path() {
        AddClaimDefinitionsToClaimSetCommand.ClaimDefinitionsToClaimSetCommand item =
                new AddClaimDefinitionsToClaimSetCommand.ClaimDefinitionsToClaimSetCommand(UUID.randomUUID(), 1, true);
        AddClaimDefinitionsToClaimSetCommand cmd = new AddClaimDefinitionsToClaimSetCommand(UUID.randomUUID(), List.of(item));
        assertDoesNotThrow(cmd::validate);
    }

    @Test
    void validate_throws_when_claimSetId_null() {
        AddClaimDefinitionsToClaimSetCommand.ClaimDefinitionsToClaimSetCommand item =
                new AddClaimDefinitionsToClaimSetCommand.ClaimDefinitionsToClaimSetCommand(UUID.randomUUID(), 1, true);
        AddClaimDefinitionsToClaimSetCommand cmd = new AddClaimDefinitionsToClaimSetCommand(null, List.of(item));
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("claimSetId"));
    }

    @Test
    void validate_throws_when_claimDefinitions_null() {
        AddClaimDefinitionsToClaimSetCommand cmd = new AddClaimDefinitionsToClaimSetCommand(UUID.randomUUID(), null);
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("claimDefinitionIds"));
    }

    @Test
    void validate_throws_when_claimDefinitions_empty() {
        AddClaimDefinitionsToClaimSetCommand cmd = new AddClaimDefinitionsToClaimSetCommand(UUID.randomUUID(), List.of());
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("At least one"));
    }

    @Test
    void validate_throws_when_claimDefinitions_has_null() {
        List<AddClaimDefinitionsToClaimSetCommand.ClaimDefinitionsToClaimSetCommand> claimDefinitions = new java.util.ArrayList<>();
        claimDefinitions.add(null);
        AddClaimDefinitionsToClaimSetCommand cmd = new AddClaimDefinitionsToClaimSetCommand(UUID.randomUUID(), claimDefinitions);
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("cannot contain null"));
    }
}

