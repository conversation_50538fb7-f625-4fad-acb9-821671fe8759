package com.vusecurity.auth.scim.util;

import org.apache.directory.scim.core.schema.SchemaRegistry;
import org.apache.directory.scim.spec.resources.ScimResource;
import org.apache.directory.scim.spec.schema.Schema;
import org.apache.directory.scim.spec.schema.Schema.Attribute;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * SchemaValidation class that verifies if all the Schemas of a ScimResource 
 * contain the required values for the attributes according to SCIM 2.0 specification.
 * 
 * Uses existing Apache Directory SCIMple classes:
 * - org.apache.directory.scim.spec.resources.ScimResource
 * - org.apache.directory.scim.spec.schema.Schema
 * - org.apache.directory.scim.spec.schema.Attribute
 * - org.apache.directory.scim.spec.schema.SchemaRegistry
 */
@Component
public class SchemaValidation {
    
    /**
     * Represents validation errors
     */
    public static class ValidationError {
        private String attribute;
        private String message;
        private String schemaId;
        
        public ValidationError(String attribute, String message, String schemaId) {
            this.attribute = attribute;
            this.message = message;
            this.schemaId = schemaId;
        }
        
        // Getters
        public String getAttribute() { return attribute; }
        public String getMessage() { return message; }
        public String getSchemaId() { return schemaId; }
        
        @Override
        public String toString() {
            return String.format("ValidationError{schema='%s', attribute='%s', message='%s'}", 
                               schemaId, attribute, message);
        }
    }
    
    /**
     * Validation result containing errors and success status
     */
    public static class ValidationResult {
        private boolean valid;
        private List<ValidationError> errors;
        
        public ValidationResult() {
            this.errors = new ArrayList<>();
            this.valid = true;
        }
        
        public void addError(ValidationError error) {
            this.errors.add(error);
            this.valid = false;
        }
        
        public boolean isValid() { return valid; }
        public List<ValidationError> getErrors() { return errors; }
        
        @Override
        public String toString() {
            if (valid) {
                return "ValidationResult{valid=true}";
            } else {
                return String.format("ValidationResult{valid=false, errors=%s}", errors);
            }
        }
    }
    
    private final SchemaRegistry schemaRegistry;
    private boolean idValidation = false;

    private static final String VALIDATION_ERROR_SCHEMA = "schemas";
    
    /**
     * Constructor that initializes the schema validation with schemas from a SchemaRegistry
     * @param schemaRegistry The schema registry to use for validation
     */
    @Autowired
    public SchemaValidation(SchemaRegistry schemaRegistry) {
        this.schemaRegistry = schemaRegistry;
    }

    public SchemaValidation(SchemaRegistry schemaRegistry, boolean idValidation) {
        this.schemaRegistry = schemaRegistry;
        this.idValidation = idValidation;
    }
    
    /**
     * Get the current schema registry
     * @return The current schema registry
     */
    public SchemaRegistry getSchemaRegistry() {
        return schemaRegistry;
    }

    public void enableIdValidation(){
        this.idValidation = true;
    }

    public void disableIdValidation(){
        this.idValidation = false;
    }

    public boolean getIdValidation(){
        return this.idValidation;
    }
    
    /**
     * Validate a SCIM resource against its declared schemas
     * @param resource The SCIM resource to validate
     * @return ValidationResult with validation errors if any
     * @throws IllegalStateException if no schema registry is configured
     */
    public ValidationResult validateResource(ScimResource resource) {
        ValidationResult result = new ValidationResult();
        
        if (schemaRegistry == null) {
            throw new IllegalStateException("SchemaRegistry must be configured before validation");
        }
        
        if (resource == null) {
            result.addError(new ValidationError("resource", "ScimResource cannot be null", null));
            return result;
        }
        
        List<String> schemas = resource.getSchemas().stream().toList();
        if (schemas == null || schemas.isEmpty()) {
            result.addError(new ValidationError(VALIDATION_ERROR_SCHEMA, "Resource must declare at least one schema", null));
            return result;
        }
        
        // Validate against each declared schema
        for (String schemaId : schemas) {
            try {
                Schema schema = schemaRegistry.getSchema(schemaId);
                if (schema == null) {
                    result.addError(new ValidationError(VALIDATION_ERROR_SCHEMA, 
                        "Unknown schema: " + schemaId, schemaId));
                    continue;
                }
                
                validateAgainstSchema(resource, schema, result);
            } catch (Exception e) {
                result.addError(new ValidationError(VALIDATION_ERROR_SCHEMA, 
                    "Error retrieving schema " + schemaId + ": " + e.getMessage(), schemaId));
            }
        }
        
        return result;
    }
    
    /**
     * Validate a resource against a specific schema definition
     * @param resource The SCIM resource
     * @param schema The schema to validate against
     * @param result The validation result to accumulate errors
     */
    private void validateAgainstSchema(ScimResource resource, Schema schema, ValidationResult result) {
        if (schema.getAttributes() != null) {
            for (Attribute attrDef : schema.getAttributes()) {
                validateAttribute(resource, attrDef, result, schema.getId());
            }
        }
    }
    
    /**
     * Validate a specific attribute against its definition
     * @param resource The SCIM resource
     * @param attrDef The attribute definition
     * @param result The validation result to accumulate errors
     * @param schemaId The schema ID for error reporting
     */
    private void validateAttribute(ScimResource resource, Attribute attrDef, 
                                 ValidationResult result, String schemaId) {
        String attributeName = attrDef.getName();
        Object value = getAttributeValue(resource, attributeName);
        
        // Check required attributes
        if (attrDef.isRequired() && (value == null || isEmpty(value))) {
            if (attrDef.getName().equals("id") && !idValidation){
                return;
            }
            result.addError(new ValidationError(attributeName, 
                "Required attribute '" + attributeName + "' is missing or empty", schemaId));
            return;
        }
        
        // Skip validation if attribute is not present and not required
        if (value == null) {
            return;
        }
        
        // Validate attribute type
        if (attrDef.getType() != null && !validateAttributeType(value, attrDef)) {
            result.addError(new ValidationError(attributeName, 
                "Attribute '" + attributeName + "' has invalid type. Expected: " + attrDef.getType(), schemaId));
        }
        
        // Validate multi-valued attributes
        if (attrDef.isMultiValued() && !(value instanceof List)) {
            result.addError(new ValidationError(attributeName, 
                "Multi-valued attribute '" + attributeName + "' must be an array", schemaId));
        }
        
        // Validate complex attributes (with sub-attributes)
        if (Attribute.Type.COMPLEX.equals(attrDef.getType()) && 
            attrDef.getSubAttributes() != null && !attrDef.getSubAttributes().isEmpty()) {
            validateComplexAttribute(value, attrDef, result, schemaId);
        }
        
        // Validate uniqueness constraints
        if (attrDef.getUniqueness() != null && !Attribute.Uniqueness.NONE.equals(attrDef.getUniqueness())) {
            // Note: Uniqueness validation typically requires access to other resources in the system
            // This is a placeholder for uniqueness validation logic
            validateUniqueness(value, attrDef, result, schemaId);
        }
        
        // Validate mutability constraints
        if (attrDef.getMutability() != null && Attribute.Mutability.IMMUTABLE.equals(attrDef.getMutability())) {
            // Note: Mutability validation typically requires comparing with existing resource state
            // This is a placeholder for mutability validation logic
            validateMutability(resource, attrDef, result, schemaId);
        }
    }
    
    /**
     * Get attribute value from ScimResource using a generic approach
     * This method handles all SCIM attributes dynamically without hardcoding specific types
     * @param resource The SCIM resource
     * @param attributeName The attribute name
     * @return The attribute value or null if not found
     */
    private Object getAttributeValue(ScimResource resource, String attributeName) {
        if (resource == null || attributeName == null) {
            return null;
        }
        
        // Try multiple access patterns in order of preference
        Object value = null;
        
        // 1. Try direct getter method (most common)
        value = tryGetterMethod(resource, attributeName);
        if (value != null) return value;
        
        // 2. Try generic attribute access
        value = tryGenericAttributeAccess(resource, attributeName);
        if (value != null) return value;
        
        // 3. Try extension attribute access
        value = tryExtensionAttributeAccess(resource, attributeName);
        if (value != null) return value;
        
        // 4. Try field access as last resort
        value = tryFieldAccess(resource, attributeName);
        return value;
    }
    
    /**
     * Try to access attribute via getter method
     */
    private Object tryGetterMethod(Object resource, String attributeName) {
        try {
            // Try standard getter naming convention
            String getterName = "get" + Character.toUpperCase(attributeName.charAt(0)) + 
                              (attributeName.length() > 1 ? attributeName.substring(1) : "");
            return resource.getClass().getMethod(getterName).invoke(resource);
        } catch (Exception e) {
            // Try boolean getter for boolean attributes
            try {
                String booleanGetterName = "is" + Character.toUpperCase(attributeName.charAt(0)) + 
                                         (attributeName.length() > 1 ? attributeName.substring(1) : "");
                return resource.getClass().getMethod(booleanGetterName).invoke(resource);
            } catch (Exception ex) {
                return null;
            }
        }
    }
    
    /**
     * Try to access attribute via generic getAttribute method
     */
    private Object tryGenericAttributeAccess(Object resource, String attributeName) {
        try {
            return resource.getClass().getMethod("getAttribute", String.class).invoke(resource, attributeName);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * Try to access attribute via extension mechanism
     */
    private Object tryExtensionAttributeAccess(Object resource, String attributeName) {
        try {
            return resource.getClass().getMethod("getExtension", String.class).invoke(resource, attributeName);
        } catch (Exception e) {
            try {
                // Try with class parameter for typed extensions
                return resource.getClass().getMethod("getExtension", Class.class).invoke(resource, Object.class);
            } catch (Exception ex) {
                return null;
            }
        }
    }
    
    /**
     * Try to access attribute via direct field access (last resort)
     */
    private Object tryFieldAccess(Object resource, String attributeName) {
        try {
            Class<?> clazz = resource.getClass();
            while (clazz != null) {
                try {
                    java.lang.reflect.Field field = clazz.getDeclaredField(attributeName);
                    field.setAccessible(true);
                    return field.get(resource);
                } catch (NoSuchFieldException e) {
                    clazz = clazz.getSuperclass();
                }
            }
        } catch (Exception e) {
            // Ignore and return null
        }
        return null;
    }
    
    /**
     * Validate complex attributes with sub-attributes
     * @param value The attribute value
     * @param attrDef The attribute definition
     * @param result The validation result
     * @param schemaId The schema ID
     */
    @SuppressWarnings("unchecked")
    private void validateComplexAttribute(Object value, Attribute attrDef, 
                                        ValidationResult result, String schemaId) {
        if (attrDef.isMultiValued() && value instanceof List) {
            List<Object> values = (List<Object>) value;
            for (int i = 0; i < values.size(); i++) {
                Object item = values.get(i);
                validateComplexAttributeValue(item, attrDef, result, schemaId, i);
            }
        } else {
            validateComplexAttributeValue(value, attrDef, result, schemaId, -1);
        }
    }
    
    /**
     * Validate a complex attribute value (handles both Map and SCIM objects)
     * @param value The complex attribute value
     * @param attrDef The attribute definition
     * @param result The validation result
     * @param schemaId The schema ID
     * @param index The index for multi-valued attributes (-1 for single-valued)
     */
    private void validateComplexAttributeValue(Object value, Attribute attrDef, 
                                             ValidationResult result, String schemaId, int index) {
        Map<String, Object> attributeMap = null;
        
        if (value instanceof Map) {
            attributeMap = (Map<String, Object>) value;
        } else {
            // Handle SCIM complex objects (like Meta) by converting to Map-like access
            attributeMap = convertComplexObjectToMap(value);
        }
        
        if (attributeMap != null) {
            validateComplexAttributeItem(attributeMap, attrDef, result, schemaId, index);
        } else {
            // If we can't convert to map, validate that the object at least exists for required complex attributes
            if (value == null && attrDef.isRequired()) {
                String attributePath = index >= 0 ? 
                    attrDef.getName() + "[" + index + "]" : attrDef.getName();
                result.addError(new ValidationError(attributePath, 
                    "Required complex attribute '" + attrDef.getName() + "' is missing", schemaId));
            }
        }
    }
    
    /**
     * Convert any SCIM complex object to a Map for validation
     * This generic approach works for any SCIM complex type
     * @param complexObject The complex object
     * @return Map representation or null if conversion fails
     */
    private Map<String, Object> convertComplexObjectToMap(Object complexObject) {
        if (complexObject == null) {
            return null;
        }
        
        // If it's already a Map, return it
        if (complexObject instanceof Map) {
            return (Map<String, Object>) complexObject;
        }
        
        Map<String, Object> map = new HashMap<>();
        
        try {
            // Get all methods from the object and its superclasses
            Class<?> clazz = complexObject.getClass();
            Set<java.lang.reflect.Method> allMethods = new HashSet<>();
            
            while (clazz != null && clazz != Object.class) {
                allMethods.addAll(Arrays.asList(clazz.getDeclaredMethods()));
                allMethods.addAll(Arrays.asList(clazz.getMethods()));
                clazz = clazz.getSuperclass();
            }
            
            for (java.lang.reflect.Method method : allMethods) {
                // Process getter methods
                if (isGetterMethod(method)) {
                    String attributeName = extractAttributeName(method.getName());
                    
                    if (attributeName != null && !map.containsKey(attributeName)) {
                        try {
                            method.setAccessible(true);
                            Object value = method.invoke(complexObject);
                            map.put(attributeName, value);
                        } catch (Exception e) {
                            // Skip this method if invocation fails
                        }
                    }
                }
            }
            
            return map.isEmpty() ? null : map;
            
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * Check if a method is a getter method
     * @param method The method to check
     * @return true if it's a getter method
     */
    private boolean isGetterMethod(java.lang.reflect.Method method) {
        String methodName = method.getName();
        
        // Must have no parameters
        if (method.getParameterCount() != 0) {
            return false;
        }
        
        // Must have a return type (not void)
        if (method.getReturnType() == void.class) {
            return false;
        }
        
        // Exclude common Object methods
        if (methodName.equals("getClass") || methodName.equals("hashCode") || 
            methodName.equals("toString")) {
            return false;
        }
        
        // Check for getter patterns
        if (methodName.startsWith("get") && methodName.length() > 3) {
            return true;
        }
        
        if (methodName.startsWith("is") && methodName.length() > 2 &&
            (method.getReturnType() == boolean.class || method.getReturnType() == Boolean.class)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Extract attribute name from getter method name
     * @param methodName The method name
     * @return The attribute name or null
     */
    private String extractAttributeName(String methodName) {
        String attributeName = null;
        
        if (methodName.startsWith("get") && methodName.length() > 3) {
            attributeName = methodName.substring(3);
        } else if (methodName.startsWith("is") && methodName.length() > 2) {
            attributeName = methodName.substring(2);
        }
        
        if (attributeName != null && attributeName.length() > 0) {
            // Convert first character to lowercase
            attributeName = Character.toLowerCase(attributeName.charAt(0)) + 
                          (attributeName.length() > 1 ? attributeName.substring(1) : "");
        }
        
        return attributeName;
    }
    
    /**
     * Check if a value is a primitive type
     * @param value The value to check
     * @return true if primitive, false otherwise
     */
    private boolean isPrimitiveType(Object value) {
        return value instanceof String || 
               value instanceof Number || 
               value instanceof Boolean || 
               value instanceof Character;
    }
    
    /**
     * Validate individual complex attribute item
     * @param item The complex attribute item
     * @param attrDef The attribute definition
     * @param result The validation result
     * @param schemaId The schema ID
     * @param index The index in multi-valued attribute (-1 for single-valued)
     */
    private void validateComplexAttributeItem(Map<String, Object> item, Attribute attrDef, 
                                            ValidationResult result, String schemaId, int index) {
        for (Attribute subAttr : attrDef.getSubAttributes()) {
            Object subValue = item.get(subAttr.getName());
            String attributePath = index >= 0 ? 
                attrDef.getName() + "[" + index + "]." + subAttr.getName() :
                attrDef.getName() + "." + subAttr.getName();
            
            if (subAttr.isRequired() && (subValue == null || isEmpty(subValue))) {
                result.addError(new ValidationError(attributePath, 
                    "Required sub-attribute '" + subAttr.getName() + "' is missing in complex attribute '" + attrDef.getName() + "'", 
                    schemaId));
            }
            
            if (subValue != null && subAttr.getType() != null && !validateAttributeType(subValue, subAttr)) {
                result.addError(new ValidationError(attributePath, 
                    "Sub-attribute '" + subAttr.getName() + "' has invalid type. Expected: " + subAttr.getType(), 
                    schemaId));
            }
        }
    }
    
    /**
     * Validate attribute type with generic SCIM object support
     * @param value The attribute value
     * @param attrDef The attribute definition
     * @return true if type is valid, false otherwise
     */
    private boolean validateAttributeType(Object value, Attribute attrDef) {
        if (value == null || attrDef.getType() == null) return true;
        
        Attribute.Type type = attrDef.getType();
        
        switch (type) {
            case STRING, REFERENCE:
                return value instanceof String;
            case BOOLEAN:
                return value instanceof Boolean;
            case DECIMAL:
                return value instanceof Number;
            case INTEGER:
                return value instanceof Integer || value instanceof Long;
            case DATE_TIME:
                return isValidDateTimeType(value);
            case BINARY:
                return value instanceof String; // Base64 encoded
            case COMPLEX:
                return isValidComplexType(value, attrDef);
            default:
                return true; // Unknown types are allowed
        }
    }
    
    /**
     * Check if value is a valid DateTime type
     * @param value The value to check
     * @return true if valid DateTime type
     */
    private boolean isValidDateTimeType(Object value) {
        return value instanceof String || 
               value instanceof java.util.Date || 
               value instanceof java.time.LocalDateTime || 
               value instanceof java.time.Instant ||
               value instanceof java.time.ZonedDateTime ||
               value instanceof java.time.OffsetDateTime ||
               value instanceof java.sql.Timestamp;
    }
    
    /**
     * Check if value is a valid Complex type (works for any SCIM complex object)
     * @param value The value to check
     * @param attrDef The attribute definition
     * @return true if valid complex type
     */
    private boolean isValidComplexType(Object value, Attribute attrDef) {
        // Handle multi-valued complex attributes
        if (attrDef.isMultiValued()) {
            if (value instanceof List) {
                // Check that list contains valid complex objects
                List<?> list = (List<?>) value;
                return list.stream().allMatch(this::isComplexObject);
            }
            return false;
        }
        
        // Single complex object
        return isComplexObject(value);
    }
    
    /**
     * Check if an object is a valid SCIM complex object
     * Uses generic approach that works for Meta, Name, Address, etc.
     * @param obj The object to check
     * @return true if it's a complex object
     */
    private boolean isComplexObject(Object obj) {
        if (obj == null) return false;
        
        // Maps are always valid complex objects
        if (obj instanceof Map) return true;
        
        // Primitive types are not complex
        if (isPrimitiveType(obj)) return false;
        
        // Check if object has getter methods (indicating it's a structured object)
        return hasGetterMethods(obj);
    }
    
    /**
     * Check if an object has getter methods (indicating it's a structured SCIM object)
     * @param obj The object to check
     * @return true if object has getter methods
     */
    private boolean hasGetterMethods(Object obj) {
        if (obj == null) return false;
        
        java.lang.reflect.Method[] methods = obj.getClass().getMethods();
        
        // Look for getter methods (excluding getClass)
        for (java.lang.reflect.Method method : methods) {
            String methodName = method.getName();
            if (((methodName.startsWith("get") && !methodName.equals("getClass")) ||
                methodName.startsWith("is")) && method.getParameterCount() == 0) {
                    return true;
                }
        }
        
        return false;
    }
    
    /**
     * Placeholder for uniqueness validation
     * @param value The attribute value
     * @param attrDef The attribute definition
     * @param result The validation result
     * @param schemaId The schema ID
     */
    private void validateUniqueness(Object value, Attribute attrDef, 
                                  ValidationResult result, String schemaId) {
        // Implementation would depend on access to other resources in the system
        // This is a placeholder for uniqueness validation logic
    }
    
    /**
     * Placeholder for mutability validation
     * @param resource The SCIM resource
     * @param attrDef The attribute definition
     * @param result The validation result
     * @param schemaId The schema ID
     */
    private void validateMutability(ScimResource resource, Attribute attrDef, 
                                  ValidationResult result, String schemaId) {
        // Implementation would depend on comparing with existing resource state
        // This is a placeholder for mutability validation logic
    }
    
    /**
     * Check if a value is empty
     * @param value The value to check
     * @return true if empty, false otherwise
     */
    private boolean isEmpty(Object value) {
        if (value == null) return true;
        if (value instanceof String) return ((String) value).trim().isEmpty();
        if (value instanceof List) return ((List<?>) value).isEmpty();
        if (value instanceof Map) return ((Map<?, ?>) value).isEmpty();
        return false;
    }
    
    /**
     * Check if all required schemas are available in the registry for a resource
     * @param resource The SCIM resource to check
     * @return List of missing schema IDs
     */
    public List<String> getMissingSchemas(ScimResource resource) {
        List<String> missingSchemas = new ArrayList<>();
        
        if (schemaRegistry == null || resource == null || resource.getSchemas() == null) {
            return missingSchemas;
        }
        
        for (String schemaId : resource.getSchemas()) {
            try {
                if (schemaRegistry.getSchema(schemaId) == null) {
                    missingSchemas.add(schemaId);
                }
            } catch (Exception e) {
                missingSchemas.add(schemaId);
            }
        }
        
        return missingSchemas;
    }
    
    /**
     * Validate multiple resources at once
     * @param resources List of SCIM resources to validate
     * @return Map of resource ID to validation result
     */
    public Map<String, ValidationResult> validateResources(List<ScimResource> resources) {
        Map<String, ValidationResult> results = new HashMap<>();
        
        for (int i = 0; i < resources.size(); i++) {
            ScimResource resource = resources.get(i);
            String resourceId = resource != null && resource.getId() != null ? 
                resource.getId() : "resource_" + i;
            results.put(resourceId, validateResource(resource));
        }
        
        return results;
    }
    
    /**
     * Extract the Attribute Definition from a SCIM Path
     * Supports paths like "userName", "name.givenName", "emails[type eq \"work\"].value", etc.
     * 
     * @param scimPath The SCIM path (e.g., "userName", "name.givenName", "emails.value")
     * @param resourceSchemas List of schema IDs that the resource declares
     * @return The Attribute definition or null if not found
     * @throws IllegalArgumentException if the path is invalid or schemas are not found
     */
    public Attribute getAttributeDefinitionFromPath(String scimPath, List<String> resourceSchemas) {
        if (scimPath == null || scimPath.trim().isEmpty()) {
            throw new IllegalArgumentException("SCIM path cannot be null or empty");
        }
        
        if (resourceSchemas == null || resourceSchemas.isEmpty()) {
            throw new IllegalArgumentException("Resource schemas cannot be null or empty");
        }
        
        if (schemaRegistry == null) {
            throw new IllegalStateException("SchemaRegistry must be configured");
        }
        
        // Parse the SCIM path
        ScimPathInfo pathInfo = parseScimPath(scimPath.trim());
        
        // Search through all declared schemas
        for (String schemaId : resourceSchemas) {
            try {
                Schema schema = schemaRegistry.getSchema(schemaId);
                if (schema == null) continue;
                
                Attribute attribute = findAttributeInSchema(schema, pathInfo);
                if (attribute != null) {
                    return attribute;
                }
            } catch (Exception e) {
                // Continue searching in other schemas
            }
        }
        
        return null; // Attribute not found in any schema
    }
    
    /**
     * Overloaded method that takes a ScimResource and extracts schemas from it
     * @param scimPath The SCIM path
     * @param resource The SCIM resource
     * @return The Attribute definition or null if not found
     */
    public Attribute getAttributeDefinitionFromPath(String scimPath, ScimResource resource) {
        if (resource == null || resource.getSchemas() == null) {
            throw new IllegalArgumentException("Resource and its schemas cannot be null");
        }
        
        return getAttributeDefinitionFromPath(scimPath, resource.getSchemas().stream().toList());
    }
    
    /**
     * Inner class to hold parsed SCIM path information
     */
    public static class ScimPathInfo {
        private String rootAttribute;
        private String subAttribute;
        private String filterExpression;
        private boolean isFiltered;
        private Attribute rootAttributeDefinition;
        private Attribute subAttributeDefinition;
        
        public ScimPathInfo(String rootAttribute, String subAttribute, String filterExpression, boolean isFiltered) {
            this.rootAttribute = rootAttribute;
            this.subAttribute = subAttribute;
            this.filterExpression = filterExpression;
            this.isFiltered = isFiltered;
        }
        
        public String getRootAttribute() { return rootAttribute; }
        public String getSubAttribute() { return subAttribute; }
        public String getFilterExpression() { return filterExpression; }
        public boolean isFiltered() { return isFiltered; }
        public Attribute getRootAttributeDefinition() { return this.rootAttributeDefinition; }
        public Attribute getSubAttributeDefinition() { return this.subAttributeDefinition; }
        public void setRootAttributeDefinition(Attribute rootAttributeDefinition) { this.rootAttributeDefinition = rootAttributeDefinition; }
        public void setSubAttributeDefinition(Attribute subAttributeDefinition) { this.subAttributeDefinition = subAttributeDefinition; }
    }

    /*
     * Will extract the schemas from the resource.
     */
    public ScimPathInfo getScimPathInfo(String scimPath, ScimResource resource) {
        ScimPathInfo pathInfo = this.parseScimPath(scimPath);
        pathInfo.rootAttributeDefinition = this.getAttributeDefinitionFromPath(pathInfo.rootAttribute, resource);
        if (pathInfo.subAttribute != null){
            pathInfo.subAttributeDefinition = this.getAttributeDefinitionFromPath(pathInfo.subAttribute, resource);
        }
        return pathInfo;
    }

    /*
     * Required the schemas to get the definition from.
     */
    public ScimPathInfo getScimPathInfo(String scimPath, List<String> schemaList) {
        ScimPathInfo pathInfo = this.parseScimPath(scimPath);
        pathInfo.rootAttributeDefinition = this.getAttributeDefinitionFromPath(pathInfo.rootAttribute, schemaList);
        if (pathInfo.subAttribute != null){
            pathInfo.subAttributeDefinition = this.getAttributeDefinitionFromPath(pathInfo.subAttribute, schemaList);
        }
        return pathInfo;
    }
    
    /**
     * Parse a SCIM path into its components
     * Examples:
     * - "userName" -> root: "userName", sub: null, filter: null
     * - "name.givenName" -> root: "name", sub: "givenName", filter: null
     * - "emails[type eq \"work\"].value" -> root: "emails", sub: "value", filter: "type eq \"work\""
     * - "emails.value" -> root: "emails", sub: "value", filter: null
     * 
     * @param scimPath The SCIM path to parse
     * @return ScimPathInfo object with parsed components
     */
    private ScimPathInfo parseScimPath(String scimPath) {
        String rootAttribute = null;
        String subAttribute = null;
        String filterExpression = null;
        boolean isFiltered = false;
        
        // Check for filter notation [...]
        int filterStart = scimPath.indexOf('[');
        int filterEnd = scimPath.indexOf(']');
        
        if (filterStart != -1 && filterEnd != -1 && filterEnd > filterStart) {
            // Extract filter expression
            filterExpression = scimPath.substring(filterStart + 1, filterEnd);
            isFiltered = true;
            
            // Remove filter part to process the rest
            String pathWithoutFilter = scimPath.substring(0, filterStart) + 
                                     (filterEnd + 1 < scimPath.length() ? scimPath.substring(filterEnd + 1) : "");
            scimPath = pathWithoutFilter;
        }
        
        // Split by dot to separate root and sub-attribute
        String[] parts = scimPath.split("\\.", 2);
        rootAttribute = parts[0];
        
        if (parts.length > 1 && !parts[1].isEmpty()) {
            subAttribute = parts[1];
        }
        
        return new ScimPathInfo(rootAttribute, subAttribute, filterExpression, isFiltered);
    }
    
    /**
     * Find an attribute in a schema based on parsed path information
     * @param schema The schema to search in
     * @param pathInfo The parsed path information
     * @return The Attribute definition or null if not found
     */
    private Attribute findAttributeInSchema(Schema schema, ScimPathInfo pathInfo) {
        if (schema.getAttributes() == null) {
            return null;
        }
        
        // Find the root attribute
        Attribute rootAttribute = null;
        for (Attribute attr : schema.getAttributes()) {
            if (pathInfo.getRootAttribute().equals(attr.getName())) {
                rootAttribute = attr;
                break;
            }
        }
        
        if (rootAttribute == null) {
            return null;
        }
        
        // If no sub-attribute, return the root attribute
        if (pathInfo.getSubAttribute() == null) {
            return rootAttribute;
        }
        
        // Look for sub-attribute in complex attribute
        if (Attribute.Type.COMPLEX.equals(rootAttribute.getType()) && 
            rootAttribute.getSubAttributes() != null) {
            
            for (Attribute subAttr : rootAttribute.getSubAttributes()) {
                if (pathInfo.getSubAttribute().equals(subAttr.getName())) {
                    return subAttr;
                }
            }
        }
        
        return null; // Sub-attribute not found
    }
    
    /**
     * Validate a specific attribute identified by SCIM path
     * @param resource The SCIM resource
     * @param scimPath The SCIM path to the attribute
     * @return ValidationResult for the specific attribute
     */
    public ValidationResult validateAttributeByPath(ScimResource resource, String scimPath) {
        ValidationResult result = new ValidationResult();
        
        if (resource == null) {
            result.addError(new ValidationError(scimPath, "ScimResource cannot be null", null));
            return result;
        }
        
        try {
            Attribute attrDef = getAttributeDefinitionFromPath(scimPath, resource);
            if (attrDef == null) {
                result.addError(new ValidationError(scimPath, 
                    "Attribute definition not found for path: " + scimPath, null));
                return result;
            }
            
            // Get the schema ID for error reporting
            String schemaId = findSchemaIdForAttribute(resource.getSchemas().stream().toList(), attrDef);
            
            // Validate the attribute
            validateAttribute(resource, attrDef, result, schemaId);
            
        } catch (Exception e) {
            result.addError(new ValidationError(scimPath, 
                "Error validating attribute path '" + scimPath + "': " + e.getMessage(), null));
        }
        
        return result;
    }
    
    /**
     * Find the schema ID that contains a specific attribute definition
     * @param resourceSchemas List of schema IDs
     * @param targetAttribute The attribute to find
     * @return The schema ID or null if not found
     */
    private String findSchemaIdForAttribute(List<String> resourceSchemas, Attribute targetAttribute) {
        for (String schemaId : resourceSchemas) {
            try {
                Schema schema = schemaRegistry.getSchema(schemaId);
                if (schema != null && schema.getAttributes() != null) {
                    if (containsAttribute(schema.getAttributes().stream().toList(), targetAttribute)) {
                        return schemaId;
                    }
                }
            } catch (Exception e) {
                // Continue searching
            }
        }
        return null;
    }
    
    /**
     * Check if a list of attributes contains a specific attribute (by reference or name match)
     * @param attributes The list of attributes to search
     * @param targetAttribute The attribute to find
     * @return true if found, false otherwise
     */
    private boolean containsAttribute(List<Attribute> attributes, Attribute targetAttribute) {
        for (Attribute attr : attributes) {
            // Check by reference first
            if (attr == targetAttribute) {
                return true;
            }
            
            // Check by name match
            if (attr.getName().equals(targetAttribute.getName())) {
                // For complex attributes, also check sub-attributes
                if (Attribute.Type.COMPLEX.equals(attr.getType()) && 
                    attr.getSubAttributes() != null && targetAttribute.getName().equals(attr.getName())) {
                    return true;
                }
                // For simple attributes, name match is sufficient
                if (!Attribute.Type.COMPLEX.equals(attr.getType())) {
                    return true;
                }
            }
            
            // Check in sub-attributes for complex types
            if (Attribute.Type.COMPLEX.equals(attr.getType()) && attr.getSubAttributes() != null) {
                if (containsAttribute(attr.getSubAttributes().stream().toList(), targetAttribute)) {
                    return true;
                }
            }
        }
        return false;
    }
}
