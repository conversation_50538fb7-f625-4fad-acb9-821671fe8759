package com.vusecurity.auth.contracts.api.v1.dto.scimConfig;

import java.util.UUID;

import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "SCIM mapper response")
public class ScimFilterMapperResponse extends BaseResponse {

    @Schema(description = "Id of the filter", example = "f9fc0a64-56fc-4477-87a7-8e525505abce")
    private UUID id;

    @Schema(description = "Origin claimSetId", example = "f9fc0a64-56fc-4477-87a7-8e525505abce")
    private UUID claimSetIdOrigin;

    @Schema(description = "Referenced claimSetId.", example = "f9fc0a64-56fc-4477-87a7-8e525505abce")
    private UUID claimSetIdReferenced;

    @Schema(description = "Unique identifier of the claim definition", example = "123e4567-e89b-12d3-a456-************")
    private UUID claimDefinitionId;

    @Schema(description = "SCIM Filter that represents the extracted value to be saved on a claim. e.g.",
            examples = {
                "userName",
                "name.giuveName",
                "emails[primary eq true].value"
            })
    private String filter;

    @Schema(description = "Order of the secuence", example = "1")
    private int sequenceOrder = 0;

    @Schema(description = "Description of the filter", example = "Retrieves the primary email")
    private String description;
}
