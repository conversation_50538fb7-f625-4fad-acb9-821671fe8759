package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.command.UpdateGroupCommand;
import com.vusecurity.auth.authorization.application.exception.*;
import com.vusecurity.auth.authorization.domain.model.GroupMembershipRole;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.GroupMembershipJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.GroupMembershipRepository;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.RoleRepository;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.identities.application.dto.AccountBusinessInfo;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountGroupRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UpdateGroupHandlerTest {

    @Mock private AccountGroupRepository accountGroupRepository;
    @Mock private GroupMembershipRepository groupMembershipRepository;
    @Mock private RoleRepository roleRepository;
    @Mock private AccountRepository accountRepository;

    @InjectMocks private UpdateGroupHandler updateGroupHandler;

    private UUID groupId;
    private UUID businessId;
    private UUID roleId1, roleId2;
    private UUID accountId1, accountId2, ownerAccountId;

    private AccountGroupJpaEntity existingGroup;
    private RoleJpaEntity testRole1, testRole2;
    private AccountJpaEntity testAccount1, testAccount2, testOwnerAccount;
    private AccountBusinessInfo businessInfo;

    @BeforeEach
    void setUp() {
        groupId = UUID.randomUUID();
        businessId = UUID.randomUUID();
        roleId1 = UUID.randomUUID();
        roleId2 = UUID.randomUUID();
        accountId1 = UUID.randomUUID();
        accountId2 = UUID.randomUUID();
        ownerAccountId = UUID.randomUUID();

        existingGroup = new AccountGroupJpaEntity();
        existingGroup.setId(groupId);
        existingGroup.setName("Original Name");
        existingGroup.setDescription("Original Description");
        existingGroup.setRoles(new HashSet<>());

        testRole1 = createTestRole(roleId1, "Role1", businessId);
        testRole2 = createTestRole(roleId2, "Role2", businessId);

        testAccount1 = createTestAccount(accountId1);
        testAccount2 = createTestAccount(accountId2);
        testOwnerAccount = createTestAccount(ownerAccountId);

        businessInfo = new AccountBusinessInfo(businessId, AccountType.WORKFORCE);

        // Common stubs
        when(accountGroupRepository.findById(groupId)).thenReturn(Optional.of(existingGroup));
    }

    @Test
    void shouldSuccessfullyUpdateAllGroupProperties() {
        // Given
        UpdateGroupCommand command = new UpdateGroupCommand(
                groupId, "New Name", "New Description",
                List.of(roleId1), List.of(accountId1, ownerAccountId), List.of(ownerAccountId),
                Map.of("key", "value")
        );

        when(accountGroupRepository.saveAndFlush(any(AccountGroupJpaEntity.class))).thenAnswer(i -> i.getArgument(0));
        when(accountRepository.findInfoById(accountId1)).thenReturn(Optional.of(businessInfo));
        when(accountRepository.findInfoById(ownerAccountId)).thenReturn(Optional.of(businessInfo));
        when(roleRepository.findAllById(List.of(roleId1))).thenReturn(List.of(testRole1));
        when(accountRepository.findAllById(List.of(accountId1, ownerAccountId))).thenReturn(List.of(testAccount1, testOwnerAccount));

        // When
        AccountGroupJpaEntity result = updateGroupHandler.updateGroup(command);

        // Then
        assertEquals("New Name", result.getName());
        assertEquals("New Description", result.getDescription());
        assertEquals(1, result.getRoles().size());
        assertTrue(result.getRoles().stream().anyMatch(r -> r.getId().equals(roleId1)));
        assertEquals("value", result.getMetadata().get("key"));

        verify(groupMembershipRepository).deleteAll(any());
        
        ArgumentCaptor<List<GroupMembershipJpaEntity>> membershipCaptor = ArgumentCaptor.forClass(List.class);
        verify(groupMembershipRepository).saveAll(membershipCaptor.capture());
        
        List<GroupMembershipJpaEntity> savedMemberships = membershipCaptor.getValue();
        assertEquals(2, savedMemberships.size());
        assertTrue(savedMemberships.stream().anyMatch(m -> m.getAccount().getId().equals(ownerAccountId) && m.getMembershipRole() == GroupMembershipRole.OWNER));
        assertTrue(savedMemberships.stream().anyMatch(m -> m.getAccount().getId().equals(accountId1) && m.getMembershipRole() == GroupMembershipRole.MEMBER));
    }

    @Test
    void shouldThrowGroupNotFoundException_whenGroupDoesNotExist() {
        // Given
        when(accountGroupRepository.findById(groupId)).thenReturn(Optional.empty());
        UpdateGroupCommand command = new UpdateGroupCommand(groupId, "N/A", "N/A", List.of(roleId1), List.of(), List.of(), null);

        // When & Then
        assertThrows(GroupNotFoundException.class, () -> updateGroupHandler.updateGroup(command));
    }
    
    @Test
    void shouldThrowGroupNameAlreadyExistsException_whenNameIsTaken() {
        // Given
        UpdateGroupCommand command = new UpdateGroupCommand(groupId, "Existing Name", "Desc", List.of(roleId1), List.of(), List.of(), null);
        when(roleRepository.findAllById(any())).thenReturn(List.of(testRole1));
        when(roleRepository.findById(roleId1)).thenReturn(Optional.of(testRole1));
        when(accountGroupRepository.saveAndFlush(any(AccountGroupJpaEntity.class)))
            .thenThrow(new GroupNameAlreadyExistsException("Group with name 'Existing Name' already exists"));

        // When & Then
        assertThrows(GroupNameAlreadyExistsException.class, () -> updateGroupHandler.updateGroup(command));
    }

    @Test
    void shouldThrowRoleNotFoundException_whenRoleIsMissing() {
        // Given
        UpdateGroupCommand command = new UpdateGroupCommand(groupId, "New Name", "Desc", List.of(roleId1, roleId2), List.of(), List.of(), null);
        when(roleRepository.findById(roleId1)).thenReturn(Optional.of(testRole1));
        when(roleRepository.findAllById(any())).thenReturn(List.of(testRole1)); // Only return one role

        // When & Then
        assertThrows(RoleNotFoundException.class, () -> updateGroupHandler.updateGroup(command));
    }

    @Test
    void shouldThrowAccountNotFoundException_whenAccountIsMissing() {
        // Given
        UpdateGroupCommand command = new UpdateGroupCommand(groupId, "New Name", "Desc", List.of(roleId1), List.of(accountId1), List.of(), null);
        when(accountRepository.findInfoById(accountId1)).thenReturn(Optional.empty()); // Account not found during business validation

        // When & Then
        assertThrows(AccountNotFoundException.class, () -> updateGroupHandler.updateGroup(command));
    }

    @Test
    void shouldThrowMismatchedBusinessException_forAccounts() {
        // Given
        UpdateGroupCommand command = new UpdateGroupCommand(groupId, "New Name", "Desc", List.of(), List.of(accountId1, accountId2), List.of(), null);
        when(accountRepository.findInfoById(accountId1)).thenReturn(Optional.of(businessInfo));
        when(accountRepository.findInfoById(accountId2)).thenReturn(Optional.of(new AccountBusinessInfo(UUID.randomUUID(), AccountType.WORKFORCE)));

        // When & Then
        assertThrows(MismatchedBusinessException.class, () -> updateGroupHandler.updateGroup(command));
    }
    
    @Test
    void shouldThrowMismatchedBusinessException_forRoles() {
        // Given
        UpdateGroupCommand command = new UpdateGroupCommand(groupId, "New Name", "Desc", List.of(roleId1), List.of(accountId1), List.of(), null);
        RoleJpaEntity roleFromOtherBusiness = createTestRole(roleId1, "Role1", UUID.randomUUID());

        when(accountRepository.findInfoById(accountId1)).thenReturn(Optional.of(businessInfo));
        when(roleRepository.findAllById(List.of(roleId1))).thenReturn(List.of(roleFromOtherBusiness));

        // When & Then
        assertThrows(MismatchedBusinessException.class, () -> updateGroupHandler.updateGroup(command));
    }

    @Test
    void shouldClearMemberships_whenUpdatingWithEmptyAccountList() {
        // Given
        UpdateGroupCommand command = new UpdateGroupCommand(groupId, "New Name", "Desc", List.of(roleId1), List.of(), List.of(), null);
        
        when(accountGroupRepository.saveAndFlush(any(AccountGroupJpaEntity.class))).thenAnswer(i -> i.getArgument(0));
        when(roleRepository.findById(roleId1)).thenReturn(Optional.of(testRole1));
        when(roleRepository.findAllById(any())).thenReturn(List.of(testRole1));

        // When
        updateGroupHandler.updateGroup(command);
        
        // Then
        verify(groupMembershipRepository, never()).deleteAll(any());
        verify(groupMembershipRepository, never()).saveAll(any());
    }

    private RoleJpaEntity createTestRole(UUID id, String name, UUID businessId) {
        RoleJpaEntity role = new RoleJpaEntity();
        role.setId(id);
        role.setName(name);
        role.setBusinessId(businessId);
        return role;
    }

    private AccountJpaEntity createTestAccount(UUID id) {
        AccountJpaEntity account = new AccountJpaEntity();
        account.setId(id);
        return account;
    }
} 