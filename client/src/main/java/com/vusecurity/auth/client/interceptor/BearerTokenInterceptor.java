package com.vusecurity.auth.client.interceptor;

import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;

@RequiredArgsConstructor
public class BearerTokenInterceptor implements ClientHttpRequestInterceptor {
    
    private static final Logger log = LoggerFactory.getLogger(BearerTokenInterceptor.class);
    
    private static final String AUTHORIZATION_HEADER = "Authorization";
    
    private final String bearerToken;
    
    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, 
                                       ClientHttpRequestExecution execution) throws IOException {
        log.debug("Adding Bearer token header '{}' with value: {}", 
                AUTHORIZATION_HEADER, 
                bearerToken != null ? "Bearer ***masked***" : "null");
        request.getHeaders().add(AUTHORIZATION_HEADER, "Bearer " + bearerToken);
        return execution.execute(request, body);
    }
}