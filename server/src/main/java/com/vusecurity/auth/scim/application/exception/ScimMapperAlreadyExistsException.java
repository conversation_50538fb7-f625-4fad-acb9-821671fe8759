package com.vusecurity.auth.scim.application.exception;

public class ScimMapperAlreadyExistsException extends RuntimeException {

    public ScimMapperAlreadyExistsException() {
        super("A filter already exists for the provided business, claimSet and claimDefinition");
    }

    public ScimMapperAlreadyExistsException(String businessId, String claimSetId, String claimDefinitionId) {
        super(String.format("A filter already exists for the provided business %s, claimSet %s and claimDefinition %s", businessId, claimSetId, claimDefinitionId));
    }
    public ScimMapperAlreadyExistsException(String message) {
        super(message);
    }
} 