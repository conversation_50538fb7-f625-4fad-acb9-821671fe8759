package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.CreateClaimSetCommand;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.business.domain.repositories.BusinessRepository;
import com.vusecurity.business.usecases.exceptions.BusinessNotFoundException;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CreateClaimSetHandler {

    private static final Logger logger = LoggerFactory.getLogger(CreateClaimSetHandler.class);

    private final ClaimSetRepository claimSetRepository;
    private final BusinessRepository businessRepository;

    @Transactional
    public ClaimSetJpaEntity createClaimSet(CreateClaimSetCommand cmd) {
        logger.debug("Creating claim set with command: {}", cmd);

        cmd.validate();

        var business = businessRepository.findById(cmd.businessId())
                .orElseThrow(BusinessNotFoundException::new);

        // Validate business rule: only one identifier claim set per business/account type combination
        if (Boolean.TRUE.equals(cmd.isIdentifier())) {
            var existingIdentifierClaimSet = claimSetRepository.findByBusinessIdAndAccountTypeAndIsIdentifier(
                    cmd.businessId(), cmd.accountType(), true);
            if (existingIdentifierClaimSet.isPresent()) {
                throw new IllegalArgumentException(
                        String.format("An identifier claim set already exists for business %s and account type %s",
                                cmd.businessId(), cmd.accountType()));
            }
        }

        ClaimSetJpaEntity claimSet = new ClaimSetJpaEntity(business, cmd.accountType(), cmd.isIdentifier());
        claimSet.setName(cmd.name());
        claimSet.setDescription(cmd.description());
        claimSet.setLookupStrategy(cmd.lookupStrategy());

        ClaimSetJpaEntity savedClaimSet = claimSetRepository.save(claimSet);
        logger.debug("Claim set created successfully with ID: {}", savedClaimSet.getId());

        return savedClaimSet;
    }
}
