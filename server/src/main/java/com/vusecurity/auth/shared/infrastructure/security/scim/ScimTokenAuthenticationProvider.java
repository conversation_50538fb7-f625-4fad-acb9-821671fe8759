package com.vusecurity.auth.shared.infrastructure.security.scim;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import com.vusecurity.auth.scim.application.service.ScimTokensService;

import java.util.NoSuchElementException;

/**
 * AuthenticationProvider that validates scim authentication (tokens)
 */
@Component
public class ScimTokenAuthenticationProvider implements AuthenticationProvider {

    private static final Logger logger = LoggerFactory.getLogger(ScimTokenAuthenticationProvider.class);

    private final ScimTokensService scimTokensService;

    public ScimTokenAuthenticationProvider(ScimTokensService scimTokensService) {
        this.scimTokensService = scimTokensService;
    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        logger.debug("Attempting scim tokens authentication");

        if (!(authentication instanceof ScimToken)) {
            logger.debug("Authentication token is not ScimToken");
            return null;
        }

        ScimToken token = (ScimToken) authentication;
        String tokenToVerify = authentication.getCredentials().toString();

        if (tokenToVerify == null || tokenToVerify.isEmpty()) {
            logger.debug("Missing authentication token");
            throw new BadCredentialsException("Missing authentication token");
        }

        try {
            logger.debug("Validating scim authentication for token: {}", tokenToVerify);

            // Load user details to get business and channel information
            TokenDetails userDetails = new TokenDetails(scimTokensService.findByPlainToken(tokenToVerify));

            logger.debug("Scim authentication successful for token: {}", tokenToVerify);

            // Create successful authentication token
            ScimToken successToken = new ScimToken(
                    userDetails,
                    userDetails.getAuthorities());

            return successToken;

        } catch (NoSuchElementException e) {
            logger.debug("Token not found: {}", tokenToVerify);
            throw new BadCredentialsException("Invalid token");
        } catch (BadCredentialsException e) {
            // Re-throw BadCredentialsException as-is
            throw e;
        } catch (Exception e) {
            logger.error("Scim authentication error for token: {}", tokenToVerify, e);
            throw new BadCredentialsException("Authentication failed");
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return ScimToken.class.isAssignableFrom(authentication);
    }
}
