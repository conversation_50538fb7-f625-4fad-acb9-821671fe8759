package com.vusecurity.auth.claims.infrastructure.persistence.jpa.service;

import com.vusecurity.auth.claims.application.command.ClaimSetAssignmentCommand;
import com.vusecurity.auth.claims.application.command.PatchClaimDefinitionsOnClaimSetCommand;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetDefinitionMappingJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetDefinitionMappingRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.UUID;
import java.util.ArrayList;
import java.util.Map;

/**
 * Service to manage ClaimSet to ClaimDefinition mappings with additional properties
 * (claimDefinitionOrder and enforceUniqueness).
 */
@Service
@Transactional
public class ClaimSetDefinitionMappingService {

    private final ClaimSetDefinitionMappingRepository mappingRepository;
    private final ClaimSetRepository claimSetRepository;
    private final ClaimDefinitionRepository claimDefinitionRepository;

    public ClaimSetDefinitionMappingService(ClaimSetDefinitionMappingRepository mappingRepository,
                                            ClaimSetRepository claimSetRepository,
                                            ClaimDefinitionRepository claimDefinitionRepository) {
        this.mappingRepository = mappingRepository;
        this.claimSetRepository = claimSetRepository;
        this.claimDefinitionRepository = claimDefinitionRepository;
    }

    /**
     * Add a claim definition to a claim set with optional order and uniqueness enforcement
     */
    public ClaimSetDefinitionMappingJpaEntity addClaimDefinitionToClaimSet(
            ClaimSetJpaEntity claimSet,
            ClaimDefinitionJpaEntity claimDefinition,
            Integer order,
            Boolean enforceUniqueness) {

        // Check if mapping already exists
        ClaimSetDefinitionMappingJpaEntity existing = mappingRepository.findByClaimSetIdAndClaimDefinitionId(
                claimSet.getId(), claimDefinition.getId());

        if (existing != null) {
            // Update existing mapping
            existing.setClaimDefinitionOrder(order);
            existing.setEnforceUniqueness(enforceUniqueness);
            return mappingRepository.save(existing);
        } else {
            // Create new mapping
            ClaimSetDefinitionMappingJpaEntity mapping = new ClaimSetDefinitionMappingJpaEntity(claimSet, claimDefinition);
            mapping.setClaimDefinitionOrder(order);
            mapping.setEnforceUniqueness(enforceUniqueness);
            return mappingRepository.save(mapping);
        }
    }

    /**
     * Remove a claim definition from a claim set
     */
    @Transactional
    public void removeClaimDefinitionFromClaimSet(ClaimSetJpaEntity claimSet, ClaimDefinitionJpaEntity claimDefinition) {
        mappingRepository.deleteByClaimSetIdAndClaimDefinitionId(claimSet.getId(), claimDefinition.getId());
    }

    /**
     * Get all claim definitions for a claim set, ordered by claimDefinitionOrder
     */
    public List<ClaimSetDefinitionMappingJpaEntity> getClaimDefinitionMappingsForClaimSet(ClaimSetJpaEntity claimSet) {
        return mappingRepository.findByClaimSetId(claimSet.getId());
    }

    /**
     * Get all claim definitions for a claim set (without mapping details), ordered by claimDefinitionOrder
     */
    public List<ClaimDefinitionJpaEntity> getClaimDefinitionsForClaimSet(ClaimSetJpaEntity claimSet) {
        return getClaimDefinitionMappingsForClaimSet(claimSet).stream()
                .map(ClaimSetDefinitionMappingJpaEntity::getClaimDefinition)
                .collect(Collectors.toList());
    }

    /**
     * Replace all claim definitions for a claim set
     */
    @Transactional
    public void replaceClaimDefinitionsForClaimSet(ClaimSetJpaEntity claimSet,
                                                   List<ClaimDefinitionWithOrder> claimDefinitionsWithOrder) {
        // Remove all existing mappings
        mappingRepository.deleteByClaimSetId(claimSet.getId());

        // Add new mappings
        for (ClaimDefinitionWithOrder item : claimDefinitionsWithOrder) {
            addClaimDefinitionToClaimSet(claimSet, item.claimDefinition, item.order, item.enforceUniqueness);
        }
    }

    /**
     * Clear all claim definitions from a claim set
     */
    @Transactional
    public void clearClaimDefinitionsFromClaimSet(ClaimSetJpaEntity claimSet) {
        mappingRepository.deleteByClaimSetId(claimSet.getId());
    }

    /**
     * Clear all claim set from a claim definition
     */
    @Transactional
    public void clearClaimSetFromClaimDefinitions(ClaimDefinitionJpaEntity claimDefinition) {
        mappingRepository.deleteByClaimDefinitionId(claimDefinition.getId());
    }

    /**
     * Batch add multiple claim sets to a claim definition
     */
    @Transactional
    public void batchAddClaimSetsToClaimDefinition(
            ClaimDefinitionJpaEntity claimDefinition,
            Set<UUID> claimSetIds,
            List<ClaimSetAssignmentCommand> assignments) {

        if (assignments.isEmpty()) {
            return;
        }

        // Get existing mappings
        List<ClaimSetDefinitionMappingJpaEntity> existingMappings = mappingRepository
                .findByClaimDefinitionIdAndClaimSetIdIn(claimDefinition.getId(), claimSetIds);

        Map<UUID, ClaimSetDefinitionMappingJpaEntity> existingMap = existingMappings.stream()
                .collect(Collectors.toMap(
                        mapping -> mapping.getClaimSet().getId(),
                        mapping -> mapping
                ));

        List<ClaimSetDefinitionMappingJpaEntity> toSave = new ArrayList<>();

        for (ClaimSetAssignmentCommand assignment : assignments) {
            UUID claimSetId = assignment.claimSetId();
            ClaimSetDefinitionMappingJpaEntity existing = existingMap.get(claimSetId);

            if (existing != null) {
                // Update existing mapping
                existing.setEnforceUniqueness(assignment.enforceUniqueness());
                toSave.add(existing);
            } else {
                // Create new mapping
                ClaimSetJpaEntity claimSet = claimSetRepository.getReferenceById(claimSetId);
                ClaimSetDefinitionMappingJpaEntity mapping = new ClaimSetDefinitionMappingJpaEntity(claimSet, claimDefinition);
                mapping.setEnforceUniqueness(assignment.enforceUniqueness());
                toSave.add(mapping);
            }
        }

        // Batch save all mappings
        mappingRepository.saveAll(toSave);
    }

    /**
     * Batch add multiple claim definitions to a single claim set
     */
    @Transactional
    public void batchAddClaimDefinitionsToClaimSet(
            ClaimSetJpaEntity claimSet,
            Set<UUID> claimDefinitionIds,
            List<PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand> assignments) {

        if (assignments.isEmpty()) {
            return;
        }

        // Get existing mappings
        List<ClaimSetDefinitionMappingJpaEntity> existingMappings = mappingRepository
                .findByClaimSetIdAndClaimDefinitionIdIn(claimSet.getId(), claimDefinitionIds);

        Map<UUID, ClaimSetDefinitionMappingJpaEntity> existingMap = existingMappings.stream()
                .collect(Collectors.toMap(
                        mapping -> mapping.getClaimDefinition().getId(),
                        mapping -> mapping
                ));

        List<ClaimSetDefinitionMappingJpaEntity> toSave = new ArrayList<>();

        for (PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand assignment : assignments) {
            UUID claimDefinitionId = assignment.claimDefinitionId();
            ClaimSetDefinitionMappingJpaEntity existing = existingMap.get(claimDefinitionId);

            if (existing != null) {
                // Update existing mapping
                existing.setClaimDefinitionOrder(assignment.claimDefinitionOrder());
                existing.setEnforceUniqueness(assignment.enforceUniqueness());
                toSave.add(existing);
            } else {
                // Create new mapping
                ClaimDefinitionJpaEntity claimDefinition = claimDefinitionRepository.getReferenceById(claimDefinitionId);
                ClaimSetDefinitionMappingJpaEntity mapping = new ClaimSetDefinitionMappingJpaEntity(claimSet, claimDefinition);
                mapping.setClaimDefinitionOrder(assignment.claimDefinitionOrder());
                mapping.setEnforceUniqueness(assignment.enforceUniqueness());
                toSave.add(mapping);
            }
        }

        // Batch save all mappings
        mappingRepository.saveAll(toSave);
    }

    /**
     * Helper class to pass claim definition with order and uniqueness settings
     */
    public static class ClaimDefinitionWithOrder {
        public final ClaimDefinitionJpaEntity claimDefinition;
        public final Integer order;
        public final Boolean enforceUniqueness;

        public ClaimDefinitionWithOrder(ClaimDefinitionJpaEntity claimDefinition, Integer order, Boolean enforceUniqueness) {
            this.claimDefinition = claimDefinition;
            this.order = order;
            this.enforceUniqueness = enforceUniqueness;
        }
    }
}
