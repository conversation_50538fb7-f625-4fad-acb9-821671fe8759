package com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

/**
 * Spring Data JPA repository for ClaimDefinitionJpaEntity.
 * Infrastructure-only interface for database operations.
 */
@Repository
public interface ClaimDefinitionRepository extends JpaRepository<ClaimDefinitionJpaEntity, UUID>, JpaSpecificationExecutor<ClaimDefinitionJpaEntity> {

    /**
     * Find claim definition by ID.
     * @param id the claim definition ID
     * @return the claim definition if found
     */
    Optional<ClaimDefinitionJpaEntity> findById(UUID id);

    /**
     * Find claim definition by code.
     * @param code the claim definition code
     * @return the claim definition if found
     */
    Optional<ClaimDefinitionJpaEntity> findByCode(String code);

    /**
     * Find claim definitions by name pattern.
     * @param namePattern the name pattern
     * @return list of matching claim definitions
     */
    List<ClaimDefinitionJpaEntity> findByNameContaining(String namePattern);

    /**
     * Check if claim definition exists by code.
     * @param code the claim definition code
     * @return true if exists, false otherwise
     */
    boolean existsByCode(String code);

    /**
     * Find claim definition by ID with claim set mappings eagerly loaded.
     * @param id the claim definition ID
     * @return the claim definition with claim set mappings if found
     */
    @Query("SELECT DISTINCT cd FROM ClaimDefinitionJpaEntity cd LEFT JOIN FETCH cd.claimSetMappings WHERE cd.id = :id")
    Optional<ClaimDefinitionJpaEntity> findByIdWithClaimSets(@Param("id") UUID id);


    long countByIdIn(Set<UUID> requestedIds);
}
