package com.vusecurity.auth.claims.application.command;

import java.util.List;
import java.util.UUID;

/**
 * Command for adding claim definitions to a claim set.
 */
public record AddClaimDefinitionsToClaimSetCommand(
        UUID claimSetId,
        List<ClaimDefinitionsToClaimSetCommand> claimDefinitions) {

    public void validate() {
        if (claimSetId == null) {
            throw new IllegalArgumentException("claimSetId is required");
        }
        if (claimDefinitions == null) {
            throw new IllegalArgumentException("claimDefinitionIds is required");
        }
        if (claimDefinitions.isEmpty()) {
            throw new IllegalArgumentException("At least one claim definition ID must be provided");
        }
        for (ClaimDefinitionsToClaimSetCommand claimDefinition : claimDefinitions) {
            if (claimDefinition == null) {
                throw new IllegalArgumentException("claimDefinitionIds cannot contain null values");
            }
        }
    }

    public record ClaimDefinitionsToClaimSetCommand(
            UUID claimDefinitionId,
            Integer claimDefinitionOrder,
            Boolean enforceUniqueness) {}
}
