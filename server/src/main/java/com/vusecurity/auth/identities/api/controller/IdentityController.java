package com.vusecurity.auth.identities.api.controller;

import static com.vusecurity.auth.authorization.domain.PermissionNames.IDENTITY_CREATE;
import static com.vusecurity.auth.authorization.domain.PermissionNames.IDENTITY_READ;
import static com.vusecurity.auth.authorization.domain.PermissionNames.IDENTITY_UPDATE;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.vusecurity.auth.claims.application.query.GetClaimDefinitionQuery;
import com.vusecurity.auth.claims.application.query.GetClaimSetQuery;
import com.vusecurity.auth.claims.application.query.GetClaimValueQuery;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.mapper.ClaimDefinitionDtoMapper;
import com.vusecurity.auth.claims.mapper.ClaimSetDtoMapper;
import com.vusecurity.auth.claims.mapper.ClaimValueDtoMapper;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition.ClaimDefinitionResponse;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.ClaimSetResponse;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.ClaimValueResponse;
import com.vusecurity.auth.contracts.api.v1.dto.identities.CreateIdentityRequest;
import com.vusecurity.auth.contracts.api.v1.dto.identities.IdentityAndAccountsWithClaimsResponse;
import com.vusecurity.auth.contracts.api.v1.dto.identities.IdentityResponse;
import com.vusecurity.auth.contracts.api.v1.dto.identities.IdentityWithClaimsResponse;
import com.vusecurity.auth.contracts.api.v1.dto.identities.UpdateIdentityRequest;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.identities.application.command.CreateIdentityCommand;
import com.vusecurity.auth.identities.application.command.UpdateIdentityCommand;
import com.vusecurity.auth.identities.application.handler.CreateIdentityHandler;
import com.vusecurity.auth.identities.application.handler.GetIdentityHandler;
import com.vusecurity.auth.identities.application.handler.UpdateIdentityHandler;
import com.vusecurity.auth.identities.application.query.GetIdentitiesPagedQuery;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.mapper.IdentityDtoMapper;
import com.vusecurity.auth.shared.config.aspect.Oauth2Authorize;
import com.vusecurity.auth.shared.util.IncludeParams;
import com.vusecurity.core.commons.models.PageableResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

@RestController
@SecurityRequirement(name = "oauth2 client credentials")
@Tag(name = "Identities", description = "Identity management operations")
@RequestMapping("${app.authorization.context-path}")
public class IdentityController {

        private final CreateIdentityHandler createIdentityHandler;
        private final GetIdentityHandler getIdentityHandler;
        private final UpdateIdentityHandler updateIdentityHandler;
        private final IdentityDtoMapper identityDtoMapper;
        private final GetClaimValueQuery getClaimValueQuery;
        private final ClaimValueDtoMapper claimValueDtoMapper;
        private final GetClaimSetQuery getClaimSetQuery;
        private final ClaimSetDtoMapper claimSetDtoMapper;
        private final GetClaimDefinitionQuery getClaimDefinitionQuery;
        private final ClaimDefinitionDtoMapper claimDefinitionDtoMapper;

        public IdentityController(
                        CreateIdentityHandler createIdentityHandler,
                        GetIdentityHandler getIdentityHandler,
                        UpdateIdentityHandler updateIdentityHandler,
                        IdentityDtoMapper identityDtoMapper,
                        GetClaimValueQuery getClaimValueQuery,
                        ClaimValueDtoMapper claimValueDtoMapper,
                        GetClaimSetQuery getClaimSetQuery,
                        ClaimSetDtoMapper claimSetDtoMapper,
                        GetClaimDefinitionQuery getClaimDefinitionQuery,
                        ClaimDefinitionDtoMapper claimDefinitionDtoMapper) {
                this.createIdentityHandler = createIdentityHandler;
                this.getIdentityHandler = getIdentityHandler;
                this.updateIdentityHandler = updateIdentityHandler;
                this.identityDtoMapper = identityDtoMapper;
                this.getClaimValueQuery = getClaimValueQuery;
                this.claimValueDtoMapper = claimValueDtoMapper;
                this.getClaimSetQuery = getClaimSetQuery;
                this.claimSetDtoMapper = claimSetDtoMapper;
                this.getClaimDefinitionQuery = getClaimDefinitionQuery;
                this.claimDefinitionDtoMapper = claimDefinitionDtoMapper;
        }

        private final Set<String> allowedIncludes = Set.of("business");

        @Operation(summary = "Get all identities", description = "Retrieves a paginated list of identities with optional filtering by name, identity type, lifecycle state, or general filter.")
        @ApiResponses({
                        @ApiResponse(responseCode = "200", description = "Successful retrieval", content = @Content(schema = @Schema(implementation = PageableResponse.class))),
                        @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
                        @ApiResponse(responseCode = "401", description = "Authentication required"),
                        @ApiResponse(responseCode = "403", description = "Access denied")
        })
        @GetMapping("/identities")
        @Oauth2Authorize(permission = IDENTITY_READ)
        public PageableResponse<IdentityResponse> get(@RequestParam(name = "page", defaultValue = "1") int page,
                        @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                        @RequestParam(name = "name", required = false) String name,
                        @RequestParam(name = "identityType", required = false) String identityType,
                        @RequestParam(name = "lifecycleState", required = false) String lifecycleState,
                        @RequestParam(name = "filter", required = false) String filter,
                        @RequestParam(name = "include", required = false) String include) {

                IncludeParams includes = new IncludeParams(include, allowedIncludes);

                GetIdentitiesPagedQuery query = GetIdentitiesPagedQuery.builder()
                                .page(page)
                                .pageSize(pageSize)
                                .name(name)
                                .identityType(identityType)
                                .lifecycleState(lifecycleState)
                                .filter(filter)
                                .build();

                Page<IdentityJpaEntity> identities = getIdentityHandler.getAllIdentities(query);
                return new PageableResponse<>(identities.getNumber() + 1, identities.getSize(),
                                identities.getTotalElements(),
                                identities.get().map(identity -> identityDtoMapper.toResponse(identity, includes))
                                                .toList());
        }

        @Operation(summary = "Create a new identity", description = "Creates a new identity with the specified name, type, and optional metadata. The name and identity type are required fields.")
        @ApiResponses({
                        @ApiResponse(responseCode = "201", description = "Identity created successfully", content = @Content(schema = @Schema(implementation = IdentityResponse.class))),
                        @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors"),
                        @ApiResponse(responseCode = "401", description = "Authentication required"),
                        @ApiResponse(responseCode = "403", description = "Access denied"),
                        @ApiResponse(responseCode = "409", description = "Identity with same name already exists")
        })
        @PostMapping("/identities")
        @ResponseStatus(HttpStatus.CREATED)
        @Oauth2Authorize(permission = IDENTITY_CREATE)
        public IdentityResponse create(@RequestBody @Valid CreateIdentityRequest request) {

                CreateIdentityCommand command = CreateIdentityCommand.builder()
                                .name(request.getName())
                                .identityType(request.getIdentityType().name())
                                .metadata(request.getMetadata())
                                .lifecycleState(request.getLifecycleState() != null ? request.getLifecycleState().name()
                                                : null)
                                .build();

                IdentityJpaEntity identity = createIdentityHandler.createIdentity(command);
                return identityDtoMapper.toResponse(identity);
        }

        @Operation(summary = "Get an identity by identityId", description = "Retrieves an identity by its identityId")
        @ApiResponses({
                        @ApiResponse(responseCode = "200", description = "Successful retrieval", content = @Content(schema = @Schema(implementation = IdentityResponse.class))),
                        @ApiResponse(responseCode = "404", description = "Identity not found"),
                        @ApiResponse(responseCode = "401", description = "Client is not authenticated or the credentials provided are incorrect"),
                        @ApiResponse(responseCode = "403", description = "Client authenticated, but does not have permission to access the resource, even if it has valid credentials")
        })
        @GetMapping("/identities/{identityId}")
        @Oauth2Authorize(permission = IDENTITY_READ)
        public IdentityResponse get(@PathVariable(value = "identityId") String identityId) {

                IdentityJpaEntity identity = getIdentityHandler.getIdentityById(identityId);
                return identityDtoMapper.toResponse(identity);
        }

        @Operation(summary = "Partially update an identity", description = "Allows you to update one or more attributes of an identity by its identityId")
        @ApiResponses({
                        @ApiResponse(responseCode = "204", description = "Identity updated successfully"),
                        @ApiResponse(responseCode = "404", description = "Identity not found"),
                        @ApiResponse(responseCode = "401", description = "Client is not authenticated or the credentials provided are incorrect"),
                        @ApiResponse(responseCode = "403", description = "Client authenticated, but does not have permission to access the resource, even if it has valid credentials")
        })
        @PatchMapping(path = "/identities/{identityId}")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        @Oauth2Authorize(permission = IDENTITY_UPDATE)
        public void update(@PathVariable(value = "identityId") String identityId,
                        @RequestBody @Valid UpdateIdentityRequest request) {
                UpdateIdentityCommand command = UpdateIdentityCommand.builder()
                                .identityId(identityId)
                                .name(request.getName())
                                .lifecycleState(request.getLifecycleState() != null ? request.getLifecycleState().name()
                                                : null)
                                .metadata(request.getMetadata())
                                .build();

                updateIdentityHandler.updateIdentity(command);
        }

        @GetMapping("/identities/{identityId}/claims")
        @Oauth2Authorize(permission = IDENTITY_READ)
        public IdentityWithClaimsResponse getIdentityWithClaims(@PathVariable(value = "identityId") String identityId) {

                IdentityJpaEntity identity = getIdentityHandler.getIdentityById(identityId);
                List<ClaimValueJpaEntity> claimValuesList = getClaimValueQuery
                                .getClaimValuesByOwnerTypeAndOwnerId(OwnerType.IDENTITY, UUID.fromString(identityId));

                return new IdentityWithClaimsResponse(identityDtoMapper.toResponse(identity),
                                claimValuesList.stream().map(claimValueDtoMapper::toResponse).toList());
        }

        @GetMapping("/identities/{identityId}/claims/grouped-by-business")
        @Oauth2Authorize(permission = IDENTITY_READ)
        public IdentityAndAccountsWithClaimsResponse getIdentityAndAccountsClaimsGroupedByBusiness(
                        @PathVariable(value = "identityId") String identityId) {
                final String BUSINESS = "business";

                // Get the identity Data
                IdentityJpaEntity identity = getIdentityHandler.getIdentityById(identityId);
                List<ClaimValueResponse> identityClaimValuesList = getClaimValueQuery
                                .getClaimValuesByOwnerTypeAndOwnerId(OwnerType.IDENTITY, UUID.fromString(identityId))
                                .stream().map(claimValueDtoMapper::toResponse).toList();

                List<UUID> identitiesClaimDefinitionsIds = new ArrayList<>(
                                identityClaimValuesList.stream()
                                                .map(ClaimValueResponse::getId)
                                                .collect(Collectors.toSet()));
                List<ClaimDefinitionResponse> identityClaimDefinitions = getClaimDefinitionQuery
                                .getAllClaimDefinitionsByIds(identitiesClaimDefinitionsIds)
                                .stream().map(claimDefinitionDtoMapper::toResponse).toList();

                // Get all the accounts claim values associated to the identity
                Map<UUID, List<ClaimValueResponse>> accountClaimValueList = new HashMap<>();
                Set<UUID> claimSetIds = new HashSet<>();
                for (AccountJpaEntity account : identity.getAccounts()) {
                        List<ClaimValueResponse> accountClaims = getClaimValueQuery
                                        .getClaimValuesByOwnerTypeAndOwnerId(OwnerType.ACCOUNT, account.getId())
                                        .stream().map(claimValueDtoMapper::toResponse).toList();
                        claimSetIds.addAll(accountClaims.stream().map(ClaimValueResponse::getClaimSetId).toList());
                        accountClaimValueList.put(account.getId(), accountClaims);
                }

                // Get all the claim set info (and claim definitions) associated to the business
                // and accounts
                List<ClaimSetResponse> claimSetResponses = getClaimSetQuery
                                .getAllClaimSetsByIds(claimSetIds.stream().toList())
                                .stream().map(claimSetDtoMapper::toResponse).toList();

                return new IdentityAndAccountsWithClaimsResponse(
                                identityDtoMapper.toResponse(identity, new IncludeParams(BUSINESS, allowedIncludes)),
                                identityClaimDefinitions,
                                identityClaimValuesList,
                                claimSetResponses,
                                accountClaimValueList);
        }
}
