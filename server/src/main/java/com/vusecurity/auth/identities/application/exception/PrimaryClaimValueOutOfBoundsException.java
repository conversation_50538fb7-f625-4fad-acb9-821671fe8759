package com.vusecurity.auth.identities.application.exception;

/**
 * Exception thrown when the primaryIndex is out of bounds for an array claim value.
 * This occurs when the primaryIndex is negative or greater than or equal to the size of the values array.
 */
public class PrimaryClaimValueOutOfBoundsException extends RuntimeException {

    /**
     * Constructs a new PrimaryClaimValueOutOfBoundsException with the specified primary index and array size.
     *
     * @param primaryIndex the invalid primary index
     * @param arraySize the actual size of the values array
     */
    public PrimaryClaimValueOutOfBoundsException(int primaryIndex, int arraySize) {
        super(String.format("primaryIndex (%d) is out of range for list size %d", primaryIndex, arraySize));
    }

    /**
     * Constructs a new PrimaryClaimValueOutOfBoundsException with the specified detail message.
     *
     * @param message the detail message
     */
    public PrimaryClaimValueOutOfBoundsException(String message) {
        super(message);
    }

    /**
     * Constructs a new PrimaryClaimValueOutOfBoundsException with the specified detail message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public PrimaryClaimValueOutOfBoundsException(String message, Throwable cause) {
        super(message, cause);
    }
}