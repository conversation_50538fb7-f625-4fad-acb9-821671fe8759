package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.DeleteClaimValueCommand;
import com.vusecurity.auth.claims.application.exception.DeleteClaimTypeSystemException;
import com.vusecurity.auth.claims.application.exception.ClaimValueNotFoundException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.enums.ClaimType;

import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class DeleteClaimValueHandler {

    private static final Logger logger = LoggerFactory.getLogger(DeleteClaimValueHandler.class);
    
    private final ClaimValueRepository claimValueRepository;

    @Transactional
    public void deleteClaimValue(DeleteClaimValueCommand cmd) {
        this.deleteClaimValue(cmd, false);
    }

    @Transactional
    public void deleteClaimValue(DeleteClaimValueCommand cmd, Boolean allowToDeleteSystemClaim) {
        logger.debug("Deleting claim value with command: {}", cmd);
        
        try {
            // Validate command
            cmd.validate();

            // Check if claim value exists, we need the object to get the definition type
            ClaimValueJpaEntity claimValue = claimValueRepository.findById(cmd.id())
                    .orElseThrow(() -> new ClaimValueNotFoundException(cmd.id()));
                
            if (claimValue.getClaimDefinition().getClaimType().equals(ClaimType.SYSTEM_DEFINED) && Boolean.FALSE.equals(allowToDeleteSystemClaim)){
                throw new DeleteClaimTypeSystemException("Cannot delete claim value if the definition is of type SYSTEM_DEFINED");
            }

            // Delete the claim value
            claimValueRepository.deleteById(cmd.id());
            logger.debug("Claim value deleted successfully with ID: {}", cmd.id());

        } catch (IllegalArgumentException | DeleteClaimTypeSystemException e) {
            logger.error("Validation error deleting claim value: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("Error deleting claim value", e);
            throw new RuntimeException("Failed to delete claim value: " + e.getMessage(), e);
        }
    }
}
