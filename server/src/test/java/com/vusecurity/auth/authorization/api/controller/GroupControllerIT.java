package com.vusecurity.auth.authorization.api.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.GroupMembershipRepository;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.RoleRepository;
import com.vusecurity.auth.contracts.api.v1.dto.authorization.*;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.IdentityType;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountGroupRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityProviderRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityRepository;
import com.vusecurity.auth.shared.test.BaseIntegrationTest;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.domain.repositories.BusinessRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.web.context.WebApplicationContext;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.setup.MockMvcBuilders.webAppContextSetup;

/**
 * Integration tests for GroupController covering all endpoints and edge cases.
 * Tests include group creation, retrieval, updates, deletion, member management,
 * role management, validation scenarios, and business logic constraints.
 */

@AutoConfigureWebMvc
@Testcontainers
class GroupControllerIT extends BaseIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private BusinessRepository businessRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private AccountGroupRepository accountGroupRepository;

    @Autowired
    private IdentityRepository identityRepository;

    @Autowired
    private IdentityProviderRepository identityProviderRepository;


    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    private Business bizA;
    private Business bizB;
    private RoleJpaEntity roleA;
    private RoleJpaEntity roleB;
    private AccountJpaEntity accountA;
    private AccountJpaEntity accountB;
    private AccountGroupJpaEntity groupA;

    @BeforeEach
    void setUp() {
        mockMvc = webAppContextSetup(webApplicationContext).build();

        bizA = new Business().setName("Business A").setBusinessType(com.vusecurity.business.domain.enums.BusinessTypeEnum.BUSINESS_UNIT);
        bizA.setCreatedBy("test");
        bizB = new Business().setName("Business B").setBusinessType(com.vusecurity.business.domain.enums.BusinessTypeEnum.BUSINESS_UNIT);
        bizB.setCreatedBy("test");
        bizA = businessRepository.save(bizA);
        bizB = businessRepository.save(bizB);

        IdentityProviderJpaEntity idp = identityProviderRepository.save(new IdentityProviderJpaEntity("Default"));

        IdentityJpaEntity identityA = identityRepository.save(new IdentityJpaEntity(IdentityType.PERSON, "<EMAIL>"));
        IdentityJpaEntity identityB = identityRepository.save(new IdentityJpaEntity(IdentityType.PERSON, "<EMAIL>"));

        accountA = new AccountJpaEntity(bizA, identityA, idp, AccountType.WORKFORCE);
        accountRepository.save(accountA);

        accountB = new AccountJpaEntity(bizB, identityB, idp, AccountType.WORKFORCE);
        accountRepository.save(accountB);

        roleA = new RoleJpaEntity();
        roleA.setName("Role A");
        roleA.setBusinessId(bizA.getId());
        roleA = roleRepository.save(roleA);

        roleB = new RoleJpaEntity();
        roleB.setName("Role B");
        roleB.setBusinessId(bizB.getId());
        roleB = roleRepository.save(roleB);

        groupA = new AccountGroupJpaEntity("Group A");
        groupA = accountGroupRepository.save(groupA);
    }

    @Test
    void shouldReturnConflict_WhenGroupNameAlreadyExistsInBusiness() throws Exception {
        // Given
        CreateGroupRequest createGroupRequest1 = new CreateGroupRequest();
        createGroupRequest1.setName("Duplicate Group");
        createGroupRequest1.setDescription("Description");
        createGroupRequest1.setRoleIds(List.of(roleA.getId()));

        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createGroupRequest1)))
                .andExpect(status().isCreated());

        CreateGroupRequest createGroupRequest2 = new CreateGroupRequest();
        createGroupRequest2.setName("Duplicate Group");
        createGroupRequest2.setDescription("Another Description");
        createGroupRequest2.setRoleIds(List.of(roleA.getId()));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createGroupRequest2)))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.code").value(5501))
                .andExpect(jsonPath("$.message").value("Group with name 'Duplicate Group' already exists"));
    }

    @Test
    void shouldReturnBadRequest_WhenRoleNotFound() throws Exception {
        // Given
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Test Group");
        request.setDescription("Description");
        request.setRoleIds(List.of(UUID.randomUUID()));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(5502));
    }

    @Test
    void shouldReturnBadRequest_WhenAccountNotFound() throws Exception {
        // Given
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Test Group");
        request.setDescription("Description");
        request.setRoleIds(List.of(roleA.getId()));
        request.setAccountIds(List.of(UUID.randomUUID()));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(5503));
    }

    @Test
    void shouldReturnBadRequest_WhenRoleAndAccountHaveMismatchedBusinesses() throws Exception {
        // Given
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Mismatched Group");
        request.setDescription("Description");
        request.setRoleIds(List.of(roleB.getId()));
        request.setAccountIds(List.of(accountA.getId()));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(5505))
                .andExpect(jsonPath("$.message").value("Role " + roleB.getName() + " does not belong to the same business as the accounts"));

    }

    @Test
    void shouldReturnBadRequest_WhenAccountsHaveMismatchedBusinesses() throws Exception {
        // Given
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Mismatched Group");
        request.setDescription("Description");
        request.setRoleIds(List.of(roleA.getId()));
        request.setAccountIds(List.of(accountA.getId(), accountB.getId()));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(5505))
                .andExpect(jsonPath("$.message").value("All accounts must belong to the same business"));
    }

    @Test
    void shouldReturnBadRequest_WhenRequestIsInvalid() throws Exception {
        // Given
        CreateGroupRequest request = new CreateGroupRequest();
        request.setDescription("Description");
        request.setRoleIds(List.of(roleA.getId()));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    // UpdateGroupHandler Error Tests

    @Test
    void shouldReturnNotFound_WhenUpdatingNonExistentGroup() throws Exception {
        // Given
        UUID nonExistentGroupId = UUID.randomUUID();
        UpdateGroupRequest request = new UpdateGroupRequest();
        request.setName("Updated Name");
        request.setDescription("Updated Desc");
        request.setRoleIds(List.of(roleA.getId()));

        // When & Then
        mockMvc.perform(put("/api/v1/groups/" + nonExistentGroupId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.code").value(5500));
    }

    @Test
    void shouldReturnConflict_WhenUpdatingGroupNameToExistingName() throws Exception {
        // Given
        AccountGroupJpaEntity groupB = new AccountGroupJpaEntity("Group B");
        accountGroupRepository.save(groupB);

        UpdateGroupRequest request = new UpdateGroupRequest();
        request.setName("Group B");
        request.setDescription("Updated Desc");
        request.setRoleIds(List.of(roleA.getId()));

        // When & Then
        mockMvc.perform(put("/api/v1/groups/" + groupA.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.code").value(5501));
    }

    @Test
    void shouldReturnBadRequest_WhenUpdatingGroupWithNonExistentRole() throws Exception {
        // Given
        UUID nonExistentRoleId = UUID.randomUUID();
        UpdateGroupRequest request = new UpdateGroupRequest();
        request.setName("Updated Name");
        request.setDescription("Updated Desc");
        request.setRoleIds(List.of(nonExistentRoleId));


        // When & Then
        mockMvc.perform(put("/api/v1/groups/" + groupA.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(5502));
    }

    @Test
    void shouldReturnBadRequest_WhenUpdatingGroupWithNonExistentAccount() throws Exception {
        // Given
        UUID nonExistentAccountId = UUID.randomUUID();
        UpdateGroupRequest request = new UpdateGroupRequest();
        request.setName("Updated Name");
        request.setDescription("Updated Desc");
        request.setRoleIds(List.of(roleA.getId()));
        request.setAccountIds(List.of(nonExistentAccountId));

        // When & Then
        mockMvc.perform(put("/api/v1/groups/" + groupA.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(5503));
    }

    @Test
    void shouldReturnBadRequest_WhenUpdatingGroupWithMismatchedBusinessRole() throws Exception {
        // Given
        UpdateGroupRequest request = new UpdateGroupRequest();
        request.setName("Updated Name");
        request.setDescription("Updated Desc");
        request.setRoleIds(List.of(roleA.getId(), roleB.getId()));

        // When & Then
        mockMvc.perform(put("/api/v1/groups/" + groupA.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(5505));
    }

    @Test
    void shouldReturnBadRequest_WhenUpdatingGroupWithMismatchedBusinessAccounts() throws Exception {
        // Given
        UpdateGroupRequest request = new UpdateGroupRequest();
        request.setName("Updated Name");
        request.setDescription("Updated Desc");
        request.setRoleIds(List.of(roleA.getId()));
        request.setAccountIds(List.of(accountA.getId(), accountB.getId()));

        // When & Then
        mockMvc.perform(put("/api/v1/groups/" + groupA.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(5505));
    }

    // DeleteGroupHandler Error Tests

    @Test
    void shouldReturnNotFound_WhenDeletingNonExistentGroup() throws Exception {
        // Given
        UUID nonExistentGroupId = UUID.randomUUID();

        // When & Then
        mockMvc.perform(delete("/api/v1/groups/" + nonExistentGroupId))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.code").value(5500));
    }

    @Test
    void shouldUpdateGroupMemberRoleAndReflectInGetGroup() throws Exception {
        // Step 1: Create group with accountA as MEMBER
        CreateGroupRequest createGroupRequest = new CreateGroupRequest();
        createGroupRequest.setName("Role Change Group");
        createGroupRequest.setDescription("Test group for membership role change");
        createGroupRequest.setRoleIds(List.of(roleA.getId()));
        createGroupRequest.setAccountIds(List.of(accountA.getId()));
        // No ownerAccountIds: accountA will be MEMBER

        String createResponse = mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createGroupRequest)))
                .andExpect(status().isCreated())
                .andReturn().getResponse().getContentAsString();

        GroupResponse group =
                objectMapper.readValue(createResponse, GroupResponse.class);
        UUID groupId = group.getId();

        // Step 2: GET group and verify accountA is MEMBER
        String getResponse1 = mockMvc.perform(
                                MockMvcRequestBuilders.get("/api/v1/groups/" + groupId)
                                .param("includeDetails", "true")
                                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        GroupResponse groupFetched1 =
                objectMapper.readValue(getResponse1, GroupResponse.class);
        AccountSummary member1 =
                groupFetched1.getAccounts().stream()
                        .filter(a -> a.getId().equals(accountA.getId()))
                        .findFirst().orElseThrow();
        assertEquals("MEMBER", member1.getGroupRole());

        // Step 3: PATCH group members, promote accountA to OWNER
        UpdateGroupMembersRequest patchRequest =
                new UpdateGroupMembersRequest();
        patchRequest.setAccountIds(List.of(accountA.getId()));
        patchRequest.setOwnerAccountIds(List.of(accountA.getId()));

        mockMvc.perform(MockMvcRequestBuilders.patch("/api/v1/groups/" + groupId + "/members")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(patchRequest)))
                .andExpect(status().isOk());

        // Step 4: GET group and verify accountA is OWNER
        String getResponse2 = mockMvc.perform(
                                MockMvcRequestBuilders.get("/api/v1/groups/" + groupId)
                                .param("includeDetails", "true")
                                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        GroupResponse groupFetched2 =
                objectMapper.readValue(getResponse2, GroupResponse.class);
        AccountSummary member2 =
                groupFetched2.getAccounts().stream()
                        .filter(a -> a.getId().equals(accountA.getId()))
                        .findFirst().orElseThrow();
        assertEquals("OWNER", member2.getGroupRole());
    }

    // ========================================
    // POST /groups Tests - Success Scenarios
    // ========================================

    @Test
    void shouldCreateGroupSuccessfully_WithMinimalData() throws Exception {
        // Given
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Engineering Team");
        request.setDescription("Engineering team members");
        request.setRoleIds(List.of(roleA.getId()));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.name").value("Engineering Team"))
                .andExpect(jsonPath("$.description").value("Engineering team members"))
                .andExpect(jsonPath("$.roles").isArray())
                .andExpect(jsonPath("$.roles", hasSize(1)))
                .andExpect(jsonPath("$.accounts").isArray())
                .andExpect(jsonPath("$.accounts", hasSize(0)))
                .andExpect(jsonPath("$._audit").exists())
                .andExpect(jsonPath("$._audit.createdAt").exists())
                .andExpect(jsonPath("$._audit.createdBy").exists());
    }

    @Test
    void shouldCreateGroupSuccessfully_WithAccountsAndOwners() throws Exception {
        // Given
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Management Team");
        request.setDescription("Management team with owners");
        request.setRoleIds(List.of(roleA.getId()));
        request.setAccountIds(List.of(accountA.getId()));
        request.setOwnerAccountIds(List.of(accountA.getId()));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.name").value("Management Team"))
                .andExpect(jsonPath("$.accounts").isArray())
                .andExpect(jsonPath("$.accounts", hasSize(1)))
                .andExpect(jsonPath("$.accounts[0].id").value(accountA.getId().toString()))
                .andExpect(jsonPath("$.accounts[0].groupRole").value("OWNER"));
    }

    @Test
    void shouldCreateGroupSuccessfully_WithMetadata() throws Exception {
        // Given
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("DevOps Team");
        request.setDescription("DevOps team with metadata");
        request.setRoleIds(List.of(roleA.getId()));
        request.setMetadata(Map.of(
                "department", "Engineering",
                "location", "Remote",
                "budget", 50000
        ));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.metadata.department").value("Engineering"))
                .andExpect(jsonPath("$.metadata.location").value("Remote"))
                .andExpect(jsonPath("$.metadata.budget").value(50000));
    }

    @Test
    void shouldCreateGroupSuccessfully_WithMultipleRoles() throws Exception {
        // Given
        RoleJpaEntity roleA2 = new RoleJpaEntity();
        roleA2.setName("Role A2");
        roleA2.setBusinessId(bizA.getId());
        roleA2 = roleRepository.save(roleA2);

        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Multi-Role Team");
        request.setDescription("Team with multiple roles");
        request.setRoleIds(List.of(roleA.getId(), roleA2.getId()));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.roles").isArray())
                .andExpect(jsonPath("$.roles", hasSize(2)));
    }

    // ========================================
    // POST /groups Tests - Validation Errors
    // ========================================

    @Test
    void shouldReturnBadRequest_WhenNameIsMissing() throws Exception {
        // Given
        CreateGroupRequest request = new CreateGroupRequest();
        request.setDescription("Description without name");
        request.setRoleIds(List.of(roleA.getId()));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenNameIsEmpty() throws Exception {
        // Given
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("");
        request.setDescription("Description with empty name");
        request.setRoleIds(List.of(roleA.getId()));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenNameIsTooLong() throws Exception {
        // Given
        String longName = "a".repeat(256); // Exceeds 255 character limit
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName(longName);
        request.setDescription("Description with too long name");
        request.setRoleIds(List.of(roleA.getId()));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenDescriptionIsTooLong() throws Exception {
        // Given
        String longDescription = "a".repeat(256); // Exceeds 255 character limit
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Valid Name");
        request.setDescription(longDescription);
        request.setRoleIds(List.of(roleA.getId()));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenRoleIdsIsEmpty() throws Exception {
        // Given
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Valid Name");
        request.setDescription("Valid Description");
        request.setRoleIds(List.of()); // Empty list

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenRoleIdsIsNull() throws Exception {
        // Given
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Valid Name");
        request.setDescription("Valid Description");
        request.setRoleIds(null);

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldBadRequestError_WhenOwnerAccountNotInAccountIds() throws Exception {
        // Given
        IdentityJpaEntity identity2 = identityRepository.save(new IdentityJpaEntity(IdentityType.PERSON, "<EMAIL>"));
        AccountJpaEntity account2 = new AccountJpaEntity(bizA, identity2, identityProviderRepository.findAll().get(0), AccountType.WORKFORCE);
        account2 = accountRepository.save(account2);

        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Invalid Owner Group");
        request.setDescription("Group with owner not in account list");
        request.setRoleIds(List.of(roleA.getId()));
        request.setAccountIds(List.of(accountA.getId()));
        request.setOwnerAccountIds(List.of(account2.getId())); // Owner not in accountIds

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    // ========================================
    // GET /groups Tests
    // ========================================

    @Test
    void shouldGetAllGroups_WithDefaultPagination() throws Exception {
        // Given - Create test groups
        CreateGroupRequest request1 = new CreateGroupRequest();
        request1.setName("Test Group 1");
        request1.setDescription("First test group");
        request1.setRoleIds(List.of(roleA.getId()));

        CreateGroupRequest request2 = new CreateGroupRequest();
        request2.setName("Test Group 2");
        request2.setDescription("Second test group");
        request2.setRoleIds(List.of(roleA.getId()));

        mockMvc.perform(post("/api/v1/groups")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isCreated());

        mockMvc.perform(post("/api/v1/groups")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isCreated());

        // When & Then
        mockMvc.perform(get("/api/v1/groups"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.currentPage").value(1))
                .andExpect(jsonPath("$.pageSize").value(10))
                .andExpect(jsonPath("$.totalElements").value(greaterThanOrEqualTo(2)))
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content", hasSize(greaterThanOrEqualTo(2))));
    }

    @Test
    void shouldGetAllGroups_WithCustomPagination() throws Exception {
        // Given - Create multiple test groups
        for (int i = 1; i <= 5; i++) {
            CreateGroupRequest request = new CreateGroupRequest();
            request.setName("Pagination Group " + i);
            request.setDescription("Group for pagination test " + i);
            request.setRoleIds(List.of(roleA.getId()));

            mockMvc.perform(post("/api/v1/groups")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isCreated());
        }

        // When & Then - Test pagination
        mockMvc.perform(get("/api/v1/groups")
                        .param("page", "1")
                        .param("pageSize", "3"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.currentPage").value(1))
                .andExpect(jsonPath("$.pageSize").value(3))
                .andExpect(jsonPath("$.content", hasSize(3)));

        mockMvc.perform(get("/api/v1/groups")
                        .param("page", "2")
                        .param("pageSize", "3"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.currentPage").value(2))
                .andExpect(jsonPath("$.pageSize").value(3));
    }

    @Test
    void shouldGetAllGroups_WithFiltering() throws Exception {
        // Given - Create groups with different names
        CreateGroupRequest request1 = new CreateGroupRequest();
        request1.setName("Engineering Team Alpha");
        request1.setDescription("Alpha engineering team");
        request1.setRoleIds(List.of(roleA.getId()));

        CreateGroupRequest request2 = new CreateGroupRequest();
        request2.setName("Marketing Team Beta");
        request2.setDescription("Beta marketing team");
        request2.setRoleIds(List.of(roleA.getId()));

        mockMvc.perform(post("/api/v1/groups")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isCreated());

        mockMvc.perform(post("/api/v1/groups")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isCreated());

        // When & Then - Filter by "Engineering"
        mockMvc.perform(get("/api/v1/groups")
                        .param("filter", "Engineering"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[?(@.name =~ /.*Engineering.*/)].name").exists());
    }

    @Test
    void shouldGetAllGroups_WithSorting() throws Exception {
        // Given - Create groups with different names for sorting
        CreateGroupRequest request1 = new CreateGroupRequest();
        request1.setName("Zebra Team");
        request1.setDescription("Last alphabetically");
        request1.setRoleIds(List.of(roleA.getId()));

        CreateGroupRequest request2 = new CreateGroupRequest();
        request2.setName("Alpha Team");
        request2.setDescription("First alphabetically");
        request2.setRoleIds(List.of(roleA.getId()));

        mockMvc.perform(post("/api/v1/groups")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isCreated());

        mockMvc.perform(post("/api/v1/groups")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isCreated());

        // When & Then - Sort by name ascending
        mockMvc.perform(get("/api/v1/groups")
                        .param("sortBy", "name")
                        .param("sortDirection", "asc"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());

        // Sort by name descending
        mockMvc.perform(get("/api/v1/groups")
                        .param("sortBy", "name")
                        .param("sortDirection", "desc"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());
    }

    @Test
    void shouldGetAllGroups_WithIncludeDetails() throws Exception {
        // Given - Create group with accounts
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Detailed Group");
        request.setDescription("Group for details test");
        request.setRoleIds(List.of(roleA.getId()));
        request.setAccountIds(List.of(accountA.getId()));

        mockMvc.perform(post("/api/v1/groups")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated());

        // When & Then - Without details
        mockMvc.perform(get("/api/v1/groups")
                        .param("includeDetails", "false"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());

        // With details
        mockMvc.perform(get("/api/v1/groups")
                        .param("includeDetails", "true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());
    }

    // ========================================
    // GET /groups/{id} Tests
    // ========================================

    @Test
    void shouldGetGroupById_Successfully() throws Exception {
        // Given - Create a group
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Single Group Test");
        request.setDescription("Group for single retrieval test");
        request.setRoleIds(List.of(roleA.getId()));
        request.setAccountIds(List.of(accountA.getId()));
        request.setOwnerAccountIds(List.of(accountA.getId()));
        request.setMetadata(Map.of("department", "Engineering", "level", "Senior"));

        String createResponse = mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andReturn().getResponse().getContentAsString();

        GroupResponse createdGroup = objectMapper.readValue(createResponse, GroupResponse.class);

        // When & Then
        mockMvc.perform(get("/api/v1/groups/{id}", createdGroup.getId())
                        .param("includeDetails", "true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(createdGroup.getId().toString()))
                .andExpect(jsonPath("$.name").value("Single Group Test"))
                .andExpect(jsonPath("$.description").value("Group for single retrieval test"))
                .andExpect(jsonPath("$.metadata.department").value("Engineering"))
                .andExpect(jsonPath("$.metadata.level").value("Senior"))
                .andExpect(jsonPath("$.roles").isArray())
                .andExpect(jsonPath("$.roles", hasSize(1)))
                .andExpect(jsonPath("$.accounts").isArray())
                .andExpect(jsonPath("$.accounts", hasSize(1)))
                .andExpect(jsonPath("$.accounts[0].groupRole").value("OWNER"))
                .andExpect(jsonPath("$._audit").exists());
    }

    @Test
    void shouldGetGroupById_WithoutDetails() throws Exception {
        // Given - Create a group
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Simple Group Test");
        request.setDescription("Group for simple retrieval test");
        request.setRoleIds(List.of(roleA.getId()));

        String createResponse = mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andReturn().getResponse().getContentAsString();

        GroupResponse createdGroup = objectMapper.readValue(createResponse, GroupResponse.class);

        // When & Then
        mockMvc.perform(get("/api/v1/groups/{id}", createdGroup.getId())
                        .param("includeDetails", "false"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(createdGroup.getId().toString()))
                .andExpect(jsonPath("$.name").value("Simple Group Test"));
    }

    @Test
    void shouldReturnNotFound_WhenGroupIdDoesNotExist() throws Exception {
        // Given
        UUID nonExistentGroupId = UUID.randomUUID();

        // When & Then
        mockMvc.perform(get("/api/v1/groups/{id}", nonExistentGroupId))
                .andExpect(status().isNotFound());
    }

    @Test
    void shouldReturnInternalServerError_WhenGroupIdIsInvalid() throws Exception {
        // When & Then - Invalid UUID format causes 500 error
        mockMvc.perform(get("/api/v1/groups/{id}", "invalid-uuid"))
                .andExpect(status().isInternalServerError());
    }

    // ========================================
    // PUT /groups/{id} Tests - Success Scenarios
    // ========================================

    @Test
    void shouldUpdateGroupSuccessfully_AllFields() throws Exception {
        // Given - Create a group first
        CreateGroupRequest createRequest = new CreateGroupRequest();
        createRequest.setName("Original Group");
        createRequest.setDescription("Original description");
        createRequest.setRoleIds(List.of(roleA.getId()));

        String createResponse = mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andReturn().getResponse().getContentAsString();

        GroupResponse createdGroup = objectMapper.readValue(createResponse, GroupResponse.class);

        // Create additional role for update
        RoleJpaEntity roleA2 = new RoleJpaEntity();
        roleA2.setName("Role A2");
        roleA2.setBusinessId(bizA.getId());
        roleA2 = roleRepository.save(roleA2);

        // Update request
        UpdateGroupRequest updateRequest = new UpdateGroupRequest();
        updateRequest.setName("Updated Group");
        updateRequest.setDescription("Updated description");
        updateRequest.setRoleIds(List.of(roleA.getId(), roleA2.getId()));
        updateRequest.setAccountIds(List.of(accountA.getId()));
        updateRequest.setOwnerAccountIds(List.of(accountA.getId()));
        updateRequest.setMetadata(Map.of("updated", "true", "version", 2));

        // When & Then
        mockMvc.perform(put("/api/v1/groups/{id}", createdGroup.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(createdGroup.getId().toString()))
                .andExpect(jsonPath("$.name").value("Updated Group"))
                .andExpect(jsonPath("$.description").value("Updated description"))
                .andExpect(jsonPath("$.metadata.updated").value("true"))
                .andExpect(jsonPath("$.metadata.version").value(2))
                .andExpect(jsonPath("$.roles", hasSize(2)))
                .andExpect(jsonPath("$.accounts", hasSize(1)))
                .andExpect(jsonPath("$.accounts[0].groupRole").value("OWNER"));
    }

    @Test
    void shouldUpdateGroupSuccessfully_KeepExistingAccounts() throws Exception {
        // Given - Create a group with accounts
        CreateGroupRequest createRequest = new CreateGroupRequest();
        createRequest.setName("Group With Accounts");
        createRequest.setDescription("Group that will be updated");
        createRequest.setRoleIds(List.of(roleA.getId()));
        createRequest.setAccountIds(List.of(accountA.getId()));

        String createResponse = mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andReturn().getResponse().getContentAsString();

        GroupResponse createdGroup = objectMapper.readValue(createResponse, GroupResponse.class);

        // Update request - keep existing accounts (API might not support removing all accounts via PUT)
        UpdateGroupRequest updateRequest = new UpdateGroupRequest();
        updateRequest.setName("Group With Updated Name");
        updateRequest.setDescription("Updated description");
        updateRequest.setRoleIds(List.of(roleA.getId()));
        updateRequest.setAccountIds(List.of(accountA.getId())); // Keep existing account
        updateRequest.setOwnerAccountIds(List.of()); // No owners

        // When & Then
        mockMvc.perform(put("/api/v1/groups/{id}", createdGroup.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("Group With Updated Name"))
                .andExpect(jsonPath("$.accounts", hasSize(1)));
    }

    // ========================================
    // DELETE /groups/{id} Tests
    // ========================================

    @Test
    void shouldDeleteGroupSuccessfully() throws Exception {
        // Given - Create a group
        CreateGroupRequest createRequest = new CreateGroupRequest();
        createRequest.setName("Group To Delete");
        createRequest.setDescription("This group will be deleted");
        createRequest.setRoleIds(List.of(roleA.getId()));

        String createResponse = mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andReturn().getResponse().getContentAsString();

        GroupResponse createdGroup = objectMapper.readValue(createResponse, GroupResponse.class);

        // When & Then - Delete the group
        mockMvc.perform(delete("/api/v1/groups/{id}", createdGroup.getId()))
                .andExpect(status().isNoContent());

        // Verify group is deleted
        mockMvc.perform(get("/api/v1/groups/{id}", createdGroup.getId()))
                .andExpect(status().isNotFound());
    }

    @Test
    void shouldDeleteGroupSuccessfully_WithMembersAndRoles() throws Exception {
        // Given - Create a group with members and roles
        CreateGroupRequest createRequest = new CreateGroupRequest();
        createRequest.setName("Complex Group To Delete");
        createRequest.setDescription("Group with members and roles to delete");
        createRequest.setRoleIds(List.of(roleA.getId()));
        createRequest.setAccountIds(List.of(accountA.getId()));
        createRequest.setOwnerAccountIds(List.of(accountA.getId()));

        String createResponse = mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andReturn().getResponse().getContentAsString();

        GroupResponse createdGroup = objectMapper.readValue(createResponse, GroupResponse.class);

        // When & Then - Delete the group
        mockMvc.perform(delete("/api/v1/groups/{id}", createdGroup.getId()))
                .andExpect(status().isNoContent());

        // Verify group is deleted
        mockMvc.perform(get("/api/v1/groups/{id}", createdGroup.getId()))
                .andExpect(status().isNotFound());
    }

    // ========================================
    // PATCH /groups/{id}/members Tests
    // ========================================

    @Test
    void shouldUpdateGroupMembersSuccessfully_AddMembers() throws Exception {
        // Given - Create a group without members
        CreateGroupRequest createRequest = new CreateGroupRequest();
        createRequest.setName("Group For Member Addition");
        createRequest.setDescription("Group to test member addition");
        createRequest.setRoleIds(List.of(roleA.getId()));

        String createResponse = mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andReturn().getResponse().getContentAsString();

        GroupResponse createdGroup = objectMapper.readValue(createResponse, GroupResponse.class);

        // Update members request
        UpdateGroupMembersRequest updateRequest = new UpdateGroupMembersRequest();
        updateRequest.setAccountIds(List.of(accountA.getId()));

        // When & Then
        mockMvc.perform(patch("/api/v1/groups/{id}/members", createdGroup.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk());

        // Verify member was added
        mockMvc.perform(get("/api/v1/groups/{id}", createdGroup.getId())
                        .param("includeDetails", "true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accounts", hasSize(1)))
                .andExpect(jsonPath("$.accounts[0].id").value(accountA.getId().toString()))
                .andExpect(jsonPath("$.accounts[0].groupRole").value("MEMBER"));
    }

    @Test
    void shouldUpdateGroupMembersSuccessfully_AddOwners() throws Exception {
        // Given - Create a group
        CreateGroupRequest createRequest = new CreateGroupRequest();
        createRequest.setName("Group For Owner Addition");
        createRequest.setDescription("Group to test owner addition");
        createRequest.setRoleIds(List.of(roleA.getId()));

        String createResponse = mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andReturn().getResponse().getContentAsString();

        GroupResponse createdGroup = objectMapper.readValue(createResponse, GroupResponse.class);

        // Update members request with owners
        UpdateGroupMembersRequest updateRequest = new UpdateGroupMembersRequest();
        updateRequest.setAccountIds(List.of(accountA.getId()));
        updateRequest.setOwnerAccountIds(List.of(accountA.getId()));

        // When & Then
        mockMvc.perform(patch("/api/v1/groups/{id}/members", createdGroup.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk());

        // Verify owner was added
        mockMvc.perform(get("/api/v1/groups/{id}", createdGroup.getId())
                        .param("includeDetails", "true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accounts", hasSize(1)))
                .andExpect(jsonPath("$.accounts[0].groupRole").value("OWNER"));
    }

    @Test
    void shouldUpdateGroupMembersSuccessfully_RemoveAllMembers() throws Exception {
        // Given - Create a group with members
        CreateGroupRequest createRequest = new CreateGroupRequest();
        createRequest.setName("Group For Member Removal");
        createRequest.setDescription("Group to test member removal");
        createRequest.setRoleIds(List.of(roleA.getId()));
        createRequest.setAccountIds(List.of(accountA.getId()));

        String createResponse = mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andReturn().getResponse().getContentAsString();

        GroupResponse createdGroup = objectMapper.readValue(createResponse, GroupResponse.class);

        // Update members request - remove all members
        UpdateGroupMembersRequest updateRequest = new UpdateGroupMembersRequest();
        updateRequest.setAccountIds(List.of()); // Empty list
        updateRequest.setOwnerAccountIds(List.of()); // Empty list

        // When & Then
        mockMvc.perform(patch("/api/v1/groups/{id}/members", createdGroup.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk());

        // Verify all members were removed
        mockMvc.perform(get("/api/v1/groups/{id}", createdGroup.getId())
                        .param("includeDetails", "true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accounts", hasSize(0)));
    }

    @Test
    void shouldReturnNotFound_WhenUpdatingMembersOfNonExistentGroup() throws Exception {
        // Given
        UUID nonExistentGroupId = UUID.randomUUID();
        UpdateGroupMembersRequest updateRequest = new UpdateGroupMembersRequest();
        updateRequest.setAccountIds(List.of(accountA.getId()));

        // When & Then
        mockMvc.perform(patch("/api/v1/groups/{id}/members", nonExistentGroupId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isNotFound());
    }

    @Test
    void shouldReturnBadRequest_WhenUpdatingMembersWithNonExistentAccount() throws Exception {
        // Given - Create a group
        CreateGroupRequest createRequest = new CreateGroupRequest();
        createRequest.setName("Group For Invalid Member Update");
        createRequest.setDescription("Group to test invalid member update");
        createRequest.setRoleIds(List.of(roleA.getId()));

        String createResponse = mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andReturn().getResponse().getContentAsString();

        GroupResponse createdGroup = objectMapper.readValue(createResponse, GroupResponse.class);

        // Update members request with non-existent account
        UpdateGroupMembersRequest updateRequest = new UpdateGroupMembersRequest();
        updateRequest.setAccountIds(List.of(UUID.randomUUID()));

        // When & Then
        mockMvc.perform(patch("/api/v1/groups/{id}/members", createdGroup.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isBadRequest());
    }

    // ========================================
    // PATCH /groups/{id}/roles Tests
    // ========================================

    @Test
    void shouldUpdateGroupRolesSuccessfully() throws Exception {
        // Given - Create a group with one role
        CreateGroupRequest createRequest = new CreateGroupRequest();
        createRequest.setName("Group For Role Update");
        createRequest.setDescription("Group to test role update");
        createRequest.setRoleIds(List.of(roleA.getId()));

        String createResponse = mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andReturn().getResponse().getContentAsString();

        GroupResponse createdGroup = objectMapper.readValue(createResponse, GroupResponse.class);

        // Create additional role
        RoleJpaEntity roleA2 = new RoleJpaEntity();
        roleA2.setName("Role A2");
        roleA2.setBusinessId(bizA.getId());
        roleA2 = roleRepository.save(roleA2);

        // Update roles request
        UpdateGroupRolesRequest updateRequest = new UpdateGroupRolesRequest();
        updateRequest.setRoleIds(List.of(roleA.getId(), roleA2.getId()));

        // When & Then
        mockMvc.perform(patch("/api/v1/groups/{id}/roles", createdGroup.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk());

        // Verify roles were updated
        mockMvc.perform(get("/api/v1/groups/{id}", createdGroup.getId())
                        .param("includeDetails", "true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.roles", hasSize(2)));
    }

    @Test
    void shouldReturnNotFound_WhenUpdatingRolesOfNonExistentGroup() throws Exception {
        // Given
        UUID nonExistentGroupId = UUID.randomUUID();
        UpdateGroupRolesRequest updateRequest = new UpdateGroupRolesRequest();
        updateRequest.setRoleIds(List.of(roleA.getId()));

        // When & Then
        mockMvc.perform(patch("/api/v1/groups/{id}/roles", nonExistentGroupId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isNotFound());
    }

    @Test
    void shouldReturnBadRequest_WhenUpdatingRolesWithNonExistentRole() throws Exception {
        // Given - Create a group
        CreateGroupRequest createRequest = new CreateGroupRequest();
        createRequest.setName("Group For Invalid Role Update");
        createRequest.setDescription("Group to test invalid role update");
        createRequest.setRoleIds(List.of(roleA.getId()));

        String createResponse = mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andReturn().getResponse().getContentAsString();

        GroupResponse createdGroup = objectMapper.readValue(createResponse, GroupResponse.class);

        // Update roles request with non-existent role
        UpdateGroupRolesRequest updateRequest = new UpdateGroupRolesRequest();
        updateRequest.setRoleIds(List.of(UUID.randomUUID()));

        // When & Then
        mockMvc.perform(patch("/api/v1/groups/{id}/roles", createdGroup.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldUpdateGroupRolesSuccessfully_WithEmptyList() throws Exception {
        // Given - Create a group
        CreateGroupRequest createRequest = new CreateGroupRequest();
        createRequest.setName("Group For Empty Role Update");
        createRequest.setDescription("Group to test empty role update");
        createRequest.setRoleIds(List.of(roleA.getId()));

        String createResponse = mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andReturn().getResponse().getContentAsString();

        GroupResponse createdGroup = objectMapper.readValue(createResponse, GroupResponse.class);

        // Update roles request with empty list
        UpdateGroupRolesRequest updateRequest = new UpdateGroupRolesRequest();
        updateRequest.setRoleIds(List.of()); // Empty list

        // When & Then - PATCH operations might allow empty lists
        mockMvc.perform(patch("/api/v1/groups/{id}/roles", createdGroup.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk());
    }

    // ========================================
    // Edge Cases and Special Scenarios
    // ========================================

    @Test
    void shouldHandleSpecialCharactersInGroupName() throws Exception {
        // Given
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Group with Special Characters: @#$%^&*()");
        request.setDescription("Testing special characters in name");
        request.setRoleIds(List.of(roleA.getId()));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name").value("Group with Special Characters: @#$%^&*()"));
    }

    @Test
    void shouldHandleUnicodeCharactersInGroupName() throws Exception {
        // Given
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Группа с Unicode символами 中文 🚀");
        request.setDescription("Testing Unicode characters");
        request.setRoleIds(List.of(roleA.getId()));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name").value("Группа с Unicode символами 中文 🚀"));
    }

    @Test
    void shouldHandleNullDescription() throws Exception {
        // Given
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Group with Null Description");
        request.setDescription(null);
        request.setRoleIds(List.of(roleA.getId()));

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name").value("Group with Null Description"));
    }

    @Test
    void shouldHandleEmptyMetadata() throws Exception {
        // Given
        CreateGroupRequest request = new CreateGroupRequest();
        request.setName("Group with Empty Metadata");
        request.setDescription("Testing empty metadata");
        request.setRoleIds(List.of(roleA.getId()));
        request.setMetadata(Map.of()); // Empty map

        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name").value("Group with Empty Metadata"));
    }

    @Test
    void shouldReturnBadRequest_WhenRequestBodyIsEmpty() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnInternalServerError_WhenRequestBodyIsInvalidJson() throws Exception {
        // When & Then - Invalid JSON causes 500 error
        mockMvc.perform(post("/api/v1/groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("invalid json"))
                .andExpect(status().isInternalServerError());
    }
}