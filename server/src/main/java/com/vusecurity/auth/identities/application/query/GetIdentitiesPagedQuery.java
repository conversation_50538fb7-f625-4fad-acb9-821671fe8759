package com.vusecurity.auth.identities.application.query;

import lombok.Builder;
import lombok.Data;

/**
 * Query for getting paginated identities with optional filtering.
 */
@Data
@Builder
public class GetIdentitiesPagedQuery {
    private int page;
    private int pageSize;
    private String name;
    private String identityType;
    private String lifecycleState;
    private String filter; // General filter string
}
