package com.vusecurity.auth.scim.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimToClaimMappingJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import com.vusecurity.business.domain.Business;



@Repository
public interface ScimToClaimMappingRepository extends JpaRepository<ScimToClaimMappingJpaEntity, UUID>, JpaSpecificationExecutor<ScimToClaimMappingJpaEntity> {

    Optional<ScimToClaimMappingJpaEntity> findById(UUID id);    

    List<ScimToClaimMappingJpaEntity> findAllByBusiness(Business business);    

    void deleteById(UUID id);

    void deleteByBusiness(Business business);

    List<ScimToClaimMappingJpaEntity> findAllByBusinessId(UUID businessId);

    void deleteByBusinessId(UUID businessId);
}
