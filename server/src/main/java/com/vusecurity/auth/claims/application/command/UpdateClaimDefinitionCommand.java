package com.vusecurity.auth.claims.application.command;

import com.vusecurity.auth.contracts.enums.ClaimType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;

import java.util.UUID;

/**
 * Command for updating an existing claim definition.
 */
public record UpdateClaimDefinitionCommand (
        UUID claimDefinitionId,
        String code,
        String name,
        String description,
        ClaimType claimType,
        DataTypeEnum dataFormat,
        String format
) {

    public UpdateClaimDefinitionCommand (
        UUID claimDefinitionId,
        String code,
        String name,
        String description,
        DataTypeEnum dataType,
        String dataFormat) {
            this(claimDefinitionId, code, name, description, ClaimType.USER_DEFINED, dataType, dataFormat);
    }
    
    /**
     * Validates the command parameters.
     * @throws IllegalArgumentException if validation fails
     */
    public void validate() {
        if (claimDefinitionId == null) {
            throw new IllegalArgumentException("Claim definition ID cannot be null");
        }
        if (code != null && code.trim().isEmpty()) {
            throw new IllegalArgumentException("Code cannot be empty");
        }
        if (name != null && name.trim().isEmpty()) {
            throw new IllegalArgumentException("Name cannot be empty");
        }
        if (code != null && code.length() > 255) {
            throw new IllegalArgumentException("Code must not exceed 255 characters");
        }
        if (name != null && name.length() > 255) {
            throw new IllegalArgumentException("Name must not exceed 255 characters");
        }
        if (description != null && description.length() > 1024) {
            throw new IllegalArgumentException("Description must not exceed 1024 characters");
        }
        if (format != null && format.length() > 1024) {
            throw new IllegalArgumentException("Format must not exceed 1024 characters");
        }
    }
}
