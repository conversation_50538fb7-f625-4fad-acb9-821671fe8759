package com.vusecurity.auth.scim.application.handler;

import com.vusecurity.auth.scim.application.exception.ScimTokenNotFoundException;
import com.vusecurity.auth.scim.application.service.ScimTokensService;
import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimTokenJpaEntity;

import lombok.RequiredArgsConstructor;

import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class UpdateScimTokenHandler {

    private static final Logger logger = LoggerFactory.getLogger(UpdateScimTokenHandler.class);

    private final ScimTokensService scimTokensService;
    
    @Transactional
    public ScimTokenJpaEntity deactivateToken(UUID tokenId) {
        logger.debug("UpdateScimTokenHandler - deactivateToken - Deactivating SCIM token: {}", tokenId.toString());

        ScimTokenJpaEntity token = scimTokensService.get(tokenId).orElseThrow(() -> new ScimTokenNotFoundException("Token not found for for id: " + tokenId.toString()));
        token.deactivate();

        token = scimTokensService.save(token);

        logger.debug("UpdateScimTokenHandler - deactivateToken - SCIM token deactivated: {}", tokenId.toString());
        return token;
    }

    @Transactional
    public ScimTokenJpaEntity activateToken(UUID tokenId) {
        logger.debug("UpdateScimTokenHandler - activateToken - Activating SCIM token: {}", tokenId.toString());

        ScimTokenJpaEntity token = scimTokensService.get(tokenId).orElseThrow(() -> new ScimTokenNotFoundException("Token not found for for id: " + tokenId.toString()));
        token.activate();

        token = scimTokensService.save(token);

        logger.debug("UpdateScimTokenHandler - activateToken - SCIM token activated: {}", tokenId.toString());
        return token;
    }
} 