package com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.business.domain.Business;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for AccountJpaEntity business logic.
 * Tests pure domain logic with no external dependencies.
 */
class AccountJpaEntityTest {

    @Test
    void shouldCreateAccountWithValidData() {
        // Given
        Business business = createTestBusiness();
        IdentityJpaEntity identity = createTestIdentity();
        IdentityProviderJpaEntity identityProvider = createTestIdentityProvider();
        AccountType accountType = AccountType.WORKFORCE;

        // When
        AccountJpaEntity account = new AccountJpaEntity(business, identity, identityProvider, accountType);

        // Then
        assertEquals(business, account.getBusiness());
        assertEquals(identity, account.getIdentity());
        assertEquals(identityProvider, account.getIdentityProvider());
        assertEquals(accountType, account.getAccountType());
        assertEquals(AccountLifecycleState.NONE, account.getLifecycleState());
        assertNotNull(account.getMetadata());
        assertTrue(account.getMetadata().isEmpty());
        assertNotNull(account.getRoles());
        assertTrue(account.getRoles().isEmpty());
        assertFalse(account.isActive());
    }

    @Test
    void shouldCreateAccountWithId() {
        // Given
        UUID accountId = UUID.randomUUID();
        Business business = createTestBusiness();
        IdentityJpaEntity identity = createTestIdentity();
        IdentityProviderJpaEntity identityProvider = createTestIdentityProvider();
        AccountType accountType = AccountType.LOCAL;

        // When
        AccountJpaEntity account = new AccountJpaEntity(accountId, business, identity, identityProvider, accountType);

        // Then
        assertEquals(accountId, account.getId());
        assertEquals(business, account.getBusiness());
        assertEquals(identity, account.getIdentity());
        assertEquals(identityProvider, account.getIdentityProvider());
        assertEquals(accountType, account.getAccountType());
    }

    @Test
    void shouldThrowException_WhenMandatoryFieldsAreNull() {
        // Test null business
        assertThrows(IllegalArgumentException.class, () -> 
                new AccountJpaEntity(null, createTestIdentity(), createTestIdentityProvider(), AccountType.WORKFORCE));

        // Test null identity
        assertThrows(IllegalArgumentException.class, () -> 
                new AccountJpaEntity(createTestBusiness(), null, createTestIdentityProvider(), AccountType.WORKFORCE));

        // Test null identity provider
        assertThrows(IllegalArgumentException.class, () -> 
                new AccountJpaEntity(createTestBusiness(), createTestIdentity(), null, AccountType.WORKFORCE));

        // Test null account type
        assertThrows(IllegalArgumentException.class, () -> 
                new AccountJpaEntity(createTestBusiness(), createTestIdentity(), createTestIdentityProvider(), null));
    }

    @Test
    void shouldActivateAccount() {
        // Given
        AccountJpaEntity account = createTestAccount();
        Instant beforeActivation = Instant.now();

        // When
        account.activate();

        // Then
        assertTrue(account.isActive());
        assertEquals(AccountLifecycleState.ACTIVE, account.getLifecycleState());
        assertNotNull(account.getUpdatedAt());
        assertTrue(account.getUpdatedAt().isAfter(beforeActivation) || account.getUpdatedAt().equals(beforeActivation));
    }

    @Test
    void shouldSuspendActiveAccount() {
        // Given
        AccountJpaEntity account = createTestAccount();
        account.activate();
        Instant beforeSuspension = Instant.now();

        // When
        account.suspend();

        // Then
        assertTrue(account.isSuspended());
        assertEquals(AccountLifecycleState.SUSPENDED, account.getLifecycleState());
        assertNotNull(account.getUpdatedAt());
        assertTrue(account.getUpdatedAt().isAfter(beforeSuspension) || account.getUpdatedAt().equals(beforeSuspension));
    }

    @Test
    void shouldThrowException_WhenSuspendingNonActiveAccount() {
        // Given
        AccountJpaEntity account = createTestAccount();
        // Account is not active (default state is NONE)

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, account::suspend);
        assertEquals("Cannot suspend non-active account", exception.getMessage());
    }

    @Test
    void shouldDeactivateAccount() {
        // Given
        AccountJpaEntity account = createTestAccount();
        account.activate();
        Instant beforeDeactivation = Instant.now();

        // When
        account.deactivate();

        // Then
        assertTrue(account.isInactive());
        assertEquals(AccountLifecycleState.DEACTIVATED, account.getLifecycleState());
        assertNotNull(account.getUpdatedAt());
        assertTrue(account.getUpdatedAt().isAfter(beforeDeactivation) || account.getUpdatedAt().equals(beforeDeactivation));
    }

    @Test
    void shouldCheckLifecycleStates() {
        // Given
        AccountJpaEntity account = createTestAccount();

        // Test initial state
        assertFalse(account.isActive());
        assertFalse(account.isSuspended());
        assertFalse(account.isInactive());

        // Test active state
        account.activate();
        assertTrue(account.isActive());
        assertFalse(account.isSuspended());
        assertFalse(account.isInactive());

        // Test suspended state
        account.suspend();
        assertFalse(account.isActive());
        assertTrue(account.isSuspended());
        assertFalse(account.isInactive());

        // Test deactivated state
        account.setLifecycleState(AccountLifecycleState.ACTIVE); // Reset to active first
        account.deactivate();
        assertFalse(account.isActive());
        assertFalse(account.isSuspended());
        assertTrue(account.isInactive());
    }

    @Test
    void shouldManageMetadata() {
        // Given
        AccountJpaEntity account = createTestAccount();
        String key = "department";
        String value = "Engineering";
        Instant beforeUpdate = Instant.now();

        // When
        account.addMetadata(key, value);

        // Then
        assertEquals(value, account.getMetadata().get(key));
        assertNotNull(account.getUpdatedAt());
        assertTrue(account.getUpdatedAt().isAfter(beforeUpdate) || account.getUpdatedAt().equals(beforeUpdate));
    }

    @Test
    void shouldAddAndRemoveMetadata() {
        // Given
        AccountJpaEntity account = createTestAccount();

        // When - Add metadata
        account.addMetadata("key1", "value1");
        account.addMetadata("key2", 42);
        account.addMetadata("key3", true);

        // Then - Verify added
        assertEquals("value1", account.getMetadata().get("key1"));
        assertEquals(42, account.getMetadata().get("key2"));
        assertEquals(true, account.getMetadata().get("key3"));

        // When - Remove metadata
        account.removeMetadata("key1");

        // Then - Verify removed
        assertFalse(account.getMetadata().containsKey("key1"));
        assertTrue(account.getMetadata().containsKey("key2"));
        assertTrue(account.getMetadata().containsKey("key3"));
    }

    @Test
    void shouldThrowException_WhenAddingMetadataWithNullKey() {
        // Given
        AccountJpaEntity account = createTestAccount();

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> account.addMetadata(null, "value"));
        assertThrows(IllegalArgumentException.class, () -> account.addMetadata("", "value"));
        assertThrows(IllegalArgumentException.class, () -> account.addMetadata("   ", "value"));
    }

    @Test
    void shouldHandleNullMetadataValue() {
        // Given
        AccountJpaEntity account = createTestAccount();

        // When
        account.addMetadata("nullKey", null);

        // Then
        assertTrue(account.getMetadata().containsKey("nullKey"));
        assertNull(account.getMetadata().get("nullKey"));
    }

    @Test
    void shouldAssignAndRemoveRoles() {
        // Given
        AccountJpaEntity account = createTestAccount();

        // When & Then - These methods exist but are simplified in current implementation
        assertDoesNotThrow(() -> account.assignRole("ADMIN"));
        assertDoesNotThrow(() -> account.assignRole("USER"));
        assertDoesNotThrow(() -> account.removeRole("USER"));
    }

    @Test
    void shouldThrowException_WhenAssigningRoleWithNullName() {
        // Given
        AccountJpaEntity account = createTestAccount();

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> account.assignRole(null));
        assertThrows(IllegalArgumentException.class, () -> account.assignRole(""));
        assertThrows(IllegalArgumentException.class, () -> account.assignRole("   "));
    }

    @Test
    void shouldReturnDefensiveCopyOfMetadata() {
        // Given
        AccountJpaEntity account = createTestAccount();
        account.addMetadata("key1", "value1");

        // When
        Map<String, Object> metadata1 = account.getMetadata();
        Map<String, Object> metadata2 = account.getMetadata();

        // Then
        assertNotSame(metadata1, metadata2, "Should return different instances");
        assertEquals(metadata1, metadata2, "But content should be the same");

        // Modify returned map should not affect entity
        metadata1.put("newKey", "newValue");
        assertFalse(account.getMetadata().containsKey("newKey"));
    }

    @Test
    void shouldReturnDefensiveCopyOfRoles() {
        // Given
        AccountJpaEntity account = createTestAccount();

        // When
        var roles1 = account.getRoles();
        var roles2 = account.getRoles();

        // Then
        assertNotSame(roles1, roles2, "Should return different instances");
        assertEquals(roles1, roles2, "But content should be the same");
    }

    @Test
    void shouldSetMetadataWithDefensiveCopy() {
        // Given
        AccountJpaEntity account = createTestAccount();
        Map<String, Object> originalMetadata = new HashMap<>();
        originalMetadata.put("key1", "value1");

        // When
        account.setMetadata(originalMetadata);
        originalMetadata.put("key2", "value2"); // Modify original

        // Then
        assertFalse(account.getMetadata().containsKey("key2"), 
                "Entity should not be affected by changes to original map");
        assertEquals(1, account.getMetadata().size());
    }

    @Test
    void shouldHandleNullMetadataInSetter() {
        // Given
        AccountJpaEntity account = createTestAccount();

        // When
        account.setMetadata(null);

        // Then
        assertNotNull(account.getMetadata());
        assertTrue(account.getMetadata().isEmpty());
    }

    @Test
    void shouldGetCorrectIds() {
        // Given
        UUID businessId = UUID.randomUUID();
        UUID identityId = UUID.randomUUID();
        UUID identityProviderId = UUID.randomUUID();

        Business business = createTestBusiness();
        business.setId(businessId);
        
        IdentityJpaEntity identity = createTestIdentity();
        identity.setId(identityId);
        
        IdentityProviderJpaEntity identityProvider = createTestIdentityProvider();
        identityProvider.setId(identityProviderId);

        AccountJpaEntity account = new AccountJpaEntity(business, identity, identityProvider, AccountType.CUSTOMER);

        // When & Then
        assertEquals(businessId, account.getBusinessId());
        assertEquals(identityId, account.getIdentityId());
        assertEquals(identityProviderId, account.getIdentityProviderId());
    }

    @Test
    void shouldHandleNullRelationshipsInIdGetters() {
        // Given
        AccountJpaEntity account = new AccountJpaEntity();

        // When & Then
        assertNull(account.getBusinessId());
        assertNull(account.getIdentityId());
        assertNull(account.getIdentityProviderId());
    }

    // Helper methods
    private AccountJpaEntity createTestAccount() {
        return new AccountJpaEntity(
                createTestBusiness(),
                createTestIdentity(),
                createTestIdentityProvider(),
                AccountType.WORKFORCE
        );
    }

    private Business createTestBusiness() {
        Business business = new Business();
        business.setId(UUID.randomUUID());
        business.setName("Test Business");
        return business;
    }

    private IdentityJpaEntity createTestIdentity() {
        IdentityJpaEntity identity = new IdentityJpaEntity();
        identity.setId(UUID.randomUUID());
        return identity;
    }

    private IdentityProviderJpaEntity createTestIdentityProvider() {
        IdentityProviderJpaEntity identityProvider = new IdentityProviderJpaEntity();
        identityProvider.setId(UUID.randomUUID());
        return identityProvider;
    }
}
