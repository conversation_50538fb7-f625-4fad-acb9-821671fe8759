<configuration>

    <springProperty name="springAppName" source="spring.application.name"/>

    <!-- Raw text appender for development -->
    <appender name="STDOUT_RAW" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Async appender for raw text -->
    <appender name="ASYNC_STDOUT" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="STDOUT_RAW" />
    </appender>

    <!-- Root logger -->
    <root level="INFO">
        <appender-ref ref="ASYNC_STDOUT" />
    </root>

</configuration>
