package com.vusecurity.auth.scim.application.resource;

import java.lang.reflect.InvocationTargetException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Response.Status;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.directory.scim.core.repository.PatchHandler;
import org.apache.directory.scim.core.repository.Repository;
import org.apache.directory.scim.server.exception.UnableToCreateResourceException;
import org.apache.directory.scim.server.exception.UnableToRetrieveResourceException;
import org.apache.directory.scim.server.exception.UnableToUpdateResourceException;
import org.apache.directory.scim.spec.exception.ResourceException;
import org.apache.directory.scim.spec.extension.EnterpriseExtension;
import org.apache.directory.scim.spec.filter.*;
import org.apache.directory.scim.spec.filter.attribute.AttributeReference;
import org.apache.directory.scim.spec.patch.PatchOperation;
import org.apache.directory.scim.spec.resources.*;
import org.apache.directory.scim.spec.schema.Meta;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.vusecurity.auth.claims.application.command.CreateClaimValueCommand;
import com.vusecurity.auth.claims.application.exception.ClaimDefinitionNotFoundException;
import com.vusecurity.auth.claims.application.exception.DuplicateClaimValueException;
import com.vusecurity.auth.claims.application.handler.CreateClaimValueHandler;
import com.vusecurity.auth.claims.application.query.GetClaimValueQuery;
import com.vusecurity.auth.claims.application.service.ClaimDefinitionLookupService;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.BusinessIdentifierClaimSetResponse;
import com.vusecurity.auth.contracts.api.v1.dto.identities.ArrayClaimValueRequest;
import com.vusecurity.auth.contracts.api.v1.dto.identities.ClaimIdentifierRequest;
import com.vusecurity.auth.contracts.api.v1.dto.identities.AccountClaimValueRequest;
import com.vusecurity.auth.contracts.api.v1.dto.identities.ScalarClaimValueRequest;
import com.vusecurity.auth.identities.application.command.CreateAccountCommand;
import com.vusecurity.auth.identities.application.handler.CreateAccountWithClaimValuesHandler;
import com.vusecurity.auth.identities.application.handler.CreateIdentityHandler;
import com.vusecurity.auth.identities.application.handler.UpdateAccountHandler;
import com.vusecurity.auth.identities.application.query.GetAccountQuery;
import com.vusecurity.auth.identities.application.query.IdentifyIdentityByClaimsQuery;
import com.vusecurity.auth.identities.application.query.IdentifyIdentityQuery;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.application.command.CreateIdentityCommand;
import com.vusecurity.auth.identities.application.command.UpdateAccountCommand;
import com.vusecurity.auth.identities.application.exception.AccountAlreadyExistsException;
import com.vusecurity.auth.identities.application.exception.AccountNotFoundException;
import com.vusecurity.auth.identities.application.exception.IdentityNotFoundException;
import com.vusecurity.auth.scim.application.service.ScimToClaimMappingService;
import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimToClaimMappingJpaEntity;
import com.vusecurity.auth.scim.mapper.FilterToSpecificationBuilder;
import com.vusecurity.auth.scim.mapper.ScimFilterMapper;
import com.vusecurity.auth.scim.mapper.FilterToSpecificationBuilder.ColumnConstraint;
import com.vusecurity.auth.scim.mapper.FilterToSpecificationBuilder.FieldSpecificConstraint;
import com.vusecurity.auth.scim.util.SchemaValidation;
import com.vusecurity.auth.scim.util.SchemaValidation.ValidationResult;
import com.vusecurity.auth.scim.util.SchemaValidation.ScimPathInfo;
import com.vusecurity.auth.shared.infrastructure.migration.initial.DataSeedConstants;
import com.vusecurity.auth.shared.infrastructure.security.scim.TokenDetails;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.usecases.interfaces.IRetrieveBusiness;
import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.IdentityLifecycleState;
import com.vusecurity.auth.contracts.enums.IdentityType;
import com.vusecurity.auth.contracts.enums.OwnerType;

/**
 * Creates a singleton (effectively) Provider<User> with a memory-based
 * persistence layer.
 * 
 * <AUTHOR> Harm &lt;<EMAIL>&gt;
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UserService implements Repository<ScimUser> {

    private final UpdateAccountHandler updateAccountHandler;

    private final SchemaValidation schemaValidation;

    private final Map<String, ScimUser> users = new HashMap<>();

    private final PatchHandler patchHandler;

    private final CreateAccountWithClaimValuesHandler createAccountWithClaimValuesHandler;

    private final CreateIdentityHandler createIdentityHandler;

    private final GetAccountQuery getAccountQuery;

    private final ClaimDefinitionLookupService claimDefinitionLookupService;

    private final ScimToClaimMappingService scimToClaimMappingService;

    private final IRetrieveBusiness retrieveBusiness;

    private final IdentifyIdentityQuery identifyIdentityQuery;

    private final CreateClaimValueHandler createClaimValueHandler;

    private final GetClaimValueQuery getClaimValueQuery;

    private UUID businessID = null;
    private UUID providerID = null;
    private String sourceName = null;

    @Override
    public Class<ScimUser> getResourceClass() {
        return ScimUser.class;
    }

    /**
     * @throws ResourceException 
     * @see Repository#create(ScimResource)
     */
    @Override
    @Transactional(rollbackFor = ResourceException.class)
    public ScimUser create(ScimUser resource) throws ResourceException {
        log.info("create SCIM User - {}", resource.getUserName());
        ValidationResult validation = new ValidationResult();
        try{
            validation = schemaValidation.validateResource(resource);
            setBusinesAndProvider();
        } catch (WebApplicationException e){
            throw e;
        } catch (Exception e){
            log.error("Error validating the schema " + e.getMessage(), e);
            validation.addError(new SchemaValidation.ValidationError(null, e.getMessage(), resource.getBaseUrn()));
        }
        if (Boolean.FALSE.equals(validation.isValid())){
            //For now show the first error.
            throw new UnableToCreateResourceException(Status.BAD_REQUEST, "Validation for schema failed: " + validation.getErrors().getFirst().getMessage());
        }

        //Try to find the identity as it's a requirement for the account, there should only be one identity and one account per SCIM userName.
        // 1 - get the Identity and check if the account already exist.
        IdentityJpaEntity identityEntity;
        try{
        
            com.vusecurity.auth.contracts.api.v1.dto.identities.ClaimIdentifierRequest identityUsernameClaim = new ClaimIdentifierRequest();
            identityUsernameClaim.setClaimDefinitionId(DataSeedConstants.CLAIM_DEF_SCIM_USER_NAME_ID);
            identityUsernameClaim.setValue(resource.getUserName());

            IdentifyIdentityByClaimsQuery identityQuery = IdentifyIdentityByClaimsQuery.builder()
                .identityType(IdentityType.FEDERATED)
                .claimIdentifiers(List.of(identityUsernameClaim))
                .build();

            identityEntity = identifyIdentityQuery.identifyIdentityByClaims(identityQuery);

            boolean hasAccount = identityEntity.getAccounts().stream().anyMatch(
                account -> 
                    account.getAccountType().equals(AccountType.FEDERATED) &&
                    account.getBusinessId().equals(businessID) &&
                    account.getIdentityProviderId().equals(providerID) 
                    /* What to do with delete / deactivated accounts
                    &&
                    (!account.getLifecycleState().equals(AccountLifecycleState.DELETED) ||
                    !account.getLifecycleState().equals(AccountLifecycleState.DEACTIVATED)
                     */
                );

            if(hasAccount) {
                throw new AccountAlreadyExistsException(String.format("Account already exists for %s", resource.getUserName()));
            }

        } catch (IdentityNotFoundException e) {
            //TODO: Move to function as handle their own exception
            //Create the identity
            CreateIdentityCommand createIdentityCommand = CreateIdentityCommand.builder()
                .name(resource.getUserName())
                .identityType(IdentityType.FEDERATED.toString())
                .lifecycleState(IdentityLifecycleState.ACTIVE.toString()) //For now
                .build();

            identityEntity = createIdentityHandler.createIdentity(createIdentityCommand);

            CreateClaimValueCommand createIdentityClaimUserName = new CreateClaimValueCommand(
                DataSeedConstants.CLAIM_DEF_SCIM_USER_NAME_ID,
                identityEntity.getId(),
                null,
                resource.getUserName(),
                true,
                false,
                "SCIM-Provider-".concat(providerID.toString().substring(providerID.toString().length()-12))
            );

            createClaimValueHandler.handle(createIdentityClaimUserName);

            //After the identity Account should be created.
        } catch (AccountAlreadyExistsException ex) {
            throw new UnableToCreateResourceException(Status.CONFLICT, ex.getMessage());
        } catch (Exception e){
            //IllegalStateException or any exception should return a 500.
            //Messages must be descriptive.
            log.error("SCIM - create - account identification error - {}", e.getMessage());
            throw new UnableToCreateResourceException(Status.INTERNAL_SERVER_ERROR, e.getMessage());
        }

        //At this point the identity should exist and we should have checked for the account existence.
        //2 - Identify the claims to be mapped from the SCIM User to the new account, userName MUST be mapped.

        //2.1 Get the SCIM mapping for the business
        List<ScimToClaimMappingJpaEntity> scimToClaims = getScimMappings();
        
        //2.2Get the business claims
        List<BusinessIdentifierClaimSetResponse> businessesClaimSet = claimDefinitionLookupService.getBusinessAndAccountTypeClaimSet(businessID, AccountType.FEDERATED);

        //2.2.1 list the current valid claimSets and filter them
        scimToClaims = filterValidBusinessClaimSets(scimToClaims, businessesClaimSet);

        Map<String, ScimPathInfo> scimPathInfo = getPathInfoFromScimClaimsMapping(scimToClaims, resource.getSchemas().stream().toList());


        //TODO: Refactor when Complext types are implemented
        //3 - Map the definitions so they are simple to find.
        //For when Array or complex types are implemented
        /*
        Map<UUID, ClaimDefinitionSummaryResponse> claimDefinitions = new HashMap<>();
        for (BusinessIdentifierClaimSetResponse businessClaimSets : businessesClaimSet) {
            businessClaimSets.getClaimDefinitions().forEach(cd -> claimDefinitions.put(cd.getId(), cd));
        }
        */

        List<AccountClaimValueRequest> claimValueList = new ArrayList<>();
        //! TODO: ¿Add validation for claimSet of identity type?
        for (ScimToClaimMappingJpaEntity cvReference : scimToClaims) {
            ScimPathInfo pathInfo = scimPathInfo.get(cvReference.getFilterMapping());
            //For when Array or complex types are implemented
            //ClaimDefinitionSummaryResponse claimDefinition = claimDefinitions.get(cvReference.getClaimDefinitionReferenced());
            if (pathInfo.getRootAttributeDefinition().isMultiValued()){
                    List<String> arrayValues = ScimFilterMapper.getValueAsList(resource, cvReference.getFilterMapping());
                    Integer primaryIndex = 0;
                    if (!arrayValues.isEmpty() && arrayValues.size() > 1){
                        //extract the primary index value, should only exist one
                        String primaryValueFilter = pathInfo.getRootAttribute().concat("[primary eq \"true\"]");
                        if (pathInfo.getSubAttribute() != null){ 
                            primaryValueFilter = primaryValueFilter.concat(".").concat(pathInfo.getSubAttribute());
                        }
                        String primaryValue = (String)ScimFilterMapper.getValue(resource, primaryValueFilter).toString();
                        primaryIndex = arrayValues.indexOf(primaryValue);

                    }
                    
                    ArrayClaimValueRequest arrayClaimValueRequest = 
                        new ArrayClaimValueRequest(cvReference.getClaimSetOrigin(), cvReference.getClaimDefinitionReferenced(), 
                            arrayValues, primaryIndex >= 0 ? primaryIndex : 0, sourceName
                        );
                    claimValueList.add(arrayClaimValueRequest);
            } else {
                ScalarClaimValueRequest scalarClaimValueRequest = 
                    new ScalarClaimValueRequest(cvReference.getClaimSetOrigin(), cvReference.getClaimDefinitionReferenced(), 
                        ScimFilterMapper.getValueAsString(resource, cvReference.getFilterMapping()), sourceName
                    );
                //Only add non-null values to avoid error of null claimValue verification, the check for required null values was done previously.
                if (scalarClaimValueRequest.value() != null){
                    claimValueList.add(scalarClaimValueRequest);
                }
            }
        }

        //5 - Add metadata
        //? needed? What metadata we want to save here.
        Map<String, Object> metadata = new HashMap<>();

        CreateAccountCommand createAccountCmd = new CreateAccountCommand(
            identityEntity.getId(),
            businessID,
            providerID,
            AccountType.FEDERATED,
            AccountLifecycleState.ACTIVE,
            metadata,
            claimValueList);

        AccountJpaEntity newAccount;
        Instant from;
        try {
            log.info("create SCIM User - starting to create an account - {}", resource.getUserName());
            from = Instant.now();
            newAccount = createAccountWithClaimValuesHandler.handle(createAccountCmd);
        } catch (ClaimDefinitionNotFoundException | IllegalArgumentException |  AccountNotFoundException | IllegalStateException ex){
            log.error(ex.getMessage());
            throw new UnableToCreateResourceException(Status.BAD_REQUEST, ex.getMessage());
        } catch (AccountAlreadyExistsException | DuplicateClaimValueException ex) {
            log.error(ex.getMessage());
            throw new UnableToCreateResourceException(Status.CONFLICT, ex.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new UnableToCreateResourceException(Status.INTERNAL_SERVER_ERROR, e.getMessage());
        }
        log.info("create SCIM User - finished to create an account - {} \r\n Time taken {} miliseconds", resource.getUserName(), Instant.now().minusMillis(from.toEpochMilli()).toEpochMilli());

        ScimUser newUserResource = mapAccountAndClaimsToUserResource(newAccount, scimToClaims, businessesClaimSet);

        return newUserResource;
    }

    @Override
    public ScimUser update(String id, String version, ScimUser resource,
            Set<AttributeReference> includedAttributeReferences, Set<AttributeReference> excludedAttributeReferences)
            throws ResourceException {
        users.put(id, resource);
        return resource;
    }

    @Override
    public ScimUser patch(String id, String version, List<PatchOperation> patchOperations,
            Set<AttributeReference> includedAttributeReferences, Set<AttributeReference> excludedAttributeReferences)
            throws ResourceException {
        ScimUser resource = patchHandler.apply(get(id), patchOperations);
        users.put(id, resource);
        return resource;
    }

    /**
     * @see Repository#get(java.lang.String)
     */
    @Override
    @Transactional(readOnly = true)
    public ScimUser get(String id) throws ResourceException {

        List<ScimToClaimMappingJpaEntity> scimToClaims = getScimMappings();

        List<BusinessIdentifierClaimSetResponse> businessClaimSet = claimDefinitionLookupService.getBusinessAndAccountTypeClaimSet(businessID, AccountType.FEDERATED);

        AccountJpaEntity account;
        try {
            account = getAccountQuery.getAccountById(id);
        } catch (IllegalArgumentException e) {
            return null;
        } catch (Exception e) {
            log.error("Error while retrieving the account", e);
            throw new UnableToRetrieveResourceException(Status.INTERNAL_SERVER_ERROR, e.getMessage());
        }

        ScimUser user = mapAccountAndClaimsToUserResource(account, scimToClaims, businessClaimSet);
        
        return user;
    }

    /**
     * @see Repository#delete(java.lang.String)
     */
    @Override
    @Transactional(rollbackFor = ResourceException.class)
    public void delete(String id) throws ResourceException {

        //! TODO: Add logic deletion when added. For now, deactivate the account.
        try{
            UpdateAccountCommand updateCommand = new UpdateAccountCommand(UUID.fromString(id), AccountLifecycleState.DEACTIVATED, null);
            updateAccountHandler.updateAccount(updateCommand);
        } catch (AccountNotFoundException e){
            //Taken from the ms scim validation tool, future version may required to return NOT_FOUND
            throw new UnableToRetrieveResourceException(Status.NOT_FOUND, e.getMessage());
        } catch (AccountAlreadyExistsException e){
            throw new UnableToUpdateResourceException(Status.CONFLICT, e.getMessage());
        } catch (Exception e){
            throw new ResourceException(500, e.getMessage());
        }

    }

    /**
     * @throws ResourceException 
     * @see Repository#find(Filter, PageRequest, SortRequest)
     */
    @Override
    @Transactional(readOnly = true)
    public FilterResponse<ScimUser> find(Filter filter, PageRequest pageRequest, SortRequest sortRequest) throws ResourceException {

        int count = pageRequest.getCount() != null && pageRequest.getCount() < 100 ? pageRequest.getCount() : 100;
        //Spring PageRequest are 0 index based and SCIM are 1 index based.
        int startIndex = pageRequest.getStartIndex() != null ? pageRequest.getStartIndex() - 1 : 0;

        Page<AccountJpaEntity> queryResult = null;

        Specification<AccountJpaEntity> filterSpec = null;

        List<ScimToClaimMappingJpaEntity> scimToClaims = getScimMappings();

        List<BusinessIdentifierClaimSetResponse> businessClaimSet = claimDefinitionLookupService.getBusinessAndAccountTypeClaimSet(businessID, AccountType.FEDERATED);

        scimToClaims = filterValidBusinessClaimSets(scimToClaims, businessClaimSet);

        Map<String, ScimPathInfo> scimPathInfo = getPathInfoFromScimClaimsMapping(scimToClaims, createSchemaList());

        if (filter != null && !filter.toString().isEmpty()){

            filterSpec = mapFilterToSpecification(filter, scimToClaims, scimPathInfo);
        }

        queryResult = getAccountQuery.getAllAccountsBySpecs(businessID, providerID, filterSpec, org.springframework.data.domain.PageRequest.of(startIndex, count));
        
        List<ScimUser> result = new ArrayList<>();
        for (AccountJpaEntity account : queryResult) {
            ScimUser newUser = mapAccountAndClaimsToUserResource(account, scimToClaims, businessClaimSet);

            result.add(newUser
                .setId(account.getId().toString())
                .setActive(account.isActive())
                .setDisplayName(account.getIdentity().getName())
                .setProfileUrl("/scim/v2/".concat(account.getId().toString())));
        }

        return new FilterResponse<>(result, pageRequest, (int)queryResult.getTotalElements());
    }

    /**
     * @see Repository#getExtensionList()
     */
    @Override
    public List<Class<? extends ScimExtension>> getExtensionList() {
        return List.of(EnterpriseExtension.class);
    }

    private void setBusinesAndProvider() throws ResourceException {
        try{
             Object authenticationDetails = SecurityContextHolder.getContext().getAuthentication().getDetails();
            if (!(authenticationDetails instanceof TokenDetails)){
                throw new WebApplicationException("Invalid authentication credentials type");
            }
            businessID = ((TokenDetails)authenticationDetails).getBusinessId();
            providerID = ((TokenDetails)authenticationDetails).getIdentityProviderId();
            sourceName = ((TokenDetails)authenticationDetails).getUsername();
        } catch (Exception ex){
            log.error("setBusinesAndProvider - error - {}", ex);
            throw new WebApplicationException("Invalid authentication credentials");
        }

    }

    private List<ScimToClaimMappingJpaEntity> getScimMappings() throws ResourceException {
        List<ScimToClaimMappingJpaEntity> scimToClaims = new ArrayList<>();
        try{
            if (businessID == null || providerID == null || sourceName == null) {
                setBusinesAndProvider();
            }
            scimToClaims = scimToClaimMappingService.getByBusinessId(businessID);
        } catch (Exception ex){
            throw new ResourceException(Status.INTERNAL_SERVER_ERROR.getStatusCode(), ex.getMessage());
        }

        if (scimToClaims.isEmpty()){
            //Automatically create some mappings, for now as test.
            //! TODO: delete after
            return initilizeDefaultMapper(businessID);
            //throw new ResourceException(Status.INTERNAL_SERVER_ERROR.getStatusCode(), "No mapping found for the business");
        }

        return scimToClaims;
    }

    private List<ScimToClaimMappingJpaEntity> filterValidBusinessClaimSets(List<ScimToClaimMappingJpaEntity> scimToClaims, List<BusinessIdentifierClaimSetResponse> businessesClaimSet){
        Set<ScimToClaimMappingJpaEntity> scimConfigMap = new HashSet<>();
        final List<ScimToClaimMappingJpaEntity> retrievedScimToClaimMapping = scimToClaims;
        for (BusinessIdentifierClaimSetResponse businessClaimSets : businessesClaimSet) {
            businessClaimSets.getClaimDefinitions().stream().forEach((cd) -> {
                for (ScimToClaimMappingJpaEntity entry : retrievedScimToClaimMapping) {
                    if (cd.getId().equals(entry.getClaimDefinitionReferenced())
                        && (businessClaimSets.getClaimSet().getId().equals(entry.getClaimSetOrigin()) 
                        || businessClaimSets.getClaimSet().getId().equals(entry.getClaimSetReferenced()))){
                        scimConfigMap.add(entry);
                    }
                }
            });
        }
        //Replace the retrieved mappings for the filtered ones.
        return scimConfigMap.stream().toList();
    }

    private ScimUser mapAccountAndClaimsToUserResource(AccountJpaEntity account, List<ScimToClaimMappingJpaEntity> scimToClaims, List<BusinessIdentifierClaimSetResponse> businessClaimSet) throws ResourceException {

        scimToClaims = filterValidBusinessClaimSets(scimToClaims, businessClaimSet);

        Map<String, ScimPathInfo> scimPathInfo = getPathInfoFromScimClaimsMapping(scimToClaims, createSchemaList());

        ScimUser userResource = new ScimUser()
            .setId(account.getId().toString())
            .setActive(account.isActive())
            .setMeta(
                new Meta()
                    .setResourceType("User")
                    .setCreated(LocalDateTime.ofInstant(account.getCreatedAt(), ZoneId.systemDefault()))
                    .setLastModified(LocalDateTime.ofInstant(account.getUpdatedAt(), ZoneId.systemDefault()))
                    .setLocation("/scim/v2/Users/".concat(account.getId().toString()))
            );
            final List<ClaimValueJpaEntity> claimList = getClaimValueQuery.getClaimValuesByOwnerTypeAndOwnerId(OwnerType.ACCOUNT, account.getId()).stream().filter(cv -> cv.getClaimSetClaimValue().getClaimSet().getAccountType().equals(AccountType.FEDERATED)).toList();
            scimToClaims.forEach(scimClaim -> {
                if (scimPathInfo.get(scimClaim.getFilterMapping()).getRootAttributeDefinition().isMultiValued()){
                    List<String> values = claimList.stream()
                        .filter(
                        cv ->
                            cv.getClaimDefinition().getId().equals(scimClaim.getClaimDefinitionReferenced()) &&
                            (cv.getClaimSetClaimValue().getClaimSetId().equals(scimClaim.getClaimSetOrigin()) ||
                            cv.getClaimSetClaimValue().getClaimSetId().equals(scimClaim.getClaimSetReferenced()))
                        ).map(ClaimValueJpaEntity::getValue).collect(Collectors.toList());
                    if (!values.isEmpty()){
                        values.stream().forEach(value -> {
                            ScimFilterMapper.setValue(userResource, scimClaim.getFilterMapping(), value);
                        });

                    }
                } else {
                    Optional<ClaimValueJpaEntity> optValue = claimList.stream().filter(
                        cv ->
                            cv.getClaimDefinition().getId().equals(scimClaim.getClaimDefinitionReferenced()) &&
                            (cv.getClaimSetClaimValue().getClaimSetId().equals(scimClaim.getClaimSetOrigin()) ||
                            cv.getClaimSetClaimValue().getClaimSetId().equals(scimClaim.getClaimSetReferenced()))
                    ).findFirst();
                    if (optValue.isPresent()){
                        ScimFilterMapper.setValue(userResource, scimClaim.getFilterMapping(), optValue.get().getValue());
                    }
                }
            });

        return userResource;
    }

    private Map<String, ScimPathInfo> getPathInfoFromScimClaimsMapping(List<ScimToClaimMappingJpaEntity> scimToClaims, List<String> schemaList){
        Map<String, ScimPathInfo> scimPathInfo = new HashMap<>();
        for (ScimToClaimMappingJpaEntity scimClaim : scimToClaims) {
            scimPathInfo.put(
                scimClaim.getFilterMapping(), 
                schemaValidation.getScimPathInfo(
                    scimClaim.getFilterMapping(), 
                    schemaList)
                );
        }
        return scimPathInfo;
    }

    private List<String> createSchemaList(){
        List<String> schemaList = new ArrayList<>();
        schemaList.add(ScimUser.SCHEMA_URI);
        this.getExtensionList().stream().forEach(schema -> {
            ScimExtension extension;
            try {
                extension = schema.getDeclaredConstructor().newInstance();
            } catch (InstantiationException | IllegalAccessException | IllegalArgumentException
                    | InvocationTargetException | NoSuchMethodException | SecurityException e) {
                throw new WebApplicationException("Failed to add the schema extensions to the schemaList detail: " + e.getMessage());
            }
            schemaList.add(extension.getUrn());
        });

        return schemaList;
    }

    private Specification<AccountJpaEntity> mapFilterToSpecification(Filter filter, List<ScimToClaimMappingJpaEntity> scimToClaims, Map<String, ScimPathInfo> scimPathInfo){
         Map<String, List<String>> filterMapping = new HashMap<>();

            //Static mapping for id and active properties.
            filterMapping.put("id", List.of("account.id"));
            filterMapping.put("active", List.of("account.lifecycleState"));
            Map<String, FieldSpecificConstraint> fieldSpecificConstraints = new HashMap<>();
            scimToClaims.forEach(scimClaim -> {
                ScimPathInfo pathInfo = scimPathInfo.get(scimClaim.getFilterMapping());
                //Without Filters.
                String normalizedField = pathInfo.getRootAttribute();
                if (pathInfo.getSubAttribute() != null){ 
                    normalizedField = normalizedField.concat(".").concat(pathInfo.getSubAttribute());
                }
                
                //Maps the searched value to the claim.value column.
                if (!filterMapping.containsKey(normalizedField)){
                    List<String> newList = new ArrayList<>();
                    newList.add("claim.value");
                    filterMapping.put(normalizedField, newList);
                }

                //maps primary attribute for each multivalued attribute that may have it.
                if (pathInfo.getRootAttributeDefinition().isMultiValued()){
                    String primaryMapping = pathInfo.getRootAttribute().concat(".primary");
                    filterMapping.put(primaryMapping, List.of("claim.isPrimary"));
                }

                //Maps the constraint of the ClaimDefinition to the corresponding UUID and claim value.
                if (fieldSpecificConstraints.containsKey(normalizedField)) {
                    FieldSpecificConstraint fsc = fieldSpecificConstraints.get(normalizedField);
                    Map<String, ColumnConstraint> columnConstraintMap = new HashMap<>();
                    columnConstraintMap.put("claim.value", new ColumnConstraint("claim.claimDefinition.id", scimClaim.getClaimDefinitionReferenced()));
                    columnConstraintMap.putAll(fsc.getFieldToConstraintMap());
                    fieldSpecificConstraints.put(normalizedField, new FieldSpecificConstraint(columnConstraintMap));
                } else {
                    Map<String, ColumnConstraint> columnConstraintMap = new HashMap<>();
                    columnConstraintMap.put("claim.value", new ColumnConstraint("claim.claimDefinition.id", scimClaim.getClaimDefinitionReferenced()));
                    fieldSpecificConstraints.put(normalizedField, new FieldSpecificConstraint(columnConstraintMap));
                }
            });

            //The filter maps the `account` or `claim` prefix to the classes AccountJpaEntity or ClaimValueJpaEntity correspondingly
            Specification<AccountJpaEntity> filterSpec = new FilterToSpecificationBuilder<AccountJpaEntity>(filterMapping, null, fieldSpecificConstraints).build(filter);

            return filterSpec;
    }

    private List<ScimToClaimMappingJpaEntity> initilizeDefaultMapper(UUID idBusiness){
        List<ScimToClaimMappingJpaEntity> scimToClaims = new ArrayList<>();
        Business business = retrieveBusiness.retrieveById(idBusiness);
            //CS <-> CD ó PATH
            scimToClaims.add(
                new ScimToClaimMappingJpaEntity(business,
                    "emails[primary eq true].value",
                    DataSeedConstants.FEDERATED_IDENTIFIER_CLAIM_SET_ID,
                    DataSeedConstants.FEDERATED_IDENTIFIER_CLAIM_SET_ID,
                    DataSeedConstants.CLAIM_DEF_EMAIL_ADDRESS_ID,
                    0,
                    "Extracts primary email"
            ));

            scimToClaims.add(
                new ScimToClaimMappingJpaEntity(business,
                    "name.givenName",
                    DataSeedConstants.FEDERATED_PROFILE_CLAIM_SET_ID,
                    DataSeedConstants.FEDERATED_PROFILE_CLAIM_SET_ID,
                    DataSeedConstants.CLAIM_DEF_FIRST_NAME_ID,
                    0,
                    "Extracts Name"
            ));

            scimToClaims.add(
                new ScimToClaimMappingJpaEntity(business,
                    "name.familyName",
                    DataSeedConstants.FEDERATED_PROFILE_CLAIM_SET_ID,
                    DataSeedConstants.FEDERATED_PROFILE_CLAIM_SET_ID,
                    DataSeedConstants.CLAIM_DEF_LAST_NAME_ID,
                    0,
                    "Extracts Surname"
            ));

            scimToClaims.add(
                new ScimToClaimMappingJpaEntity(business,
                    "userName",
                    DataSeedConstants.FEDERATED_PROFILE_CLAIM_SET_ID,
                    DataSeedConstants.FEDERATED_PROFILE_CLAIM_SET_ID,
                    DataSeedConstants.CLAIM_DEF_SCIM_USER_NAME_ID,
                    0,
                    "Extracts Username"
            ));

            scimToClaims.add(
                new ScimToClaimMappingJpaEntity(business,
                    "externalId",
                    DataSeedConstants.FEDERATED_PROFILE_CLAIM_SET_ID,
                    DataSeedConstants.FEDERATED_PROFILE_CLAIM_SET_ID,
                    DataSeedConstants.CLAIM_DEF_SCIM_EXTERNAL_ID,
                    0,
                    "Extracts externalId"
            ));

            scimToClaimMappingService.saveAll(scimToClaims);

            return scimToClaims;
    }
}
