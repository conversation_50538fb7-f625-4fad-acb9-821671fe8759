package com.vusecurity.auth.authorization.dto;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class TokenSettingsDtoTest {

    @Test
    void withDefaults_sets_expected_defaults() {
        TokenSettingsDto dto = TokenSettingsDto.withDefaults();
        assertNotNull(dto);
        assertEquals(300, dto.getAccessTokenTimeToLive());
        assertEquals("self-contained", dto.getAccessTokenFormat());
        assertEquals(Boolean.TRUE, dto.getReuseRefreshTokens());
        assertEquals(3600, dto.getRefreshTokenTimeToLive());
        assertEquals("RS256", dto.getIdTokenSignatureAlgorithm());
        assertEquals(Boolean.FALSE, dto.getX509CertificateBoundAccessTokens());
        assertEquals(300, dto.getAuthorizationCodeTimeToLive());
        assertEquals(300, dto.getDeviceCodeTimeToLive());
    }
}

