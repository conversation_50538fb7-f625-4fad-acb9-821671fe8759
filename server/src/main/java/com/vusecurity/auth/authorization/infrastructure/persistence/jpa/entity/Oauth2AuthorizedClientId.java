package com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity;

import java.io.Serializable;
import java.util.Objects;

public class Oauth2AuthorizedClientId implements Serializable {
    private String clientRegistrationId;
    private String principalName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Oauth2AuthorizedClientId that)) return false;
        return Objects.equals(clientRegistrationId, that.clientRegistrationId) &&
                Objects.equals(principalName, that.principalName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(clientRegistrationId, principalName);
    }
}
