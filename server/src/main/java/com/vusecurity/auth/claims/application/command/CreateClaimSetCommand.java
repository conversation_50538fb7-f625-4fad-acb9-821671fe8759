package com.vusecurity.auth.claims.application.command;

import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.LookupStrategy;

import java.util.UUID;

public record CreateClaimSetCommand(
        UUID businessId,
        AccountType accountType,
        Boolean isIdentifier,
        String name,
        String description,
        LookupStrategy lookupStrategy) {

    public void validate() {
        if (businessId == null) throw new IllegalArgumentException("businessId is required");
        if (accountType == null) throw new IllegalArgumentException("accountType is required");
        if (isIdentifier == null) throw new IllegalArgumentException("isIdentifier is required");
        if (name == null || name.trim().isEmpty()) throw new IllegalArgumentException("name is required");
    }
}
