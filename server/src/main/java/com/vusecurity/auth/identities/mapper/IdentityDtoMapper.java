package com.vusecurity.auth.identities.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vusecurity.auth.contracts.api.v1.dto.identities.IdentityResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BusinessBasicInfoResponse;
import com.vusecurity.auth.identities.application.handler.GetIdentityBusinessHandler;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.shared.util.IncludeParams;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Manual mapper for Identity DTOs and entities.
 * Follows the simplified refactoring pattern without MapStruct.
 */
@Component
@RequiredArgsConstructor
public class IdentityDtoMapper {

    private final ObjectMapper objectMapper;
    private final AccountDtoMapper accountDtoMapper;
    private final GetIdentityBusinessHandler getIdentityBusinessHandler;

    /**
     * Convert IdentityJpaEntity to IdentityResponse.
     */
    public IdentityResponse toResponse(IdentityJpaEntity entity) {
        return toResponse(entity, new IncludeParams(null, Collections.emptySet()));
    }

    /**
     * Convert IdentityJpaEntity to IdentityResponse.
     */
    public IdentityResponse toResponse(IdentityJpaEntity entity, IncludeParams includes) {
        if (entity == null) {
            return null;
        }

        IdentityResponse response = new IdentityResponse();
        response.setId(entity.getId());
        response.setName(entity.getName());
        response.setIdentityType(entity.getIdentityType());
        response.setLifecycleState(entity.getLifecycleState());
        response.setMetadata(entity.getMetadata());

        // Set audit information
        response.setAudit(new BaseResponse.AuditInfo()
                .setCreatedAt(entity.getCreatedAt())
                .setUpdatedAt(entity.getUpdatedAt())
                .setCreatedBy(entity.getCreatedBy())
                .setUpdatedBy(entity.getUpdatedBy()));

        // Map accounts if they are loaded (to avoid lazy loading issues)
        if (entity.getAccounts() != null) {
            response.setAccounts(
                entity.getAccounts().stream()
                    .map(accountDtoMapper::toResponse)
                    .toList()
            );

            // Add business if requested
            if (includes.contains("business")) {
                Set<UUID> businessIds = entity.getAccounts().stream()
                        .map(AccountJpaEntity::getBusinessId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                List<BusinessBasicInfoResponse> businessList = getIdentityBusinessHandler.retrieveAllBusinessBasicInfo(businessIds);

                response.setBusinesses(businessList);
            }
        }

        return response;
    }

    /**
     * Parse metadata from JSON string.
     */
    public Map<String, Object> parseMetadata(String metadataJson) {
        if (metadataJson == null || metadataJson.trim().isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            return objectMapper.readValue(metadataJson, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            // Log the error and return empty map instead of throwing exception
            return Collections.emptyMap();
        }
    }

    /**
     * Convert metadata map to JSON string.
     */
    public String metadataToJson(Map<String, Object> metadata) {
        if (metadata == null || metadata.isEmpty()) {
            return null;
        }

        try {
            return objectMapper.writeValueAsString(metadata);
        } catch (JsonProcessingException e) {
            // Log the error and return null instead of throwing exception
            return null;
        }
    }
}
