package com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition;

import com.vusecurity.auth.contracts.enums.ClaimType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.contracts.validations.ValidRegex;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.UUID;

@Data
@Schema(description = "Request to update an existing claim definition")
public class UpdateClaimDefinitionRequest {
    @Size(max = 255, message = "Code must not exceed 255 characters")
    @Schema(description = "New unique code identifier for the claim definition",
            example = "updated_email_address")
    private String code;

    @Size(max = 255, message = "Name must not exceed 255 characters")
    @Schema(description = "New human-readable name for the claim definition",
            example = "Updated Email Address")
    private String name;

    @Size(max = 1024, message = "Description must not exceed 1024 characters")
    @Schema(description = "New detailed description of what this claim represents",
            example = "Updated primary email address of the user")
    private String description;

    @Schema(description = "Type of the claim",
            example = "USER_DEFINED | SYSTEM_DEFINED")
    private ClaimType claimType = ClaimType.USER_DEFINED;

    @Schema(description = "New data type of the claim value",
            example = "STRING")
    private DataTypeEnum dataType;

    @Size(max = 1024, message = "Data format must not exceed 1024 characters")
    @Schema(description = "New regular expression or format specification for validation",
            example = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
    @ValidRegex
    private String dataFormat;

    @Schema(description = "New reference to another claim definition if this is a list type",
            example = "f9fc0a64-56fc-4477-87a7-8e525505abce")
    private UUID isAListOf;
}
