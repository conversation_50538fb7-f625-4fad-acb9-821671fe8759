package com.vusecurity.auth.authorization.application.command;

import com.vusecurity.auth.authorization.application.exception.InvalidGroupRequestException;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for UpdateGroupCommand validation logic.
 */
class UpdateGroupCommandTest {

    @Test
    void shouldValidateSuccessfully_WhenAllFieldsAreValid() {
        // Given
        UUID groupId = UUID.randomUUID();
        UUID ownerId = UUID.randomUUID();
        UpdateGroupCommand command = new UpdateGroupCommand(
                groupId,
                "New Group Name",
                "New description.",
                List.of(UUID.randomUUID()),
                List.of(ownerId),
                List.of(ownerId),
                null
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldThrowException_WhenGroupIdIsNull() {
        // Given
        UpdateGroupCommand command = new UpdateGroupCommand(null, "Name", "Desc", List.of(UUID.randomUUID()), null, null, null);

        // When & Then
        var exception = assertThrows(InvalidGroupRequestException.class, command::validate);
        assertEquals("groupId is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenNameIsNull() {
        // Given
        UpdateGroupCommand command = new UpdateGroupCommand(UUID.randomUUID(), null, "Desc", List.of(UUID.randomUUID()), null, null, null);

        // When & Then
        var exception = assertThrows(InvalidGroupRequestException.class, command::validate);
        assertEquals("name is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenOwnerIdIsNotInAccountIds() {
        // Given
        UpdateGroupCommand command = new UpdateGroupCommand(
                UUID.randomUUID(),
                "Group Name",
                "Desc",
                null,
                List.of(UUID.randomUUID()),
                List.of(UUID.randomUUID()), // Different ID
                null
        );

        // When & Then
        var exception = assertThrows(InvalidGroupRequestException.class, command::validate);
        assertEquals("All ownerAccountIds must also be present in accountIds", exception.getMessage());
    }
} 