package com.vusecurity.auth.claims.application.service;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.service.ClaimSetDefinitionMappingService;
import com.vusecurity.auth.claims.mapper.ClaimDefinitionDtoMapper;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition.ClaimDefinitionSummaryResponse;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.BusinessIdentifierClaimSetResponse;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.business.domain.Business;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ClaimDefinitionLookupService.
 * Tests service logic with mocked dependencies.
 */
@ExtendWith(MockitoExtension.class)
class ClaimDefinitionLookupServiceTest {

    @Mock
    private ClaimSetRepository claimSetRepository;

    @Mock
    private ClaimSetDefinitionMappingService mappingService;

    @Mock
    private ClaimDefinitionDtoMapper claimDefinitionDtoMapper;

    private ClaimDefinitionLookupService service;

    private UUID businessId;
    private ClaimSetJpaEntity testClaimSet;
    private ClaimDefinitionJpaEntity testClaimDefinition;
    private Business testBusiness;

    @BeforeEach
    void setUp() {
        service = new ClaimDefinitionLookupService(claimSetRepository, mappingService, claimDefinitionDtoMapper);
        businessId = UUID.randomUUID();

        // Create test business
        testBusiness = new Business();
        testBusiness.setId(businessId);
        testBusiness.setName("Test Business");

        // Create test claim set
        testClaimSet = new ClaimSetJpaEntity(
                UUID.randomUUID(),
                testBusiness,
                AccountType.WORKFORCE,
                true,
                "Employee Identifier Claims",
                "Claims used to identify employees"
        );

        // Create test claim definition
        testClaimDefinition = new ClaimDefinitionJpaEntity(
                UUID.randomUUID(),
                "email",
                "Email Address",
                "User's email address",
                DataTypeEnum.STRING,
                "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
        );
    }

    @Test
    void shouldReturnClaimSetWithDefinitions_WhenClaimSetExists() {
        // Given
        List<ClaimDefinitionJpaEntity> claimDefinitions = List.of(testClaimDefinition);
        ClaimDefinitionSummaryResponse mockSummaryResponse = new ClaimDefinitionSummaryResponse()
                .setId(testClaimDefinition.getId())
                .setCode(testClaimDefinition.getCode())
                .setName(testClaimDefinition.getName())
                .setDescription(testClaimDefinition.getDescription())
                .setDataType(testClaimDefinition.getDataType())
                .setDataFormat(testClaimDefinition.getDataFormat());

        when(claimSetRepository.findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true))
                .thenReturn(Optional.of(testClaimSet));
        when(mappingService.getClaimDefinitionsForClaimSet(testClaimSet))
                .thenReturn(claimDefinitions);
        when(claimDefinitionDtoMapper.toSummaryResponse(testClaimDefinition))
                .thenReturn(mockSummaryResponse);

        // When
        BusinessIdentifierClaimSetResponse result = service.getIdentifierClaimSet(businessId, AccountType.WORKFORCE);

        // Then
        assertNotNull(result);
        assertNotNull(result.getClaimSet());
        assertNotNull(result.getClaimDefinitions());

        // Verify claim set info
        BusinessIdentifierClaimSetResponse.ClaimSetInfo claimSetInfo = result.getClaimSet();
        assertEquals(testClaimSet.getId(), claimSetInfo.getId());
        assertEquals(testClaimSet.getBusinessId(), claimSetInfo.getBusinessId());
        assertEquals(AccountType.WORKFORCE, claimSetInfo.getAccountType());
        assertEquals(testClaimSet.getIsIdentifier(), claimSetInfo.getIsIdentifier());
        assertEquals(testClaimSet.getName(), claimSetInfo.getName());
        assertEquals(testClaimSet.getDescription(), claimSetInfo.getDescription());
        assertEquals(testClaimSet.getLookupStrategy(), claimSetInfo.getLookupStrategy());

        // Verify claim definitions
        assertEquals(1, result.getClaimDefinitions().size());
        ClaimDefinitionSummaryResponse claimDef = result.getClaimDefinitions().get(0);
        assertEquals(testClaimDefinition.getId(), claimDef.getId());
        assertEquals(testClaimDefinition.getCode(), claimDef.getCode());

        verify(claimSetRepository).findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true);
        verify(mappingService).getClaimDefinitionsForClaimSet(testClaimSet);
        verify(claimDefinitionDtoMapper).toSummaryResponse(testClaimDefinition);
    }

    @Test
    void shouldReturnClaimSetWithMultipleDefinitions_WhenClaimSetHasMultipleDefinitions() {
        // Given
        ClaimDefinitionJpaEntity claimDef2 = new ClaimDefinitionJpaEntity(
                UUID.randomUUID(),
                "username",
                "Username",
                "User's username",
                DataTypeEnum.STRING,
                null
        );

        List<ClaimDefinitionJpaEntity> claimDefinitions = List.of(testClaimDefinition, claimDef2);
        ClaimDefinitionSummaryResponse mockSummaryResponse1 = new ClaimDefinitionSummaryResponse().setId(testClaimDefinition.getId());
        ClaimDefinitionSummaryResponse mockSummaryResponse2 = new ClaimDefinitionSummaryResponse().setId(claimDef2.getId());

        when(claimSetRepository.findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true))
                .thenReturn(Optional.of(testClaimSet));
        when(mappingService.getClaimDefinitionsForClaimSet(testClaimSet))
                .thenReturn(claimDefinitions);
        when(claimDefinitionDtoMapper.toSummaryResponse(testClaimDefinition))
                .thenReturn(mockSummaryResponse1);
        when(claimDefinitionDtoMapper.toSummaryResponse(claimDef2))
                .thenReturn(mockSummaryResponse2);

        // When
        BusinessIdentifierClaimSetResponse result = service.getIdentifierClaimSet(businessId, AccountType.WORKFORCE);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getClaimDefinitions().size());

        verify(claimSetRepository).findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true);
        verify(mappingService).getClaimDefinitionsForClaimSet(testClaimSet);
        verify(claimDefinitionDtoMapper).toSummaryResponse(testClaimDefinition);
        verify(claimDefinitionDtoMapper).toSummaryResponse(claimDef2);
    }

    @Test
    void shouldThrowNoSuchElementException_WhenClaimSetNotFound() {
        // Given
        when(claimSetRepository.findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true))
                .thenReturn(Optional.empty());

        // When & Then
        NoSuchElementException exception = assertThrows(
                NoSuchElementException.class,
                () -> service.getIdentifierClaimSet(businessId, AccountType.WORKFORCE)
        );

        assertEquals("No identifier ClaimSet found for business: " + businessId + " and account type: WORKFORCE", exception.getMessage());
        verify(claimSetRepository).findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true);
        verifyNoInteractions(mappingService);
        verifyNoInteractions(claimDefinitionDtoMapper);
    }

    @Test
    void shouldThrowIllegalArgumentException_WhenBusinessIdIsNull() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> service.getIdentifierClaimSet(null, AccountType.WORKFORCE)
        );

        assertEquals("businessId cannot be null", exception.getMessage());
        verifyNoInteractions(claimSetRepository);
        verifyNoInteractions(mappingService);
        verifyNoInteractions(claimDefinitionDtoMapper);
    }

    @Test
    void shouldThrowIllegalArgumentException_WhenAccountTypeIsNull() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> service.getIdentifierClaimSet(businessId, null)
        );

        assertEquals("accountType cannot be null", exception.getMessage());
        verifyNoInteractions(claimSetRepository);
        verifyNoInteractions(mappingService);
        verifyNoInteractions(claimDefinitionDtoMapper);
    }

    @Test
    void shouldCallRepositoryWithCorrectBusinessId() {
        // Given
        UUID specificBusinessId = UUID.fromString("6e5349c6-d6e9-4f25-b117-2591bc240b3e");
        List<ClaimDefinitionJpaEntity> claimDefinitions = List.of(testClaimDefinition);
        ClaimDefinitionSummaryResponse mockSummaryResponse = new ClaimDefinitionSummaryResponse().setId(testClaimDefinition.getId());

        when(claimSetRepository.findByBusinessIdAndAccountTypeAndIsIdentifier(specificBusinessId, AccountType.WORKFORCE, true))
                .thenReturn(Optional.of(testClaimSet));
        when(mappingService.getClaimDefinitionsForClaimSet(testClaimSet))
                .thenReturn(claimDefinitions);
        when(claimDefinitionDtoMapper.toSummaryResponse(testClaimDefinition))
                .thenReturn(mockSummaryResponse);

        // When
        service.getIdentifierClaimSet(specificBusinessId, AccountType.WORKFORCE);

        // Then
        verify(claimSetRepository).findByBusinessIdAndAccountTypeAndIsIdentifier(specificBusinessId, AccountType.WORKFORCE, true);
        verify(mappingService).getClaimDefinitionsForClaimSet(testClaimSet);
        verify(claimDefinitionDtoMapper).toSummaryResponse(testClaimDefinition);
    }

    @Test
    void shouldHandleRepositoryException() {
        // Given
        RuntimeException repositoryException = new RuntimeException("Database error");
        when(claimSetRepository.findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true))
                .thenThrow(repositoryException);

        // When & Then
        RuntimeException exception = assertThrows(
                RuntimeException.class,
                () -> service.getIdentifierClaimSet(businessId, AccountType.WORKFORCE)
        );

        assertEquals("Database error", exception.getMessage());
        verify(claimSetRepository).findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true);
        verifyNoInteractions(mappingService);
        verifyNoInteractions(claimDefinitionDtoMapper);
    }
}
