package com.vusecurity.auth.contracts.api.v1.dto.claims.claimset;


import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.LookupStrategy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "Claim set information response")
public class ClaimSetResponse extends BaseResponse {
    @Schema(description = "Unique identifier of the claim set",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    @Schema(description = "Unique identifier of the business this claim set belongs to",
            example = "987fcdeb-51a2-43d1-9c4f-123456789abc")
    private UUID businessId;

    @Schema(description = "Name of the business this claim set belongs to",
            example = "Test Business")
    private String businessName;

    @Schema(description = "Account type this claim set applies to",
            example = "WORKFORCE")
    private AccountType accountType;

    @Schema(description = "Whether this claim set is an identifier claim set (part of composite primary key)",
            example = "true")
    private Boolean isIdentifier;

    @Schema(description = "Name of the claim set",
            example = "Employee Basic Claims")
    private String name;

    @Schema(description = "Detailed description of the claim set",
            example = "Basic claims required for all employee accounts")
    private String description;

    @Schema(description = "Strategy for resolving claim handles when multiple claim definitions are involved",
            example = "ALL_CLAIMS_MUST_MATCH")
    private LookupStrategy lookupStrategy;

    @Schema(description = "List of claim definitions included in this claim set, ordered by claimDefinitionOrder")
    private List<ClaimDefinitionInClaimSetResponse> claimDefinitions;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        ClaimSetResponse that = (ClaimSetResponse) o;
        return Objects.equals(id, that.id) &&
               Objects.equals(businessId, that.businessId) &&
               Objects.equals(businessName, that.businessName) &&
               accountType == that.accountType &&
               Objects.equals(isIdentifier, that.isIdentifier) &&
               Objects.equals(name, that.name) &&
               Objects.equals(description, that.description) &&
               lookupStrategy == that.lookupStrategy &&
               Objects.equals(claimDefinitions, that.claimDefinitions);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), id, businessId, businessName, accountType, isIdentifier, name, description, lookupStrategy, claimDefinitions);
    }
}
