package com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.PermissionJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

/**
 * Spring Data JPA repository for PermissionJpaEntity.
 * Infrastructure-only interface for database operations.
 */
@Repository
public interface PermissionRepository extends JpaRepository<PermissionJpaEntity, UUID>, JpaSpecificationExecutor<PermissionJpaEntity> {

    /**
     * Find permission by ID.
     * @param id the permission ID
     * @return the permission if found
     */
    Optional<PermissionJpaEntity> findById(UUID id);

    /**
     * Find permission by name.
     * @param name the permission name
     * @return the permission if found
     */
    Optional<PermissionJpaEntity> findByName(String name);

    /**
     * Check if permission exists by name.
     * @param name the permission name
     * @return true if exists, false otherwise
     */
    boolean existsByName(String name);

    /**
     * Check if permission exists by ID.
     * @param id the permission ID
     * @return true if exists, false otherwise
     */
    boolean existsById(UUID id);

    /**
     * Delete permission by ID.
     * @param id the permission ID
     */
    void deleteById(UUID id);
}
