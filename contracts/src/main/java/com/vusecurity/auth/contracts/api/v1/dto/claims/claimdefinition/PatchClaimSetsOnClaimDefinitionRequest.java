package com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "Request to partially update claim sets associations in a claim definition. Allows adding new claim sets and updating properties of existing ones without affecting unmentioned associations.")
public class PatchClaimSetsOnClaimDefinitionRequest {
    
    @Schema(description = "List of claim sets to add or update. Existing claim sets not mentioned in this list will remain unchanged.")
    private List<ClaimSetToClaimDefinition> claimSets;
} 