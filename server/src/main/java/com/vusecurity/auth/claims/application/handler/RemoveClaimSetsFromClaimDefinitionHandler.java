package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.RemoveClaimSetsFromClaimDefinitionCommand;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.service.ClaimSetDefinitionMappingService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
public class RemoveClaimSetsFromClaimDefinitionHandler {
    private static final Logger logger = LoggerFactory.getLogger(RemoveClaimSetsFromClaimDefinitionHandler.class);

    private final ClaimDefinitionRepository claimDefinitionRepository;
    private final ClaimSetRepository claimSetRepository;
    private final ClaimSetDefinitionMappingService mappingService;

    @Transactional
    public void removeClaimSetsFromClaimDefinition(RemoveClaimSetsFromClaimDefinitionCommand command) {
        logger.debug("Removing claim sets from claim definition with command: {}", command);

        try {
            // Validate command
            command.validate();

            // Find the claim definition
            ClaimDefinitionJpaEntity claimDefinition = claimDefinitionRepository.findById(command.claimDefinitionId())
                    .orElseThrow(() -> new IllegalArgumentException("Claim definition not found with ID: " + command.claimDefinitionId()));

            // Find all claim sets to remove
            command.claimSetIds().forEach(claimSetId -> {
                ClaimSetJpaEntity claimSet = claimSetRepository.findById(claimSetId)
                    .orElseThrow(() -> new IllegalArgumentException("Claim set not found with ID: " + claimSetId));

                // Remove the mapping between claim set and claim definition
                mappingService.removeClaimDefinitionFromClaimSet(claimSet, claimDefinition);
            });

            logger.debug("Successfully removed claim sets from claim definition: {}", command.claimDefinitionId());

        }
        catch (Exception e) {
            logger.error("Error removing claim sets from claim definition {}: {}", command.claimDefinitionId(), e.getMessage(), e);
            throw e;
        }
    }
}
