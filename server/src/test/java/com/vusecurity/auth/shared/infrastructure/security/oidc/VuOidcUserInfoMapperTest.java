package com.vusecurity.auth.shared.infrastructure.security.oidc;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.time.Instant;
import java.util.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.oauth2.core.oidc.OidcUserInfo;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.authorization.oidc.authentication.OidcUserInfoAuthenticationContext;
import org.springframework.security.oauth2.server.authorization.oidc.authentication.OidcUserInfoAuthenticationToken;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.identities.application.query.GetAccountClaimsQuery;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;

import com.vusecurity.business.domain.Business;

/**
 * Unit tests for VuOidcUserInfoMapper.
 * Tests the OIDC UserInfo mapping functionality with comprehensive coverage.
 */
@ExtendWith(MockitoExtension.class)
class VuOidcUserInfoMapperTest {

    @Mock
    private AccountDetailService accountDetailService;

    @Mock
    private GetAccountClaimsQuery getAccountClaimsQuery;

    @Mock
    private OidcUserInfoAuthenticationContext context;

    @Mock
    private OidcUserInfoAuthenticationToken authenticationToken;

    @Mock
    private JwtAuthenticationToken jwtAuthenticationToken;

    @Mock
    private Jwt jwt;

    private VuOidcUserInfoMapper mapper;
    private AccountJpaEntity testAccount;
    private AccountUserDetails testUserDetails;
    private Map<String, Object> testClaimValues;

    @BeforeEach
    void setUp() {
        // Create test account
        testAccount = createTestAccount();
        
        // Create test claim values
        testClaimValues = createTestClaimValues();
        
        // Create test user details
        testUserDetails = new AccountUserDetails(testAccount, testClaimValues);
        
        // Create mapper with default config
        mapper = new VuOidcUserInfoMapper(accountDetailService, getAccountClaimsQuery, null);
    }

    @Test
    void shouldCreateMapperWithValidDependencies() {
        // When
        VuOidcUserInfoMapper newMapper = new VuOidcUserInfoMapper(accountDetailService, getAccountClaimsQuery, null);

        // Then
        assertNotNull(newMapper);
    }

    @Test
    void shouldBuildUserInfoWithBasicClaims() {
        // Given
        setupSuccessfulAuthentication();
        when(accountDetailService.loadUserByUsername(anyString())).thenReturn(testUserDetails);

        // When
        OidcUserInfo userInfo = mapper.apply(context);

        // Then
        assertNotNull(userInfo);
        assertEquals(testAccount.getId().toString(), userInfo.getSubject());
        
        // Check that basic claims are present
        Map<String, Object> claims = userInfo.getClaims();
        assertNotNull(claims);
        assertTrue(claims.containsKey("sub"));
        assertEquals(testAccount.getId().toString(), claims.get("sub"));
    }

    @Test
    void shouldIncludeDisplayNameWhenAvailable() {
        // Given
        setupSuccessfulAuthentication();
        when(accountDetailService.loadUserByUsername(anyString())).thenReturn(testUserDetails);

        // When
        OidcUserInfo userInfo = mapper.apply(context);

        // Then
        assertNotNull(userInfo);
        Map<String, Object> claims = userInfo.getClaims();
        assertEquals("John Doe", claims.get("name"));
    }

    @Test
    void shouldIncludeStandardOidcClaims() {
        // Given
        setupSuccessfulAuthentication();
        when(accountDetailService.loadUserByUsername(anyString())).thenReturn(testUserDetails);

        // When
        OidcUserInfo userInfo = mapper.apply(context);

        // Then
        assertNotNull(userInfo);
        Map<String, Object> claims = userInfo.getClaims();
        
        // Check standard OIDC claims
        assertEquals("<EMAIL>", claims.get("email"));
        assertEquals("John", claims.get("given_name"));
        assertEquals("Doe", claims.get("family_name"));
        assertEquals("+**********", claims.get("phone_number"));
    }

    @Test
    void shouldIncludeAuthorities() {
        // Given
        setupSuccessfulAuthentication();
        when(accountDetailService.loadUserByUsername(anyString())).thenReturn(testUserDetails);

        // When
        OidcUserInfo userInfo = mapper.apply(context);

        // Then
        assertNotNull(userInfo);
        Map<String, Object> claims = userInfo.getClaims();
        
        @SuppressWarnings("unchecked")
        List<String> authorities = (List<String>) claims.get("authorities");
        assertNotNull(authorities);
        assertFalse(authorities.isEmpty());
    }

    @Test
    void shouldIncludeUpdatedAtTimestamp() {
        // Given
        setupSuccessfulAuthentication();
        when(accountDetailService.loadUserByUsername(anyString())).thenReturn(testUserDetails);

        // When
        OidcUserInfo userInfo = mapper.apply(context);

        // Then
        assertNotNull(userInfo);
        Map<String, Object> claims = userInfo.getClaims();
        assertNotNull(claims.get("updated_at"));
        assertTrue(claims.get("updated_at") instanceof Long);
    }

    @Test
    void shouldHandleExceptionAndReturnMinimalUserInfo() {
        // Given
        setupSuccessfulAuthentication();
        when(accountDetailService.loadUserByUsername(anyString())).thenThrow(new RuntimeException("Database error"));

        // When
        OidcUserInfo userInfo = mapper.apply(context);

        // Then
        assertNotNull(userInfo);
        assertEquals("test-subject", userInfo.getSubject());
        assertEquals(1, userInfo.getClaims().size()); // Only subject claim
    }

    @Test
    void shouldHandleExceptionInTokenExtractionAndReturnUnknownSubject() {
        // Given
        when(context.getAuthentication()).thenThrow(new RuntimeException("Token error"));

        // When
        OidcUserInfo userInfo = mapper.apply(context);

        // Then
        assertNotNull(userInfo);
        assertEquals("unknown", userInfo.getSubject());
        assertEquals(1, userInfo.getClaims().size()); // Only subject claim
    }

    @Test
    void shouldHandleNullAccountTimestamps() {
        // Given
        setupSuccessfulAuthentication();
        AccountJpaEntity accountWithoutTimestamps = createTestAccount();
        accountWithoutTimestamps.setUpdatedAt(null);
        accountWithoutTimestamps.setCreatedAt(null);
        
        AccountUserDetails userDetailsWithoutTimestamps = new AccountUserDetails(accountWithoutTimestamps, testClaimValues);
        when(accountDetailService.loadUserByUsername(anyString())).thenReturn(userDetailsWithoutTimestamps);

        // When
        OidcUserInfo userInfo = mapper.apply(context);

        // Then
        assertNotNull(userInfo);
        Map<String, Object> claims = userInfo.getClaims();
        assertFalse(claims.containsKey("updated_at"));
    }

    @Test
    void shouldUseCreatedAtWhenUpdatedAtIsNull() {
        // Given
        setupSuccessfulAuthentication();
        AccountJpaEntity accountWithCreatedAtOnly = createTestAccount();
        accountWithCreatedAtOnly.setUpdatedAt(null);
        Instant createdAt = Instant.now().minusSeconds(7200);
        accountWithCreatedAtOnly.setCreatedAt(createdAt);
        
        AccountUserDetails userDetailsWithCreatedAtOnly = new AccountUserDetails(accountWithCreatedAtOnly,testClaimValues);
        when(accountDetailService.loadUserByUsername(anyString())).thenReturn(userDetailsWithCreatedAtOnly);

        // When
        OidcUserInfo userInfo = mapper.apply(context);

        // Then
        assertNotNull(userInfo);
        Map<String, Object> claims = userInfo.getClaims();
        assertEquals(createdAt.getEpochSecond(), claims.get("updated_at"));
    }

    // Helper methods
    private void setupSuccessfulAuthentication() {
        when(context.getAuthentication()).thenReturn(authenticationToken);
        when(authenticationToken.getPrincipal()).thenReturn(jwtAuthenticationToken);
        when(jwtAuthenticationToken.getToken()).thenReturn(jwt);
        when(jwt.getSubject()).thenReturn("test-subject");
    }

    private AccountJpaEntity createTestAccount() {
        Business business = new Business();
        business.setId(UUID.randomUUID());
        business.setName("Test Business");

        IdentityJpaEntity identity = new IdentityJpaEntity();
        identity.setId(UUID.randomUUID());
        identity.setName("John Doe");

        IdentityProviderJpaEntity identityProvider = new IdentityProviderJpaEntity();
        identityProvider.setId(UUID.randomUUID());
        identityProvider.setName("Test Provider");

        AccountJpaEntity account = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        account.setId(UUID.randomUUID());
        account.setLifecycleState(AccountLifecycleState.ACTIVE);
        account.setUpdatedAt(Instant.now());
        account.setCreatedAt(Instant.now().minusSeconds(3600));

        return account;
    }

    private Map<String, Object> createTestClaimValues() {
        Map<String, Object> claimValues = new HashMap<>();
        
        // Email claim
        Map<String, Object> emailClaim = new HashMap<>();
        emailClaim.put("value", "<EMAIL>");
        emailClaim.put("isVerified", true);
        claimValues.put("email_address", emailClaim);
        
        // Phone claim
        Map<String, Object> phoneClaim = new HashMap<>();
        phoneClaim.put("value", "+**********");
        phoneClaim.put("isVerified", true);
        claimValues.put("phone_number", phoneClaim);
        
        // Name claims
        Map<String, Object> firstNameClaim = new HashMap<>();
        firstNameClaim.put("value", "John");
        claimValues.put("first_name", firstNameClaim);
        
        Map<String, Object> lastNameClaim = new HashMap<>();
        lastNameClaim.put("value", "Doe");
        claimValues.put("last_name", lastNameClaim);
        
        return claimValues;
    }

    @Test
    void shouldHandleEmptyClaimValues() {
        // Given
        setupSuccessfulAuthentication();
        Map<String, Object> emptyClaimValues = new HashMap<>();
        AccountUserDetails userDetailsWithEmptyName = new AccountUserDetails(testAccount, emptyClaimValues);
        when(accountDetailService.loadUserByUsername(anyString())).thenReturn(userDetailsWithEmptyName);

        // When
        OidcUserInfo userInfo = mapper.apply(context);

        // Then
        assertNotNull(userInfo);
        Map<String, Object> claims = userInfo.getClaims();
        assertEquals(testAccount.getId().toString(), claims.get("sub"));
        // The display name should fallback to identity name "John Doe"
        assertEquals("John Doe", claims.get("name"));
    }

    @Test
    void shouldHandleNullClaimValues() {
        // Given
        setupSuccessfulAuthentication();
        AccountUserDetails userDetailsWithNullClaims = new AccountUserDetails(testAccount, null);
        when(accountDetailService.loadUserByUsername(anyString())).thenReturn(userDetailsWithNullClaims);

        // When
        OidcUserInfo userInfo = mapper.apply(context);

        // Then
        assertNotNull(userInfo);
        Map<String, Object> claims = userInfo.getClaims();
        assertEquals(testAccount.getId().toString(), claims.get("sub"));
        assertEquals("John Doe", claims.get("name"));
    }

    @Test
    void shouldHandleNullAccountIds() {
        // Given
        setupSuccessfulAuthentication();
        AccountJpaEntity accountWithNullIds = new AccountJpaEntity();
        accountWithNullIds.setId(UUID.randomUUID());
        accountWithNullIds.setLifecycleState(AccountLifecycleState.ACTIVE);

        AccountUserDetails userDetailsWithNullIds = new AccountUserDetails(accountWithNullIds, testClaimValues);
        when(accountDetailService.loadUserByUsername(anyString())).thenReturn(userDetailsWithNullIds);

        // When
        OidcUserInfo userInfo = mapper.apply(context);

        // Then
        assertNotNull(userInfo);
        Map<String, Object> claims = userInfo.getClaims();
        assertEquals(accountWithNullIds.getId().toString(), claims.get("sub"));
        // Should not contain business_id, identity_id, etc. since they're null
        assertFalse(claims.containsKey("business_id"));
        assertFalse(claims.containsKey("identity_id"));
        assertFalse(claims.containsKey("identity_provider_id"));
    }
}
