package com.vusecurity.auth.shared.util;

import java.util.*;
import java.util.stream.Collectors;

public class IncludeParams {

    private final Set<String> requested;
    private final Set<String> allowed;

    public IncludeParams(String rawIncludes, Set<String> allowedIncludes) {
        this.allowed = allowedIncludes.stream()
                .map(String::toLowerCase)
                .collect(Collectors.toSet());

        this.requested = rawIncludes != null
                ? Arrays.stream(rawIncludes.split(","))
                .map(String::trim)
                .map(String::toLowerCase)
                .filter(allowed::contains)
                .collect(Collectors.toSet())
                : Collections.emptySet();
    }

    public boolean contains(String value) {
        return requested.contains(value.toLowerCase());
    }

    public Set<String> getRequested() {
        return Collections.unmodifiableSet(requested);
    }

    public Set<String> getAllowed() {
        return Collections.unmodifiableSet(allowed);
    }
}
