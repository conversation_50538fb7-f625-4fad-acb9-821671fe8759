package com.vusecurity.auth.scim.api.handler;

import com.vusecurity.auth.scim.application.exception.InvalidScimMapperRequest;
import com.vusecurity.auth.scim.application.exception.ScimMapperAlreadyExistsException;
import com.vusecurity.auth.scim.application.exception.ScimMapperNotFoundException;
import com.vusecurity.auth.scim.application.exception.ScimTokenNotFoundException;
import com.vusecurity.core.commons.ApiMessage;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice(assignableTypes = {com.vusecurity.auth.scim.api.controller.ScimMappersController.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ScimMappersErrorControllerHandler {

    @ExceptionHandler(ScimTokenNotFoundException.class)
    public ResponseEntity<ApiMessage> handleMapperNotFoundException(ScimMapperNotFoundException ex) {
        ApiMessage error = new ApiMessage(5700, ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }

    @ExceptionHandler(ScimMapperAlreadyExistsException.class)
    public ResponseEntity<ApiMessage> handleMapperAlreadyExistsException(ScimMapperAlreadyExistsException ex) {
        ApiMessage error = new ApiMessage(5701, ex.getMessage());
        return ResponseEntity.status(HttpStatus.CONFLICT).body(error);
    }

    @ExceptionHandler(InvalidScimMapperRequest.class)
    public ResponseEntity<ApiMessage> handleInvalidMapperRequestException(InvalidScimMapperRequest ex) {
        ApiMessage error = new ApiMessage(5704, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<ApiMessage> handleDataIntegrityViolationException(DataIntegrityViolationException ex) {
        String message = ex.getMessage();
        String userMessage = "A record with the same unique value already exists.";
        int code = 5706;

        if (message != null) {
            // Example: handle known constraint for group name
            if (message.contains("uk_scim_to_claim_mapping_business_id_claim_set_origin_claim_definition_referenced")) {
                // Try to extract the duplicate value
                String duplicateName = null;
                java.util.regex.Matcher matcher = java.util.regex.Pattern
                        .compile("The duplicate key value is \\((.*?)\\)").matcher(message);
                if (matcher.find()) {
                    duplicateName = matcher.group(1);
                }
                userMessage = "SCIM mapper for '" + (duplicateName != null ? duplicateName : "") + "' already exists.";
            } else if (message.contains("unique") || message.contains("duplicate key")) {
                // Try to extract the duplicate value for generic unique constraint
                java.util.regex.Matcher matcher = java.util.regex.Pattern
                        .compile("The duplicate key value is \\((.*?)\\)").matcher(message);
                if (matcher.find()) {
                    userMessage = "A record with this value already exists: " + matcher.group(1);
                }
            }
        }

        ApiMessage error = new ApiMessage(code, userMessage);
        return ResponseEntity.status(HttpStatus.CONFLICT).body(error);
    }
} 