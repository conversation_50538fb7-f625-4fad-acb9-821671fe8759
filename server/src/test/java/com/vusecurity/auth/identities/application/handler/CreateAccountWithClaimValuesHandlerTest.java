package com.vusecurity.auth.identities.application.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vusecurity.auth.claims.application.command.CreateClaimValueCommand;
import com.vusecurity.auth.claims.application.exception.ClaimDefinitionNotFoundException;
import com.vusecurity.auth.claims.application.exception.DuplicateClaimValueException;
import com.vusecurity.auth.claims.application.handler.CreateClaimValueHandler;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.contracts.api.v1.dto.identities.ArrayClaimValueRequest;
import com.vusecurity.auth.contracts.api.v1.dto.identities.AccountClaimValueRequest;
import com.vusecurity.auth.contracts.api.v1.dto.identities.ScalarClaimValueRequest;
import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.identities.application.command.CreateAccountCommand;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for CreateAccountWithClaimValuesHandler.
 * Tests the orchestration logic with mocked dependencies.
 */
@ExtendWith(MockitoExtension.class)
class CreateAccountWithClaimValuesHandlerTest {

    @Mock
    private CreateAccountHandler createAccountHandler;

    @Mock
    private CreateClaimValueHandler createClaimValueHandler;

    @Mock
    private ClaimDefinitionRepository claimDefinitionRepository;

    @Mock
    private ObjectMapper objectMapper;

    private CreateAccountWithClaimValuesHandler handler;

    @BeforeEach
    void setUp() {
        handler = new CreateAccountWithClaimValuesHandler(createAccountHandler, createClaimValueHandler, claimDefinitionRepository, objectMapper);
    }

    @Test
    void shouldCreateAccountSuccessfully_WithoutClaimValues() {
        // Given
        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                Collections.emptyMap(),
                null
        );

        AccountJpaEntity expectedAccount = createMockAccount();
        when(createAccountHandler.createAccount(command)).thenReturn(expectedAccount);

        // When
        AccountJpaEntity result = handler.handle(command);

        // Then
        assertNotNull(result);
        assertEquals(expectedAccount.getId(), result.getId());
        verify(createAccountHandler).createAccount(command);
        verifyNoInteractions(createClaimValueHandler);
    }

    @Test
    void shouldCreateAccountSuccessfully_WithEmptyClaimValueList() {
        // Given
        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                Collections.emptyMap(),
                Collections.emptyList()
        );

        AccountJpaEntity expectedAccount = createMockAccount();
        when(createAccountHandler.createAccount(command)).thenReturn(expectedAccount);

        // When
        AccountJpaEntity result = handler.handle(command);

        // Then
        assertNotNull(result);
        assertEquals(expectedAccount.getId(), result.getId());
        verify(createAccountHandler).createAccount(command);
        verifyNoInteractions(createClaimValueHandler);
    }

    @Test
    void shouldCreateAccountSuccessfully_WithSingleClaimValue() {
        // Given
        AccountClaimValueRequest claimValueRequest = new ScalarClaimValueRequest(
                UUID.randomUUID(),
                UUID.randomUUID(),
                "<EMAIL>"
        );

        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                Collections.emptyMap(),
                List.of(claimValueRequest)
        );

        AccountJpaEntity expectedAccount = createMockAccount();
        when(createAccountHandler.createAccount(command)).thenReturn(expectedAccount);
        when(createClaimValueHandler.handle(any(CreateClaimValueCommand.class)))
                .thenReturn(new ClaimValueJpaEntity());

        // When
        AccountJpaEntity result = handler.handle(command);

        // Then
        assertNotNull(result);
        assertEquals(expectedAccount.getId(), result.getId());
        verify(createAccountHandler).createAccount(command);
        
        ArgumentCaptor<CreateClaimValueCommand> captor = ArgumentCaptor.forClass(CreateClaimValueCommand.class);
        verify(createClaimValueHandler).handle(captor.capture());
        
        CreateClaimValueCommand capturedCommand = captor.getValue();
        assertEquals(claimValueRequest.claimDefinitionId(), capturedCommand.claimDefinitionId());
        assertNull(capturedCommand.identityId());
        assertEquals(expectedAccount.getId(), capturedCommand.accountId());
        assertEquals(((ScalarClaimValueRequest) claimValueRequest).value(), capturedCommand.value());
        assertTrue(capturedCommand.isPrimary()); // scalar values are always primary
        assertFalse(capturedCommand.isComputed());
        assertEquals("USER_INPUT", capturedCommand.source());
    }

    @Test
    void shouldCreateAccountSuccessfully_WithMultipleClaimValues() {
        // Given
        AccountClaimValueRequest claimValue1 = new ScalarClaimValueRequest(
                UUID.randomUUID(),
                UUID.randomUUID(),
                "<EMAIL>"
        );

        AccountClaimValueRequest claimValue2 = new ScalarClaimValueRequest(
                UUID.randomUUID(),
                UUID.randomUUID(),
                "John Doe"
        );

        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                Collections.emptyMap(),
                Arrays.asList(claimValue1, claimValue2)
        );

        AccountJpaEntity expectedAccount = createMockAccount();
        when(createAccountHandler.createAccount(command)).thenReturn(expectedAccount);
        when(createClaimValueHandler.handle(any(CreateClaimValueCommand.class)))
                .thenReturn(new ClaimValueJpaEntity());

        // When
        AccountJpaEntity result = handler.handle(command);

        // Then
        assertNotNull(result);
        assertEquals(expectedAccount.getId(), result.getId());
        verify(createAccountHandler).createAccount(command);
        verify(createClaimValueHandler, times(2)).handle(any(CreateClaimValueCommand.class));
    }

    @Test
    void shouldCreateAccountSuccessfully_WithArrayClaimValue_ArrayType() throws Exception {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        AccountClaimValueRequest claimValueRequest = new ArrayClaimValueRequest(
                UUID.randomUUID(),
                claimDefinitionId,
                List.of("admin", "user", "viewer"),
                1 // "user" is primary
        );

        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                Collections.emptyMap(),
                List.of(claimValueRequest)
        );

        AccountJpaEntity expectedAccount = createMockAccount();
        ClaimDefinitionJpaEntity arrayClaimDefinition = new ClaimDefinitionJpaEntity(
                claimDefinitionId, "roles", "User Roles", "Array of user roles",
                DataTypeEnum.ARRAY, "some-uuid-or-pattern");

        when(createAccountHandler.createAccount(command)).thenReturn(expectedAccount);
        when(claimDefinitionRepository.findById(claimDefinitionId))
                .thenReturn(Optional.of(arrayClaimDefinition));
        when(objectMapper.writeValueAsString(List.of("admin", "user", "viewer")))
                .thenReturn("[\"admin\",\"user\",\"viewer\"]");
        when(createClaimValueHandler.handle(any(CreateClaimValueCommand.class)))
                .thenReturn(new ClaimValueJpaEntity());

        // When
        AccountJpaEntity result = handler.handle(command);

        // Then
        assertNotNull(result);
        assertEquals(expectedAccount.getId(), result.getId());
        verify(createAccountHandler).createAccount(command);
        verify(createClaimValueHandler, times(1)).handle(any(CreateClaimValueCommand.class)); // 1 JSON value for ARRAY type

        ArgumentCaptor<CreateClaimValueCommand> captor = ArgumentCaptor.forClass(CreateClaimValueCommand.class);
        verify(createClaimValueHandler).handle(captor.capture());
        
        CreateClaimValueCommand capturedCommand = captor.getValue();
        assertEquals(claimDefinitionId, capturedCommand.claimDefinitionId());
        assertEquals("[\"admin\",\"user\",\"viewer\"]", capturedCommand.value()); // JSON serialized array
        assertTrue(capturedCommand.isPrimary());
    }

    @Test
    void shouldCreateAccountSuccessfully_WithArrayClaimValue_NonArrayType() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        AccountClaimValueRequest claimValueRequest = new ArrayClaimValueRequest(
                UUID.randomUUID(),
                claimDefinitionId,
                List.of("admin", "user", "viewer"),
                1 // "user" is primary
        );

        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                Collections.emptyMap(),
                List.of(claimValueRequest)
        );

        AccountJpaEntity expectedAccount = createMockAccount();
        ClaimDefinitionJpaEntity stringClaimDefinition = new ClaimDefinitionJpaEntity(
                claimDefinitionId, "role", "User Role", "Single user role",
                DataTypeEnum.STRING, "^[a-zA-Z]+$");

        when(createAccountHandler.createAccount(command)).thenReturn(expectedAccount);
        when(claimDefinitionRepository.findById(claimDefinitionId))
                .thenReturn(Optional.of(stringClaimDefinition));
        when(createClaimValueHandler.handle(any(CreateClaimValueCommand.class)))
                .thenReturn(new ClaimValueJpaEntity());

        // When
        AccountJpaEntity result = handler.handle(command);

        // Then
        assertNotNull(result);
        assertEquals(expectedAccount.getId(), result.getId());
        verify(createAccountHandler).createAccount(command);
        verify(createClaimValueHandler, times(3)).handle(any(CreateClaimValueCommand.class)); // 3 separate values for non-ARRAY type
    }

    @Test
    void shouldRollbackTransaction_WhenAccountCreationFails() {
        // Given
        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                Collections.emptyMap(),
                null
        );

        when(createAccountHandler.createAccount(command))
                .thenThrow(new RuntimeException("Account creation failed"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> handler.handle(command));

        assertEquals("Account creation failed", exception.getMessage());
        verify(createAccountHandler).createAccount(command);
        verifyNoInteractions(createClaimValueHandler);
    }

    @Test
    void shouldRollbackTransaction_WhenClaimValueCreationFails() {
        // Given
        AccountClaimValueRequest claimValueRequest = new ScalarClaimValueRequest(
                UUID.randomUUID(),
                UUID.randomUUID(),
                "<EMAIL>"
        );

        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                Collections.emptyMap(),
                List.of(claimValueRequest)
        );

        AccountJpaEntity expectedAccount = createMockAccount();
        when(createAccountHandler.createAccount(command)).thenReturn(expectedAccount);
        when(createClaimValueHandler.handle(any(CreateClaimValueCommand.class)))
                .thenThrow(new ClaimDefinitionNotFoundException(claimValueRequest.claimDefinitionId()));

        // When & Then
        ClaimDefinitionNotFoundException exception = assertThrows(ClaimDefinitionNotFoundException.class,
                () -> handler.handle(command));

        assertTrue(exception.getMessage().contains("Claim definition not found"));
        assertTrue(exception.getMessage().contains(claimValueRequest.claimDefinitionId().toString()));
        verify(createAccountHandler).createAccount(command);
        verify(createClaimValueHandler).handle(any(CreateClaimValueCommand.class));
    }

    @Test
    void shouldRollbackTransaction_WhenDuplicateClaimValueException() {
        // Given
        AccountClaimValueRequest claimValueRequest = new ScalarClaimValueRequest(
                UUID.randomUUID(),
                UUID.randomUUID(),
                "<EMAIL>"
        );

        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                Collections.emptyMap(),
                List.of(claimValueRequest)
        );

        AccountJpaEntity expectedAccount = createMockAccount();
        when(createAccountHandler.createAccount(command)).thenReturn(expectedAccount);
        when(createClaimValueHandler.handle(any(CreateClaimValueCommand.class)))
                .thenThrow(new DuplicateClaimValueException("Value already exists"));

        // When & Then
        DuplicateClaimValueException exception = assertThrows(DuplicateClaimValueException.class,
                () -> handler.handle(command));

        assertEquals("Value already exists", exception.getMessage());
        verify(createAccountHandler).createAccount(command);
        verify(createClaimValueHandler).handle(any(CreateClaimValueCommand.class));
    }

    private AccountJpaEntity createMockAccount() {
        AccountJpaEntity account = new AccountJpaEntity();
        account.setId(UUID.randomUUID());
        return account;
    }
}
