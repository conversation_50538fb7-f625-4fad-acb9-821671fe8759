package com.vusecurity.auth.claims.application.command;

import java.time.Instant;
import java.util.UUID;

public record UpdateClaimValueCommand(
        UUID id,
        UUID claimSetId,
        UUID claimDefinitionId,
        UUID identityId,
        UUID accountId,
        Object value,
        Boolean isPrimary,
        Boolean isComputed,
        String source,
        Instant issuedAt,
        UUID claimVerificationId,
        Integer primaryIndex) {

        public UpdateClaimValueCommand(
            UUID id,
            UUID claimDefinitionId,
            UUID identityId,
            UUID accountId,
            Object value,
            Boolean isPrimary,
            Boolean isComputed,
            String source,
            Instant issuedAt,
            UUID claimVerificationId) {
                this(id, null, claimDefinitionId, identityId, accountId, value, isPrimary, isComputed, source, issuedAt, claimVerificationId, null);
        }

    public void validate() {
        if (id == null) {
            throw new IllegalArgumentException("id is required");
        }
    }

    public void validateWithPattern(String pattern) {
        if (pattern != null && !pattern.isBlank() && value != null) {
            String stringValue = convertValueToString();
            if (!stringValue.matches(pattern)) {
                throw new IllegalArgumentException("value does not match the required pattern");
            }
        }
    }

    /**
     * Converts the Object value to String for validation and persistence purposes
     */
    public String convertValueToString() {
        if (value == null) {
            return null;
        }
        if (value instanceof String) {
            return (String) value;
        }
        // For complex objects, convert to JSON string
        try {
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            return mapper.writeValueAsString(value);
        } catch (Exception e) {
            // Fallback to toString if JSON serialization fails
            return value.toString();
        }
    }
}
