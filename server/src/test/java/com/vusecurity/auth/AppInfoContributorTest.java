package com.vusecurity.auth;

import org.junit.jupiter.api.Test;
import org.springframework.boot.actuate.info.Info;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class AppInfoContributorTest {

    @Test
    void contribute_includes_app_details_from_config() {
        AppConfigProperties props = new AppConfigProperties();
        props.setVersion("1.0.1");
        props.setBuild("1234");
        props.setDate(111L);
        props.setApikey("x");

        AppInfoContributor contributor = new AppInfoContributor(props);
        Info.Builder builder = new Info.Builder();

        contributor.contribute(builder);
        Info info = builder.build();

        assertTrue(info.getDetails().containsKey("app"));
        Object app = info.getDetails().get("app");
        assertInstanceOf(Map.class, app);
        Map<?,?> appMap = (Map<?,?>) app;
        assertEquals("1.0.1", appMap.get("version"));
        assertEquals("1234", appMap.get("build"));
        assertEquals(111L, appMap.get("date"));
        assertTrue(info.getDetails().containsKey("runtime"));
        assertNotNull(info.getDetails().get("runtime"));
    }
}

