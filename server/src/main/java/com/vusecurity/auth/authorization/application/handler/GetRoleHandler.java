package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.query.GetRoleQuery;
import com.vusecurity.auth.authorization.application.query.GetRolesPagedQuery;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class GetRoleHandler implements GetRoleQuery {

    private static final Logger logger = LoggerFactory.getLogger(GetRoleHandler.class);
    
    private final RoleRepository roleRepository;

    @Override
    public RoleJpaEntity getRoleById(UUID roleId) {
        logger.debug("Getting role by ID: {}", roleId);
        
        return roleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException("Role not found with ID: " + roleId));
    }

    @Override
    public Page<RoleJpaEntity> getAllRoles(GetRolesPagedQuery query) {
        logger.debug("Getting all roles with query: {}", query);
        
        // Convert 1-based page to 0-based for Spring Data
        int pageNumber = Math.max(0, query.getPage() - 1);
        Pageable pageable = PageRequest.of(pageNumber, query.getPageSize());
        
        // For now, we'll return all roles. In the future, we can add filtering based on query.getFilter()
        Page<RoleJpaEntity> roles = roleRepository.findAll(pageable);
        
        logger.debug("Found {} roles", roles.getTotalElements());
        return roles;
    }
}
