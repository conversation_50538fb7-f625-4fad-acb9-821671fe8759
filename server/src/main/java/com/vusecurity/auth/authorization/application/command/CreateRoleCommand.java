package com.vusecurity.auth.authorization.application.command;

import java.util.Set;
import java.util.UUID;

public record CreateRoleCommand(
        String name,
        String description,
        UUID businessId,
        Set<UUID> permissionIds
) {
    public void validate() {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("name is required");
        }
    }
}
