package com.vusecurity.auth.authorization.mapper;

import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.PermissionJpaEntity;
import com.vusecurity.auth.contracts.api.v1.dto.authorization.PermissionResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PermissionDtoMapper {

    private static final Logger logger = LoggerFactory.getLogger(PermissionDtoMapper.class);

    public PermissionResponse toResponse(PermissionJpaEntity entity) {
        logger.debug("Mapping PermissionJpaEntity to PermissionResponse: {}", entity.getId());
        
        PermissionResponse response = new PermissionResponse();
        response.setId(entity.getId());
        response.setName(entity.getName());
        response.setDescription(entity.getDescription());

        // Set audit information
        response.setAudit(new BaseResponse.AuditInfo()
                .setCreatedAt(entity.getCreatedAt())
                .setUpdatedAt(entity.getUpdatedAt())
                .setCreatedBy(entity.getCreatedBy())
                .setUpdatedBy(entity.getUpdatedBy()));
        
        return response;
    }
}
