package com.vusecurity.auth.claims.application.command;

import java.util.List;
import java.util.UUID;

/**
 * Command for partially updating claim definitions associations in a claim set.
 * This allows partial updates without affecting existing associations that are not mentioned.
 */
public record PatchClaimDefinitionsOnClaimSetCommand(
    UUID claimSetId,
    List<ClaimDefinitionAssignmentCommand> claimDefinitionsAssignment) {

    public void validate() {
        if (claimSetId == null) {
            throw new IllegalArgumentException("claimSetId is required");
        }
        if (claimDefinitionsAssignment == null) {
            throw new IllegalArgumentException("claimDefinitions is required");
        }

        for (ClaimDefinitionAssignmentCommand claimDefinition : claimDefinitionsAssignment) {
            if (claimDefinition == null) {
                throw new IllegalArgumentException("claimDefinitions cannot contain null values");
            }
        }
    }

    public record ClaimDefinitionAssignmentCommand(
            UUID claimDefinitionId,
            Integer claimDefinitionOrder,
            boolean enforceUniqueness) {}
} 