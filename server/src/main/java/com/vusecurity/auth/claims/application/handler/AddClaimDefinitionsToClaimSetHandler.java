package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.AddClaimDefinitionsToClaimSetCommand;
import com.vusecurity.auth.claims.application.exception.ClaimDefinitionNotFoundException;
import com.vusecurity.auth.claims.application.exception.ClaimSetNotFoundException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.service.ClaimSetDefinitionMappingService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AddClaimDefinitionsToClaimSetHandler {

    private static final Logger logger = LoggerFactory.getLogger(AddClaimDefinitionsToClaimSetHandler.class);

    private final ClaimDefinitionRepository claimDefinitionRepository;
    private final ClaimSetRepository claimSetRepository;
    private final ClaimSetDefinitionMappingService mappingService;

    @Transactional
    public void addClaimDefinitionsToClaimSet(AddClaimDefinitionsToClaimSetCommand command) {
        logger.debug("Adding claim definitions {} to claim set: {}", command.claimDefinitions(), command.claimSetId());

        try {
            // Validate command
            command.validate();

            // Find the claim set
            ClaimSetJpaEntity claimSet = claimSetRepository.findById(command.claimSetId())
                    .orElseThrow(() -> new ClaimSetNotFoundException(command.claimSetId()));

            // Find all claim definitions
            Set<UUID> claimIdsDefinitionsList = new HashSet<>();
            for(AddClaimDefinitionsToClaimSetCommand.ClaimDefinitionsToClaimSetCommand claimIdsDefinitions:command.claimDefinitions()){
                claimIdsDefinitionsList.add(claimIdsDefinitions.claimDefinitionId());
            }
            List<ClaimDefinitionJpaEntity> claimDefinitions = claimDefinitionRepository.findAllById(claimIdsDefinitionsList);

            // Validate that all claim definitions were found
            if (claimDefinitions.size() != command.claimDefinitions().size()) {
                throw new ClaimDefinitionNotFoundException("One or more claim definitions not found");
            }

            Map<UUID, AddClaimDefinitionsToClaimSetCommand.ClaimDefinitionsToClaimSetCommand> defMap = command.claimDefinitions().stream()
                    .collect(Collectors.toMap(AddClaimDefinitionsToClaimSetCommand.ClaimDefinitionsToClaimSetCommand::claimDefinitionId, def -> def));


            // Add the claim definitions to the claim set using the mapping service
            for (ClaimDefinitionJpaEntity claimDefinition : claimDefinitions) {
                Boolean enforceFromRequest = defMap.get(claimDefinition.getId()).enforceUniqueness();

                boolean enforceUniqueness;
                if (Boolean.TRUE.equals(claimSet.getIsIdentifier())) {
                    if (Boolean.FALSE.equals(enforceFromRequest)) {
                        throw new IllegalArgumentException("Cannot set enforceUniqueness to false for identifier claim set");
                    }
                    enforceUniqueness = true; // Enforce as true always if claimSet is identifier
                } else {
                    enforceUniqueness = Boolean.TRUE.equals(enforceFromRequest); // true if explicitly true, false otherwise
                }

                mappingService.addClaimDefinitionToClaimSet(claimSet, claimDefinition, null, enforceUniqueness);
            }

            logger.debug("Successfully added claim definitions to claim set: {}", command.claimSetId());

        } catch (Exception e) {
            logger.error("Error adding claim definitions to claim set {}: {}", command.claimSetId(), e.getMessage(), e);
            throw e;
        }
    }
}
