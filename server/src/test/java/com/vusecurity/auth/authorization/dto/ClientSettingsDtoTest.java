package com.vusecurity.auth.authorization.dto;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ClientSettingsDtoTest {

    @Test
    void withDefaults_sets_expected_defaults() {
        ClientSettingsDto dto = ClientSettingsDto.withDefaults();
        assertNotNull(dto);
        assertEquals(Boolean.FALSE, dto.getRequireAuthorizationConsent());
        assertEquals(Boolean.FALSE, dto.getRequireProofKey());
        assertNull(dto.getJwkSetUrl());
        assertNull(dto.getTokenEndpointAuthenticationSigningAlgorithm());
        assertNull(dto.getX509CertificateSubjectDN());
    }
}

