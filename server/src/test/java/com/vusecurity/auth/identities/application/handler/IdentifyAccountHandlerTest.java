package com.vusecurity.auth.identities.application.handler;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.service.ClaimSetDefinitionMappingService;
import com.vusecurity.auth.contracts.api.v1.dto.identities.ClaimIdentifierRequest;
import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.identities.application.exception.AccountNotFoundException;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.contracts.enums.LookupStrategy;
import com.vusecurity.auth.identities.application.query.IdentifyAccountByClaimsQuery;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import com.vusecurity.business.domain.Business;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for IdentifyAccountHandler.
 * Tests business logic with mocked repository dependencies.
 */
@ExtendWith(MockitoExtension.class)
class IdentifyAccountHandlerTest {

    @Mock
    private AccountRepository accountRepository;

    @Mock
    private ClaimSetRepository claimSetRepository;

    @Mock
    private ClaimSetDefinitionMappingService mappingService;

    @InjectMocks
    private IdentifyAccountHandler identifyAccountHandler;

    private UUID businessId;
    private Business testBusiness;
    private ClaimSetJpaEntity testClaimSet;
    private List<ClaimDefinitionJpaEntity> testClaimDefinitions;
    private List<ClaimIdentifierRequest> validClaimIdentifiers;
    private AccountJpaEntity testAccount;

    @BeforeEach
    void setUp() {
        businessId = UUID.randomUUID();
        testBusiness = createTestBusiness();
        testClaimSet = createTestClaimSet();
        testClaimDefinitions = createTestClaimDefinitions();
        validClaimIdentifiers = createValidClaimIdentifiers();
        testAccount = createTestAccount();
    }

    @Test
    void shouldSuccessfullyIdentifyAccount_WhenExactlyOneAccountMatches() {
        // Given
        IdentifyAccountByClaimsQuery query = IdentifyAccountByClaimsQuery.builder()
                .businessId(businessId)
                .accountType(AccountType.WORKFORCE)
                .claimIdentifiers(validClaimIdentifiers)
                .build();

        when(claimSetRepository.findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true))
                .thenReturn(Optional.of(testClaimSet));
        when(mappingService.getClaimDefinitionsForClaimSet(testClaimSet))
                .thenReturn(testClaimDefinitions);
        when(accountRepository.findAccountsByBusinessAndAccountTypeAndClaims(
                eq(businessId), eq(AccountType.WORKFORCE), anyList(), eq(2L)))
                .thenReturn(Arrays.asList(testAccount));

        // When
        AccountJpaEntity result = identifyAccountHandler.identifyAccountByClaims(query);

        // Then
        assertNotNull(result);
        assertEquals(testAccount.getId(), result.getId());
        verify(claimSetRepository).findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true);
        verify(mappingService).getClaimDefinitionsForClaimSet(testClaimSet);
        verify(accountRepository).findAccountsByBusinessAndAccountTypeAndClaims(
                eq(businessId), eq(AccountType.WORKFORCE), anyList(), eq(2L));
    }

    @Test
    void shouldThrowNoSuchElementException_WhenNoClaimSetFound() {
        // Given
        IdentifyAccountByClaimsQuery query = IdentifyAccountByClaimsQuery.builder()
                .businessId(businessId)
                .accountType(AccountType.WORKFORCE)
                .claimIdentifiers(validClaimIdentifiers)
                .build();

        when(claimSetRepository.findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true))
                .thenReturn(Optional.empty());

        // When & Then
        NoSuchElementException exception = assertThrows(NoSuchElementException.class,
                () -> identifyAccountHandler.identifyAccountByClaims(query));
        
        assertTrue(exception.getMessage().contains("No identifier ClaimSet found"));
        verify(claimSetRepository).findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true);
        verifyNoInteractions(mappingService);
        verifyNoInteractions(accountRepository);
    }

    @Test
    void shouldThrowIllegalArgumentException_WhenDuplicateClaimDefinitionIds() {
        // Given
        UUID duplicateId = UUID.randomUUID();
        List<ClaimIdentifierRequest> duplicateClaimIdentifiers = Arrays.asList(
                createClaimIdentifier(duplicateId, "value1"),
                createClaimIdentifier(duplicateId, "value2")
        );
        
        IdentifyAccountByClaimsQuery query = IdentifyAccountByClaimsQuery.builder()
                .businessId(businessId)
                .accountType(AccountType.WORKFORCE)
                .claimIdentifiers(duplicateClaimIdentifiers)
                .build();

        when(claimSetRepository.findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true))
                .thenReturn(Optional.of(testClaimSet));
        when(mappingService.getClaimDefinitionsForClaimSet(testClaimSet))
                .thenReturn(testClaimDefinitions);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> identifyAccountHandler.identifyAccountByClaims(query));
        
        assertTrue(exception.getMessage().contains("Duplicate claim definition IDs are not allowed"));
        verify(claimSetRepository).findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true);
        verify(mappingService).getClaimDefinitionsForClaimSet(testClaimSet);
        verifyNoInteractions(accountRepository);
    }

    @Test
    void shouldThrowIllegalArgumentException_WhenClaimDefinitionNotInClaimSet() {
        // Given
        UUID invalidClaimDefId = UUID.randomUUID();
        List<ClaimIdentifierRequest> invalidClaimIdentifiers = Arrays.asList(
                createClaimIdentifier(invalidClaimDefId, "value1")
        );
        
        IdentifyAccountByClaimsQuery query = IdentifyAccountByClaimsQuery.builder()
                .businessId(businessId)
                .accountType(AccountType.WORKFORCE)
                .claimIdentifiers(invalidClaimIdentifiers)
                .build();

        when(claimSetRepository.findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true))
                .thenReturn(Optional.of(testClaimSet));
        when(mappingService.getClaimDefinitionsForClaimSet(testClaimSet))
                .thenReturn(testClaimDefinitions);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> identifyAccountHandler.identifyAccountByClaims(query));
        
        assertTrue(exception.getMessage().contains("does not belong to the identifier claim set"));
        verify(claimSetRepository).findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true);
        verify(mappingService).getClaimDefinitionsForClaimSet(testClaimSet);
        verifyNoInteractions(accountRepository);
    }

    @Test
    void shouldThrowAccountNotFoundException_WhenNoAccountsMatch() {
        // Given
        IdentifyAccountByClaimsQuery query = IdentifyAccountByClaimsQuery.builder()
                .businessId(businessId)
                .accountType(AccountType.WORKFORCE)
                .claimIdentifiers(validClaimIdentifiers)
                .build();

        when(claimSetRepository.findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true))
                .thenReturn(Optional.of(testClaimSet));
        when(mappingService.getClaimDefinitionsForClaimSet(testClaimSet))
                .thenReturn(testClaimDefinitions);
        when(accountRepository.findAccountsByBusinessAndAccountTypeAndClaims(
                eq(businessId), eq(AccountType.WORKFORCE), anyList(), eq(2L)))
                .thenReturn(Collections.emptyList());

        // When & Then
        AccountNotFoundException exception = assertThrows(AccountNotFoundException.class,
                () -> identifyAccountHandler.identifyAccountByClaims(query));

        assertTrue(exception.getMessage().contains("No account found matching the provided claim identifiers"));
        verify(accountRepository).findAccountsByBusinessAndAccountTypeAndClaims(
                eq(businessId), eq(AccountType.WORKFORCE), anyList(), eq(2L));
    }

    @Test
    void shouldThrowIllegalStateException_WhenMultipleAccountsMatch() {
        // Given
        IdentifyAccountByClaimsQuery query = IdentifyAccountByClaimsQuery.builder()
                .businessId(businessId)
                .accountType(AccountType.WORKFORCE)
                .claimIdentifiers(validClaimIdentifiers)
                .build();

        AccountJpaEntity secondAccount = createTestAccount();
        secondAccount.setId(UUID.randomUUID());

        when(claimSetRepository.findByBusinessIdAndAccountTypeAndIsIdentifier(businessId, AccountType.WORKFORCE, true))
                .thenReturn(Optional.of(testClaimSet));
        when(mappingService.getClaimDefinitionsForClaimSet(testClaimSet))
                .thenReturn(testClaimDefinitions);
        when(accountRepository.findAccountsByBusinessAndAccountTypeAndClaims(
                eq(businessId), eq(AccountType.WORKFORCE), anyList(), eq(2L)))
                .thenReturn(Arrays.asList(testAccount, secondAccount));

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
                () -> identifyAccountHandler.identifyAccountByClaims(query));
        
        assertTrue(exception.getMessage().contains("Multiple accounts found matching"));
        assertTrue(exception.getMessage().contains("Expected exactly one account, but found 2"));
        verify(accountRepository).findAccountsByBusinessAndAccountTypeAndClaims(
                eq(businessId), eq(AccountType.WORKFORCE), anyList(), eq(2L));
    }

    // Helper methods
    private Business createTestBusiness() {
        Business business = new Business();
        business.setId(businessId);
        business.setName("Test Business");
        return business;
    }

    private ClaimSetJpaEntity createTestClaimSet() {
        ClaimSetJpaEntity claimSet = new ClaimSetJpaEntity(testBusiness, AccountType.WORKFORCE, true);
        claimSet.setId(UUID.randomUUID());
        claimSet.setName("Workforce Identifier Claims");
        claimSet.setLookupStrategy(LookupStrategy.ALL_CLAIMS_MUST_MATCH);
        return claimSet;
    }

    private List<ClaimDefinitionJpaEntity> createTestClaimDefinitions() {
        ClaimDefinitionJpaEntity emailClaim = new ClaimDefinitionJpaEntity(
                UUID.randomUUID(), "work_email", "Work Email", "Work email address",
                DataTypeEnum.STRING, "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$");

        ClaimDefinitionJpaEntity empIdClaim = new ClaimDefinitionJpaEntity(
                UUID.randomUUID(), "employee_id", "Employee ID", "Employee identifier",
                DataTypeEnum.STRING, "^EMP\\d{3}$");

        return List.of(emailClaim, empIdClaim);
    }

    private List<ClaimIdentifierRequest> createValidClaimIdentifiers() {
        Iterator<ClaimDefinitionJpaEntity> iterator = testClaimDefinitions.iterator();
        ClaimDefinitionJpaEntity firstClaim = iterator.next();
        ClaimDefinitionJpaEntity secondClaim = iterator.next();
        
        return Arrays.asList(
                createClaimIdentifier(firstClaim.getId(), "<EMAIL>"),
                createClaimIdentifier(secondClaim.getId(), "EMP001")
        );
    }

    private ClaimIdentifierRequest createClaimIdentifier(UUID claimDefinitionId, String value) {
        ClaimIdentifierRequest request = new ClaimIdentifierRequest();
        request.setClaimDefinitionId(claimDefinitionId);
        request.setValue(value);
        return request;
    }

    private AccountJpaEntity createTestAccount() {
        IdentityJpaEntity identity = new IdentityJpaEntity();
        identity.setId(UUID.randomUUID());
        identity.setName("John Doe");

        IdentityProviderJpaEntity identityProvider = new IdentityProviderJpaEntity();
        identityProvider.setId(UUID.randomUUID());

        AccountJpaEntity account = new AccountJpaEntity(testBusiness, identity, identityProvider, AccountType.WORKFORCE);
        account.setId(UUID.randomUUID());
        account.setLifecycleState(AccountLifecycleState.ACTIVE);
        return account;
    }
}
