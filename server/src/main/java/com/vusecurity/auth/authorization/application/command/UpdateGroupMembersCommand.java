package com.vusecurity.auth.authorization.application.command;

import com.vusecurity.auth.authorization.application.exception.InvalidGroupRequestException;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;

public record UpdateGroupMembersCommand(
        UUID groupId,
        List<UUID> accountIds,
        List<UUID> ownerAccountIds
) {
    public UpdateGroupMembersCommand {
        if (accountIds == null) {
            accountIds = Collections.emptyList();
        }
        if (ownerAccountIds == null) {
            ownerAccountIds = Collections.emptyList();
        }
    }

    public void validate() {
        if (groupId == null) {
            throw new InvalidGroupRequestException("Group ID is required");
        }
        if (!new HashSet<>(accountIds).containsAll(ownerAccountIds)) {
            throw new InvalidGroupRequestException("All ownerAccountIds must also be present in accountIds");
        }
    }
} 