package com.vusecurity.auth.shared.application.service;

import com.nimbusds.jose.jwk.RSAKey;
import com.vusecurity.auth.shared.infrastructure.persistence.jpa.entity.JwkJpaEntity;
import com.vusecurity.auth.shared.infrastructure.persistence.jpa.repository.JwkRepository;
import com.vusecurity.auth.shared.util.Jwks;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.KeyFactory;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Optional;

@Service
public class JwkManagementService {

    private static final Logger logger = LoggerFactory.getLogger(JwkManagementService.class);
    
    private final JwkRepository jwkRepository;
    private volatile RSAKey cachedRsaKey;
    private volatile long cacheTimestamp = 0;
    private static final long CACHE_TTL_MS = 300_000; // 5 minutes

    public JwkManagementService(JwkRepository jwkRepository) {
        this.jwkRepository = jwkRepository;
    }

    @Transactional
    public RSAKey getOrCreateRsaKey() {
        // Check cache first
        if (isCacheValid()) {
            return cachedRsaKey;
        }

        synchronized (this) {
            // Double-check after acquiring lock
            if (isCacheValid()) {
                return cachedRsaKey;
            }

            // Try to load from database
            Optional<JwkJpaEntity> existingKey = jwkRepository.findFirstActive();
            
            if (existingKey.isPresent()) {
                logger.info("Loading existing RSA key from database");
                cachedRsaKey = convertToRsaKey(existingKey.get());
            } else {
                logger.info("No active RSA key found, generating new one");
                cachedRsaKey = generateAndStoreNewKey();
            }
            
            cacheTimestamp = System.currentTimeMillis();
            return cachedRsaKey;
        }
    }

    private boolean isCacheValid() {
        return cachedRsaKey != null && 
               (System.currentTimeMillis() - cacheTimestamp) < CACHE_TTL_MS;
    }

    @Transactional
    protected RSAKey generateAndStoreNewKey() {
        RSAKey newRsaKey = Jwks.generateRsa();
        
        try {
            // Convert keys to PEM format for storage
            String publicKeyPem = Base64.getEncoder().encodeToString(
                newRsaKey.toRSAPublicKey().getEncoded());
            String privateKeyPem = Base64.getEncoder().encodeToString(
                newRsaKey.toRSAPrivateKey().getEncoded());

            JwkJpaEntity jwkEntity = new JwkJpaEntity(
                newRsaKey.getKeyID(),
                publicKeyPem,
                privateKeyPem,
                "RS256",
                "sig"
            );

            jwkRepository.save(jwkEntity);
            logger.info("Stored new RSA key in database with ID: {}", newRsaKey.getKeyID());
            
            return newRsaKey;
        } catch (Exception e) {
            logger.error("Failed to store RSA key in database", e);
            throw new RuntimeException("Failed to store RSA key", e);
        }
    }

    private RSAKey convertToRsaKey(JwkJpaEntity jwkEntity) {
        try {
            // Decode the stored keys
            byte[] publicKeyBytes = Base64.getDecoder().decode(jwkEntity.getPublicKey());
            byte[] privateKeyBytes = Base64.getDecoder().decode(jwkEntity.getPrivateKey());

            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            
            X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(publicKeyBytes);
            RSAPublicKey publicKey = (RSAPublicKey) keyFactory.generatePublic(publicKeySpec);
            
            PKCS8EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
            RSAPrivateKey privateKey = (RSAPrivateKey) keyFactory.generatePrivate(privateKeySpec);

            return new RSAKey.Builder(publicKey)
                    .privateKey(privateKey)
                    .keyID(jwkEntity.getKeyId())
                    .build();
                    
        } catch (Exception e) {
            logger.error("Failed to convert stored key to RSAKey", e);
            throw new RuntimeException("Failed to load RSA key from database", e);
        }
    }

    public void invalidateCache() {
        synchronized (this) {
            cachedRsaKey = null;
            cacheTimestamp = 0;
        }
    }
}