/*
 * Flyway Migration: V10__fix_claim_definitions.sql
 * Purpose: Fixes old inserted claim definition on V2 where they where created with double scaped format '\\s' instead of simple `\s`.
 */

SET NOCOUNT ON;
SET XACT_ABORT ON; -- Automatically rollback on any error

BEGIN TRANSACTION;

UPDATE [claim_definition] SET [data_format] = '^[a-zA-Z0-9_+&*-]+(?:\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$' WHERE [id] = 'beb4da69-6685-481b-a940-87823788b02a' AND data_format = '^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}$'
UPDATE [claim_definition] SET [data_format] = '^[a-zA-Z\s]+$' WHERE [id] = 'fd9c993f-d544-43a1-9fd8-b77d19de1635' AND data_format = '^[a-zA-Z\\s]+$'
UPDATE [claim_definition] SET [data_format] = '^[a-zA-Z\s]+$' WHERE [id] = 'db76198f-9866-41d5-82b3-856a6a86d157' AND data_format = '^[a-zA-Z\\s]+$'
UPDATE [claim_definition] SET [data_format] = '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$' WHERE [id] = 'ed926e0f-4d85-4f3d-b9f6-971d566a8c59' AND data_format = '^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d{3})?Z?$'
UPDATE [claim_definition] SET [data_format] = '^(\+\d{1,3})?\d{7,15}$' WHERE [id] = '2784994d-dc95-491b-8843-f5664dece55e' AND data_format = '^(\\+\\d{1,3})?\\d{7,15}$'
UPDATE [claim_definition] SET [data_format] = '^[a-zA-Z0-9\s.,#-]+$' WHERE [id] = 'e78db996-7a30-46b0-8a47-6d86c0a91f49' AND data_format = '^[a-zA-Z0-9\\s.,#-]+$'
UPDATE [claim_definition] SET [data_format] = '^[\S]{1,}[\s]*$' WHERE [id] = '12afd933-ede5-4ac2-8d2b-e8978642965c' AND data_format = '^[\\S]{1,}[\\s]*$'
UPDATE [claim_definition] SET [data_format] = '^[\S]{1,}[\s]*$' WHERE [id] = 'e8978642-8d2b-4ac2-ede5-965c12afd933' AND data_format = '^[\\S]{1,}[\\s]*$'
UPDATE [claim_definition] SET [data_format] = '^[a-zA-Z\s&-]+$' WHERE [id] = 'a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d' AND data_format = '^[a-zA-Z\\s&-]+$'
UPDATE [claim_definition] SET [data_format] = '^[a-zA-Z\s&-]+$' WHERE [id] = 'b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e' AND data_format = '^[a-zA-Z\\s&-]+$'
UPDATE [claim_definition] SET [data_format] = '^[a-zA-Z0-9_+&*-]+(?:\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$' WHERE [id] = 'c3d4e5f6-a7b8-9c0d-1e2f-3a4b5c6d7e8f' AND data_format = '^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}$'
UPDATE [claim_definition] SET [data_format] = '^\d{4}-\d{2}-\d{2}$' WHERE [id] = 'd4e5f6a7-b8c9-0d1e-2f3a-4b5c6d7e8f9a' AND data_format = '^\\d{4}-\\d{2}-\\d{2}$'

COMMIT TRANSACTION;

-- Restore default NOCOUNT behavior to prevent issues with JPA/Hibernate
-- When NOCOUNT is ON, INSERT/UPDATE/DELETE statements return -1 instead of actual row count
-- This causes Hibernate's StaleStateException when it expects row count = 1
SET NOCOUNT OFF;