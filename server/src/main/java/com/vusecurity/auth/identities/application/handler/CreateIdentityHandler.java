package com.vusecurity.auth.identities.application.handler;


import com.vusecurity.auth.contracts.enums.IdentityLifecycleState;
import com.vusecurity.auth.contracts.enums.IdentityType;
import com.vusecurity.auth.identities.application.command.CreateIdentityCommand;
import com.vusecurity.auth.identities.application.exception.IdentityValidationException;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class CreateIdentityHandler {

    private static final Logger logger = LoggerFactory.getLogger(CreateIdentityHandler.class);
    
    private final IdentityRepository identityRepository;

    @Transactional
    public IdentityJpaEntity createIdentity(CreateIdentityCommand command) {
        logger.trace("Creating identity with name: {}", command.getName());
        
        // Validate command
        if (command.getName() == null || command.getName().trim().isEmpty()) {
            throw new IdentityValidationException("Identity name cannot be null or empty");
        }
        if (command.getIdentityType() == null || command.getIdentityType().trim().isEmpty()) {
            throw new IdentityValidationException("Identity type cannot be null or empty");
        }

        // Parse identity type
        IdentityType identityType;
        try {
            identityType = IdentityType.valueOf(command.getIdentityType().toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IdentityValidationException("Invalid identity type: " + command.getIdentityType());
        }

        // Parse lifecycle state
        IdentityLifecycleState lifecycleState = IdentityLifecycleState.NONE;
        if (command.getLifecycleState() != null && !command.getLifecycleState().trim().isEmpty()) {
            try {
                lifecycleState = IdentityLifecycleState.valueOf(command.getLifecycleState().toUpperCase());
            } catch (IllegalArgumentException e) {
                throw new IdentityValidationException("Invalid lifecycle state: " + command.getLifecycleState());
            }
        }

        // Get metadata (already a Map)
        Map<String, Object> metadata = command.getMetadata() != null ? command.getMetadata() : Collections.emptyMap();

        // Create identity entity
        IdentityJpaEntity identity = IdentityJpaEntity.builder(identityType, command.getName().trim());
        identity.setLifecycleState(lifecycleState);
        identity.setMetadata(metadata);

        // Validate before saving
        identity.validateForCreation();

        IdentityJpaEntity savedIdentity = identityRepository.save(identity);
        logger.debug("Identity created successfully with ID: {}", savedIdentity.getId());

        return savedIdentity;
    }
}
