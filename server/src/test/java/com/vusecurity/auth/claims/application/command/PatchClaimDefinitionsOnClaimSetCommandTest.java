package com.vusecurity.auth.claims.application.command;

import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class PatchClaimDefinitionsOnClaimSetCommandTest {

    @Test
    void shouldValidateSuccessfully_WhenValidCommand() {
        // Given
        UUID claimSetId = UUID.randomUUID();
        List<PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand> claimDefinitionsAssignment = List.of(
                new PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand(UUID.randomUUID(), 1, true),
                new PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand(UUID.randomUUID(), 2, false)
        );

        PatchClaimDefinitionsOnClaimSetCommand command = new PatchClaimDefinitionsOnClaimSetCommand(
                claimSetId,
                claimDefinitionsAssignment
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WhenEmptyClaimDefinitions() {
        // Given
        UUID claimSetId = UUID.randomUUID();
        List<PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand> claimDefinitionsAssignment = List.of();

        PatchClaimDefinitionsOnClaimSetCommand command = new PatchClaimDefinitionsOnClaimSetCommand(
                claimSetId,
                claimDefinitionsAssignment
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldThrowException_WhenClaimSetIdIsNull() {
        // Given
        List<PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand> claimDefinitionsAssignment = List.of(
                new PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand(UUID.randomUUID(), 1, true)
        );

        PatchClaimDefinitionsOnClaimSetCommand command = new PatchClaimDefinitionsOnClaimSetCommand(
                null,
                claimDefinitionsAssignment
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("claimSetId is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenClaimDefinitionsIsNull() {
        // Given
        UUID claimSetId = UUID.randomUUID();

        PatchClaimDefinitionsOnClaimSetCommand command = new PatchClaimDefinitionsOnClaimSetCommand(
                claimSetId,
                null
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("claimDefinitions is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenClaimDefinitionsContainsNull() {
        // Given
        UUID claimSetId = UUID.randomUUID();
        List<PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand> claimDefinitionsAssignment = new java.util.ArrayList<>();
        claimDefinitionsAssignment.add(new PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand(UUID.randomUUID(), 1, true));
        claimDefinitionsAssignment.add(null);

        PatchClaimDefinitionsOnClaimSetCommand command = new PatchClaimDefinitionsOnClaimSetCommand(
                claimSetId,
                claimDefinitionsAssignment
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("claimDefinitions cannot contain null values", exception.getMessage());
    }

    @Test
    void shouldValidateSuccessfully_WithMixedOrderAndUniquenessValues() {
        // Given
        UUID claimSetId = UUID.randomUUID();
        List<PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand> claimDefinitionsAssignment = List.of(
                new PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand(UUID.randomUUID(), 1, true),
                new PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand(UUID.randomUUID(), null, false),
                new PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand(UUID.randomUUID(), 3, true)
        );

        PatchClaimDefinitionsOnClaimSetCommand command = new PatchClaimDefinitionsOnClaimSetCommand(
                claimSetId,
                claimDefinitionsAssignment
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }
} 