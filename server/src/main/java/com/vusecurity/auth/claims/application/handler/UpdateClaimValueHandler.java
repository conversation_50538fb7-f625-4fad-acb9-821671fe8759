package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.UpdateClaimValueCommand;
import com.vusecurity.auth.claims.application.exception.UpdateClaimTypeSystemException;
import com.vusecurity.auth.claims.application.exception.DuplicateClaimValueException;
import com.vusecurity.auth.claims.application.exception.ClaimValueNotFoundException;
import com.vusecurity.auth.claims.application.exception.ClaimDefinitionNotFoundException;
import com.vusecurity.auth.claims.application.exception.ClaimValueValidationException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetDefinitionMappingJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.enums.ClaimType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.shared.util.DataFormatUtils;
import com.vusecurity.auth.shared.util.DatabaseConstraintUtils;
import com.vusecurity.auth.shared.util.ArrayParsingUtils;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class UpdateClaimValueHandler {

    private static final Logger logger = LoggerFactory.getLogger(UpdateClaimValueHandler.class);
    
    private final ClaimValueRepository claimValueRepository;
    private final ClaimDefinitionRepository claimDefinitionRepository;

    @Transactional
    public void updateClaimValue(UpdateClaimValueCommand cmd) {
        this.updateClaimValue(cmd, false);
    }

    @Transactional
    public void updateClaimValue(UpdateClaimValueCommand cmd, Boolean allowToUpdateSystemClaim) {
        logger.debug("Updating claim value with command: {}", cmd);
        
        ClaimDefinitionJpaEntity claimDefinition;

        // Validate command
        cmd.validate();

        // Find existing claim value
        var claimValue = claimValueRepository.findById(cmd.id())
                .orElseThrow(() -> new ClaimValueNotFoundException(cmd.id()));

        // Update claim definition if provided
        if (cmd.claimDefinitionId() != null) {
            claimDefinition = claimDefinitionRepository.findById(cmd.claimDefinitionId())
                    .orElseThrow(() -> new ClaimDefinitionNotFoundException(cmd.claimDefinitionId()));
            claimValue.setClaimDefinition(claimDefinition);
        } else {
            claimDefinition = claimDefinitionRepository.findById(claimValue.getClaimDefinition().getId())
                    .orElseThrow(() -> new ClaimDefinitionNotFoundException(claimValue.getClaimDefinition().getId()));
        }

        if (claimDefinition.getClaimType().equals(ClaimType.SYSTEM_DEFINED) && Boolean.FALSE.equals(allowToUpdateSystemClaim)){
            throw new UpdateClaimTypeSystemException("Cannot alter claim value if the definition is of type SYSTEM_DEFINED");
        }

        // Update owner fields if provided
        if (cmd.identityId() != null) {
            claimValue.setOwnerType(OwnerType.IDENTITY);
            claimValue.setOwnerId(cmd.identityId());
        }

        if (cmd.accountId() != null) {
            claimValue.setOwnerType(OwnerType.ACCOUNT);
            claimValue.setOwnerId(cmd.accountId());
        }

        /*
            If the claimValue to save is of the OwnerType Account or it's going to be saved as one.
            Check if we are updating / creating the claimSetId and that has a valid path.
            For now the path should be direct, so we need to check CS/CD/CV. As CD is in the middle, 
                if we can get from there to the CS then we have a valid path.
        */
        if (claimValue.getOwnerType().equals(OwnerType.ACCOUNT)){
            if (cmd.claimSetId() == null){
                if (claimValue.getClaimSetClaimValue() == null) {
                    throw new ClaimValueValidationException("ClaimValues for accounts MUST be binded to a ClaimSet");
                }
            } else {
                Optional<ClaimSetDefinitionMappingJpaEntity> csdMapping = claimValue.getClaimDefinition().getClaimSetMappings().stream().filter(csm -> csm.getClaimDefinitionId().equals(cmd.claimDefinitionId())).findFirst();
                if (csdMapping.isPresent()){
                    //Create a new ClaimSetClaimValue record as default.
                    ClaimSetClaimValueJpaEntity relation = new ClaimSetClaimValueJpaEntity();
                    if (claimValue.getClaimSetClaimValue() != null) {
                        relation = claimValue.getClaimSetClaimValue();
                    }
                    relation.setClaimSet(csdMapping.get().getClaimSet());
                    relation.setClaimValue(claimValue);
                    claimValue.setClaimSetClaimValue(relation);
                } else {
                    throw new IllegalStateException("No applicable path for claimSet " + cmd.claimSetId() + " and claimDefinition " + cmd.claimDefinitionId());
                }
            }
        }
        
        if (cmd.value() != null) {
            validateWithDataFormat(cmd, claimDefinition);

            DataTypeEnum type = claimDefinition.getDataType();
            if (!type.isValid(cmd.value())) {
                throw new ClaimValueValidationException("Value does not match data type: " + type.name());
            }

            String normalizedValue = type.normalize(cmd.value());

            claimValue.setValue(normalizedValue);
        }
        
        if (cmd.isPrimary() != null) {
            claimValue.setPrimary(cmd.isPrimary());
        }
        
        if (cmd.isComputed() != null) {
            claimValue.setComputed(cmd.isComputed());
        }
        
        if (cmd.source() != null) {
            claimValue.setSource(cmd.source());
        }
        
        if (cmd.primaryIndex() != null) {
            claimValue.setPrimaryIndex(cmd.primaryIndex());
        }

        // Save the updated entity with constraint handling
        DatabaseConstraintUtils.executeWithConstraintHandling(
            () -> claimValueRepository.saveAndFlush(claimValue),
            "ux_claim_value_owner_cd_val",
            () -> {
                String owner = claimValue.getOwnerType() == OwnerType.ACCOUNT ? "account" : "identity";
                return new DuplicateClaimValueException(
                    "The %s already has the value '%s' for claim '%s'"
                        .formatted(owner, cmd.convertValueToString(), claimDefinition.getCode())
                );
            }
        );
        logger.debug("Claim value updated successfully with ID: {}", cmd.id());
    }

    private void validateWithDataFormat(UpdateClaimValueCommand cmd, ClaimDefinitionJpaEntity definition) {
        String dataFormat = definition.getDataFormat();
        if (dataFormat == null || dataFormat.isBlank()) {
            return; // No validation pattern specified
        }

        // For ARRAY types, check if dataFormat is a UUID reference to another ClaimDefinition
        if (definition.getDataType() == DataTypeEnum.ARRAY && DataFormatUtils.isUuidFormat(dataFormat)) {
            validateArrayWithUuidReference(cmd, dataFormat);
        } else {
            // For non-ARRAY types or when dataFormat is not UUID, use regex validation
            cmd.validateWithPattern(dataFormat);
        }
    }

    private void validateArrayWithUuidReference(UpdateClaimValueCommand cmd, String referencedClaimDefinitionId) {
        try {
            java.util.UUID referencedId = java.util.UUID.fromString(referencedClaimDefinitionId.trim());
            ClaimDefinitionJpaEntity referencedDefinition = claimDefinitionRepository.findById(referencedId)
                    .orElseThrow(() -> new ClaimDefinitionNotFoundException(referencedId));

            // Parse the array value and validate each element against the referenced definition's pattern
            String[] arrayElements = ArrayParsingUtils.parseArrayValue(cmd.convertValueToString());
            String referencedPattern = referencedDefinition.getDataFormat();

            for (String element : arrayElements) {
                if (referencedPattern != null && !referencedPattern.isBlank() && !element.trim().matches(referencedPattern)) {
                    throw new ClaimValueValidationException(
                            "Array element '" + element + "' does not match the required pattern for " +
                            referencedDefinition.getCode());
                }

                // Also validate against the referenced definition's data type
                DataTypeEnum referencedType = referencedDefinition.getDataType();
                if (!referencedType.isValid(element.trim())) {
                    throw new ClaimValueValidationException(
                            "Array element '" + element + "' does not match data type: " + referencedType.name());
                }
            }
        } catch (ClaimValueValidationException e) {
            throw e;
        } catch (Exception e) {
            throw new ClaimValueValidationException("Invalid UUID reference in dataFormat: " + referencedClaimDefinitionId, e);
        }
    }


}
