package com.vusecurity.auth.claims.application.command;

import java.util.List;
import java.util.UUID;

/**
 * Command for adding claim sets to a claim definition.
 */
public record AddClaimSetsToClaimDefinitionCommand (
    UUID claimDefinitionId,
    List<ClaimSetAssignmentCommand> claimSetsAssignment) {

    public void validate() {
        if (claimDefinitionId == null) {
            throw new IllegalArgumentException("claimDefinitionId is required");
        }
        if (claimSetsAssignment == null) {
            throw new IllegalArgumentException("claimSets is required");
        }
        if (claimSetsAssignment.isEmpty()) {
            throw new IllegalArgumentException("At least one claim set must be provided");
        }
        for (ClaimSetAssignmentCommand claimSets : claimSetsAssignment) {
            if (claimSets == null) {
                throw new IllegalArgumentException("claimSets cannot contain null values");
            }
        }
    }
}
