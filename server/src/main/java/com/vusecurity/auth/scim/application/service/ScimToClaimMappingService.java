package com.vusecurity.auth.scim.application.service;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimToClaimMappingJpaEntity;
import com.vusecurity.auth.scim.infrastructure.persistence.jpa.repository.ScimToClaimMappingRepository;

@Service
@Transactional
public class ScimToClaimMappingService {

    private final ScimToClaimMappingRepository scimToClaimMappingRepository;

    public ScimToClaimMappingService(ScimToClaimMappingRepository scimToClaimMappingRepository){
        this.scimToClaimMappingRepository = scimToClaimMappingRepository;
    }

    public List<ScimToClaimMappingJpaEntity> getByBusinessId(UUID businessId){
        return this.scimToClaimMappingRepository.findAllByBusinessId(businessId);
    }

    public Optional<ScimToClaimMappingJpaEntity> findById(UUID id){
        return this.scimToClaimMappingRepository.findById(id);
    }

    public ScimToClaimMappingJpaEntity save(ScimToClaimMappingJpaEntity entity){
        return this.scimToClaimMappingRepository.saveAndFlush(entity);
    }

    public List<ScimToClaimMappingJpaEntity> saveAll(Collection<ScimToClaimMappingJpaEntity> entityList){
        return this.scimToClaimMappingRepository.saveAllAndFlush(entityList);
    }

    public void deleteByBusinessId(UUID businessId){
        this.scimToClaimMappingRepository.deleteByBusinessId(businessId);
        this.scimToClaimMappingRepository.flush();
    }

    public void deleteById(UUID id){
        this.scimToClaimMappingRepository.deleteById(id);
        this.scimToClaimMappingRepository.flush();
    }

}
