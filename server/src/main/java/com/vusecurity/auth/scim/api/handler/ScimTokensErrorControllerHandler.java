package com.vusecurity.auth.scim.api.handler;

import com.vusecurity.auth.scim.application.exception.InvalidScimTokenRequest;
import com.vusecurity.auth.scim.application.exception.ScimTokenNameAlreadyExistsException;
import com.vusecurity.auth.scim.application.exception.ScimTokenNotFoundException;
import com.vusecurity.core.commons.ApiMessage;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice(assignableTypes = {com.vusecurity.auth.scim.api.controller.ScimTokensController.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ScimTokensErrorControllerHandler {

    @ExceptionHandler(ScimTokenNotFoundException.class)
    public ResponseEntity<ApiMessage> handleTokenNotFoundException(ScimTokenNotFoundException ex) {
        ApiMessage error = new ApiMessage(5600, ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }

    @ExceptionHandler(ScimTokenNameAlreadyExistsException.class)
    public ResponseEntity<ApiMessage> handleTokenNameAlreadyExistsException(ScimTokenNameAlreadyExistsException ex) {
        ApiMessage error = new ApiMessage(5601, ex.getMessage());
        return ResponseEntity.status(HttpStatus.CONFLICT).body(error);
    }

    @ExceptionHandler(InvalidScimTokenRequest.class)
    public ResponseEntity<ApiMessage> handleInvalidTokenRequestException(InvalidScimTokenRequest ex) {
        ApiMessage error = new ApiMessage(5604, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<ApiMessage> handleDataIntegrityViolationException(DataIntegrityViolationException ex) {
        String message = ex.getMessage();
        String userMessage = "A record with the same unique value already exists.";
        int code = 5606;

        if (message != null) {
            // Example: handle known constraint for group name
            if (message.contains("uk_scim_tokens_name")) {
                // Try to extract the duplicate value
                String duplicateName = null;
                java.util.regex.Matcher matcher = java.util.regex.Pattern
                        .compile("The duplicate key value is \\((.*?)\\)").matcher(message);
                if (matcher.find()) {
                    duplicateName = matcher.group(1);
                }
                userMessage = "SCIM token with name '" + (duplicateName != null ? duplicateName : "") + "' already exists.";
            } else if (message.contains("unique") || message.contains("duplicate key")) {
                // Try to extract the duplicate value for generic unique constraint
                java.util.regex.Matcher matcher = java.util.regex.Pattern
                        .compile("The duplicate key value is \\((.*?)\\)").matcher(message);
                if (matcher.find()) {
                    userMessage = "A record with this value already exists: " + matcher.group(1);
                }
            }
        }

        ApiMessage error = new ApiMessage(code, userMessage);
        return ResponseEntity.status(HttpStatus.CONFLICT).body(error);
    }
} 