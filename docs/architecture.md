# VU One Auth Server Architecture

## Overview

The VU One Auth Server is built using a **Vertical Slice Architecture** combined with a **Command/Query Handler Pattern**. This architectural approach prioritizes feature-based organization over traditional layered architecture, resulting in better maintainability, testability, and team productivity.

## Why Vertical Slices?

### Traditional Layered Architecture Problems

Traditional layered architectures organize code by technical concerns (controllers, services, repositories), which leads to:

- **High coupling across layers**: Changes often require modifications in multiple layers
- **Feature scattering**: A single feature's code is spread across multiple directories
- **Team coordination overhead**: Multiple developers working on the same feature often conflict
- **Testing complexity**: Testing a feature requires understanding multiple layers
- **Cognitive load**: Developers must navigate multiple directories to understand one feature

### Vertical Slice Benefits

Our vertical slice architecture organizes code by **business features** rather than technical layers:

```
/auth
  /identities          # Identity management feature
    /api              # Controllers and DTOs
    /application      # Commands, Queries, Handlers
    /infrastructure   # JPA entities, repositories
    /mapper          # DTO-Entity mapping
  /authorization      # Role/Permission management feature
  /claims            # Claims management feature
  /shared           # Cross-cutting concerns
```

**Key Benefits:**

1. **Feature Cohesion**: All code for a feature lives together
2. **Reduced Coupling**: Features are loosely coupled, changes are isolated
3. **Team Productivity**: Teams can work on different features independently
4. **Easier Testing**: Feature tests are self-contained
5. **Better Understanding**: New developers can focus on one slice at a time
6. **Deployment Flexibility**: Features can potentially be extracted as microservices

## Command/Query Handler Pattern

### Why Not Traditional Services?

Traditional service layers often become:
- **God objects** with too many responsibilities
- **Tightly coupled** to multiple dependencies
- **Hard to test** due to complex dependency graphs
- **Difficult to maintain** as they grow larger

### Our Handler Approach

We implement the **Command/Query Responsibility Segregation (CQRS)** pattern with dedicated handlers:

#### Commands (Write Operations)
Commands represent **intent to change state**:

```java
public record CreateAccountCommand(
    UUID identityId,
    UUID businessId,
    UUID identityProviderId,
    AccountType accountType,
    AccountLifecycleState lifecycleState,
    Map<String, Object> metadata) {

    public void validate() {
        if (identityId == null) throw new IllegalArgumentException("identityId is required");
        // ... validation logic
    }
}
```

**Command Handlers** execute the business logic:

```java
@Service
@RequiredArgsConstructor
public class CreateAccountHandler {

    private final AccountRepository accountRepository;
    private final BusinessRepository businessRepository;

    @Transactional
    public AccountJpaEntity createAccount(CreateAccountCommand cmd) {
        cmd.validate();

        var business = businessRepository.findById(cmd.businessId())
                .orElseThrow(BusinessNotFoundException::new);
        var identity = identityRepository.findByIdentityId(cmd.identityId())
                .orElseThrow(() -> new IllegalArgumentException("Identity not found"));

        AccountJpaEntity account = new AccountJpaEntity();
        account.setBusiness(business);
        account.setIdentity(identity);
        // ... set other properties

        return accountRepository.save(account);
    }
}
```

#### Queries (Read Operations)
Queries represent **requests for data**:

```java
@Value
@Builder
public class GetAccountsPagedQuery {
    int page;
    int pageSize;
    String filter;
}
```

**Query Handlers** retrieve and filter data:

```java
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class GetAccountHandler implements GetAccountQuery {

    private final AccountRepository accountRepository;

    @Override
    public Page<AccountJpaEntity> getAllAccounts(GetAccountsPagedQuery query) {
        int pageNumber = Math.max(0, query.getPage() - 1);
        Pageable pageable = PageRequest.of(pageNumber, query.getPageSize());

        return accountRepository.findAll(pageable);
    }
}
```

### Handler Pattern Benefits

1. **Single Responsibility**: Each handler has one clear purpose
2. **Easy Testing**: Handlers can be tested in isolation
3. **Flexible Composition**: Controllers compose handlers as needed
4. **Clear Intent**: Command/Query names express business intent
5. **Validation Centralization**: Commands validate their own data
6. **Transaction Boundaries**: Clear transactional boundaries per operation

## Architecture Layers

### 1. API Layer (`/api`)
- **Controllers**: HTTP endpoints and request/response handling
- **DTOs**: Data transfer objects for API contracts
- **Validation**: Input validation and error handling

### 2. Application Layer (`/application`)
- **Commands**: Write operation requests with validation
- **Queries**: Read operation requests and interfaces
- **Handlers**: Business logic execution
- **No business rules in controllers**: Controllers only orchestrate

### 3. Infrastructure Layer (`/infrastructure`)
- **JPA Entities**: Database mapping and persistence
- **Repositories**: Data access interfaces and implementations
- **External Services**: Third-party integrations

### 4. Mapping Layer (`/mapper`)
- **DTO Mappers**: Convert between DTOs and entities
- **Manual Mapping**: Explicit, controlled transformations
- **No MapStruct**: Avoiding annotation processor complexity

## Key Architectural Decisions

### 1. Collapsed Domain Model
We use **"Variant A - Collapse Domain & Entity"** pattern:
- JPA entities serve as both domain models and persistence objects
- Eliminates Domain⇄Entity mapping overhead
- Reduces complexity while maintaining business logic in entities
- DTOs map directly to entities

### 2. Repository Ports
Despite using JPA entities directly, we maintain repository interfaces:
- **Storage Agnosticism**: Can switch persistence technologies
- **Testing**: Easy to mock for unit tests
- **Abstraction**: Hide JPA-specific details from handlers

### 3. Manual DTO Mapping
We prefer manual mapping over MapStruct:
- **Explicit Control**: Clear transformation logic
- **No Magic**: No annotation processor complexity
- **Debugging**: Easier to debug mapping issues
- **Flexibility**: Custom mapping logic when needed

### 4. Audit Infrastructure
Audit fields are handled at the infrastructure level:
- **JPA Auditing**: Automatic `createdAt`/`updatedAt` via `@PrePersist`/`@PreUpdate`
- **Security Context**: `createdBy`/`updatedBy` from authentication
- **Clean Domain**: Domain objects remain free of audit concerns

### 5. Query Complexity in Handlers
Complex filtering logic lives in query handlers:
- **JPA Specifications**: Dynamic query building
- **Handler Responsibility**: Handlers own their query complexity
- **Controller Simplicity**: Controllers remain thin

## Example: Account Feature Structure

```
/identities
  /api
    /controller
      AccountsController.java          # HTTP endpoints
  /application
    /command
      CreateAccountCommand.java        # Write requests
      UpdateAccountCommand.java
    /query
      GetAccountQuery.java            # Read interfaces
      GetAccountsPagedQuery.java      # Query parameters
    /handler
      CreateAccountHandler.java       # Command execution
      UpdateAccountHandler.java
      GetAccountHandler.java          # Query execution
  /infrastructure
    /persistence/jpa
      /entity
        AccountJpaEntity.java         # JPA entity
      /repository
        AccountRepository.java        # Data access
  /mapper
    AccountDtoMapper.java            # DTO-Entity mapping
```

## Benefits Realized

### 1. Team Productivity
- **Parallel Development**: Teams work on different slices independently
- **Reduced Conflicts**: Less merge conflicts due to feature isolation
- **Faster Onboarding**: New developers can focus on one slice

### 2. Maintainability
- **Feature Cohesion**: Related code lives together
- **Clear Boundaries**: Well-defined interfaces between slices
- **Easy Refactoring**: Changes are localized to features

### 3. Testing
- **Unit Testing**: Handlers test business logic in isolation
- **Integration Testing**: Feature tests are self-contained
- **Mocking**: Repository interfaces enable easy mocking

### 4. Flexibility
- **Technology Changes**: Can evolve persistence per feature
- **Microservice Extraction**: Features can become services
- **API Evolution**: Controllers can evolve independently

## Trade-offs and Considerations

### Potential Drawbacks
- **Code Duplication**: Some cross-cutting code may be duplicated
- **Learning Curve**: Teams need to understand the pattern
- **Consistency**: Need discipline to maintain architectural consistency

### Mitigation Strategies
- **Shared Module**: Common utilities in `/shared`
- **Code Reviews**: Enforce architectural patterns
- **Documentation**: Clear guidelines and examples
- **Tooling**: IDE templates and code generation

## Future Evolution

This architecture supports future evolution:

1. **Microservice Extraction**: Slices can become independent services
2. **Event Sourcing**: Commands can be extended to emit events
3. **CQRS Scaling**: Read/write models can be separated further
4. **Domain Events**: Cross-slice communication via events

## Conclusion

The Vertical Slice Architecture with Command/Query Handlers provides a **simple yet flexible** foundation that:

- **Scales with team size** and feature complexity
- **Reduces cognitive load** for developers
- **Improves maintainability** through feature cohesion
- **Enables parallel development** across teams
- **Supports future architectural evolution**

This approach prioritizes **developer productivity** and **code maintainability** while keeping the architecture **simple and understandable**.