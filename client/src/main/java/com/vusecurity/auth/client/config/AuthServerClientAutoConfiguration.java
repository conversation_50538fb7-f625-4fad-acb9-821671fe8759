package com.vusecurity.auth.client.config;

import com.vusecurity.auth.client.AccountsClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(AuthServerClientConfiguration.class)
@ConditionalOnProperty(prefix = "auth.server.client", name = "enabled", havingValue = "true", matchIfMissing = true)
public class AuthServerClientAutoConfiguration {
    
    @Bean
    @ConditionalOnMissingBean
    public AccountsClient accountsClient(AuthServerClientConfiguration configuration) {
        return new AccountsClient(configuration);
    }
}