package com.vusecurity.auth.contracts.api.v1.dto.identities;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.UUID;

@Data
@Schema(description = "Claim identifier for account identification")
public class ClaimIdentifierRequest {
    @NotNull(message = "Claim definition ID is required")
    @Schema(description = "Unique identifier of the claim definition",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID claimDefinitionId;

    @NotNull(message = "Value is required")
    @NotBlank(message = "Value cannot be blank")
    @Schema(description = "The claim value to match against",
            example = "<EMAIL>")
    private String value;
}
