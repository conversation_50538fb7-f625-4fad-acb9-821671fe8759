package com.vusecurity.auth.claims.application.command;

import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class RemoveClaimDefinitionsFromClaimSetCommandTest {

    @Test
    void validate_happy_path() {
        RemoveClaimDefinitionsFromClaimSetCommand cmd = new RemoveClaimDefinitionsFromClaimSetCommand(
                UUID.randomUUID(), List.of(UUID.randomUUID()));
        assertDoesNotThrow(cmd::validate);
    }

    @Test
    void validate_throws_when_claimSetId_null() {
        RemoveClaimDefinitionsFromClaimSetCommand cmd = new RemoveClaimDefinitionsFromClaimSetCommand(
                null, List.of(UUID.randomUUID()));
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("claimSetId"));
    }

    @Test
    void validate_throws_when_claimDefinitionIds_null() {
        RemoveClaimDefinitionsFromClaimSetCommand cmd = new RemoveClaimDefinitionsFromClaimSetCommand(
                UUID.randomUUID(), null);
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("claimDefinitionIds"));
    }

    @Test
    void validate_throws_when_claimDefinitionIds_empty() {
        RemoveClaimDefinitionsFromClaimSetCommand cmd = new RemoveClaimDefinitionsFromClaimSetCommand(
                UUID.randomUUID(), List.of());
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("At least one"));
    }

    @Test
    void validate_throws_when_claimDefinitionIds_contains_null() {
        List<UUID> claimDefinitionIds = new java.util.ArrayList<>();
        claimDefinitionIds.add(null);
        RemoveClaimDefinitionsFromClaimSetCommand cmd = new RemoveClaimDefinitionsFromClaimSetCommand(
                UUID.randomUUID(), claimDefinitionIds);
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("cannot contain null"));
    }
}
