package com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.LookupStrategy;
import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import com.vusecurity.business.domain.Business;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;

import java.util.*;

@Entity
@Table(name = "claim_set",
        indexes = {
                @Index(name = "uk_claim_set_id", columnList = "id", unique = true),
                @Index(name = "idx_claim_set_composite", columnList = "business_id,account_type,is_identifier"),
                @Index(name = "idx_claim_set_business_account", columnList = "business_id,account_type"),
                @Index(name = "uk_claim_set_business_account_name", columnList = "business_id,account_type,name", unique = true)
        })
@SuppressWarnings("LombokGetterMayBeUsed, LombokSetterMayBeUsed")
public class ClaimSetJpaEntity extends AbstractEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "business_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(name = "fk_claim_set_business")
    )
    private Business business;

    @Enumerated(EnumType.STRING)
    @Column(name = "account_type")
    private AccountType accountType;

    @Column(name = "is_identifier", nullable = false)
    private Boolean isIdentifier;

    @Column(nullable = false)
    @Size(max = 255)
    private String name;

    @Column(length = 1024)
    @Size(max = 1024)
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "lookup_strategy", nullable = false)
    private LookupStrategy lookupStrategy = LookupStrategy.ALL_CLAIMS_MUST_MATCH;

    @OneToMany(mappedBy = "claimSet", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<ClaimSetDefinitionMappingJpaEntity> claimDefinitionMappings = new HashSet<>();

    @OneToMany(mappedBy = "claimSet", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<ClaimSetClaimValueJpaEntity> claimSetClaimValues;

    // Constructors
    public ClaimSetJpaEntity() {
    }

    // Constructor with ID (for migration/test scenarios)
    public ClaimSetJpaEntity(UUID id, Business business, AccountType accountType, Boolean isIdentifier, String name, String description) {
        this.setId(id);
        this.business = business;
        this.accountType = accountType;
        this.isIdentifier = isIdentifier;
        this.name = name;
        this.description = description;
        this.lookupStrategy = LookupStrategy.ALL_CLAIMS_MUST_MATCH;
        this.claimDefinitionMappings = new HashSet<>();
    }

    // Constructor with ID and lookup strategy (for migration/test scenarios)
    public ClaimSetJpaEntity(UUID id, Business business, AccountType accountType, Boolean isIdentifier, String name, String description, LookupStrategy lookupStrategy) {
        this.setId(id);
        this.business = business;
        this.accountType = accountType;
        this.isIdentifier = isIdentifier;
        this.name = name;
        this.description = description;
        this.lookupStrategy = lookupStrategy != null ? lookupStrategy : LookupStrategy.ALL_CLAIMS_MUST_MATCH;
        this.claimDefinitionMappings = new HashSet<>();
    }

    // Constructor without ID (for production use - JPA will generate ID)
    public ClaimSetJpaEntity(Business business, AccountType accountType, Boolean isIdentifier) {
        if (business == null) {
            throw new IllegalArgumentException("business cannot be null");
        }
        if (accountType == null) {
            throw new IllegalArgumentException("accountType cannot be null");
        }
        if (isIdentifier == null) {
            throw new IllegalArgumentException("isIdentifier cannot be null");
        }

        this.business = business;
        this.accountType = accountType;
        this.isIdentifier = isIdentifier;
        this.lookupStrategy = LookupStrategy.ALL_CLAIMS_MUST_MATCH;
        this.claimDefinitionMappings = new HashSet<>();
    }

    // Getters
    public Business getBusiness() {
        return business;
    }

    public AccountType getAccountType() {
        return accountType;
    }

    public Boolean getIsIdentifier() {
        return isIdentifier;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public LookupStrategy getLookupStrategy() {
        return lookupStrategy;
    }

    public Set<ClaimSetDefinitionMappingJpaEntity> getClaimDefinitionMappings() {
        return claimDefinitionMappings != null ? Collections.unmodifiableSet(claimDefinitionMappings) : Collections.emptySet();
    }

    // Setters
    public void setBusiness(Business business) {
        this.business = business;
    }

    public void setAccountType(AccountType accountType) {
        this.accountType = accountType;
    }

    public void setIsIdentifier(Boolean isIdentifier) {
        this.isIdentifier = isIdentifier;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setLookupStrategy(LookupStrategy lookupStrategy) {
        this.lookupStrategy = lookupStrategy != null ? lookupStrategy : LookupStrategy.ALL_CLAIMS_MUST_MATCH;
    }

    public void setClaimDefinitionMappings(Set<ClaimSetDefinitionMappingJpaEntity> claimDefinitionMappings) {
        this.claimDefinitionMappings = claimDefinitionMappings != null ? new HashSet<>(claimDefinitionMappings) : new HashSet<>();
    }

    // Business behavior methods
    public void changeName(String newName) {
        this.name = newName;
    }

    public void changeDescription(String newDescription) {
        this.description = newDescription;
    }



    public UUID getBusinessId() {
        return business != null ? business.getId() : null;
    }



    // equals() and hashCode() using id (fallback since no unique business key)
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ClaimSetJpaEntity that = (ClaimSetJpaEntity) o;
        return Objects.equals(getBusinessId(), that.getBusinessId()) &&
               Objects.equals(accountType, that.accountType) &&
               Objects.equals(isIdentifier, that.isIdentifier);
    }

    @Override
    public int hashCode() {
        return Objects.hash(getBusinessId(), accountType, isIdentifier);
    }

    @Override
    public String toString() {
        return "ClaimSetJpaEntity{" +
                "id=" + getId() +
                ", businessId=" + getBusinessId() +
                ", accountType='" + accountType + '\'' +
                ", isIdentifier=" + isIdentifier +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", lookupStrategy=" + lookupStrategy +
                '}';
    }
}
