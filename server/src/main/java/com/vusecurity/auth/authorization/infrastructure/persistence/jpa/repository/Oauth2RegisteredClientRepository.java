package com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository;

import java.util.Optional;

import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.Oauth2RegisteredClient;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface Oauth2RegisteredClientRepository extends JpaRepository<Oauth2RegisteredClient, String>, JpaSpecificationExecutor<Oauth2RegisteredClient> {
    @Query("SELECT c FROM Oauth2RegisteredClient c WHERE c.clientId = :clientId")
    Optional<Oauth2RegisteredClient> findByClientId(@Param("clientId") String clientId);
}