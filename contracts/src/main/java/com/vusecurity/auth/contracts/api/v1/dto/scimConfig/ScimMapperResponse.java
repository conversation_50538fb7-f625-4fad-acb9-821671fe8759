package com.vusecurity.auth.contracts.api.v1.dto.scimConfig;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "SCIM mapper response")
public class ScimMapperResponse {

    @Schema(description = "Id of a business",
            example = "f9fc0a64-56fc-4477-87a7-8e525505abce")
    private UUID businessId;

    @Schema(
            description = "List of filters of the business",
            implementation = ScimFilterMapperResponse.class
    )
    @JsonProperty("filters")
    List<ScimFilterMapperResponse> filters;
} 