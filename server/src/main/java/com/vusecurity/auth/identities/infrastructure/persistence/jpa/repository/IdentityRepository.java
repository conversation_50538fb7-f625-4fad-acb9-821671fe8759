package com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.contracts.enums.IdentityType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Spring Data JPA repository for IdentityJpaEntity.
 * Infrastructure-only interface for database operations.
 * Works directly with IdentityJpaEntity following the "Variant A - Collapse Domain & Entity" pattern.
 */
@Repository
public interface IdentityRepository extends JpaRepository<IdentityJpaEntity, UUID>, JpaSpecificationExecutor<IdentityJpaEntity> {

    /**
     * Find identity by ID.
     * @param id the identity ID
     * @return the identity if found
     */
    @Query("SELECT i FROM IdentityJpaEntity i WHERE i.id = :id")
    Optional<IdentityJpaEntity> findById(@Param("id") UUID id);
    
    /**
     * Find identities by name.
     * @param name the name
     * @param pageable pagination information
     * @return page of identities
     */
    @Query("SELECT i FROM IdentityJpaEntity i WHERE i.name LIKE %:name%")
    Page<IdentityJpaEntity> findByNameContaining(@Param("name") String name, Pageable pageable);
    
    /**
     * Find identities by type.
     * @param identityType the identity type
     * @param pageable pagination information
     * @return page of identities
     */
    @Query("SELECT i FROM IdentityJpaEntity i WHERE i.identityType = :identityType")
    Page<IdentityJpaEntity> findByIdentityType(@Param("identityType") IdentityType identityType, Pageable pageable);

    /**
     * Check if identity exists by ID.
     * @param id the identity ID
     * @return true if exists, false otherwise
     */
    boolean existsById(UUID id);

    /**
     * Delete identity by ID.
     * @param id the identity ID
     */
    void deleteById(UUID id);

    /**
     * Find identity by identity ID (same as findById but with explicit naming).
     * @param identityId the identity ID
     * @return optional identity
     */
    default Optional<IdentityJpaEntity> findByIdentityId(UUID identityId) {
        return findById(identityId);
    }

    /**
     * Check if identity exists by name.
     * @param name the identity name
     * @return true if exists, false otherwise
     */
    @Query("SELECT COUNT(i) > 0 FROM IdentityJpaEntity i WHERE i.name = :name")
    boolean existsByName(@Param("name") String name);

    /**
     * Find identities by lifecycle state.
     * @param lifecycleState the lifecycle state
     * @param pageable pagination information
     * @return page of identities
     */
    @Query("SELECT i FROM IdentityJpaEntity i WHERE i.lifecycleState = :lifecycleState")
    Page<IdentityJpaEntity> findByLifecycleState(@Param("lifecycleState") String lifecycleState, Pageable pageable);

    @Query("""
        SELECT i FROM IdentityJpaEntity i
        WHERE i.identityType = :identityType AND
            i.id IN (
                SELECT cv.ownerId FROM ClaimValueJpaEntity cv
                JOIN cv.claimDefinition cd
                WHERE cv.ownerType = com.vusecurity.auth.contracts.enums.OwnerType.IDENTITY
                AND UPPER(CONCAT(CAST(cd.id AS string), ':', cv.value)) IN :claimPairs
                GROUP BY cv.ownerId
                HAVING COUNT(DISTINCT cd.id) = :pairCount
            )
    """)
    List<IdentityJpaEntity> findIdentitiesByClaims(
            @Param("identityType") IdentityType identityType,
            @Param("claimPairs") List<String> claimPairs,
            @Param("pairCount") long pairCount
    );
}
