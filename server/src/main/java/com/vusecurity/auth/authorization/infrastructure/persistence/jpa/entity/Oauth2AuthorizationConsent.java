package com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity;

import java.io.Serializable;

import com.vusecurity.core.commons.Auditable;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

@Table(name="oauth2_authorization_consent")
@Entity
@IdClass(Oauth2AuthorizationConsentId.class)
public class Oauth2AuthorizationConsent extends Auditable implements Serializable{
    @Id
    @Column(name = "registered_client_id", length = 100)
    @NotBlank
    private String registeredClientId;

    @Id
    @Column(name = "principal_name", length = 200)
    @NotBlank
    private String principalName;

    @Column(name = "authorities", length = 1000, nullable = false)
    @NotBlank
    private String authorities;

    public Oauth2AuthorizationConsent() {
        // Default constructor required by JPA
    }

    public String getRegisteredClientId() {
        return registeredClientId;
    }

    public void setRegisteredClientId(String registeredClientId) {
        this.registeredClientId = registeredClientId;
    }

    public String getPrincipalName() {
        return principalName;
    }

    public void setPrincipalName(String principalName) {
        this.principalName = principalName;
    }

    public String getAuthorities() {
        return authorities;
    }

    public void setAuthorities(String authorities) {
        this.authorities = authorities;
    }

}
