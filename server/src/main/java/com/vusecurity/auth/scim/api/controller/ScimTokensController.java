package com.vusecurity.auth.scim.api.controller;

import static com.vusecurity.auth.authorization.domain.PermissionNames.BUSINESS_CREATE;
import static com.vusecurity.auth.authorization.domain.PermissionNames.BUSINESS_DELETE;
import static com.vusecurity.auth.authorization.domain.PermissionNames.BUSINESS_READ;
import static com.vusecurity.auth.authorization.domain.PermissionNames.BUSINESS_UPDATE;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.vusecurity.auth.contracts.api.v1.dto.scimConfig.CreateScimTokenRequest;
import com.vusecurity.auth.contracts.api.v1.dto.scimConfig.ScimTokenResponse;
import com.vusecurity.auth.scim.application.command.CreateScimTokenCommand;
import com.vusecurity.auth.scim.application.handler.CreateScimTokenHandler;
import com.vusecurity.auth.scim.application.handler.GetScimTokenHandler;
import com.vusecurity.auth.scim.application.handler.UpdateScimTokenHandler;
import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimTokenJpaEntity;
import com.vusecurity.auth.scim.mapper.ScimTokenDtoMapper;
import com.vusecurity.auth.shared.config.aspect.Oauth2Authorize;
import com.vusecurity.core.commons.models.PageableResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;


@RestController
@RequestMapping("${app.scim.context-path}/scim/tokens")
@Tag(name = "Scim Authentication Tokens", description = "Scim authentication token management operations")
public class ScimTokensController {

    private final CreateScimTokenHandler createScimTokenHandler;
    private final GetScimTokenHandler getScimTokenHandler;
    private final UpdateScimTokenHandler updateScimTokenHandler;

    public ScimTokensController (CreateScimTokenHandler createScimTokenHandler,
        GetScimTokenHandler getScimTokenHandler,
        UpdateScimTokenHandler updateScimTokenHandler){
        this.createScimTokenHandler = createScimTokenHandler;
        this.getScimTokenHandler = getScimTokenHandler;
        this.updateScimTokenHandler = updateScimTokenHandler;

    }

    @Operation(summary = "Create SCIM auth token",
            description = "Creates a SCIM token for the specified business and provider.")
    @ApiResponse(responseCode = "201", description = "Token created successfully", content = @Content(schema = @Schema(implementation = ScimTokenResponse.class)))
    @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors")
    @ApiResponse(responseCode = "401", description = "Authentication required")
    @ApiResponse(responseCode = "403", description = "Access denied")
    @ApiResponse(responseCode = "409", description = "Token with same name already exists")
    @PostMapping({""})
    @ResponseStatus(HttpStatus.CREATED)
    @Oauth2Authorize(permission = BUSINESS_CREATE)
    public ScimTokenResponse postMethodName(@Valid @RequestBody CreateScimTokenRequest request) {

        String newUuid = UUID.randomUUID().toString();

        CreateScimTokenCommand createCommand = new CreateScimTokenCommand(
            request.getName(),
            request.getDescription(),
            request.getBusinessId(),
            request.getIdentityProviderId(),
            newUuid);
        
        ScimTokenJpaEntity scimToken = createScimTokenHandler.createToken(createCommand);
        ScimTokenResponse response = ScimTokenDtoMapper.toResponse(scimToken);
        response.setToken(newUuid);
        
        return response;
    }

    @Operation(
            summary = "Get all SCIM tokens",
            description = "Retrieves a paginated list of account groups with optional filtering."
    )
    @ApiResponse(responseCode = "200", description = "Successful retrieval", content = @Content(schema = @Schema(implementation = PageableResponse.class)))
    @ApiResponse(responseCode = "400", description = "Invalid request parameters")
    @ApiResponse(responseCode = "401", description = "Authentication required")
    @ApiResponse(responseCode = "403", description = "Access denied")
    @GetMapping({"", "/"})
    @Oauth2Authorize(permission = BUSINESS_READ)
    //The @Parameter is for the correct annotation on the Pagable type as Swagger has issues converting org.springframework.data.domain.Pageable
    public PageableResponse<ScimTokenResponse> getAll(@Valid @Parameter(schema =  @Schema(type = "org.springdoc.core.converters.models.Pageable")) Pageable page) {

        Page<ScimTokenJpaEntity> results = getScimTokenHandler.getAllTokens(null, page);

        PageableResponse<ScimTokenResponse> response = 
            new PageableResponse<>(results.getNumber(), results.getSize(), results.getTotalElements(), null);
        response.setContent(results.stream().map(
            token -> ScimTokenDtoMapper.toResponse(token)).collect(Collectors.toList())
        );

        return response;
    }

     @Operation(
            summary = "Get a SCIM tokens",
            description = "Retrieves a token."
    )
    @ApiResponse(responseCode = "200", description = "Successful retrieval", content = @Content(schema = @Schema(implementation = PageableResponse.class)))
    @ApiResponse(responseCode = "400", description = "Invalid request parameters")
    @ApiResponse(responseCode = "401", description = "Authentication required")
    @ApiResponse(responseCode = "403", description = "Access denied")

    @GetMapping("/{id}")
    @Oauth2Authorize(permission = BUSINESS_READ)
    public ScimTokenResponse get(@PathVariable(value = "id") UUID id) {
        
        ScimTokenJpaEntity result = getScimTokenHandler.getToken(id);

        return ScimTokenDtoMapper.toResponse(result);
    }

    @Operation(summary = "Deactivates SCIM auth token",
            description = "Changes the status of a SCIM token to inactive if active.")
    @ApiResponse(responseCode = "200", description = "Token deactivated successfully", content = @Content(schema = @Schema(implementation = ScimTokenResponse.class)))
    @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors")
    @ApiResponse(responseCode = "401", description = "Authentication required")
    @ApiResponse(responseCode = "403", description = "Access denied")
    @PutMapping("/{id}/deactivate")
    @Oauth2Authorize(permission = BUSINESS_UPDATE)
    public ScimTokenResponse deactivate(@PathVariable(value = "id") UUID id) {
        
        ScimTokenJpaEntity result = updateScimTokenHandler.deactivateToken(id);

        return ScimTokenDtoMapper.toResponse(result);
    }

    @Operation(summary = "Activates SCIM auth token",
            description = "Changes the status of a SCIM token to active if inactive.")
    @ApiResponse(responseCode = "200", description = "Token deactivated successfully", content = @Content(schema = @Schema(implementation = ScimTokenResponse.class)))
    @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors")
    @ApiResponse(responseCode = "401", description = "Authentication required")
    @ApiResponse(responseCode = "403", description = "Access denied")
    @PutMapping("/{id}/activate")
    @Oauth2Authorize(permission = BUSINESS_UPDATE)
    public ScimTokenResponse activate(@PathVariable(value = "id") UUID id) {
        
        ScimTokenJpaEntity result = updateScimTokenHandler.activateToken(id);

        return ScimTokenDtoMapper.toResponse(result);
    }

    //TODO Update endpoint patch/put to allow to change name, description, status.
}
