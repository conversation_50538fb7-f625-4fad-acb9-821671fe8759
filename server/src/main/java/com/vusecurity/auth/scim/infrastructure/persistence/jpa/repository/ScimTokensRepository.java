package com.vusecurity.auth.scim.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimTokenJpaEntity;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import com.vusecurity.business.domain.Business;



@Repository
public interface ScimTokensRepository extends JpaRepository<ScimTokenJpaEntity, UUID>, JpaSpecificationExecutor<ScimTokenJpaEntity> {

    Optional<ScimTokenJpaEntity> findById(UUID id);

    Optional<ScimTokenJpaEntity> findByName(String name);
    
    List<ScimTokenJpaEntity> findAllByBusiness(Business business);

    List<ScimTokenJpaEntity> findAllByBusinessId(UUID businessId);

    @Query("SELECT st FROM ScimTokenJpaEntity st WHERE st.token = :token AND st.status = 'ACTIVE'")
    Optional<ScimTokenJpaEntity> findByHashedTokenAndIsActive(String token);
}
