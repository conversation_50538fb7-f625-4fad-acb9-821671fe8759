package com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import com.vusecurity.business.domain.Business;

import jakarta.persistence.*;
import lombok.NoArgsConstructor;

import java.util.Objects;
import java.util.UUID;

@Entity
@Table(name = "scim_to_claim_mapping",
        indexes = {
                @Index(name = "uk_scim_to_claim_mapping_id", columnList = "id", unique = true),
                @Index(name = "idx_scim_to_claim_mapping_business_id",
                       columnList = "business_id")
        },
        uniqueConstraints = {
            @UniqueConstraint(name = "uk_scim_to_claim_mapping_business_id_claim_set_origin_claim_definition_referenced", 
            columnNames = {"business_id","claim_set_origin","claim_definition_referenced"})
        }
    )
@NoArgsConstructor
public class ScimToClaimMappingJpaEntity extends AbstractEntity {

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(
            name = "business_id",           // column in table
            foreignKey = @ForeignKey(name = "fk_scim_to_claim_mapping_business"),
            referencedColumnName = "id"     // PK of Business
    )
    private Business business;

    private String filterMapping;

    private UUID claimSetOrigin;

    private UUID claimSetReferenced;

    private UUID claimDefinitionReferenced;

    private int sequenceOrder;

    private String description;

    @Transient
    private UUID claimValueOrigin;

    // Constructor without ID (for production use - JPA will generate ID)
    public ScimToClaimMappingJpaEntity(
            Business business,
            String filterMapping,
            UUID claimSetOrigin,
            UUID claimSetReferenced,
            UUID claimDefinitionReferenced,
            int sequenceOrder,
            String description) {
        if (business == null) {
            throw new IllegalArgumentException("business cannot be null");
        }
        if (filterMapping == null || filterMapping.isEmpty()) {
            throw new IllegalArgumentException("filterMapping cannot be null or be empty");
        }
        if (claimSetOrigin == null) {
            throw new IllegalArgumentException("claimSetOrigin cannot be null or be empty");
        }
        if (claimDefinitionReferenced == null) {
            throw new IllegalArgumentException("claimDefinitionReferenced cannot be null or be empty");
        }
        
        this.business = business;
        this.filterMapping = filterMapping;
        this.claimSetOrigin = claimSetOrigin;
        this.claimSetReferenced = claimSetReferenced;
        this.claimDefinitionReferenced = claimDefinitionReferenced;
        this.sequenceOrder = sequenceOrder;
        this.description = description;
    }

    // Constructor with ID (for migration scenarios)
    public ScimToClaimMappingJpaEntity(
            UUID primaryId,
            Business business,
            String filterMapping,
            UUID claimSetOrigin,
            UUID claimSetReferenced,
            UUID claimDefinitionReferenced,
            int sequenceOrder,
            String description) {
        this(business,filterMapping,claimSetOrigin,claimSetReferenced,claimDefinitionReferenced,sequenceOrder,description);
        this.setId(primaryId);
    }

    public Business getBusiness() {
        return business;
    }

    public void setBusiness(Business business) {
        this.business = business;
    }

    public UUID getClaimSetOrigin() {
        return claimSetOrigin;
    }

    public void setClaimSetOrigin(UUID claimSetOrigin) {
        this.claimSetOrigin = claimSetOrigin;
    }

    public UUID getClaimSetReferenced() {
        return claimSetReferenced;
    }

    public void setClaimSetReferenced(UUID claimSetReferenced) {
        this.claimSetReferenced = claimSetReferenced;
    }

    public UUID getClaimDefinitionReferenced() {
        return claimDefinitionReferenced;
    }

    public void setClaimDefinitionReferenced(UUID claimDefinitionReferenced) {
        this.claimDefinitionReferenced = claimDefinitionReferenced;
    }

    public int getSequenceOrder() {
        return sequenceOrder;
    }

    public void setSequenceOrder(int sequenceOrder) {
        this.sequenceOrder = sequenceOrder;
    }

    public UUID getClaimValueOrigin() {
        return claimValueOrigin;
    }

    public void setClaimValueOrigin(UUID claimValueOrigin) {
        this.claimValueOrigin = claimValueOrigin;
    }

    public String getFilterMapping() {
        return filterMapping;
    }

    public void setFilterMapping(String filterMapping) {
        this.filterMapping = filterMapping;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = super.hashCode();
        result = prime * result + ((business == null) ? 0 : business.hashCode());
        result = prime * result + ((filterMapping == null) ? 0 : filterMapping.hashCode());
        result = prime * result + ((claimSetOrigin == null) ? 0 : claimSetOrigin.hashCode());
        result = prime * result + ((claimSetReferenced == null) ? 0 : claimSetReferenced.hashCode());
        result = prime * result + ((claimDefinitionReferenced == null) ? 0 : claimDefinitionReferenced.hashCode());
        result = prime * result + sequenceOrder;
        result = prime * result + ((description == null) ? 0 : description.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (!super.equals(obj))
            return false;
        if (getClass() != obj.getClass())
            return false;
        ScimToClaimMappingJpaEntity other = (ScimToClaimMappingJpaEntity) obj;
        if (business == null) {
            if (other.business != null)
                return false;
        } else if (!business.equals(other.business))
            return false;
        if (filterMapping == null) {
            if (other.filterMapping != null)
                return false;
        } else if (!filterMapping.equals(other.filterMapping))
            return false;
        if (claimSetOrigin == null) {
            if (other.claimSetOrigin != null)
                return false;
        } else if (!claimSetOrigin.equals(other.claimSetOrigin))
            return false;
        if (claimSetReferenced == null) {
            if (other.claimSetReferenced != null)
                return false;
        } else if (!claimSetReferenced.equals(other.claimSetReferenced))
            return false;
        if (claimDefinitionReferenced == null) {
            if (other.claimDefinitionReferenced != null)
                return false;
        } else if (!claimDefinitionReferenced.equals(other.claimDefinitionReferenced))
            return false;
        if (sequenceOrder != other.sequenceOrder)
            return false;
        if (description == null) {
            if (other.description != null)
                return false;
        } else if (!description.equals(other.description))
            return false;
        return true;
    }

    @Override
    public String toString() {
        return "BusinessScimToClaimMappingJpaEntity{" +
            "id=" + getId() +
            ", business=" + getBusiness() +
            ", claimSetOrigin=" + getClaimSetOrigin() +
            ", claimSetReferenced=" + getClaimSetReferenced() +
            ", claimDefinitionReferenced=" + getClaimDefinitionReferenced() +
            ", sequenceOrder=" + getSequenceOrder() +
            ", description=" + getDescription() +
            '}';
    }
}
