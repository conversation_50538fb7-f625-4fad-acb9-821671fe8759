package com.vusecurity.auth.claims.application.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class DuplicateClaimSetExceptionTest {

    @Test
    void constructor_with_message() {
        DuplicateClaimSetException ex = new DuplicateClaimSetException("duplicate");
        assertEquals("duplicate", ex.getMessage());
        assertNull(ex.getCause());
    }

    @Test
    void constructor_with_message_and_cause() {
        RuntimeException cause = new RuntimeException("root");
        DuplicateClaimSetException ex = new DuplicateClaimSetException("duplicate", cause);
        assertEquals("duplicate", ex.getMessage());
        assertSame(cause, ex.getCause());
    }
}
