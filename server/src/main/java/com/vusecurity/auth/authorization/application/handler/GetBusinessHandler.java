package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.contracts.api.v1.dto.shared.BusinessBasicInfoResponse;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.usecases.interfaces.IRetrieveBusiness;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class GetBusinessHandler {
    private static final Logger logger = LoggerFactory.getLogger(GetBusinessHandler.class);

    private final IRetrieveBusiness retrieveBusiness;

    @Transactional
    public BusinessBasicInfoResponse retrieveBusinessBasicInfo(UUID businessId) {
        try {
            Business business = retrieveBusiness.retrieveById(businessId);
            return new BusinessBasicInfoResponse()
                    .setId(businessId.toString())
                    .setName(business.getName());
        } catch (Exception e) {
            logger.warn("Failed to retrieve business info for role {}: {}", businessId, e.getMessage());
            return null;
        }
    }
}
