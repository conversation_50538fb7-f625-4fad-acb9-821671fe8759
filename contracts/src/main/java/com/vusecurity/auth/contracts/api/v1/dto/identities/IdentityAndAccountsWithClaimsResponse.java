package com.vusecurity.auth.contracts.api.v1.dto.identities;

import com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition.ClaimDefinitionResponse;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.ClaimSetResponse;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.ClaimValueResponse;
import com.vusecurity.auth.contracts.api.v1.dto.identities.IdentityAndAccountsWithClaimsResponse.AccountsByBusinessResponse;
import com.vusecurity.auth.contracts.api.v1.dto.identities.IdentityAndAccountsWithClaimsResponse.CompactAccountResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BusinessBasicInfoResponse;
import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.IdentityLifecycleState;
import com.vusecurity.auth.contracts.enums.IdentityType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "Identity information with claims and accounts grouped by business response")
public class IdentityAndAccountsWithClaimsResponse extends BaseResponse {

    @Schema(description = "Unique identifier of the identity",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    @Schema(description = "Name of the identity",
            example = "John Doe")
    private String name;

    @Schema(description = "Type of identity",
            example = "PERSON")
    private IdentityType identityType;

    @Schema(description = "Additional metadata for the identity as key-value pairs",
            example = "{\"email\": \"<EMAIL>\", \"phone\": \"+**********\"}")
    private Map<String, Object> metadata;

    @Schema(description = "Current lifecycle state of the identity",
            example = "ACTIVE")
    private IdentityLifecycleState lifecycleState;

    @Schema(description = "List of Claims definitions associated with this identity")
    private List<ClaimDefinitionResponse> claimDefinitions;

    @Schema(description = "List of Claims values associated with this identity")
    private List<ClaimValueResponse> claimValues;

    @Schema(description = "List of accounts grouped by business associated with this identity")
    private List<AccountsByBusinessResponse> businessList;

    public IdentityAndAccountsWithClaimsResponse(IdentityResponse identityResponse,
                                                 List<ClaimDefinitionResponse> identityClaimDefinitions,
                                                 List<ClaimValueResponse> identityClaimValues,
                                                 List<ClaimSetResponse> claimSetResponses,
                                                 Map<UUID, List<ClaimValueResponse>> accountClaimValues) {
        copyIdentityData(identityResponse);
        this.claimDefinitions = identityClaimDefinitions;
        this.claimValues = identityClaimValues;
        this.businessList = generateBusinessList(identityResponse, claimSetResponses, accountClaimValues);
    }

    private void copyIdentityData(IdentityResponse identityResponse) {
        this.setId(identityResponse.getId());
        this.setName(identityResponse.getName());
        this.setIdentityType(identityResponse.getIdentityType());
        this.setMetadata(identityResponse.getMetadata());
        this.setLifecycleState(identityResponse.getLifecycleState());
    }

    private List<AccountsByBusinessResponse> generateBusinessList(IdentityResponse identityResponse,
                                                                  List<ClaimSetResponse> claimSetResponses,
                                                                  Map<UUID, List<ClaimValueResponse>> accountClaimValues) {
        if (identityResponse.getBusinesses() == null || identityResponse.getBusinesses().isEmpty()) {
            return new ArrayList<>();
        }

        Map<String, List<AccountResponse>> accountsByBusinessId = groupAccountsByBusinessId(identityResponse.getAccounts());

        return identityResponse.getBusinesses().stream()
                .map(business -> createAccountsByBusiness(business, accountsByBusinessId, claimSetResponses, accountClaimValues))
                .collect(Collectors.toList());
    }

    private Map<String, List<AccountResponse>> groupAccountsByBusinessId(List<AccountResponse> accounts) {
        if (accounts == null) {
            return Collections.emptyMap();
        }
        return accounts.stream()
                .collect(Collectors.groupingBy(account -> account.getBusinessId().toString()));
    }

    private AccountsByBusinessResponse createAccountsByBusiness(BusinessBasicInfoResponse business,
                                                                Map<String, List<AccountResponse>> accountsByBusinessId,
                                                                List<ClaimSetResponse> claimSetResponses,
                                                                Map<UUID, List<ClaimValueResponse>> accountClaimValues) {
        AccountsByBusinessResponse businessAccount = new AccountsByBusinessResponse(business)
                .setClaimSets(claimSetResponses)
                .setAccounts(new ArrayList<>());

        List<AccountResponse> businessAccounts = accountsByBusinessId.getOrDefault(business.getId(), Collections.emptyList());
        List<CompactAccountResponse> compactAccounts = businessAccounts.stream()
                .map(account -> createCompactAccount(account, accountClaimValues))
                .collect(Collectors.toList());

        businessAccount.setAccounts(compactAccounts);
        return businessAccount;
    }

    private CompactAccountResponse createCompactAccount(AccountResponse account, Map<UUID, List<ClaimValueResponse>> accountClaimValues) {
        CompactAccountResponse compactAccount = new CompactAccountResponse(account);
        compactAccount.setClaimValues(accountClaimValues.getOrDefault(account.getId(), null));
        return compactAccount;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Schema(description = "Claim definition and related configuration")
    public static class AccountsByBusinessResponse extends BusinessBasicInfoResponse {

        @Schema(description = "List of Claims sets associated with this business")
        private List<ClaimSetResponse> claimSets;

        @Schema(description = "List of Accounts associated with this business / identity")
        private List<CompactAccountResponse> accounts;

        public AccountsByBusinessResponse(BusinessBasicInfoResponse business){
            this.setId(business.getId());
            this.setName(business.getName());
        }
    }

    @Data
    @Schema(description = "Claim definition and related configuration")
    public static class CompactAccountResponse {
        @Schema(description = "Unique identifier of the account",
                example = "123e4567-e89b-12d3-a456-************")
        private UUID id;

        @Schema(description = "Unique identifier of the identity provider",
                example = "789abcde-f012-3456-7890-123456789abc")
        private UUID identityProviderId;

        @Schema(description = "Type of account",
                example = "WORKFORCE")
        private AccountType accountType;

        @Schema(description = "Additional metadata for the account as key-value pairs",
                example = "{\"department\": \"Engineering\", \"role\": \"Developer\"}")
        private Map<String, Object> metadata;

        @Schema(description = "Current lifecycle state of the account",
                example = "ACTIVE")
        private AccountLifecycleState lifecycleState;

        @Schema(description = "Primary account ID for merged accounts",
                example = "111e2222-e33b-44d5-a666-************")
        private UUID mergePrimary;

        @Schema(description = "Secondary account ID for merged accounts",
                example = "aaa1bbbb-c22d-33e4-f555-************")
        private UUID mergeSecondary;

        @Schema(description = "List of Claims values associated with this account")
        private List<ClaimValueResponse> claimValues;

        public CompactAccountResponse(AccountResponse account){
            this.id = account.getId();
            this.identityProviderId = account.getIdentityProviderId();
            this.accountType = account.getAccountType();
            this.metadata = account.getMetadata();
            this.lifecycleState = account.getLifecycleState();
            this.mergePrimary = account.getMergePrimary();
            this.mergeSecondary = account.getMergeSecondary();
        }
    }
}