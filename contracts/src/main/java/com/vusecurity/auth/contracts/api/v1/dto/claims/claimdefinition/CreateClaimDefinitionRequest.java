package com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition;

import com.vusecurity.auth.contracts.enums.ClaimType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.contracts.validations.ValidRegex;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.UUID;

@Data
@Schema(description = "Request to create a new claim definition")
public class CreateClaimDefinitionRequest {
    @NotNull(message = "Code is required")
    @Size(max = 255, message = "Code must not exceed 255 characters")
    @Schema(description = "Unique code identifier for the claim definition",
            example = "email_address")
    private String code;

    @NotNull(message = "Name is required")
    @Size(max = 255, message = "Name must not exceed 255 characters")
    @Schema(description = "Human-readable name for the claim definition",
            example = "Email Address")
    private String name;

    @Size(max = 1024, message = "Description must not exceed 1024 characters")
    @Schema(description = "Detailed description of what this claim represents",
            example = "Primary email address of the user")
    private String description;

    @Schema(description = "Type of the claim",
            example = "USER_DEFINED | SYSTEM_DEFINED")
    private ClaimType claimType = ClaimType.USER_DEFINED;

    @NotNull(message = "Data type is required")
    @Schema(description = "Data type of the claim value",
            example = "STRING")
    private DataTypeEnum dataType;

    @Size(max = 1024, message = "Data format must not exceed 1024 characters")
    @Schema(description = "Regular expression or format specification for validation",
            example = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
    @ValidRegex
    private String dataFormat;

    @Schema(description = "Reference to another claim definition if this is a list type",
            example = "f9fc0a64-56fc-4477-87a7-8e525505abce")
    private UUID isAListOf;
}
