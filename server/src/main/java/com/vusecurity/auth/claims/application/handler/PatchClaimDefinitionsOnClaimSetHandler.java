package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.PatchClaimDefinitionsOnClaimSetCommand;
import com.vusecurity.auth.claims.application.exception.ClaimDefinitionNotFoundException;
import com.vusecurity.auth.claims.application.exception.ClaimSetNotFoundException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.service.ClaimSetDefinitionMappingService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PatchClaimDefinitionsOnClaimSetHandler {
    private static final Logger logger = LoggerFactory.getLogger(PatchClaimDefinitionsOnClaimSetHandler.class);

    private final ClaimDefinitionRepository claimDefinitionRepository;
    private final ClaimSetRepository claimSetRepository;
    private final ClaimSetDefinitionMappingService mappingService;

    @Transactional
    public void patchClaimDefinitionsOnClaimSet(PatchClaimDefinitionsOnClaimSetCommand command) {
        logger.debug("Patching claim definitions on claim set {} with {}", command.claimSetId(), command.claimDefinitionsAssignment());

        // Validate command
        command.validate();

        // Find the claim set
        ClaimSetJpaEntity claimSet = claimSetRepository.getReferenceById(command.claimSetId());

        // If no claim definitions provided, nothing to do (valid PATCH operation)
        if (command.claimDefinitionsAssignment().isEmpty()) {
            logger.debug("No claim definitions provided in patch request, no changes made");
            return;
        }

        // Extract claim definition IDs for validation and deduplicate assignments
        Set<UUID> claimDefinitionIds = command.claimDefinitionsAssignment().stream()
                .map(PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand::claimDefinitionId)
                .collect(Collectors.toSet());

        // Validate that all claim definitions exist
        long foundCount = claimDefinitionRepository.countByIdIn(claimDefinitionIds);
        if (foundCount != claimDefinitionIds.size()) {
            throw new ClaimDefinitionNotFoundException("One or more claim definitions not found");
        }

        // Deduplicate assignments - keep last occurrence of each claimDefinitionId
        List<PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand> deduplicatedAssignments = getDeduplicatedAssignments(command);

        // Batch add all claim definitions
        mappingService.batchAddClaimDefinitionsToClaimSet(claimSet, claimDefinitionIds, deduplicatedAssignments);

        logger.debug("Successfully patched claim definitions on claim set: {}", command.claimSetId());
    }

    private static List<PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand> getDeduplicatedAssignments(PatchClaimDefinitionsOnClaimSetCommand command) {
        Map<UUID, PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand> deduplicatedMap = new HashMap<>();
        for (PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand assignment : command.claimDefinitionsAssignment()) {
            deduplicatedMap.put(assignment.claimDefinitionId(), assignment);
        }
        return new ArrayList<>(deduplicatedMap.values());
    }
} 