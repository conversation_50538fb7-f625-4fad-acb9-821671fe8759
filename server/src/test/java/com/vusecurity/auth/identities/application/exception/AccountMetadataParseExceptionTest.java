package com.vusecurity.auth.identities.application.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class AccountMetadataParseExceptionTest {

    @Test
    void constructor_formats_message_with_cause() {
        RuntimeException cause = new RuntimeException("parse error");
        AccountMetadataParseException ex = new AccountMetadataParseException(cause);
        
        assertTrue(ex.getMessage().contains("Error parsing metadata"));
        assertTrue(ex.getMessage().contains("parse error"));
    }
}
