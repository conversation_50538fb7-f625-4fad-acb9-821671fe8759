package com.vusecurity.auth.claims.application.command;

import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.LookupStrategy;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class CreateClaimSetCommandTest {

    @Test
    void validate_happy_path() {
        CreateClaimSetCommand cmd = new CreateClaimSetCommand(
                UUID.randomUUID(), AccountType.WORKFORCE, true, "name", "desc", LookupStrategy.ANY_CLAIM_CAN_MATCH);
        assertDoesNotThrow(cmd::validate);
    }

    @Test
    void validate_throws_when_businessId_null() {
        CreateClaimSetCommand cmd = new CreateClaimSetCommand(
                null, AccountType.WORKFORCE, true, "name", "desc", LookupStrategy.ANY_CLAIM_CAN_MATCH);
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("businessId"));
    }

    @Test
    void validate_throws_when_accountType_null() {
        CreateClaimSetCommand cmd = new CreateClaimSetCommand(
                UUID.randomUUID(), null, true, "name", "desc", LookupStrategy.ANY_CLAIM_CAN_MATCH);
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("accountType"));
    }

    @Test
    void validate_throws_when_isIdentifier_null() {
        CreateClaimSetCommand cmd = new CreateClaimSetCommand(
                UUID.randomUUID(), AccountType.WORKFORCE, null, "name", "desc", LookupStrategy.ANY_CLAIM_CAN_MATCH);
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("isIdentifier"));
    }

    @Test
    void validate_throws_when_name_null() {
        CreateClaimSetCommand cmd = new CreateClaimSetCommand(
                UUID.randomUUID(), AccountType.WORKFORCE, true, null, "desc", LookupStrategy.ANY_CLAIM_CAN_MATCH);
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("name"));
    }

    @Test
    void validate_throws_when_name_empty() {
        CreateClaimSetCommand cmd = new CreateClaimSetCommand(
                UUID.randomUUID(), AccountType.WORKFORCE, true, "  ", "desc", LookupStrategy.ANY_CLAIM_CAN_MATCH);
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("name"));
    }
}
