package com.vusecurity.auth.shared.infrastructure.security.scim;

import com.fasterxml.jackson.annotation.JsonIgnore;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;
import java.util.UUID;

/**
 * Custom authentication token for scim authentication.
 */
public class ScimToken extends AbstractAuthenticationToken {

    private final UUID tokenId;
    private final UUID businessId;
    private final UUID identityProviderId;
    private final String providedToken;

    @JsonIgnore
    private final Object principal;

    /**
     * Constructor for unauthenticated token (before validation)
     */
    public ScimToken(String providedToken) {
        super(null);
        this.tokenId = null;
        this.businessId = null;
        this.identityProviderId = null;
        this.providedToken = providedToken;
        this.principal = "unknown";
        setAuthenticated(false);
    }

    /**
     * Constructor for authenticated token (after successful validation)
     */
    public ScimToken(Object principal, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        TokenDetails details = ((TokenDetails) principal);
        this.tokenId = details.getTokenId();
        this.businessId = details.getBusinessId();
        this.identityProviderId = details.getIdentityProviderId();
        this.providedToken = null;
        this.principal = details.getUsername();
        this.setDetails(details);
        setAuthenticated(true);
    }

    /**
     * Constructor for Jackson deserialization.
     * This constructor can handle both authenticated and unauthenticated tokens.
     */
    public ScimToken(
            UUID tokenId,
            UUID businessId,
            UUID identityProviderId,
            String providedToken,
            Collection<? extends GrantedAuthority> authorities,
            boolean authenticated) {
        super(authorities);
        this.tokenId = tokenId;
        this.businessId = businessId;
        this.identityProviderId = identityProviderId;
        this.providedToken = providedToken;
        this.principal = "unknown";
        setAuthenticated(authenticated);
    }

    @Override
    public Object getCredentials() {
        return providedToken;
    }

    @Override
    public Object getPrincipal() {
        return principal;
    }

    public String getPassword() {
        return providedToken;
    }

    @Override
    public void eraseCredentials() {
        super.eraseCredentials();
        // Note: We can't modify final fields, but Spring Security will handle this
        // appropriately
    }
}
