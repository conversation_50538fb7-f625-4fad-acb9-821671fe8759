package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.command.CreateGroupCommand;
import com.vusecurity.auth.authorization.application.exception.AccountNotFoundException;
import com.vusecurity.auth.authorization.application.exception.MismatchedBusinessException;
import com.vusecurity.auth.authorization.application.exception.RoleNotFoundException;
import com.vusecurity.auth.authorization.domain.model.GroupMembershipRole;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.GroupMembershipJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.GroupMembershipRepository;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.RoleRepository;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.identities.application.dto.AccountBusinessInfo;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountGroupRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for CreateGroupHandler.
 * Tests business logic with mocked repository dependencies.
 */
@ExtendWith(MockitoExtension.class)
class CreateGroupHandlerTest {

    @Mock
    private AccountGroupRepository accountGroupRepository;

    @Mock
    private GroupMembershipRepository groupMembershipRepository;

    @Mock
    private RoleRepository roleRepository;

    @Mock
    private AccountRepository accountRepository;

    @InjectMocks
    private CreateGroupHandler createGroupHandler;

    private UUID businessId;
    private UUID accountId1;
    private UUID accountId2;
    private UUID ownerAccountId;
    private UUID roleId1;
    private UUID roleId2;
    private UUID groupId;
    
    private AccountJpaEntity testAccount1;
    private AccountJpaEntity testAccount2;
    private AccountJpaEntity testOwnerAccount;
    private RoleJpaEntity testRole1;
    private RoleJpaEntity testRole2;
    private AccountBusinessInfo testAccountBusinessInfo1;
    private AccountBusinessInfo testAccountBusinessInfo2;
    private AccountBusinessInfo testOwnerAccountBusinessInfo;
    private AccountGroupJpaEntity testGroup;
    
    private CreateGroupCommand basicCommand;
    private CreateGroupCommand commandWithAccounts;
    private CreateGroupCommand commandWithAccountsAndOwners;
    private CreateGroupCommand commandWithMultipleRoles;

    @BeforeEach
    void setUp() {
        businessId = UUID.randomUUID();
        accountId1 = UUID.randomUUID();
        accountId2 = UUID.randomUUID();
        ownerAccountId = UUID.randomUUID();
        roleId1 = UUID.randomUUID();
        roleId2 = UUID.randomUUID();
        groupId = UUID.randomUUID();
        
        testAccount1 = createTestAccount(accountId1);
        testAccount2 = createTestAccount(accountId2);
        testOwnerAccount = createTestAccount(ownerAccountId);
        testRole1 = createTestRole(roleId1, "Role1");
        testRole2 = createTestRole(roleId2, "Role2");
        testAccountBusinessInfo1 = createTestAccountBusinessInfo(businessId);
        testAccountBusinessInfo2 = createTestAccountBusinessInfo(businessId);
        testOwnerAccountBusinessInfo = createTestAccountBusinessInfo(businessId);
        testGroup = createTestGroup();
        
        basicCommand = createBasicCommand();
        commandWithAccounts = createCommandWithAccounts();
        commandWithAccountsAndOwners = createCommandWithAccountsAndOwners();
        commandWithMultipleRoles = createCommandWithMultipleRoles();
    }

    @Test
    void shouldSuccessfullyCreateGroup_WithRolesOnly() {
        // Given
        when(roleRepository.findById(roleId1)).thenReturn(Optional.of(testRole1)); // For business validation
        when(roleRepository.findAllById(List.of(roleId1))).thenReturn(List.of(testRole1)); // For role retrieval
        when(accountGroupRepository.saveAndFlush(any(AccountGroupJpaEntity.class))).thenReturn(testGroup);

        // When
        AccountGroupJpaEntity result = createGroupHandler.createGroup(basicCommand);

        // Then
        assertNotNull(result);
        assertEquals(testGroup.getId(), result.getId());
        assertEquals(testGroup.getName(), result.getName());
        
        // Role is looked up once for business validation, once via findAllById for group creation
        verify(roleRepository, times(1)).findById(roleId1);
        verify(roleRepository, times(1)).findAllById(List.of(roleId1));
        verify(accountGroupRepository).saveAndFlush(any(AccountGroupJpaEntity.class));
        verifyNoInteractions(accountRepository);
        verifyNoInteractions(groupMembershipRepository);
    }

    @Test
    void shouldSuccessfullyCreateGroup_WithAccountsAndRoles() {
        // Given
        when(accountRepository.findInfoById(accountId1)).thenReturn(Optional.of(testAccountBusinessInfo1));
        when(accountRepository.findInfoById(accountId2)).thenReturn(Optional.of(testAccountBusinessInfo2));
        when(roleRepository.findAllById(List.of(roleId1))).thenReturn(List.of(testRole1));
        when(accountGroupRepository.saveAndFlush(any(AccountGroupJpaEntity.class))).thenReturn(testGroup);
        when(accountRepository.findAllById(List.of(accountId1, accountId2)))
                .thenReturn(List.of(testAccount1, testAccount2));

        // When
        AccountGroupJpaEntity result = createGroupHandler.createGroup(commandWithAccounts);

        // Then
        assertNotNull(result);
        assertEquals(testGroup.getId(), result.getId());
        
        // accountId1 is called twice: once to get business ID, once in validation loop
        verify(accountRepository, times(2)).findInfoById(accountId1);
        // accountId2 is called once in validation loop
        verify(accountRepository, times(1)).findInfoById(accountId2);
        // Role is looked up via findAllById for group creation (business validation uses accounts)
        verify(roleRepository, times(1)).findAllById(List.of(roleId1));
        verify(accountGroupRepository).saveAndFlush(any(AccountGroupJpaEntity.class));
        verify(accountRepository).findAllById(List.of(accountId1, accountId2));
        
        ArgumentCaptor<List<GroupMembershipJpaEntity>> captor = ArgumentCaptor.forClass(List.class);
        verify(groupMembershipRepository).saveAll(captor.capture());
        
        List<GroupMembershipJpaEntity> memberships = captor.getValue();
        assertEquals(2, memberships.size());
        assertTrue(memberships.stream().allMatch(m -> m.getMembershipRole() == GroupMembershipRole.MEMBER));
    }

    @Test
    void shouldSuccessfullyCreateGroup_WithAccountsRolesAndOwners() {
        // Given
        when(accountRepository.findInfoById(accountId1)).thenReturn(Optional.of(testAccountBusinessInfo1));
        when(accountRepository.findInfoById(ownerAccountId)).thenReturn(Optional.of(testOwnerAccountBusinessInfo));
        when(roleRepository.findAllById(List.of(roleId1))).thenReturn(List.of(testRole1));
        when(accountGroupRepository.saveAndFlush(any(AccountGroupJpaEntity.class))).thenReturn(testGroup);
        when(accountRepository.findAllById(List.of(accountId1, ownerAccountId)))
                .thenReturn(List.of(testAccount1, testOwnerAccount));

        // When
        AccountGroupJpaEntity result = createGroupHandler.createGroup(commandWithAccountsAndOwners);

        // Then
        assertNotNull(result);
        assertEquals(testGroup.getId(), result.getId());
        
        ArgumentCaptor<List<GroupMembershipJpaEntity>> captor = ArgumentCaptor.forClass(List.class);
        verify(groupMembershipRepository).saveAll(captor.capture());
        
        List<GroupMembershipJpaEntity> memberships = captor.getValue();
        assertEquals(2, memberships.size());
        
        // Verify owner has OWNER role and regular account has MEMBER role
        boolean hasOwner = memberships.stream()
                .anyMatch(m -> m.getAccount().getId().equals(ownerAccountId) && m.getMembershipRole() == GroupMembershipRole.OWNER);
        boolean hasMember = memberships.stream()
                .anyMatch(m -> m.getAccount().getId().equals(accountId1) && m.getMembershipRole() == GroupMembershipRole.MEMBER);
        
        assertTrue(hasOwner, "Should have an owner membership");
        assertTrue(hasMember, "Should have a member membership");
    }

    @Test
    void shouldSuccessfullyCreateGroup_WithMultipleRoles() {
        // Given
        when(roleRepository.findById(roleId1)).thenReturn(Optional.of(testRole1)); // For business validation
        when(roleRepository.findAllById(List.of(roleId1, roleId2))).thenReturn(List.of(testRole1, testRole2)); // For role retrieval
        when(accountGroupRepository.saveAndFlush(any(AccountGroupJpaEntity.class))).thenReturn(testGroup);

        // When
        AccountGroupJpaEntity result = createGroupHandler.createGroup(commandWithMultipleRoles);

        // Then
        assertNotNull(result);
        // First role is looked up once for business validation
        verify(roleRepository, times(1)).findById(roleId1);
        // All roles are retrieved via findAllById for group creation
        verify(roleRepository, times(1)).findAllById(List.of(roleId1, roleId2));
        
        ArgumentCaptor<AccountGroupJpaEntity> captor = ArgumentCaptor.forClass(AccountGroupJpaEntity.class);
        verify(accountGroupRepository).saveAndFlush(captor.capture());
        
        AccountGroupJpaEntity savedGroup = captor.getValue();
        assertEquals(2, savedGroup.getRoles().size());
    }

    @Test
    void shouldSuccessfullyCreateGroup_WithMetadata() {
        // Given
        Map<String, Object> metadata = Map.of("department", "IT", "priority", "high");
        CreateGroupCommand commandWithMetadata = new CreateGroupCommand(
                "Test Group",
                "Test Description",
                List.of(roleId1),
                null,
                null,
                metadata
        );
        
        when(roleRepository.findById(roleId1)).thenReturn(Optional.of(testRole1)); // For business validation
        when(roleRepository.findAllById(List.of(roleId1))).thenReturn(List.of(testRole1)); // For role retrieval
        when(accountGroupRepository.saveAndFlush(any(AccountGroupJpaEntity.class))).thenReturn(testGroup);

        // When
        AccountGroupJpaEntity result = createGroupHandler.createGroup(commandWithMetadata);

        // Then
        assertNotNull(result);
        ArgumentCaptor<AccountGroupJpaEntity> captor = ArgumentCaptor.forClass(AccountGroupJpaEntity.class);
        verify(accountGroupRepository).saveAndFlush(captor.capture());
        
        AccountGroupJpaEntity savedGroup = captor.getValue();
        assertNotNull(savedGroup.getMetadata());
    }

    @Test
    void shouldThrowRoleNotFoundException_WhenRoleNotFound() {
        // Given
        when(roleRepository.findById(roleId1)).thenReturn(Optional.empty());

        // When & Then
        RoleNotFoundException exception = assertThrows(RoleNotFoundException.class,
                () -> createGroupHandler.createGroup(basicCommand));
        
        assertNotNull(exception);
        assertTrue(exception.getMessage().contains("Role not found with ID: " + roleId1));
        verify(roleRepository).findById(roleId1);
        verifyNoInteractions(accountGroupRepository);
        verifyNoInteractions(accountRepository);
        verifyNoInteractions(groupMembershipRepository);
    }

    @Test
    void shouldThrowRoleNotFoundException_WhenRolesNotFoundInBatch() {
        // Given - Mock business validation for both accounts but return empty list for findAllById
        when(accountRepository.findInfoById(accountId1)).thenReturn(Optional.of(testAccountBusinessInfo1));
        when(accountRepository.findInfoById(accountId2)).thenReturn(Optional.of(testAccountBusinessInfo2));
        when(roleRepository.findAllById(List.of(roleId1))).thenReturn(List.of()); // No roles found

        // When & Then
        RoleNotFoundException exception = assertThrows(RoleNotFoundException.class,
                () -> createGroupHandler.createGroup(commandWithAccounts));
        
        assertNotNull(exception);
        assertTrue(exception.getMessage().contains("Roles not found with IDs"));
        verify(roleRepository).findAllById(List.of(roleId1));
        verifyNoInteractions(accountGroupRepository);
        verifyNoInteractions(groupMembershipRepository);
    }

    @Test
    void shouldThrowAccountNotFoundException_WhenAccountNotFoundInBusinessValidation() {
        // Given
        when(accountRepository.findInfoById(accountId1)).thenReturn(Optional.empty());

        // When & Then
        AccountNotFoundException exception = assertThrows(AccountNotFoundException.class,
                () -> createGroupHandler.createGroup(commandWithAccounts));
        
        assertNotNull(exception);
        assertTrue(exception.getMessage().contains("Account not found with ID: " + accountId1));
        verify(accountRepository).findInfoById(accountId1);
        verifyNoInteractions(roleRepository);
        verifyNoInteractions(accountGroupRepository);
        verifyNoInteractions(groupMembershipRepository);
    }

    @Test
    void shouldThrowAccountNotFoundException_WhenAccountNotFoundInMembershipCreation() {
        // Given - Mock business validation for both accounts
        when(accountRepository.findInfoById(accountId1)).thenReturn(Optional.of(testAccountBusinessInfo1));
        when(accountRepository.findInfoById(accountId2)).thenReturn(Optional.of(testAccountBusinessInfo2));
        when(roleRepository.findAllById(List.of(roleId1))).thenReturn(List.of(testRole1));
        when(accountGroupRepository.saveAndFlush(any(AccountGroupJpaEntity.class))).thenReturn(testGroup);
        // Mock findAllById to return empty list to simulate accounts not found during membership creation
        when(accountRepository.findAllById(List.of(accountId1, accountId2))).thenReturn(Collections.emptyList());

        // When & Then
        AccountNotFoundException exception = assertThrows(AccountNotFoundException.class,
                () -> createGroupHandler.createGroup(commandWithAccounts));
        
        assertNotNull(exception);
        assertTrue(exception.getMessage().contains("Accounts not found with IDs"));
        verify(accountRepository).findAllById(List.of(accountId1, accountId2));
        verify(groupMembershipRepository, never()).saveAll(any());
    }

    @Test
    void shouldThrowMismatchedBusinessException_WhenAccountsFromDifferentBusinesses() {
        // Given
        AccountBusinessInfo differentBusinessInfo = new AccountBusinessInfo(
                UUID.randomUUID(), AccountType.WORKFORCE); // Different business ID
        
        when(accountRepository.findInfoById(accountId1)).thenReturn(Optional.of(testAccountBusinessInfo1));
        when(accountRepository.findInfoById(accountId2)).thenReturn(Optional.of(differentBusinessInfo));

        CreateGroupCommand command = new CreateGroupCommand(
                "Test Group",
                "Test Description",
                List.of(roleId1),
                List.of(accountId1, accountId2),
                null,
                null
        );

        // When & Then
        MismatchedBusinessException exception = assertThrows(MismatchedBusinessException.class,
                () -> createGroupHandler.createGroup(command));
        
        assertNotNull(exception);
        assertTrue(exception.getMessage().contains("All accounts must belong to the same business"));
        verifyNoInteractions(roleRepository);
        verifyNoInteractions(accountGroupRepository);
        verifyNoInteractions(groupMembershipRepository);
    }

    @Test
    void shouldThrowMismatchedBusinessException_WhenRoleFromDifferentBusiness() {
        // Given
        RoleJpaEntity differentBusinessRole = new RoleJpaEntity();
        differentBusinessRole.setId(roleId1);
        differentBusinessRole.setName("Test Role");
        differentBusinessRole.setBusinessId(UUID.randomUUID()); // Different business ID
        
        // Mock all account lookups since commandWithAccounts includes both accountId1 and accountId2
        when(accountRepository.findInfoById(accountId1)).thenReturn(Optional.of(testAccountBusinessInfo1));
        when(accountRepository.findInfoById(accountId2)).thenReturn(Optional.of(testAccountBusinessInfo2));
        when(roleRepository.findAllById(List.of(roleId1))).thenReturn(List.of(differentBusinessRole));

        // When & Then
        MismatchedBusinessException exception = assertThrows(MismatchedBusinessException.class,
                () -> createGroupHandler.createGroup(commandWithAccounts));
        
        assertNotNull(exception);
        assertTrue(exception.getMessage().contains("does not belong to the same business as the accounts"));
        // accountId1 is called twice: once to get business ID, once in validation loop
        verify(accountRepository, times(2)).findInfoById(accountId1);
        // accountId2 is called once in validation loop  
        verify(accountRepository, times(1)).findInfoById(accountId2);
        verify(roleRepository).findAllById(List.of(roleId1));
        verifyNoInteractions(accountGroupRepository);
        verifyNoInteractions(groupMembershipRepository);
    }

    @Test
    void shouldValidateBusinessFromRoles_WhenNoAccountsProvided() {
        // Given
        when(roleRepository.findById(roleId1)).thenReturn(Optional.of(testRole1)); // For business validation
        when(roleRepository.findAllById(List.of(roleId1))).thenReturn(List.of(testRole1)); // For role retrieval
        when(accountGroupRepository.saveAndFlush(any(AccountGroupJpaEntity.class))).thenReturn(testGroup);

        // When
        AccountGroupJpaEntity result = createGroupHandler.createGroup(basicCommand);

        // Then
        assertNotNull(result);
        // Role is looked up once for business validation, once via findAllById for group creation
        verify(roleRepository, times(1)).findById(roleId1);
        verify(roleRepository, times(1)).findAllById(List.of(roleId1));
        verifyNoInteractions(accountRepository);
    }

    @Test
    void shouldThrowRoleNotFoundException_WhenGettingBusinessFromRoleAndRoleNotFound() {
        // Given
        CreateGroupCommand commandWithoutAccounts = new CreateGroupCommand(
                "Test Group",
                "Test Description",
                List.of(roleId1),
                null,
                null,
                null
        );
        
        when(roleRepository.findById(roleId1)).thenReturn(Optional.empty());

        // When & Then
        RoleNotFoundException exception = assertThrows(RoleNotFoundException.class,
                () -> createGroupHandler.createGroup(commandWithoutAccounts));
        
        assertNotNull(exception);
        assertTrue(exception.getMessage().contains("Role not found with ID: " + roleId1));
    }

    @Test
    void shouldThrowIllegalArgumentException_WhenCommandValidationFails() {
        // Given
        CreateGroupCommand invalidCommand = new CreateGroupCommand(
                null, // Invalid name
                "Test Description",
                List.of(roleId1),
                null,
                null,
                null
        );

        // When & Then
        assertThrows(Exception.class, // Could be IllegalArgumentException or InvalidGroupRequestException
                () -> createGroupHandler.createGroup(invalidCommand));
        
        verifyNoInteractions(roleRepository);
        verifyNoInteractions(accountRepository);
        verifyNoInteractions(accountGroupRepository);
        verifyNoInteractions(groupMembershipRepository);
    }

    // Helper methods for creating test data
    private AccountJpaEntity createTestAccount(UUID accountId) {
        AccountJpaEntity account = new AccountJpaEntity();
        account.setId(accountId);
        return account;
    }

    private RoleJpaEntity createTestRole(UUID roleId, String name) {
        RoleJpaEntity role = new RoleJpaEntity();
        role.setId(roleId);
        role.setName(name);
        role.setBusinessId(businessId);
        return role;
    }

    private AccountBusinessInfo createTestAccountBusinessInfo(UUID businessId) {
        return new AccountBusinessInfo(businessId, AccountType.WORKFORCE);
    }

    private AccountGroupJpaEntity createTestGroup() {
        AccountGroupJpaEntity group = new AccountGroupJpaEntity("Test Group");
        group.setId(groupId);
        group.setDescription("Test Description");
        return group;
    }

    private CreateGroupCommand createBasicCommand() {
        return new CreateGroupCommand(
                "Test Group",
                "Test Description",
                List.of(roleId1),
                null,
                null,
                null
        );
    }

    private CreateGroupCommand createCommandWithAccounts() {
        return new CreateGroupCommand(
                "Test Group",
                "Test Description",
                List.of(roleId1),
                List.of(accountId1, accountId2),
                null,
                null
        );
    }

    private CreateGroupCommand createCommandWithAccountsAndOwners() {
        return new CreateGroupCommand(
                "Test Group",
                "Test Description",
                List.of(roleId1),
                List.of(accountId1, ownerAccountId),
                List.of(ownerAccountId),
                null
        );
    }

    private CreateGroupCommand createCommandWithMultipleRoles() {
        return new CreateGroupCommand(
                "Test Group",
                "Test Description",
                List.of(roleId1, roleId2),
                null,
                null,
                null
        );
    }
} 