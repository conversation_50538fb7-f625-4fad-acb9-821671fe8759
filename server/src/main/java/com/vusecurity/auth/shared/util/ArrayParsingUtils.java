package com.vusecurity.auth.shared.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.List;

public class ArrayParsingUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private ArrayParsingUtils() {
    }

    public static String[] parseArrayValue(String value) {
        if (value == null) {
            return new String[0];
        }

        String trimmed = value.trim();

        if (trimmed.startsWith("[") && trimmed.endsWith("]")) {
            try {
                JsonNode jsonNode = objectMapper.readTree(trimmed);
                if (jsonNode.isArray()) {
                    List<String> result = new ArrayList<>();
                    for (JsonNode element : jsonNode) {
                        result.add(element.asText());
                    }
                    return result.toArray(new String[0]);
                }
            } catch (JsonProcessingException e) {
                // Fall back to manual parsing if JSON parsing fails
            }
        }

        if (trimmed.contains(",")) {
            return trimmed.split(",\\s*");
        } else {
            return new String[]{trimmed};
        }
    }
}