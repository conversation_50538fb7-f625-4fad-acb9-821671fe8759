package com.vusecurity.auth.scim.application.command;

import com.vusecurity.auth.scim.application.exception.InvalidScimTokenRequest;

import java.util.*;

public record CreateScimTokenCommand(
        String name,
        String description,
        UUID businessId,
        UUID provierId,
        String token
) {
    public void validate() {
        if (name == null || name.trim().isEmpty()) {
            throw new InvalidScimTokenRequest("name is required");
        }

         if (token == null || token.trim().isEmpty()) {
            throw new InvalidScimTokenRequest("token is required");
        }

        if (businessId == null) {
            throw new InvalidScimTokenRequest("businessId is required");
        }

        if (provierId == null) {
            throw new InvalidScimTokenRequest("provierId is required");
        }
    }
} 