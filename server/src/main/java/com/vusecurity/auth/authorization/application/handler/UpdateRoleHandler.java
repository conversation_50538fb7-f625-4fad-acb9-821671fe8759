package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.command.UpdateRoleCommand;
import com.vusecurity.auth.authorization.application.exception.PermissionNotFoundException;
import com.vusecurity.auth.authorization.application.exception.RoleNameAlreadyExistException;
import com.vusecurity.auth.authorization.application.exception.RoleNotFoundException;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.PermissionJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.PermissionRepository;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.RoleRepository;
import com.vusecurity.auth.shared.util.DatabaseConstraintUtils;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class UpdateRoleHandler {

    private static final Logger logger = LoggerFactory.getLogger(UpdateRoleHandler.class);

    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;

    @Transactional
    public void updateRole(UpdateRoleCommand command) {
        logger.debug("Updating role: {}", command.roleId());

        // Validate command
        command.validate();

        RoleJpaEntity role = roleRepository.findById(command.roleId())
                .orElseThrow(() -> new RoleNotFoundException("Role not found with ID: " + command.roleId()));

        // Update name if provided
        if (command.name() != null && !command.name().trim().isEmpty()) {
            role.setName(command.name().trim());
        }

        // Update description if provided
        if (command.description() != null) {
            role.changeDescription(command.description());
        }

        // Update business ID if provided
        if (command.businessId() != null) {
            role.setBusinessId(command.businessId());
        }

        // Update permissions if provided
        if (command.permissionIds() != null) {
            Set<PermissionJpaEntity> permissions = new HashSet<>();
            for (UUID permissionId : command.permissionIds()) {
                PermissionJpaEntity permission = permissionRepository.findById(permissionId)
                        .orElseThrow(() -> new PermissionNotFoundException("Permission not found with ID: " + permissionId));
                permissions.add(permission);
            }
            role.setPermissions(permissions);
        }

        // Save with constraint handling for unique role name
        DatabaseConstraintUtils.executeWithConstraintHandling(
                () -> roleRepository.saveAndFlush(role),
                "uk_role_name",
                () -> new RoleNameAlreadyExistException("Role with name '" + command.name() + "' already exists")
        );
        logger.debug("Role updated successfully: {}", command.roleId());
    }
}
