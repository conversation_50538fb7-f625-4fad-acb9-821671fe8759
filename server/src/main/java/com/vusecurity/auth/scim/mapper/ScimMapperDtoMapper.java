package com.vusecurity.auth.scim.mapper;

import java.util.ArrayList;
import java.util.List;

import com.vusecurity.auth.contracts.api.v1.dto.scimConfig.ScimFilterMapperResponse;
import com.vusecurity.auth.contracts.api.v1.dto.scimConfig.ScimMapperResponse;
import com.vusecurity.auth.contracts.api.v1.dto.scimConfig.ScimTokenResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse.AuditInfo;
import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimToClaimMappingJpaEntity;
import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimTokenJpaEntity;

public class ScimMapperDtoMapper {

    public static ScimMapperResponse toResponse(ScimToClaimMappingJpaEntity entity){
        return toResponse(List.of(entity), true);
    }

    public static ScimMapperResponse toResponse(List<ScimToClaimMappingJpaEntity> entities){
        return toResponse(entities, true);
    }

    public static ScimMapperResponse toResponse(List<ScimToClaimMappingJpaEntity> entities, boolean includeDetails){
        if (entities == null || entities.isEmpty()){
            return null;
        }

        ScimMapperResponse response = new ScimMapperResponse();
            response.setBusinessId(entities.get(0).getBusiness().getId());

        List<ScimFilterMapperResponse> filterList = new ArrayList<>();
        for (ScimToClaimMappingJpaEntity entity : entities) {
            ScimFilterMapperResponse filter = new ScimFilterMapperResponse()
                .setId(entity.getId())
                .setClaimDefinitionId(entity.getClaimDefinitionReferenced())
                .setClaimSetIdOrigin(entity.getClaimSetOrigin())
                .setClaimSetIdReferenced(entity.getClaimSetReferenced())
                .setFilter(entity.getFilterMapping())
                .setSequenceOrder(entity.getSequenceOrder())
                .setDescription(entity.getDescription());

            if (includeDetails){
                //Set audit info
                filter.setAudit(
                    new AuditInfo()
                        .setCreatedAt(entity.getCreatedAt())
                        .setCreatedBy(entity.getCreatedBy())
                        .setUpdatedAt(entity.getUpdatedAt())
                        .setUpdatedBy(entity.getUpdatedBy())
                    );
            }
            filterList.add(filter);
        }
        response.setFilters(filterList);
        
        return response;
    }

}
