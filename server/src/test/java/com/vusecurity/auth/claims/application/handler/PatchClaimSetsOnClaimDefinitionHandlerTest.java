package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.ClaimSetAssignmentCommand;
import com.vusecurity.auth.claims.application.command.PatchClaimSetsOnClaimDefinitionCommand;
import com.vusecurity.auth.claims.application.exception.ClaimSetNotFoundException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.service.ClaimSetDefinitionMappingService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for PatchClaimSetsOnClaimDefinitionHandler.
 * Tests business logic with mocked repository dependencies.
 */
@ExtendWith(MockitoExtension.class)
class PatchClaimSetsOnClaimDefinitionHandlerTest {

    @Mock
    private ClaimDefinitionRepository claimDefinitionRepository;

    @Mock
    private ClaimSetRepository claimSetRepository;

    @Mock
    private ClaimSetDefinitionMappingService mappingService;

    @InjectMocks
    private PatchClaimSetsOnClaimDefinitionHandler handler;

    private UUID claimDefinitionId;
    private UUID claimSetId1;
    private UUID claimSetId2;
    private ClaimDefinitionJpaEntity testClaimDefinition;
    private ClaimSetJpaEntity testClaimSet1;
    private ClaimSetJpaEntity testClaimSet2;
    private PatchClaimSetsOnClaimDefinitionCommand validCommand;
    private PatchClaimSetsOnClaimDefinitionCommand emptyCommand;

    @BeforeEach
    void setUp() {
        claimDefinitionId = UUID.randomUUID();
        claimSetId1 = UUID.randomUUID();
        claimSetId2 = UUID.randomUUID();
        
        testClaimDefinition = createTestClaimDefinition();
        testClaimSet1 = createTestClaimSet(claimSetId1);
        testClaimSet2 = createTestClaimSet(claimSetId2);
        
        validCommand = createValidCommand();
        emptyCommand = createEmptyCommand();
    }

    @Test
    void shouldSuccessfullyPatchClaimSets_WhenValidCommandProvided() {
        // Given
        when(claimDefinitionRepository.getReferenceById(claimDefinitionId)).thenReturn(testClaimDefinition);
        when(claimSetRepository.countByIdIn(any(Set.class))).thenReturn(2L);

        // When
        assertDoesNotThrow(() -> handler.patchClaimSetsOnClaimDefinition(validCommand));

        // Then
        verify(claimDefinitionRepository).getReferenceById(claimDefinitionId);
        verify(claimSetRepository).countByIdIn(Set.of(claimSetId1, claimSetId2));
        verify(mappingService).batchAddClaimSetsToClaimDefinition(
                eq(testClaimDefinition),
                eq(Set.of(claimSetId1, claimSetId2)),
                eq(deduplicate(validCommand.claimSetsAssignment()))
        );
        verifyNoMoreInteractions(claimDefinitionRepository, claimSetRepository, mappingService);
    }

    @Test
    void shouldSuccessfullyHandleEmptyClaimSetsAssignment() {
        // Given
        when(claimDefinitionRepository.getReferenceById(claimDefinitionId)).thenReturn(testClaimDefinition);

        // When
        assertDoesNotThrow(() -> handler.patchClaimSetsOnClaimDefinition(emptyCommand));

        // Then
        verify(claimDefinitionRepository).getReferenceById(claimDefinitionId);
        verifyNoInteractions(claimSetRepository, mappingService);
    }

    @Test
    void shouldSuccessfullyPatchSingleClaimSet() {
        // Given
        PatchClaimSetsOnClaimDefinitionCommand singleCommand = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                List.of(new ClaimSetAssignmentCommand(claimSetId1, true))
        );

        when(claimDefinitionRepository.getReferenceById(claimDefinitionId)).thenReturn(testClaimDefinition);
        when(claimSetRepository.countByIdIn(any(Set.class))).thenReturn(1L);

        // When
        assertDoesNotThrow(() -> handler.patchClaimSetsOnClaimDefinition(singleCommand));

        // Then
        verify(claimDefinitionRepository).getReferenceById(claimDefinitionId);
        verify(claimSetRepository).countByIdIn(Set.of(claimSetId1));
        verify(mappingService).batchAddClaimSetsToClaimDefinition(
                eq(testClaimDefinition),
                eq(Set.of(claimSetId1)),
                eq(deduplicate(singleCommand.claimSetsAssignment()))
        );
    }

    @Test
    void shouldSuccessfullyPatchMultipleClaimSets_WithMixedEnforceUniqueness() {
        // Given
        PatchClaimSetsOnClaimDefinitionCommand mixedCommand = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                List.of(
                        new ClaimSetAssignmentCommand(claimSetId1, true),
                        new ClaimSetAssignmentCommand(claimSetId2, false)
                )
        );

        when(claimDefinitionRepository.getReferenceById(claimDefinitionId)).thenReturn(testClaimDefinition);
        when(claimSetRepository.countByIdIn(any(Set.class))).thenReturn(2L);

        // When
        assertDoesNotThrow(() -> handler.patchClaimSetsOnClaimDefinition(mixedCommand));

        // Then
        verify(claimDefinitionRepository).getReferenceById(claimDefinitionId);
        verify(claimSetRepository).countByIdIn(Set.of(claimSetId1, claimSetId2));
        verify(mappingService).batchAddClaimSetsToClaimDefinition(
                eq(testClaimDefinition),
                eq(Set.of(claimSetId1, claimSetId2)),
                eq(deduplicate(mixedCommand.claimSetsAssignment()))
        );
    }

    @Test
    void shouldThrowClaimSetNotFoundException_WhenOneClaimSetNotFound() {
        // Given
        when(claimDefinitionRepository.getReferenceById(claimDefinitionId)).thenReturn(testClaimDefinition);
        when(claimSetRepository.countByIdIn(any(Set.class))).thenReturn(1L); // Only 1 found, but 2 requested

        // When & Then
        ClaimSetNotFoundException exception = assertThrows(
                ClaimSetNotFoundException.class,
                () -> handler.patchClaimSetsOnClaimDefinition(validCommand)
        );

        assertEquals("One or more claim sets not found", exception.getMessage());
        verify(claimDefinitionRepository).getReferenceById(claimDefinitionId);
        verify(claimSetRepository).countByIdIn(Set.of(claimSetId1, claimSetId2));
        verifyNoInteractions(mappingService);
    }

    @Test
    void shouldThrowClaimSetNotFoundException_WhenAllClaimSetsNotFound() {
        // Given
        when(claimDefinitionRepository.getReferenceById(claimDefinitionId)).thenReturn(testClaimDefinition);
        when(claimSetRepository.countByIdIn(any(Set.class))).thenReturn(0L); // None found

        // When & Then
        ClaimSetNotFoundException exception = assertThrows(
                ClaimSetNotFoundException.class,
                () -> handler.patchClaimSetsOnClaimDefinition(validCommand)
        );

        assertEquals("One or more claim sets not found", exception.getMessage());
        verify(claimDefinitionRepository).getReferenceById(claimDefinitionId);
        verify(claimSetRepository).countByIdIn(Set.of(claimSetId1, claimSetId2));
        verifyNoInteractions(mappingService);
    }

    @Test
    void shouldHandleLargeNumberOfClaimSets() {
        // Given
        List<ClaimSetAssignmentCommand> largeAssignment = new ArrayList<>();
        Set<UUID> largeClaimSetIds = new HashSet<>();
        
        for (int i = 0; i < 50; i++) {
            UUID claimSetId = UUID.randomUUID();
            largeClaimSetIds.add(claimSetId);
            largeAssignment.add(new ClaimSetAssignmentCommand(claimSetId, i % 2 == 0));
        }

        PatchClaimSetsOnClaimDefinitionCommand largeCommand = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                largeAssignment
        );

        when(claimDefinitionRepository.getReferenceById(claimDefinitionId)).thenReturn(testClaimDefinition);
        when(claimSetRepository.countByIdIn(any(Set.class))).thenReturn(50L);

        // When
        assertDoesNotThrow(() -> handler.patchClaimSetsOnClaimDefinition(largeCommand));

        // Then
        verify(claimDefinitionRepository).getReferenceById(claimDefinitionId);
        verify(claimSetRepository).countByIdIn(largeClaimSetIds);
        verify(mappingService).batchAddClaimSetsToClaimDefinition(
                eq(testClaimDefinition),
                eq(largeClaimSetIds),
                eq(deduplicate(largeAssignment))
        );
    }

    @Test
    void shouldHandleDuplicateClaimSetIds() {
        // Given
        UUID duplicateId = UUID.randomUUID();
        PatchClaimSetsOnClaimDefinitionCommand duplicateCommand = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                List.of(
                        new ClaimSetAssignmentCommand(duplicateId, true),
                        new ClaimSetAssignmentCommand(duplicateId, false) // Same ID, different enforceUniqueness
                )
        );

        when(claimDefinitionRepository.getReferenceById(claimDefinitionId)).thenReturn(testClaimDefinition);
        when(claimSetRepository.countByIdIn(any(Set.class))).thenReturn(1L); // Only 1 unique ID

        // When
        assertDoesNotThrow(() -> handler.patchClaimSetsOnClaimDefinition(duplicateCommand));

        // Then
        verify(claimDefinitionRepository).getReferenceById(claimDefinitionId);
        verify(claimSetRepository).countByIdIn(Set.of(duplicateId));
        verify(mappingService).batchAddClaimSetsToClaimDefinition(
                eq(testClaimDefinition),
                eq(Set.of(duplicateId)),
                eq(deduplicate(duplicateCommand.claimSetsAssignment()))
        );
    }

    @Test
    void shouldValidateCommandBeforeProcessing() {
        // Given
        PatchClaimSetsOnClaimDefinitionCommand invalidCommand = new PatchClaimSetsOnClaimDefinitionCommand(
                null, // Invalid: null claimDefinitionId
                List.of(new ClaimSetAssignmentCommand(claimSetId1, true))
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> handler.patchClaimSetsOnClaimDefinition(invalidCommand)
        );

        assertEquals("claimDefinitionId is required", exception.getMessage());
        verifyNoInteractions(claimDefinitionRepository, claimSetRepository, mappingService);
    }

    @Test
    void shouldHandleAllEnforceUniquenessTrue() {
        // Given
        PatchClaimSetsOnClaimDefinitionCommand allTrueCommand = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                List.of(
                        new ClaimSetAssignmentCommand(claimSetId1, true),
                        new ClaimSetAssignmentCommand(claimSetId2, true)
                )
        );

        when(claimDefinitionRepository.getReferenceById(claimDefinitionId)).thenReturn(testClaimDefinition);
        when(claimSetRepository.countByIdIn(any(Set.class))).thenReturn(2L);

        // When
        assertDoesNotThrow(() -> handler.patchClaimSetsOnClaimDefinition(allTrueCommand));

        // Then
        verify(claimDefinitionRepository).getReferenceById(claimDefinitionId);
        verify(claimSetRepository).countByIdIn(Set.of(claimSetId1, claimSetId2));
        verify(mappingService).batchAddClaimSetsToClaimDefinition(
                eq(testClaimDefinition),
                eq(Set.of(claimSetId1, claimSetId2)),
                eq(deduplicate(allTrueCommand.claimSetsAssignment()))
        );
    }

    @Test
    void shouldHandleAllEnforceUniquenessFalse() {
        // Given
        PatchClaimSetsOnClaimDefinitionCommand allFalseCommand = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                List.of(
                        new ClaimSetAssignmentCommand(claimSetId1, false),
                        new ClaimSetAssignmentCommand(claimSetId2, false)
                )
        );

        when(claimDefinitionRepository.getReferenceById(claimDefinitionId)).thenReturn(testClaimDefinition);
        when(claimSetRepository.countByIdIn(any(Set.class))).thenReturn(2L);

        // When
        assertDoesNotThrow(() -> handler.patchClaimSetsOnClaimDefinition(allFalseCommand));

        // Then
        verify(claimDefinitionRepository).getReferenceById(claimDefinitionId);
        verify(claimSetRepository).countByIdIn(Set.of(claimSetId1, claimSetId2));
        verify(mappingService).batchAddClaimSetsToClaimDefinition(
                eq(testClaimDefinition),
                eq(Set.of(claimSetId1, claimSetId2)),
                eq(deduplicate(allFalseCommand.claimSetsAssignment()))
        );
    }

    @Test
    void shouldHandleSpecialUUIDs() {
        // Given
        UUID specialClaimDefinitionId = UUID.fromString("00000000-0000-0000-0000-000000000000");
        UUID specialClaimSetId = UUID.fromString("ffffffff-ffff-ffff-ffff-ffffffffffff");
        
        PatchClaimSetsOnClaimDefinitionCommand specialCommand = new PatchClaimSetsOnClaimDefinitionCommand(
                specialClaimDefinitionId,
                List.of(new ClaimSetAssignmentCommand(specialClaimSetId, true))
        );

        when(claimDefinitionRepository.getReferenceById(specialClaimDefinitionId)).thenReturn(testClaimDefinition);
        when(claimSetRepository.countByIdIn(any(Set.class))).thenReturn(1L);

        // When
        assertDoesNotThrow(() -> handler.patchClaimSetsOnClaimDefinition(specialCommand));

        // Then
        verify(claimDefinitionRepository).getReferenceById(specialClaimDefinitionId);
        verify(claimSetRepository).countByIdIn(Set.of(specialClaimSetId));
        verify(mappingService).batchAddClaimSetsToClaimDefinition(
                eq(testClaimDefinition),
                eq(Set.of(specialClaimSetId)),
                eq(deduplicate(specialCommand.claimSetsAssignment()))
        );
    }

    // Helper methods
    private static List<ClaimSetAssignmentCommand> deduplicate(List<ClaimSetAssignmentCommand> assignments) {
        Map<UUID, ClaimSetAssignmentCommand> deduped = new HashMap<>();
        for (ClaimSetAssignmentCommand assignment : assignments) {
            deduped.put(assignment.claimSetId(), assignment);
        }
        return new ArrayList<>(deduped.values());
    }

    private ClaimDefinitionJpaEntity createTestClaimDefinition() {
        ClaimDefinitionJpaEntity entity = new ClaimDefinitionJpaEntity();
        entity.setId(claimDefinitionId);
        entity.setCode("test-claim");
        entity.setName("Test Claim");
        entity.setDescription("Test claim definition");
        return entity;
    }

    private ClaimSetJpaEntity createTestClaimSet(UUID id) {
        ClaimSetJpaEntity entity = new ClaimSetJpaEntity();
        entity.setId(id);
        entity.setName("Test Claim Set");
        entity.setDescription("Test claim set");
        return entity;
    }

    private PatchClaimSetsOnClaimDefinitionCommand createValidCommand() {
        return new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                List.of(
                        new ClaimSetAssignmentCommand(claimSetId1, true),
                        new ClaimSetAssignmentCommand(claimSetId2, false)
                )
        );
    }

    private PatchClaimSetsOnClaimDefinitionCommand createEmptyCommand() {
        return new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                new ArrayList<>()
        );
    }
} 