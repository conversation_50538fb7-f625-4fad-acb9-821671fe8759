package com.vusecurity.auth.identities.application.exception;

import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class AccountCreationExceptionTest {

    @Test
    void constructor_formats_message_with_ids() {
        UUID businessId = UUID.randomUUID();
        UUID identityId = UUID.randomUUID();
        RuntimeException cause = new RuntimeException("root");
        
        AccountCreationException ex = new AccountCreationException(businessId, identityId, "failed", cause);
        
        assertTrue(ex.getMessage().contains("failed"));
        assertTrue(ex.getMessage().contains(businessId.toString()));
        assertTrue(ex.getMessage().contains(identityId.toString()));
        assertEquals(businessId, ex.getBusinessId());
        assertEquals(identityId, ex.getIdentityId());
        assertSame(cause, ex.getCause());
    }
}
