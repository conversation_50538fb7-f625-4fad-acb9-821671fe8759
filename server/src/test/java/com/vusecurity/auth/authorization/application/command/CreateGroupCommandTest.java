package com.vusecurity.auth.authorization.application.command;

import com.vusecurity.auth.authorization.application.exception.InvalidGroupRequestException;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for CreateGroupCommand validation logic.
 */
class CreateGroupCommandTest {

    @Test
    void shouldValidateSuccessfully_WhenAllFieldsAreValid() {
        // Given
        UUID roleId = UUID.randomUUID();
        UUID accountId = UUID.randomUUID();
        UUID ownerId = UUID.randomUUID();
        CreateGroupCommand command = new CreateGroupCommand(
                "Valid Group Name",
                "A description.",
                List.of(roleId),
                List.of(accountId, ownerId),
                List.of(ownerId),
                Map.of("key", "value")
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithOnlyRoleIds() {
        // Given
        CreateGroupCommand command = new CreateGroupCommand(
                "Role-Based Group",
                "Desc",
                List.of(UUID.randomUUID()),
                null,
                null,
                null
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithOnlyAccountIds() {
        // Given
        CreateGroupCommand command = new CreateGroupCommand(
                "Account-Based Group",
                "Desc",
                null,
                List.of(UUID.randomUUID()),
                null,
                null
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldThrowException_WhenNameIsNull() {
        // Given
        CreateGroupCommand command = new CreateGroupCommand(null, "Desc", List.of(UUID.randomUUID()), null, null, null);

        // When & Then
        var exception = assertThrows(InvalidGroupRequestException.class, command::validate);
        assertEquals("name is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenNameIsEmpty() {
        // Given
        CreateGroupCommand command = new CreateGroupCommand("", "Desc", List.of(UUID.randomUUID()), null, null, null);

        // When & Then
        var exception = assertThrows(InvalidGroupRequestException.class, command::validate);
        assertEquals("name is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenOwnerIdIsNotInAccountIds() {
        // Given
        CreateGroupCommand command = new CreateGroupCommand(
                "Group Name",
                "Desc",
                null,
                List.of(UUID.randomUUID()), // Account IDs
                List.of(UUID.randomUUID()), // A different Owner ID
                null
        );

        // When & Then
        var exception = assertThrows(InvalidGroupRequestException.class, command::validate);
        assertEquals("All ownerAccountIds must also be present in accountIds", exception.getMessage());
    }

    @Test
    @Disabled("TODO: Check the validity of the test")
    void shouldHandleNullListsGracefully() {
        // Given
        CreateGroupCommand command = new CreateGroupCommand("Group Name", "Desc", null, null, null, null);

        // When & Then
        // Compact constructor should handle nulls, but validate will fail as both lists are empty.
        var exception = assertThrows(InvalidGroupRequestException.class, command::validate);
        assertEquals("Either roleIds or accountIds must be provided", exception.getMessage());

        // Verify lists are initialized to empty lists
        assertNotNull(command.roleIds());
        assertNotNull(command.accountIds());
        assertNotNull(command.ownerAccountIds());
    }
} 