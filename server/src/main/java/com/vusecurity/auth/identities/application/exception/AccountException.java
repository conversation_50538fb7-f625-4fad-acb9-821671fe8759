package com.vusecurity.auth.identities.application.exception;

/**
 * Base exception class for all account-related exceptions.
 */
public class AccountException extends RuntimeException {

    private static final String DEFAULT_MESSAGE = "An unhandled exception occurred with the account";

    /**
     * Constructs a new AccountException with the specified message.
     *
     * @param message the detail message (nullable)
     */
    public AccountException(String message) {
        super(message != null ? message : DEFAULT_MESSAGE);
    }

    /**
     * Constructs a new AccountException with the specified message and cause.
     *
     * @param message the detail message (nullable)
     * @param cause   the cause of the exception (nullable)
     */
    public AccountException(String message, Throwable cause) {
        super(message != null ? message : DEFAULT_MESSAGE, cause);
    }

    /**
     * Constructs a new AccountException with the specified cause and default message.
     *
     * @param cause the cause of the exception (nullable)
     */
    public AccountException(Throwable cause) {
        super(DEFAULT_MESSAGE, cause);
    }

    /**
     * Constructs a new AccountException with the default message.
     */
    public AccountException() {
        super(DEFAULT_MESSAGE);
    }
}