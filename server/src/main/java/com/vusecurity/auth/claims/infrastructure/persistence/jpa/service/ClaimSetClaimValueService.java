package com.vusecurity.auth.claims.infrastructure.persistence.jpa.service;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.*;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetClaimValuesRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueReferencesRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Transactional
public class ClaimSetClaimValueService {
    
    private final ClaimSetClaimValuesRepository claimSetClaimValuesRepo;
    private final ClaimValueReferencesRepository claimValueReferencesRepo;
    
    public ClaimSetClaimValueService(ClaimSetClaimValuesRepository claimSetClaimValuesRepo,
                                   ClaimValueReferencesRepository claimValueReferencesRepo) {
        this.claimSetClaimValuesRepo = claimSetClaimValuesRepo;
        this.claimValueReferencesRepo = claimValueReferencesRepo;
    }
    
    /**
     * Asocia un ClaimValue a un ClaimSet
     */
    public ClaimSetClaimValuesJpaEntity associateClaimValueToClaimSet(ClaimSetJpaEntity claimSet,
                                                                      ClaimValueJpaEntity claimValue) {
        // Verificar que no existe ya la asociación
        if (claimSetClaimValuesRepo.existsByClaimSetIdAndClaimValueId(claimSet.getId(), claimValue.getId())) {
            throw new IllegalArgumentException("ClaimValue already associated with ClaimSet");
        }
        
        ClaimSetClaimValuesJpaEntity association = new ClaimSetClaimValuesJpaEntity(claimSet, claimValue);
        return claimSetClaimValuesRepo.save(association);
    }
    
    /**
     * Crea una referencia de ClaimValue a ClaimSet (para tipo CLAIMSET)
     */
    public ClaimValueReferencesJpaEntity createClaimSetReference(ClaimValueJpaEntity sourceClaimValue,
                                                                 ClaimSetJpaEntity referencedClaimSet,
                                                                 ClaimDefinitionJpaEntity referencedClaimDefinition,
                                                                 Integer sequenceOrder) {
        ClaimValueReferencesJpaEntity reference = new ClaimValueReferencesJpaEntity(
            sourceClaimValue, referencedClaimSet, referencedClaimDefinition, sequenceOrder);
        
        return claimValueReferencesRepo.save(reference);
    }
    
    /**
     * Obtiene todos los ClaimValues asociados a un ClaimSet
     */
    @Transactional(readOnly = true)
    public List<ClaimValueJpaEntity> getClaimValuesForClaimSet(UUID claimSetId) {
        return claimSetClaimValuesRepo.findByClaimSetId(claimSetId)
            .stream()
            .map(ClaimSetClaimValuesJpaEntity::getClaimValue)
            .collect(Collectors.toList());
    }
    
    /**
     * Obtiene las referencias de un ClaimValue (para navegación jerárquica)
     */
    @Transactional(readOnly = true)
    public List<ClaimValueReferencesJpaEntity> getReferencesForClaimValue(UUID claimValueId) {
        return claimValueReferencesRepo.findBySourceClaimValueId(claimValueId);
    }
    
    /**
     * Elimina todas las asociaciones de un ClaimSet
     */
    public void removeAllAssociationsForClaimSet(UUID claimSetId) {
        claimSetClaimValuesRepo.deleteByClaimSetId(claimSetId);
    }
    
    /**
     * Elimina todas las asociaciones de un ClaimValue
     */
    public void removeAllAssociationsForClaimValue(UUID claimValueId) {
        claimSetClaimValuesRepo.deleteByClaimValueId(claimValueId);
        claimValueReferencesRepo.deleteBySourceClaimValueId(claimValueId);
    }
}