package com.vusecurity.auth.contracts.api.v1.dto.identities;

import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Data
@Schema(description = "Request to update an existing account")
public class UpdateAccountRequest {

    @Schema(description = "Additional metadata for the account as key-value pairs",
            example = "{\"department\": \"Engineering\", \"role\": \"Senior Developer\"}")
    private Map<String, Object> metadata;

    @Schema(description = "New lifecycle state of the account",
            example = "ACTIVE")
    private AccountLifecycleState lifecycleState;
}
