package com.vusecurity.auth.shared.infrastructure.migration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import com.vusecurity.auth.shared.infrastructure.migration.initial.OAuth2RegisteredClientSetupService;
import com.vusecurity.multitenant.jpa.hibernate.events.DataSourceCreatedEvent;
import com.zaxxer.hikari.HikariDataSource;

@Component
public class MultitenantDataSourceGeneratedListener implements ApplicationListener<DataSourceCreatedEvent> {
    private static final Logger logger = LoggerFactory.getLogger(MultitenantDataSourceGeneratedListener.class);

    private final OAuth2RegisteredClientSetupService oAuth2RegisteredClientSetupService;
    private final FlywayMigrationService flywayMigrationService;

    @Value("${app.dataseed.enabled:false}")
    private boolean dataSeedEnabled;

    public MultitenantDataSourceGeneratedListener(OAuth2RegisteredClientSetupService oAuth2RegisteredClientSetupService, FlywayMigrationService flywayMigrationService) {
        this.oAuth2RegisteredClientSetupService = oAuth2RegisteredClientSetupService;
        this.flywayMigrationService = flywayMigrationService;
    }

    @Override
    public void onApplicationEvent(@NonNull DataSourceCreatedEvent event) {
        logger.info("Received DataSourceCreatedEvent for tenant {}. Running Flyway…", event.getDataSource().toString());
        String poolName = ((HikariDataSource) event.getDataSource()).getPoolName();
        logger.info("Data initialization request received for datasource ({})", poolName);

        // 1) Always migrate first so schema exists
        try {
            flywayMigrationService.migrate(event.getDataSource(), poolName);
        } catch (Exception e) {
            logger.error("Flyway migration failed for datasource ({}): {}", poolName, e.getMessage(), e);
            throw e; // Stop if migrations fail
        }

        // 2) Then OAuth2 clients (depend on schema)
        try {
            oAuth2RegisteredClientSetupService.start();
            logger.info("OAuth2 registered client setup completed for datasource ({})", poolName);
        } catch (Exception e) {
            logger.error("OAuth2 client setup failed for datasource ({}): {}", poolName, e.getMessage(), e);
            throw e; // Critical failure - re-throw to prevent startup
        }
    }
}
