package com.vusecurity.auth.identities;

import com.vusecurity.auth.contracts.api.v1.dto.identities.AccountResponse;
import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.auth.identities.mapper.AccountDtoMapper;
import com.vusecurity.business.domain.Business;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class AccountDtoMapperTest {

    private AccountDtoMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = new AccountDtoMapper();
    }

    @Test
    void shouldMapBusinessNameAndFields() {
        // Given
        UUID accountId = UUID.randomUUID();
        UUID businessId = UUID.randomUUID();
        String businessName = "Test Business";
        UUID identityId = UUID.randomUUID();
        UUID identityProviderId = UUID.randomUUID();
        Business business = new Business();
        business.setId(businessId);
        business.setName(businessName);
        IdentityJpaEntity identity = new IdentityJpaEntity();
        identity.setId(identityId);
        IdentityProviderJpaEntity identityProvider = new IdentityProviderJpaEntity();
        identityProvider.setId(identityProviderId);
        AccountJpaEntity entity = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        entity.setId(accountId);
        entity.setLifecycleState(AccountLifecycleState.ACTIVE);
        entity.setMetadata(Map.of("role", "Developer"));
        entity.setMergePrimary(UUID.randomUUID());
        entity.setMergeSecondary(UUID.randomUUID());

        // When
        AccountResponse response = mapper.toResponse(entity);

        // Then
        assertNotNull(response);
        assertEquals(accountId, response.getId());
        assertEquals(businessId, response.getBusinessId());
        assertEquals(businessName, response.getBusinessName());
        assertEquals(identityId, response.getIdentityId());
        assertEquals(identityProviderId, response.getIdentityProviderId());
        assertEquals(AccountType.WORKFORCE, response.getAccountType());
        assertEquals(AccountLifecycleState.ACTIVE, response.getLifecycleState());
        assertEquals("Developer", response.getMetadata().get("role"));
        assertNotNull(response.getMergePrimary());
        assertNotNull(response.getMergeSecondary());
    }

    @Test
    void shouldMapAuditInfo() {
        // Given
        Business business = new Business();
        business.setId(UUID.randomUUID());
        business.setName("Test Business");
        IdentityJpaEntity identity = new IdentityJpaEntity();
        identity.setId(UUID.randomUUID());
        IdentityProviderJpaEntity identityProvider = new IdentityProviderJpaEntity();
        identityProvider.setId(UUID.randomUUID());
        AccountJpaEntity entity = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        entity.setId(UUID.randomUUID());
        entity.setCreatedAt(Instant.parse("2024-01-01T10:00:00Z"));
        entity.setUpdatedAt(Instant.parse("2024-01-02T12:00:00Z"));
        entity.setCreatedBy("<EMAIL>");
        entity.setUpdatedBy("<EMAIL>");

        // When
        AccountResponse response = mapper.toResponse(entity);

        // Then
        assertNotNull(response.getAudit());
        assertEquals(Instant.parse("2024-01-01T10:00:00Z"), response.getAudit().getCreatedAt());
        assertEquals(Instant.parse("2024-01-02T12:00:00Z"), response.getAudit().getUpdatedAt());
        assertEquals("<EMAIL>", response.getAudit().getCreatedBy());
        assertEquals("<EMAIL>", response.getAudit().getUpdatedBy());
    }

    @Test
    void shouldReturnNullForNullInput() {
        // When
        AccountResponse response = mapper.toResponse(null);
        // Then
        assertNull(response);
    }

    @Test
    void shouldHandleBusinessWithNoName() {
        // Given
        Business business = new Business();
        business.setId(UUID.randomUUID());
        business.setName(null);
        IdentityJpaEntity identity = new IdentityJpaEntity();
        identity.setId(UUID.randomUUID());
        IdentityProviderJpaEntity identityProvider = new IdentityProviderJpaEntity();
        identityProvider.setId(UUID.randomUUID());
        AccountJpaEntity entity = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        entity.setId(UUID.randomUUID());
        entity.setLifecycleState(AccountLifecycleState.ACTIVE);

        // When
        AccountResponse response = mapper.toResponse(entity);

        // Then
        assertNotNull(response);
        assertNull(response.getBusinessName());
    }
} 