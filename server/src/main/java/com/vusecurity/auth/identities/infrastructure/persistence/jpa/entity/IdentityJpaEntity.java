package com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.contracts.enums.IdentityLifecycleState;
import com.vusecurity.auth.contracts.enums.IdentityType;
import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import com.vusecurity.core.commons.utils.converters.MapToJsonConverter;

import jakarta.persistence.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;


@Entity
@Table(name = "identities",
        indexes = {
                @Index(name = "uk_identity_id",          columnList = "id",             unique = true),
                @Index(name = "idx_identity_name",       columnList = "name"),
                @Index(name = "idx_identity_type",       columnList = "identity_type")
        })
@SuppressWarnings("LombokGetterMayBeUsed, LombokSetterMayBeUsed")
public class IdentityJpaEntity extends AbstractEntity {
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private IdentityType identityType = IdentityType.NONE;

    private String name;
    
    @Convert(converter = MapToJsonConverter.class)
    private Map<String, Object> metadata = new HashMap<>();
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private IdentityLifecycleState lifecycleState = IdentityLifecycleState.NONE;
    
    private UUID mergedTo;
    
    @OneToMany(mappedBy = "identity", fetch = FetchType.LAZY)
    private List<AccountJpaEntity> accounts = new ArrayList<>();

    // Constructors
    public IdentityJpaEntity() {
    }

    public IdentityJpaEntity(IdentityType identityType, String name) {
        if (identityType == null) {
            throw new IllegalArgumentException("identityType cannot be null");
        }
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("name cannot be null or empty");
        }
        this.identityType = identityType;
        this.name = name.trim();
        this.lifecycleState = IdentityLifecycleState.NONE;
        this.metadata = new HashMap<>();
        this.accounts = new ArrayList<>();
    }

    public IdentityJpaEntity(UUID id, IdentityType identityType, String name) {
        this(identityType, name);
        this.setId(id);
    }

    // Getters
    public IdentityType getIdentityType() {
        return identityType;
    }

    public String getName() {
        return name;
    }

    public IdentityLifecycleState getLifecycleState() {
        return lifecycleState;
    }

    public UUID getMergedTo() {
        return mergedTo;
    }

    // Setters
    public void setIdentityType(IdentityType identityType) {
        this.identityType = identityType;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setLifecycleState(IdentityLifecycleState lifecycleState) {
        this.lifecycleState = lifecycleState;
    }

    public void setMergedTo(UUID mergedTo) {
        this.mergedTo = mergedTo;
    }

    // Business behavior methods
    public void activate() {
        if (this.lifecycleState == IdentityLifecycleState.ACTIVE) {
            throw new IllegalStateException("Identity is already active");
        }
        this.lifecycleState = IdentityLifecycleState.ACTIVE;
    }
    
    public void suspend() {
        if (this.lifecycleState != IdentityLifecycleState.ACTIVE) {
            throw new IllegalStateException("Only active identities can be suspended");
        }
        this.lifecycleState = IdentityLifecycleState.SUSPENDED;
    }
    
    public void deactivate() {
        if (this.lifecycleState == IdentityLifecycleState.INACTIVE) {
            throw new IllegalStateException("Identity is already inactive");
        }
        this.lifecycleState = IdentityLifecycleState.INACTIVE;
    }
    
    public boolean isActive() {
        return this.lifecycleState == IdentityLifecycleState.ACTIVE;
    }
    
    public boolean isSuspended() {
        return this.lifecycleState == IdentityLifecycleState.SUSPENDED;
    }
    
    public boolean isInactive() {
        return this.lifecycleState == IdentityLifecycleState.INACTIVE;
    }
    
    public boolean isMerged() {
        return this.mergedTo != null;
    }
    
    public void mergeInto(UUID targetIdentityId) {
        if (targetIdentityId == null) {
            throw new IllegalArgumentException("Target identity ID cannot be null");
        }
        if (targetIdentityId.equals(this.getId())) {
            throw new IllegalArgumentException("Cannot merge identity into itself");
        }
        this.mergedTo = targetIdentityId;
        this.lifecycleState = IdentityLifecycleState.INACTIVE;
    }
    
    public void addMetadata(String key, Object value) {
        if (key == null || key.trim().isEmpty()) {
            throw new IllegalArgumentException("Metadata key cannot be null or empty");
        }
        if (this.metadata == null) {
            this.metadata = new HashMap<>();
        }
        this.metadata.put(key, value);
    }
    
    public void removeMetadata(String key) {
        if (this.metadata != null) {
            this.metadata.remove(key);
        }
    }
    
    public void clearMetadata() {
        if (this.metadata != null) {
            this.metadata.clear();
        }
    }
    
    public boolean hasMetadata(String key) {
        return this.metadata != null && this.metadata.containsKey(key);
    }
    
    public Object getMetadataValue(String key) {
        return this.metadata != null ? this.metadata.get(key) : null;
    }
    
    // Validation methods
    public void validateForCreation() {
        if (this.identityType == null) {
            throw new IllegalArgumentException("Identity type cannot be null");
        }
        if (this.name == null || this.name.trim().isEmpty()) {
            throw new IllegalArgumentException("Identity name cannot be null or empty");
        }
    }
    
    public void validateForUpdate() {
        validateForCreation();
        if (this.getId() == null) {
            throw new IllegalArgumentException("Identity ID cannot be null for update");
        }
    }

    // Custom getters for defensive copies
    public Map<String, Object> getMetadata() {
        return this.metadata != null ? new HashMap<>(this.metadata) : new HashMap<>();
    }

    public List<AccountJpaEntity> getAccounts() {
        return this.accounts != null ? new ArrayList<>(this.accounts) : new ArrayList<>();
    }

    // Custom setters for defensive copies
    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata != null ? new HashMap<>(metadata) : new HashMap<>();
    }

    public void setAccounts(List<AccountJpaEntity> accounts) {
        this.accounts = accounts != null ? new ArrayList<>(accounts) : new ArrayList<>();
    }

    // equals() and hashCode() using id (fallback since no unique business key)
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        IdentityJpaEntity that = (IdentityJpaEntity) o;
        return Objects.equals(getId(), that.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getId());
    }

    @Override
    public String toString() {
        return "IdentityJpaEntity{" +
                "id=" + getId() +
                ", identityType=" + identityType +
                ", name='" + name + '\'' +
                ", lifecycleState=" + lifecycleState +
                ", mergedTo=" + mergedTo +
                '}';
    }

    // Custom public builder that enforces mandatory fields
    public static IdentityJpaEntity builder(
            IdentityType identityType,
            String name
    ) {
        if (identityType == null) {
            throw new IllegalArgumentException("identityType cannot be null");
        }
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("name cannot be null or empty");
        }

        IdentityJpaEntity entity = new IdentityJpaEntity();
        entity.setIdentityType(identityType);
        entity.setName(name.trim());
        entity.setLifecycleState(IdentityLifecycleState.NONE);
        entity.setMetadata(new HashMap<>());
        entity.setAccounts(new ArrayList<>());

        return entity;
    }

    // Optional: Builder method that allows setting a specific ID (for testing or special cases)
    public static IdentityJpaEntity builderWithId(
            UUID id,
            IdentityType identityType,
            String name
    ) {
        if (id == null) {
            throw new IllegalArgumentException("id cannot be null");
        }

        IdentityJpaEntity entity = builder(identityType, name);
        entity.setId(id); // Override the auto-generated ID
        return entity;
    }
}
