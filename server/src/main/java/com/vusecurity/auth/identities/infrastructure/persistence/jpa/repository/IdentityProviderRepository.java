package com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

/**
 * Spring Data JPA repository for IdentityProviderJpaEntity.
 * Infrastructure-only interface for database operations.
 */
@Repository
public interface IdentityProviderRepository extends JpaRepository<IdentityProviderJpaEntity, UUID>, JpaSpecificationExecutor<IdentityProviderJpaEntity> {

    /**
     * Find identity provider by ID.
     * @param id the identity provider ID
     * @return the identity provider if found
     */
    Optional<IdentityProviderJpaEntity> findById(UUID id);
}
