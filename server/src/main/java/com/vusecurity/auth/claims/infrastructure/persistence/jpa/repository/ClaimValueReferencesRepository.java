package com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueReferencesJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface ClaimValueReferencesRepository extends JpaRepository<ClaimValueReferencesJpaEntity, Long> {
    
    List<ClaimValueReferencesJpaEntity> findBySourceClaimValueId(UUID sourceClaimValueId);
    
    List<ClaimValueReferencesJpaEntity> findByReferencedClaimSetId(UUID referencedClaimSetId);
    
    List<ClaimValueReferencesJpaEntity> findByReferencedClaimDefinitionId(UUID referencedClaimDefinitionId);
    
    void deleteBySourceClaimValueId(UUID sourceClaimValueId);
    
    void deleteByReferencedClaimSetId(UUID referencedClaimSetId);
}