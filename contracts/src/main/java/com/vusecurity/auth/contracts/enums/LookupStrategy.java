package com.vusecurity.auth.contracts.enums;

/**
 * Enum representing the strategy for resolving claim handles when multiple claim definitions are involved.
 */
public enum LookupStrategy {
    /**
     * All linked claims are required (for simple or complex handles).
     * This is the default strategy.
     */
    ALL_CLAIMS_MUST_MATCH,
    
    /**
     * Any of the linked claims can satisfy the handle (for indistinct/alternative handles).
     */
    ANY_CLAIM_CAN_MATCH
}
