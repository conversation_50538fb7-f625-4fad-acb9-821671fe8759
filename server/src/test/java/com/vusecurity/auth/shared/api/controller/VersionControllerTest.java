package com.vusecurity.auth.shared.api.controller;

import com.vusecurity.auth.AppConfigProperties;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class VersionControllerTest {

    @Test
    void getVersion_returns_map_with_config_values() {
        AppConfigProperties props = new AppConfigProperties();
        props.setVersion("9.9.9");
        props.setBuild("999");
        props.setDate(123456789L);
        props.setApikey("ignored");

        VersionController controller = new VersionController(props);

        Map<String, Object> out = controller.getVersion();
        assertEquals("9.9.9", out.get("version"));
        assertEquals("999", out.get("build"));
        assertEquals(123456789L, out.get("date"));
    }
}

