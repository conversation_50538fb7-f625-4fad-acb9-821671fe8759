package com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.UUID;

@Data
@Schema(description = "Claim set and related configuration")
public class ClaimSetToClaimDefinition {
    @NotNull(message = "Claim set IDs are required")
    @Schema(description = "Claim set IDs to assign",
            example = "\"123e4567-e89b-12d3-a456-************\"")
    private UUID claimSetId;
    @Schema(
            description = "Whether to enforce uniqueness for this claim",
            example = "true"
    )
    private Boolean enforceUniqueness;
}
