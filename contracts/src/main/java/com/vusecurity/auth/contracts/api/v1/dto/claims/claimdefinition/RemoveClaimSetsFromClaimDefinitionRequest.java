package com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "Request to remove claim sets from a claim definition")
public class RemoveClaimSetsFromClaimDefinitionRequest {

    @Schema(description = "List of claim set IDs to remove",
            example = "[\"123e4567-e89b-12d3-a456-************\", \"987fcdeb-51a2-43d1-9c4f-123456789abc\"]")
    @NotNull(message = "Claim set IDs are required")
    @NotEmpty(message = "At least one claim set ID must be provided")
    private List<UUID> claimSetIds;
}
