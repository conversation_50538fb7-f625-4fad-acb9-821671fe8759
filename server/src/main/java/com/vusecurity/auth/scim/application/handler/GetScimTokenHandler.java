package com.vusecurity.auth.scim.application.handler;

import com.vusecurity.auth.scim.application.service.ScimTokensService;
import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimTokenJpaEntity;

import lombok.RequiredArgsConstructor;

import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class GetScimTokenHandler {

    private static final Logger logger = LoggerFactory.getLogger(GetScimTokenHandler.class);

    private final ScimTokensService scimTokensService;
    
    @Transactional(readOnly = true)
    public ScimTokenJpaEntity getToken(UUID tokenId) {
        logger.debug("Retrieving SCIM token: {}", tokenId.toString());

        return scimTokensService.get(tokenId).orElseThrow();
    }

    @Transactional(readOnly = true)
    public Page<ScimTokenJpaEntity> getAllTokens(Specification<ScimTokenJpaEntity> spec, Pageable page) {
        return scimTokensService.getAll(spec, page);
    }
} 