package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.command.UpdateGroupRolesCommand;
import com.vusecurity.auth.authorization.application.exception.GroupNotFoundException;
import com.vusecurity.auth.authorization.application.exception.InvalidGroupRequestException;
import com.vusecurity.auth.authorization.application.exception.RoleNotFoundException;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.RoleRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountGroupRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UpdateGroupRolesHandlerTest {

    @Mock
    private AccountGroupRepository accountGroupRepository;

    @Mock
    private RoleRepository roleRepository;

    @InjectMocks
    private UpdateGroupRolesHandler updateGroupRolesHandler;

    private UUID groupId;
    private UUID roleId1, roleId2;

    private AccountGroupJpaEntity group;
    private RoleJpaEntity role1, role2;

    @BeforeEach
    void setUp() {
        groupId = UUID.randomUUID();
        roleId1 = UUID.randomUUID();
        roleId2 = UUID.randomUUID();

        role1 = new RoleJpaEntity();
        role1.setId(roleId1);
        role1.setName("Role1");

        role2 = new RoleJpaEntity();
        role2.setId(roleId2);
        role2.setName("Role2");

        group = new AccountGroupJpaEntity();
        group.setId(groupId);
        group.setRoles(new HashSet<>(Set.of(role1))); // Start with one role
    }

    @Test
    void shouldSuccessfullyAddAndRemoveRoles() {
        // Given: Group has role1, we want it to have role2 instead.
        when(accountGroupRepository.findByIdWithRoles(groupId)).thenReturn(Optional.of(group));
        UpdateGroupRolesCommand command = new UpdateGroupRolesCommand(groupId, List.of(roleId2));
        when(roleRepository.findAllById(Set.of(roleId2))).thenReturn(List.of(role2));

        // When
        updateGroupRolesHandler.updateRoles(command);

        // Then
        ArgumentCaptor<AccountGroupJpaEntity> groupCaptor = ArgumentCaptor.forClass(AccountGroupJpaEntity.class);
        verify(accountGroupRepository).save(groupCaptor.capture());

        AccountGroupJpaEntity savedGroup = groupCaptor.getValue();
        assertEquals(1, savedGroup.getRoles().size());
        assertTrue(savedGroup.getRoles().contains(role2));
        assertFalse(savedGroup.getRoles().contains(role1));
    }

    @Test
    void shouldSuccessfullyRemoveAllRoles() {
        // Given
        when(accountGroupRepository.findByIdWithRoles(groupId)).thenReturn(Optional.of(group));
        UpdateGroupRolesCommand command = new UpdateGroupRolesCommand(groupId, Collections.emptyList());

        // When
        updateGroupRolesHandler.updateRoles(command);

        // Then
        ArgumentCaptor<AccountGroupJpaEntity> groupCaptor = ArgumentCaptor.forClass(AccountGroupJpaEntity.class);
        verify(accountGroupRepository).save(groupCaptor.capture());

        AccountGroupJpaEntity savedGroup = groupCaptor.getValue();
        assertTrue(savedGroup.getRoles().isEmpty());
    }
    
    @Test
    void shouldDoNothing_WhenRolesAreUnchanged() {
        // Given: Group has role1, command provides role1.
        when(accountGroupRepository.findByIdWithRoles(groupId)).thenReturn(Optional.of(group));
        UpdateGroupRolesCommand command = new UpdateGroupRolesCommand(groupId, List.of(roleId1));

        // When
        updateGroupRolesHandler.updateRoles(command);

        // Then
        verify(roleRepository, never()).findAllById(any());
        verify(accountGroupRepository).save(any(AccountGroupJpaEntity.class)); // Save is still called
    }

    @Test
    void shouldThrowGroupNotFoundException_whenGroupDoesNotExist() {
        // Given
        when(accountGroupRepository.findByIdWithRoles(groupId)).thenReturn(Optional.empty());
        UpdateGroupRolesCommand command = new UpdateGroupRolesCommand(groupId, List.of(roleId1));

        // When & Then
        assertThrows(GroupNotFoundException.class, () -> updateGroupRolesHandler.updateRoles(command));
    }

    @Test
    void shouldThrowRoleNotFoundException_whenRoleToAddIsNotFound() {
        // Given
        when(accountGroupRepository.findByIdWithRoles(groupId)).thenReturn(Optional.of(group));
        UpdateGroupRolesCommand command = new UpdateGroupRolesCommand(groupId, List.of(roleId1, roleId2));
        when(roleRepository.findAllById(Set.of(roleId2))).thenReturn(Collections.emptyList()); // Return empty list for the new role

        // When & Then
        assertThrows(RoleNotFoundException.class, () -> updateGroupRolesHandler.updateRoles(command));
    }

    @Test
    void shouldHandleNullRoleIds_byRemovingAllRoles() {
        // Given
        when(accountGroupRepository.findByIdWithRoles(groupId)).thenReturn(Optional.of(group));
        UpdateGroupRolesCommand command = new UpdateGroupRolesCommand(groupId, null);

        // When
        updateGroupRolesHandler.updateRoles(command);

        // Then
        ArgumentCaptor<AccountGroupJpaEntity> groupCaptor = ArgumentCaptor.forClass(AccountGroupJpaEntity.class);
        verify(accountGroupRepository).save(groupCaptor.capture());
        assertTrue(groupCaptor.getValue().getRoles().isEmpty());
    }

    @Test
    void shouldThrowException_whenCommandValidationFails() {
        // Given
        UpdateGroupRolesCommand command = new UpdateGroupRolesCommand(null, List.of(roleId1));

        // When & Then
        assertThrows(InvalidGroupRequestException.class, () -> updateGroupRolesHandler.updateRoles(command));
        verifyNoInteractions(accountGroupRepository);
        verifyNoInteractions(roleRepository);
    }
} 