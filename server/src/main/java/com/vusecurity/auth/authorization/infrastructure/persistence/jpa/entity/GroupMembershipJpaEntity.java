package com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.authorization.domain.model.GroupMembershipRole;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import jakarta.persistence.*;

import java.util.Objects;
import java.util.UUID;

@Entity
@Table(name = "group_membership",
        indexes = {
                @Index(name = "uk_group_membership_id",       columnList = "id",             unique = true),
                @Index(name = "idx_group_membership_acc_grp", columnList = "account_id,group_id"),
                @Index(name = "idx_group_membership_acc",     columnList = "account_id"),
                @Index(name = "idx_group_membership_grp",     columnList = "group_id")
        })
@SuppressWarnings("LombokGetterMayBeUsed, LombokSetterMayBeUsed")
public class GroupMembershipJpaEntity extends AbstractEntity {

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(
            name = "account_id",           // FK column in GROUP_MEMBERSHIP
            referencedColumnName = "id",   // PK column of ACCOUNT
            foreignKey = @ForeignKey(name = "fk_group_membership_account")
    )
    private AccountJpaEntity account;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(
            name = "group_id",             // FK column in GROUP_MEMBERSHIP
            referencedColumnName = "id",   // PK column of ACCOUNT_GROUP
            foreignKey = @ForeignKey(name = "fk_group_membership_group")
    )
    private AccountGroupJpaEntity group;

    @Enumerated(EnumType.STRING)
    @Column(name = "membership_role")
    private GroupMembershipRole membershipRole;

    @Column(nullable = false)
    private boolean isActive = true;

    // Constructors
    public GroupMembershipJpaEntity() {
    }

    public GroupMembershipJpaEntity(AccountJpaEntity account, AccountGroupJpaEntity group) {
        if (account == null || group == null) {
            throw new IllegalArgumentException("account and group cannot be null");
        }
        this.account = account;
        this.group = group;
        this.isActive = true;
    }

    public GroupMembershipJpaEntity(AccountJpaEntity account, AccountGroupJpaEntity group, GroupMembershipRole membershipRole) {
        this(account, group);
        this.membershipRole = membershipRole;
    }

    public GroupMembershipJpaEntity(UUID id, AccountJpaEntity account, AccountGroupJpaEntity group) {
        this(account, group);
        this.setId(id);
    }

    public GroupMembershipJpaEntity(UUID id, AccountJpaEntity account, AccountGroupJpaEntity group, GroupMembershipRole membershipRole) {
        this(account, group, membershipRole);
        this.setId(id);
    }

    // Getters
    public AccountJpaEntity getAccount() {
        return account;
    }

    public AccountGroupJpaEntity getGroup() {
        return group;
    }

    public GroupMembershipRole getMembershipRole() {
        return membershipRole;
    }

    public boolean isActive() {
        return isActive;
    }

    // Convenience getters for IDs
    public UUID getAccountId() {
        return account != null ? account.getId() : null;
    }

    public UUID getGroupId() {
        return group != null ? group.getId() : null;
    }

    // Setters
    public void setAccount(AccountJpaEntity account) {
        this.account = account;
    }

    public void setGroup(AccountGroupJpaEntity group) {
        this.group = group;
    }

    public void setMembershipRole(GroupMembershipRole membershipRole) {
        this.membershipRole = membershipRole;
    }

    public void setActive(boolean active) {
        this.isActive = active;
    }

    // equals() and hashCode() using account and group (business key)
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        GroupMembershipJpaEntity that = (GroupMembershipJpaEntity) o;
        return Objects.equals(account, that.account) && 
               Objects.equals(group, that.group);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), account, group);
    }

    @Override
    public String toString() {
        return "GroupMembershipJpaEntity{" +
                "id=" + getId() +
                ", accountId=" + getAccountId() +
                ", groupId=" + getGroupId() +
                ", membershipRole=" + membershipRole +
                ", isActive=" + isActive +
                '}';
    }
}
