package com.vusecurity.auth.identities.api.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.lang.reflect.Field;

public class IdentityIdOrAccountIdValidator implements ConstraintValidator<IdentityIdOrAccountIdRequired, Object> {

    private String identityId;
    private String accountId;

    @Override
    public void initialize(IdentityIdOrAccountIdRequired constraintAnnotation) {
        this.identityId = constraintAnnotation.identityId();
        this.accountId = constraintAnnotation.accountId();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        try {
            Field identityIdField = value.getClass().getDeclaredField(identityId);
            Field accountIdField = value.getClass().getDeclaredField(accountId);

            identityIdField.setAccessible(true);
            accountIdField.setAccessible(true);

            Object identityIdValue = identityIdField.get(value);
            Object accountIdValue = accountIdField.get(value);

            return identityIdValue != null || accountIdValue != null;

        } catch (Exception e) {
            // Si falla la reflexión, consideramos inválido por seguridad
            return false;
        }
    }
}