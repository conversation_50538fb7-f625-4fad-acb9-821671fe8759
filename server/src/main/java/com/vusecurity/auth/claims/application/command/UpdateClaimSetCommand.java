package com.vusecurity.auth.claims.application.command;

import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.LookupStrategy;

import java.util.UUID;

public record UpdateClaimSetCommand(
        UUID id,
        UUID businessId,
        AccountType accountType,
        Boolean isIdentifier,
        String name,
        String description,
        LookupStrategy lookupStrategy) {

    public void validate() {
        if (id == null) throw new IllegalArgumentException("id is required");
    }
}
