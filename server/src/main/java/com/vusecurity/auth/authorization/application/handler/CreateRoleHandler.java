package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.command.CreateRoleCommand;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.PermissionJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.PermissionRepository;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.RoleRepository;
import com.vusecurity.auth.shared.util.DatabaseConstraintUtils;
import com.vusecurity.auth.authorization.application.exception.RoleNameAlreadyExistException;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class CreateRoleHandler {

    private static final Logger logger = LoggerFactory.getLogger(CreateRoleHandler.class);
    
    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;

    @Transactional
    public RoleJpaEntity createRole(CreateRoleCommand command) {
        logger.debug("Creating role: {}", command.name());

        // Validate command
        command.validate();

        // Create role entity
        RoleJpaEntity role = new RoleJpaEntity(command.businessId(), command.name());
        role.setDescription(command.description());

        // Add permissions if provided
        if (command.permissionIds() != null && !command.permissionIds().isEmpty()) {
            Set<PermissionJpaEntity> permissions = new HashSet<>();
            for (UUID permissionId : command.permissionIds()) {
                PermissionJpaEntity permission = permissionRepository.findById(permissionId)
                        .orElseThrow(() -> new IllegalArgumentException("Permission not found with ID: " + permissionId));
                permissions.add(permission);
            }
            role.setPermissions(permissions);
        }

        // Save role with DB constraint handling for unique name
        RoleJpaEntity savedRole = DatabaseConstraintUtils.executeWithConstraintHandling(
            () -> roleRepository.saveAndFlush(role),
            "uk_role_name", // Unique constraint on role name
                () -> new RoleNameAlreadyExistException()
        );
        logger.debug("Role created successfully: {}", savedRole.getId());
        return savedRole;
    }
}
