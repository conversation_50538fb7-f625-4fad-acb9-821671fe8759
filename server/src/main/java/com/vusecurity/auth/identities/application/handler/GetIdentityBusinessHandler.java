package com.vusecurity.auth.identities.application.handler;

import com.vusecurity.auth.contracts.api.v1.dto.shared.BusinessBasicInfoResponse;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.usecases.interfaces.IRetrieveBusiness;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class GetIdentityBusinessHandler {
    private static final Logger logger = LoggerFactory.getLogger(GetIdentityBusinessHandler.class);

    private final IRetrieveBusiness retrieveBusiness;

    public BusinessBasicInfoResponse retrieveBusinessBasicInfo(UUID businessId) {
        try {
            Business business = retrieveBusiness.retrieveById(businessId);
            return new BusinessBasicInfoResponse()
                    .setId(businessId.toString())
                    .setName(business.getName());
        } catch (Exception e) {
            logger.warn("Failed to retrieve business info for identity {}: {}", businessId, e.getMessage());
            return null;
        }
    }

    public List<BusinessBasicInfoResponse> retrieveAllBusinessBasicInfo(Set<UUID> businessIds) {
        try {
            List<Business> business = retrieveBusiness.retrieveAllByIdsIn(businessIds);

            return mapBusinessBasicInfo(business);
        } catch (Exception e) {
            logger.warn("Failed to retrieve business info for identity {}: {}", businessIds, e.getMessage());
            return null;
        }
    }

    private List<BusinessBasicInfoResponse> mapBusinessBasicInfo(List<Business> businesses) {
        List<BusinessBasicInfoResponse> businessBasicInfoResponses = new ArrayList<>();
        for (Business business : businesses) {
            businessBasicInfoResponses.add(
                    new BusinessBasicInfoResponse()
                    .setId(business.getId().toString())
                    .setName(business.getName())

            );
        }
        return businessBasicInfoResponses;
    }
}
