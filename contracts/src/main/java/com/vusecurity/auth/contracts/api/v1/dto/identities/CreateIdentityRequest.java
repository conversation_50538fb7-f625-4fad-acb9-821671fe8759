package com.vusecurity.auth.contracts.api.v1.dto.identities;

import com.vusecurity.auth.contracts.enums.IdentityLifecycleState;
import com.vusecurity.auth.contracts.enums.IdentityType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

@Data
@Schema(description = "Request to create a new identity")
public class CreateIdentityRequest {
    @NotNull(message = "Identity type is required")
    @Schema(description = "Type of identity being created",
            example = "PERSON")
    private IdentityType identityType;

    @NotNull(message = "Name is required")
    @Schema(description = "Name of the identity",
            example = "John Doe")
    private String name;

    @Schema(description = "Additional metadata for the identity as key-value pairs",
            example = "{\"email\": \"<EMAIL>\", \"phone\": \"+1234567890\"}")
    private Map<String, Object> metadata;

    @Schema(description = "Initial lifecycle state of the identity",
            example = "ACTIVE")
    private IdentityLifecycleState lifecycleState;
}
