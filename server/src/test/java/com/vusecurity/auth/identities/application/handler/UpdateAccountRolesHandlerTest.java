package com.vusecurity.auth.identities.application.handler;

import com.vusecurity.auth.authorization.application.exception.RoleNotFoundException;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.RoleRepository;
import com.vusecurity.auth.identities.application.command.UpdateAccountRolesCommand;
import com.vusecurity.auth.identities.application.exception.AccountNotFoundException;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UpdateAccountRolesHandlerTest {

    @Mock
    private AccountRepository accountRepository;

    @Mock
    private RoleRepository roleRepository;

    @InjectMocks
    private UpdateAccountRolesHandler updateAccountRolesHandler;

    private UUID accountId;
    private UUID roleId1, roleId2;

    private AccountJpaEntity account;
    private RoleJpaEntity role1, role2;

    @BeforeEach
    void setUp() {
        accountId = UUID.randomUUID();
        roleId1 = UUID.randomUUID();
        roleId2 = UUID.randomUUID();

        role1 = new RoleJpaEntity();
        role1.setId(roleId1);
        role1.setName("Role1");

        role2 = new RoleJpaEntity();
        role2.setId(roleId2);
        role2.setName("Role2");

        account = new AccountJpaEntity();
        account.setId(accountId);
        account.setRoles(new HashSet<>(Set.of(role1))); // Start with one role
    }

    @Test
    void shouldSuccessfullyAddAndRemoveRoles() {
        // Given: Account has role1, we want it to have role2 instead.
        when(accountRepository.findByIdWithRoles(accountId)).thenReturn(Optional.of(account));
        UpdateAccountRolesCommand command = new UpdateAccountRolesCommand(accountId, List.of(roleId2));
        
        // Mock the roles with accounts for removal
        when(roleRepository.findAllByIdWithAccounts(Set.of(roleId1))).thenReturn(List.of(role1));
        
        // Mock the roles with accounts for addition  
        when(roleRepository.findAllByIdWithAccounts(Set.of(roleId2))).thenReturn(List.of(role2));

        // When
        updateAccountRolesHandler.updateRoles(command);

        // Then - verify calls in order: first added roles, then removed roles
        verify(roleRepository).saveAll(List.of(role2)); // Roles that had accounts added (first)
        verify(roleRepository).saveAll(argThat(roles -> {
            Collection<RoleJpaEntity> roleCollection = (Collection<RoleJpaEntity>) roles;
            return roleCollection.contains(role1);
        })); // Roles that had accounts removed (second)
        verify(accountRepository).save(any(AccountJpaEntity.class));
    }

    @Test
    void shouldSuccessfullyRemoveAllRoles() {
        // Given
        when(accountRepository.findByIdWithRoles(accountId)).thenReturn(Optional.of(account));
        UpdateAccountRolesCommand command = new UpdateAccountRolesCommand(accountId, Collections.emptyList());
        
        // Mock the roles with accounts for removal
        when(roleRepository.findAllByIdWithAccounts(Set.of(roleId1))).thenReturn(List.of(role1));

        // When
        updateAccountRolesHandler.updateRoles(command);

        // Then
        verify(roleRepository).saveAll(argThat(roles -> {
            Collection<RoleJpaEntity> roleCollection = (Collection<RoleJpaEntity>) roles;
            return roleCollection.contains(role1);
        })); // Roles that had accounts removed
        verify(accountRepository).save(any(AccountJpaEntity.class));
    }
    
    @Test
    void shouldDoNothing_WhenRolesAreUnchanged() {
        // Given: Account has role1, command provides role1.
        when(accountRepository.findByIdWithRoles(accountId)).thenReturn(Optional.of(account));
        UpdateAccountRolesCommand command = new UpdateAccountRolesCommand(accountId, List.of(roleId1));

        // When
        updateAccountRolesHandler.updateRoles(command);

        // Then
        verify(roleRepository, never()).findAllByIdWithAccounts(any());
        verify(accountRepository).save(any(AccountJpaEntity.class)); // Save is still called
    }

    @Test
    void shouldThrowAccountNotFoundException_whenAccountDoesNotExist() {
        // Given
        when(accountRepository.findByIdWithRoles(accountId)).thenReturn(Optional.empty());
        UpdateAccountRolesCommand command = new UpdateAccountRolesCommand(accountId, List.of(roleId1));

        // When & Then
        assertThrows(AccountNotFoundException.class, () -> updateAccountRolesHandler.updateRoles(command));
    }

    @Test
    void shouldThrowRoleNotFoundException_whenRoleToAddIsNotFound() {
        // Given
        when(accountRepository.findByIdWithRoles(accountId)).thenReturn(Optional.of(account));
        UpdateAccountRolesCommand command = new UpdateAccountRolesCommand(accountId, List.of(roleId1, roleId2));
        when(roleRepository.findAllByIdWithAccounts(Set.of(roleId2))).thenReturn(Collections.emptyList()); // Return empty list for the new role

        // When & Then
        assertThrows(RoleNotFoundException.class, () -> updateAccountRolesHandler.updateRoles(command));
    }

    @Test
    void shouldHandleNullRoleIds_byRemovingAllRoles() {
        // Given
        when(accountRepository.findByIdWithRoles(accountId)).thenReturn(Optional.of(account));
        UpdateAccountRolesCommand command = new UpdateAccountRolesCommand(accountId, null);
        
        // Mock the roles with accounts for removal
        when(roleRepository.findAllByIdWithAccounts(Set.of(roleId1))).thenReturn(List.of(role1));

        // When
        updateAccountRolesHandler.updateRoles(command);

        // Then
        verify(roleRepository).saveAll(argThat(roles -> {
            Collection<RoleJpaEntity> roleCollection = (Collection<RoleJpaEntity>) roles;
            return roleCollection.contains(role1);
        })); // Roles that had accounts removed
        verify(accountRepository).save(any(AccountJpaEntity.class));
    }

    @Test
    void shouldThrowException_whenCommandValidationFails() {
        // Given
        UpdateAccountRolesCommand command = new UpdateAccountRolesCommand(null, List.of(roleId1));

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> updateAccountRolesHandler.updateRoles(command));
        verifyNoInteractions(accountRepository);
        verifyNoInteractions(roleRepository);
    }
}