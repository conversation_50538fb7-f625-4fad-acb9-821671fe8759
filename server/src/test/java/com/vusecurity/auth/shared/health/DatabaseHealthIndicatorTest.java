package com.vusecurity.auth.shared.health;

import org.junit.jupiter.api.Test;
import org.springframework.boot.actuate.health.Health;

import static org.junit.jupiter.api.Assertions.*;

class DatabaseHealthIndicatorTest {

    @Test
    void health_should_returnUpWithDetails() {
        // Given
        DatabaseHealthIndicator indicator = new DatabaseHealthIndicator();

        // When
        Health health = indicator.health();

        // Then
        assertNotNull(health);
        assertEquals("UP", health.getStatus().getCode());
        assertEquals("Available", health.getDetails().get("database"));
        assertTrue(health.getDetails().get("note").toString().toLowerCase().contains("placeholder"));
    }
}

