package com.vusecurity.auth.claims.application.service;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimVerificationJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimVerificationRepository;
import com.vusecurity.auth.shared.enums.ResultType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Helper service for working with claim verifications.
 * Provides convenient methods to access verification data from ClaimValueJpaEntity.
 */
@Service
@Transactional(readOnly = true)
public class ClaimVerificationHelper {

    private static final Logger logger = LoggerFactory.getLogger(ClaimVerificationHelper.class);

    private final ClaimValueRepository claimValueRepository;
    private final ClaimVerificationRepository claimVerificationRepository;

    public ClaimVerificationHelper(ClaimValueRepository claimValueRepository,
                                  ClaimVerificationRepository claimVerificationRepository) {
        this.claimValueRepository = claimValueRepository;
        this.claimVerificationRepository = claimVerificationRepository;
    }

    /**
     * Gets claim values for an account with verification data loaded.
     *
     * @param accountId the account ID
     * @return list of claim values with verification loaded
     */
    public List<ClaimValueJpaEntity> getClaimValuesWithVerification(UUID accountId) {
        logger.debug("Getting claim values with verification for account: {}", accountId);

        List<ClaimValueJpaEntity> claimValues = 
            claimValueRepository.findByAccountIdWithDefinitionAndVerification(accountId);

        // Mark verification as loaded for each claim value
        for (ClaimValueJpaEntity claimValue : claimValues) {
            claimValue.setVerificationLoaded(true);
        }

        logger.debug("Retrieved {} claim values with verification for account {}", 
                    claimValues.size(), accountId);
        return claimValues;
    }

    /**
     * Gets a claim value by ID with verification data loaded.
     *
     * @param claimValueId the claim value ID
     * @return the claim value with verification if found
     */
    public Optional<ClaimValueJpaEntity> getClaimValueWithVerification(UUID claimValueId) {
        logger.debug("Getting claim value with verification: {}", claimValueId);

        Optional<ClaimValueJpaEntity> claimValueOpt = 
            claimValueRepository.findByIdWithVerification(claimValueId);

        claimValueOpt.ifPresent(claimValue -> claimValue.setVerificationLoaded(true));

        return claimValueOpt;
    }

    /**
     * Checks if a claim value is verified (has successful verification).
     *
     * @param claimValueId the claim value ID
     * @return true if the claim value has successful verification
     */
    public boolean isClaimValueVerified(UUID claimValueId) {
        return claimVerificationRepository.hasSuccessfulVerification(claimValueId);
    }

    /**
     * Gets the latest verification for a claim value.
     *
     * @param claimValueId the claim value ID
     * @return the latest verification if found
     */
    public Optional<ClaimVerificationJpaEntity> getLatestVerification(UUID claimValueId) {
        return claimVerificationRepository.findLatestByClaimValueId(claimValueId);
    }

    /**
     * Gets all verifications for a claim value.
     *
     * @param claimValueId the claim value ID
     * @return list of verifications
     */
    public List<ClaimVerificationJpaEntity> getAllVerifications(UUID claimValueId) {
        return claimVerificationRepository.findByClaimValueId(claimValueId);
    }

    /**
     * Gets verifications by result type for a claim value.
     *
     * @param claimValueId the claim value ID
     * @param result the result type
     * @return list of verifications with the specified result
     */
    public List<ClaimVerificationJpaEntity> getVerificationsByResult(UUID claimValueId, ResultType result) {
        return claimVerificationRepository.findByClaimValueIdAndResult(claimValueId, result);
    }

    /**
     * Gets verifications by type for a claim value.
     *
     * @param claimValueId the claim value ID
     * @param verificationType the verification type
     * @return list of verifications with the specified type
     */
    public List<ClaimVerificationJpaEntity> getVerificationsByType(UUID claimValueId, String verificationType) {
        return claimVerificationRepository.findByClaimValueIdAndVerificationType(claimValueId, verificationType);
    }

    /**
     * Gets all verifications for an account's claim values.
     *
     * @param accountId the account ID
     * @return list of verifications for the account
     */
    public List<ClaimVerificationJpaEntity> getAccountVerifications(UUID accountId) {
        return claimVerificationRepository.findByAccountId(accountId);
    }

    /**
     * Convenience method to check if a claim value has been verified.
     * Uses the loaded verification data if available, otherwise queries the database.
     *
     * @param claimValue the claim value to check
     * @return true if the claim value has successful verification
     */
    public boolean isVerified(ClaimValueJpaEntity claimValue) {
        if (claimValue == null || claimValue.getId() == null) {
            return false;
        }

        // If verification is already loaded, use it
        if (claimValue.isVerificationLoaded()) {
            return claimValue.isVerified();
        }

        // Otherwise, query the database
        return isClaimValueVerified(claimValue.getId());
    }

    /**
     * Convenience method to get the verification status of a claim value.
     * Returns the result of the verification.
     *
     * @param claimValue the claim value
     * @return the verification result, or NONE if no verification exists
     */
    public ResultType getVerificationStatus(ClaimValueJpaEntity claimValue) {
        if (claimValue == null || claimValue.getId() == null) {
            return ResultType.NONE;
        }

        // If verification is loaded, use it
        if (claimValue.isVerificationLoaded()) {
            return claimValue.getVerificationResult();
        }

        // Otherwise, get the latest verification
        Optional<ClaimVerificationJpaEntity> latestVerification = getLatestVerification(claimValue.getId());
        return latestVerification.map(ClaimVerificationJpaEntity::getResult).orElse(ResultType.NONE);
    }

    /**
     * Loads verification data for a claim value if not already loaded.
     * This populates the verification property using a fetch strategy.
     *
     * @param claimValue the claim value to load verification for
     */
    public void ensureVerificationLoaded(ClaimValueJpaEntity claimValue) {
        if (claimValue == null || claimValue.getId() == null || claimValue.isVerificationLoaded()) {
            return;
        }

        logger.debug("Loading verification for claim value: {}", claimValue.getId());

        Optional<ClaimValueJpaEntity> claimValueWithVerification = 
            getClaimValueWithVerification(claimValue.getId());

        claimValueWithVerification.ifPresent(loaded -> {
            claimValue.setVerification(loaded.getVerification());
            claimValue.setVerificationLoaded(true);
        });
    }

    /**
     * Loads verification data for multiple claim values if not already loaded.
     *
     * @param claimValues the claim values to load verification for
     */
    public void ensureVerificationLoaded(List<ClaimValueJpaEntity> claimValues) {
        if (claimValues == null || claimValues.isEmpty()) {
            return;
        }

        logger.debug("Loading verification for {} claim values", claimValues.size());

        for (ClaimValueJpaEntity claimValue : claimValues) {
            ensureVerificationLoaded(claimValue);
        }
    }
}
