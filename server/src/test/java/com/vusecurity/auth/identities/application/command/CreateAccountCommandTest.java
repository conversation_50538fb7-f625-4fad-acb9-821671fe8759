package com.vusecurity.auth.identities.application.command;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.junit.jupiter.api.Test;

import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.ArrayClaimValueRequest;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.ClaimValueRequest;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.ScalarClaimValueRequest;
import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.contracts.enums.AccountType;

/**
 * Unit tests for CreateAccountCommand validation logic.
 * Tests pure domain logic with no external dependencies.
 */
class CreateAccountCommandTest {

    @Test
    void shouldValidateSuccessfully_WhenAllRequiredFieldsProvided() {
        // Given
        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                new HashMap<>(),
                null
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WhenOptionalFieldsAreNull() {
        // Given
        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                null, // lifecycleState is optional
                null, // metadata is optional
                null  // claimValueList is optional
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldThrowException_WhenIdentityIdIsNull() {
        // Given
        CreateAccountCommand command = new CreateAccountCommand(
                null, // identityId is null
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                new HashMap<>(),
                null
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("identityId is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenBusinessIdIsNull() {
        // Given
        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                null, // businessId is null
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                new HashMap<>(),
                null
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("businessId is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenIdentityProviderIdIsNull() {
        // Given
        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                null, // identityProviderId is null
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                new HashMap<>(),
                null
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("identityProviderId is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenAccountTypeIsNull() {
        // Given
        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                null, // accountType is null
                AccountLifecycleState.ACTIVE,
                new HashMap<>(),
                null
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("accountType is required", exception.getMessage());
    }

    @Test
    void shouldValidateSuccessfully_WithAllAccountTypes() {
        // Test all enum values
        for (AccountType accountType : AccountType.values()) {
            // Given
            CreateAccountCommand command = new CreateAccountCommand(
                    UUID.randomUUID(),
                    UUID.randomUUID(),
                    UUID.randomUUID(),
                    accountType,
                    AccountLifecycleState.ACTIVE,
                    new HashMap<>(),
                    null
            );

            // When & Then
            assertDoesNotThrow(command::validate,
                    "Validation should pass for AccountType: " + accountType);
        }
    }

    @Test
    void shouldValidateSuccessfully_WithAllLifecycleStates() {
        // Test all enum values including null
        for (AccountLifecycleState lifecycleState : AccountLifecycleState.values()) {
            // Given
            CreateAccountCommand command = new CreateAccountCommand(
                    UUID.randomUUID(),
                    UUID.randomUUID(),
                    UUID.randomUUID(),
                    AccountType.WORKFORCE,
                    lifecycleState,
                    new HashMap<>(),
                    null
            );

            // When & Then
            assertDoesNotThrow(command::validate,
                    "Validation should pass for LifecycleState: " + lifecycleState);
        }
    }

    @Test
    void shouldValidateSuccessfully_WithMetadata() {
        // Given
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("department", "Engineering");
        metadata.put("level", 5);
        metadata.put("active", true);

        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                metadata,
                null
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithEmptyMetadata() {
        // Given
        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                new HashMap<>(),
                null
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithClaimValues() {
        // Given
        ClaimValueRequest claimValue = new ScalarClaimValueRequest(
                UUID.randomUUID(),
                UUID.randomUUID(),
                "<EMAIL>"
        );

        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                new HashMap<>(),
                List.of(claimValue)
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithEmptyClaimValueList() {
        // Given
        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                new HashMap<>(),
                Collections.emptyList()
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldThrowException_WhenClaimValueHasNullClaimSetId() {
        // Given
        ClaimValueRequest claimValue = new ScalarClaimValueRequest(
                null, 
                UUID.randomUUID(),
                "<EMAIL>"
        );

        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                new HashMap<>(),
                List.of(claimValue)
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, command::validate);
        assertEquals("claimSetId is required for all claim values", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenClaimValueHasNullClaimDefinitionId() {
        // Given
        ClaimValueRequest claimValue = new ScalarClaimValueRequest(
                UUID.randomUUID(),
                null, // null claimDefinitionId
                "<EMAIL>"
        );

        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                new HashMap<>(),
                List.of(claimValue)
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, command::validate);
        assertEquals("claimDefinitionId is required for all claim values", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenClaimValueHasNullValue() {
        // Given
        ClaimValueRequest claimValue = new ScalarClaimValueRequest(
                UUID.randomUUID(),
                UUID.randomUUID(),
                null // null value
        );

        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                new HashMap<>(),
                List.of(claimValue)
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, command::validate);
        assertEquals("value is required for scalar claim values", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenClaimValueHasEmptyValue() {
        // Given
        ClaimValueRequest claimValue = new ScalarClaimValueRequest(
                UUID.randomUUID(),
                UUID.randomUUID(),
                "   " // empty value
        );

        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                new HashMap<>(),
                List.of(claimValue)
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, command::validate);
        assertEquals("value is required for scalar claim values", exception.getMessage());
    }

    @Test
    void shouldValidateSuccessfully_WithArrayClaimValues() {
        // Given
        ClaimValueRequest arrayClaimValue = new ArrayClaimValueRequest(
                UUID.randomUUID(),
                UUID.randomUUID(),
                List.of("admin", "user", "viewer"),
                1 // "user" is primary
        );

        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                Collections.emptyMap(),
                List.of(arrayClaimValue)
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldThrowException_WhenArrayClaimValueHasNullValueInList() {
        // Given
        ClaimValueRequest arrayClaimValue = new ArrayClaimValueRequest(
                UUID.randomUUID(),
                UUID.randomUUID(),
                Arrays.asList("admin", null, "user"), // null value in list
                0
        );

        CreateAccountCommand command = new CreateAccountCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                AccountType.WORKFORCE,
                AccountLifecycleState.ACTIVE,
                Collections.emptyMap(),
                List.of(arrayClaimValue)
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, command::validate);
        assertEquals("all values in array claim must be non-empty", exception.getMessage());
    }
}
