package com.vusecurity.auth.claims.mapper;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.ClaimSetResponse;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.LookupStrategy;
import com.vusecurity.business.domain.Business;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class ClaimSetDtoMapperTest {

    private ClaimSetDtoMapper mapper;

    @BeforeEach
    void setUp() {
        // No dependencies for constructor
        mapper = new ClaimSetDtoMapper(null);
    }

    @Test
    void shouldMapBusinessNameAndFields() {
        // Given
        UUID claimSetId = UUID.randomUUID();
        UUID businessId = UUID.randomUUID();
        String businessName = "Test Business";
        Business business = new Business();
        business.setId(businessId);
        business.setName(businessName);

        ClaimSetJpaEntity entity = new ClaimSetJpaEntity();
        entity.setId(claimSetId);
        entity.setBusiness(business);
        entity.setAccountType(AccountType.WORKFORCE);
        entity.setIsIdentifier(true);
        entity.setName("Employee Claims");
        entity.setDescription("Claims for employees");
        entity.setLookupStrategy(LookupStrategy.ALL_CLAIMS_MUST_MATCH);

        // When
        ClaimSetResponse response = mapper.toResponse(entity);

        // Then
        assertNotNull(response);
        assertEquals(claimSetId, response.getId());
        assertEquals(businessId, response.getBusinessId());
        assertEquals(businessName, response.getBusinessName());
        assertEquals(AccountType.WORKFORCE, response.getAccountType());
        assertTrue(response.getIsIdentifier());
        assertEquals("Employee Claims", response.getName());
        assertEquals("Claims for employees", response.getDescription());
        assertEquals(LookupStrategy.ALL_CLAIMS_MUST_MATCH, response.getLookupStrategy());
    }

    @Test
    void shouldMapAuditInfo() {
        // Given
        UUID claimSetId = UUID.randomUUID();
        Business business = new Business();
        business.setId(UUID.randomUUID());
        business.setName("Test Business");
        ClaimSetJpaEntity entity = new ClaimSetJpaEntity();
        entity.setId(claimSetId);
        entity.setBusiness(business);
        entity.setAccountType(AccountType.WORKFORCE);
        entity.setIsIdentifier(true);
        entity.setName("Employee Claims");
        entity.setDescription("Claims for employees");
        entity.setLookupStrategy(LookupStrategy.ALL_CLAIMS_MUST_MATCH);
        entity.setCreatedAt(java.time.Instant.parse("2024-01-01T10:00:00Z"));
        entity.setUpdatedAt(java.time.Instant.parse("2024-01-02T12:00:00Z"));
        entity.setCreatedBy("<EMAIL>");
        entity.setUpdatedBy("<EMAIL>");

        // When
        ClaimSetResponse response = mapper.toResponse(entity);

        // Then
        assertNotNull(response.getAudit());
        assertEquals(java.time.Instant.parse("2024-01-01T10:00:00Z"), response.getAudit().getCreatedAt());
        assertEquals(java.time.Instant.parse("2024-01-02T12:00:00Z"), response.getAudit().getUpdatedAt());
        assertEquals("<EMAIL>", response.getAudit().getCreatedBy());
        assertEquals("<EMAIL>", response.getAudit().getUpdatedBy());
    }

    @Test
    void shouldMapClaimDefinitions() {
        // Given
        UUID claimSetId = UUID.randomUUID();
        Business business = new Business();
        business.setId(UUID.randomUUID());
        business.setName("Test Business");
        ClaimSetJpaEntity entity = new ClaimSetJpaEntity();
        entity.setId(claimSetId);
        entity.setBusiness(business);
        entity.setAccountType(AccountType.WORKFORCE);
        entity.setIsIdentifier(true);
        entity.setName("Employee Claims");
        entity.setDescription("Claims for employees");
        entity.setLookupStrategy(LookupStrategy.ALL_CLAIMS_MUST_MATCH);
        // Simulate claim definitions via the mappingService (mapper will fallback to empty list if service is null)

        // When
        ClaimSetResponse response = mapper.toResponse(entity);

        // Then
        assertNotNull(response.getClaimDefinitions());
        assertTrue(response.getClaimDefinitions().isEmpty()); // Because mappingService is null
    }

    @Test
    void shouldReturnNullForNullInput() {
        // When
        ClaimSetResponse response = mapper.toResponse(null);
        // Then
        assertNull(response);
    }

    @Test
    void shouldHandleBusinessWithNoName() {
        // Given
        Business business = new Business();
        business.setId(UUID.randomUUID());
        business.setName(null);
        ClaimSetJpaEntity entity = new ClaimSetJpaEntity();
        entity.setId(UUID.randomUUID());
        entity.setBusiness(business);
        entity.setAccountType(AccountType.WORKFORCE);
        entity.setIsIdentifier(false);
        entity.setName("No Name Business");
        entity.setDescription("Business with no name");
        entity.setLookupStrategy(LookupStrategy.ALL_CLAIMS_MUST_MATCH);

        // When
        ClaimSetResponse response = mapper.toResponse(entity);

        // Then
        assertNull(response.getBusinessName());
    }
} 