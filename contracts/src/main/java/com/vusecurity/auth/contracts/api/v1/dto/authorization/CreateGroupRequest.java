package com.vusecurity.auth.contracts.api.v1.dto.authorization;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
@Schema(description = "Request to create a new account group")
public class CreateGroupRequest {
    
    @NotNull(message = "Name is required")
    @Size(min = 1, max = 255, message = "Name must be between 1 and 255 characters")
    @Schema(description = "Name of the account group",
            example = "Engineering Team")
    private String name;

    @Size(max = 255, message = "Description must not exceed 255 characters")
    @Schema(description = "Description of the account group",
            example = "Engineering team members in Argentina office")
    private String description;

    @Schema(description = "Additional metadata for the group as key-value pairs",
            example = "{\"department\": \"engineering\", \"location\": \"argentina\"}")
    private Map<String, Object> metadata;

    @NotNull(message = "Role IDs are required")
    @NotEmpty(message = "At least one role ID must be provided")
    @Schema(description = "List of role IDs to assign to the group",
            example = "[\"123e4567-e89b-12d3-a456-************\", \"987fcdeb-51a2-43d1-9c4f-123456789abc\"]")
    private List<UUID> roleIds;

    @Schema(description = "List of account IDs to add to the group (optional)",
            example = "[\"456e7890-e89b-12d3-a456-************\", \"789fcdeb-51a2-43d1-9c4f-123456789def\"]")
    private List<UUID> accountIds;

    @Schema(description = "List of account IDs that should be owners of the group (optional, must be subset of accountIds)",
            example = "[\"456e7890-e89b-12d3-a456-************\"]")
    private List<UUID> ownerAccountIds;
} 