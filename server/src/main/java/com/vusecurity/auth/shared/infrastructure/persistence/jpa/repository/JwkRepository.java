package com.vusecurity.auth.shared.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.shared.infrastructure.persistence.jpa.entity.JwkJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface JwkRepository extends JpaRepository<JwkJpaEntity, UUID> {
    
    Optional<JwkJpaEntity> findByKeyId(String keyId);
    
    @Query("SELECT j FROM JwkJpaEntity j WHERE j.isActive = true ORDER BY j.createdAt DESC")
    List<JwkJpaEntity> findAllActive();
    
    @Query("SELECT j FROM JwkJpaEntity j WHERE j.isActive = true ORDER BY j.createdAt DESC LIMIT 1")
    Optional<JwkJpaEntity> findFirstActive();
    
    boolean existsByIsActive(Boolean isActive);
}