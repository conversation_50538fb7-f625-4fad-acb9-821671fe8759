package com.vusecurity.auth.claims.application.query;

import lombok.Builder;
import lombok.Value;

import java.util.UUID;

@Value
@Builder
public class GetClaimSetsPagedQuery {
    int page;
    int pageSize;
    String filter;
    UUID businessId;
    String accountType;
    Boolean isIdentifier; // null = no filter, true = identifier claim sets, false = non-identifier claim sets
    Boolean hasClaimDefinitions; // null = no filter, true = has definitions, false = no definitions
    Boolean hasBusiness; // null = no filter, true = has business, false = no business
    String sortBy; // null = no sorting, otherwise field name to sort by (e.g., "name", "createdAt")
    String sortDirection; // "ASC" or "DESC", defaults to "ASC" if not specified
}
