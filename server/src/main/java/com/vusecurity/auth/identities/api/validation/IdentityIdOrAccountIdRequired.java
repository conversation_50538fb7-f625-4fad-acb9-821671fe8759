package com.vusecurity.auth.identities.api.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = IdentityIdOrAccountIdValidator.class)
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface IdentityIdOrAccountIdRequired {
    String message() default "Either email or phoneNumber must be provided";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};

    String identityId();   // nombre del primer campo
    String accountId();  // nombre del segundo campo
}
