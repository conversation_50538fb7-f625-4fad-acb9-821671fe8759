package com.vusecurity.auth.identities.application.command;

import com.vusecurity.auth.contracts.enums.AccountLifecycleState;

import java.util.Map;
import java.util.UUID;

public record UpdateAccountCommand (
    UUID accountId,
    AccountLifecycleState lifecycleState,
    Map<String, Object> metadata
) {
    public void validate() {
        if (accountId == null) throw new IllegalArgumentException("accountId is required");
    }
}
