package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.DeleteClaimValueCommand;
import com.vusecurity.auth.claims.application.exception.DeleteClaimTypeSystemException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.enums.ClaimType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DeleteClaimValueHandlerTest {

    @Mock
    private ClaimValueRepository claimValueRepository;
    
    @Mock
    private ClaimDefinitionRepository claimDefinitionRepository;

    @InjectMocks
    private DeleteClaimValueHandler handler;

    @Test
    void should_not_fail_when_deleting_definition_of_claimType_user() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        DeleteClaimValueCommand cmd = new DeleteClaimValueCommand(claimValueId);

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.NUMERIC, null, ClaimType.USER_DEFINED);
        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        existingValue.setClaimDefinition(definition);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));

        // When & Then
        assertDoesNotThrow(() -> handler.deleteClaimValue(cmd));
    }

    @Test
    void should_fail_when_deleting_definition_of_claimType_system() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        DeleteClaimValueCommand cmd = new DeleteClaimValueCommand(claimValueId);

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.NUMERIC, null, ClaimType.SYSTEM_DEFINED);
        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        existingValue.setClaimDefinition(definition);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));

        // When & Then
        assertThrows(DeleteClaimTypeSystemException.class, () -> handler.deleteClaimValue(cmd));
    }

    private ClaimValueJpaEntity createClaimValue(UUID id, UUID claimDefinitionId) {
        ClaimValueJpaEntity value = new ClaimValueJpaEntity();
        value.setId(id);
        
        ClaimDefinitionJpaEntity definition = new ClaimDefinitionJpaEntity();
        definition.setId(claimDefinitionId);
        value.setClaimDefinition(definition);
        
        return value;
    }

    private ClaimDefinitionJpaEntity createClaimDefinition(UUID id, DataTypeEnum dataType, String dataFormat, ClaimType claimType) {
        ClaimDefinitionJpaEntity definition = new ClaimDefinitionJpaEntity();
        definition.setId(id);
        definition.setCode("test-code");
        definition.setName("Test Claim");
        definition.setDataType(dataType);
        definition.setDataFormat(dataFormat);
        definition.setClaimType(claimType);
        return definition;
    }
}