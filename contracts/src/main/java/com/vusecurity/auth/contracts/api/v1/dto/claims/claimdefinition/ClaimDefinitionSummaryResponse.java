package com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition;

import com.vusecurity.auth.contracts.enums.ClaimType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.UUID;

@Data
@Accessors(chain = true)
@Schema(description = "Summary information about a claim definition (used to avoid circular references)")
public class ClaimDefinitionSummaryResponse {
    @Schema(description = "Unique identifier of the claim definition",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    @Schema(description = "Unique code identifier for the claim definition",
            example = "email_address")
    private String code;

    @Schema(description = "Human-readable name for the claim definition",
            example = "Email Address")
    private String name;

    @Schema(description = "Detailed description of what this claim represents",
            example = "Primary email address of the user")
    private String description;

    @Schema(description = "Type of the claim",
            example = "USER_DEFINED | SYSTEM_DEFINED")
    private ClaimType claimType;

    @Schema(description = "Data type of the claim value",
            example = "STRING")
    private DataTypeEnum dataType;

    @Schema(description = "Regular expression or format specification for validation",
            example = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
    private String dataFormat;
}
