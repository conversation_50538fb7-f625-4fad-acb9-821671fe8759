package com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.business.domain.Business;
import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ClaimDefinitionJpaEntity business logic.
 * Tests pure domain logic with no external dependencies.
 */
class ClaimDefinitionJpaEntityTest {

    @Test
    void shouldCreateClaimDefinitionWithValidData() {
        // Given
        String code = "email";
        String name = "Email Address";
        String description = "User's email address";
        DataTypeEnum dataType = DataTypeEnum.STRING;
        String dataFormat = "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$";

        // When
        ClaimDefinitionJpaEntity claimDef = new ClaimDefinitionJpaEntity(code, name, description, dataType, dataFormat);

        // Then
        assertEquals(code, claimDef.getCode());
        assertEquals(name, claimDef.getName());
        assertEquals(description, claimDef.getDescription());
        assertEquals(dataType, claimDef.getDataType());
        assertEquals(dataFormat, claimDef.getDataFormat());
        assertNotNull(claimDef.getClaimSetMappings());
        assertTrue(claimDef.getClaimSetMappings().isEmpty());
        assertNull(claimDef.getIsAListOf());
    }

    @Test
    void shouldCreateClaimDefinitionWithId() {
        // Given
        UUID id = UUID.randomUUID();
        String code = "username";
        String name = "Username";
        String description = "User's username";
        DataTypeEnum dataType = DataTypeEnum.STRING;
        String dataFormat = null;

        // When
        ClaimDefinitionJpaEntity claimDef = new ClaimDefinitionJpaEntity(id, code, name, description, dataType, dataFormat);

        // Then
        assertEquals(id, claimDef.getId());
        assertEquals(code, claimDef.getCode());
        assertEquals(name, claimDef.getName());
        assertEquals(description, claimDef.getDescription());
        assertEquals(dataType, claimDef.getDataType());
        assertNull(claimDef.getDataFormat());
    }

    @Test
    void shouldCreateClaimDefinitionWithMinimalData() {
        // Given
        String code = "age";
        String name = "Age";
        DataTypeEnum dataType = DataTypeEnum.NUMERIC;

        // When
        ClaimDefinitionJpaEntity claimDef = new ClaimDefinitionJpaEntity(code, name, null, dataType, null);

        // Then
        assertEquals(code, claimDef.getCode());
        assertEquals(name, claimDef.getName());
        assertNull(claimDef.getDescription());
        assertEquals(dataType, claimDef.getDataType());
        assertNull(claimDef.getDataFormat());
    }

    @Test
    void shouldTrimCodeAndNameInConstructor() {
        // Given
        String codeWithWhitespace = "  email  ";
        String nameWithWhitespace = "  Email Address  ";

        // When
        ClaimDefinitionJpaEntity claimDef = new ClaimDefinitionJpaEntity(
                codeWithWhitespace, nameWithWhitespace, "Description", DataTypeEnum.STRING, null);

        // Then
        assertEquals("email", claimDef.getCode());
        assertEquals("Email Address", claimDef.getName());
    }

    @Test
    void shouldThrowException_WhenCodeIsNull() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> new ClaimDefinitionJpaEntity(null, "Name", "Description", DataTypeEnum.STRING, null)
        );
        assertEquals("code cannot be null or empty", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenCodeIsEmpty() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> new ClaimDefinitionJpaEntity("", "Name", "Description", DataTypeEnum.STRING, null)
        );
        assertEquals("code cannot be null or empty", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenCodeIsWhitespace() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> new ClaimDefinitionJpaEntity("   ", "Name", "Description", DataTypeEnum.STRING, null)
        );
        assertEquals("code cannot be null or empty", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenNameIsNull() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> new ClaimDefinitionJpaEntity("code", null, "Description", DataTypeEnum.STRING, null)
        );
        assertEquals("name cannot be null or empty", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenNameIsEmpty() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> new ClaimDefinitionJpaEntity("code", "", "Description", DataTypeEnum.STRING, null)
        );
        assertEquals("name cannot be null or empty", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenNameIsWhitespace() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> new ClaimDefinitionJpaEntity("code", "   ", "Description", DataTypeEnum.STRING, null)
        );
        assertEquals("name cannot be null or empty", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenDataTypeIsNull() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> new ClaimDefinitionJpaEntity("code", "Name", "Description", null, null)
        );
        assertEquals("dataType cannot be null", exception.getMessage());
    }





    @Test
    void shouldSetIsAListOfReference() {
        // Given
        ClaimDefinitionJpaEntity claimDef = createTestClaimDefinition();
        ClaimDefinitionJpaEntity listItemDef = new ClaimDefinitionJpaEntity(
                "list_item", "List Item", "Item definition", DataTypeEnum.STRING, null);

        // When
        claimDef.setIsAListOf(listItemDef);

        // Then
        assertEquals(listItemDef, claimDef.getIsAListOf());
    }

    @Test
    void shouldTestAllDataTypes() {
        // Test all enum values
        for (DataTypeEnum dataType : DataTypeEnum.values()) {
            // Given & When
            ClaimDefinitionJpaEntity claimDef = new ClaimDefinitionJpaEntity(
                    "test_" + dataType.name().toLowerCase(),
                    "Test " + dataType.name(),
                    "Test description",
                    dataType,
                    null
            );

            // Then
            assertEquals(dataType, claimDef.getDataType(), 
                    "Should correctly set DataType: " + dataType);
        }
    }

//    @Test
//    void shouldUseCodeForEqualsAndHashCode() {
//        // Given
//        ClaimDefinitionJpaEntity claimDef1 = new ClaimDefinitionJpaEntity(
//                "email", "Email", "Description", DataTypeEnum.STRING, null);
//        ClaimDefinitionJpaEntity claimDef2 = new ClaimDefinitionJpaEntity(
//                "email", "Different Name", "Different Description", DataTypeEnum.NUMERIC, "format");
//        ClaimDefinitionJpaEntity claimDef3 = new ClaimDefinitionJpaEntity(
//                "phone", "Phone", "Description", DataTypeEnum.STRING, null);
//
//        // When & Then
//        assertEquals(claimDef1, claimDef2, "Should be equal when codes are the same");
//        assertNotEquals(claimDef1, claimDef3, "Should not be equal when codes are different");
//        assertEquals(claimDef1.hashCode(), claimDef2.hashCode(), "Hash codes should be equal when codes are the same");
//    }

    @Test
    void shouldHandleComplexDataFormats() {
        // Test various data format patterns
        String[] dataFormats = {
                null,
                "",
                "^[a-zA-Z0-9]+$",
                "^\\d{4}-\\d{2}-\\d{2}$",
                "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$",
                "Very long format string with special characters !@#$%^&*()"
        };

        for (String dataFormat : dataFormats) {
            // Given & When
            ClaimDefinitionJpaEntity claimDef = new ClaimDefinitionJpaEntity(
                    "test_format", "Test Format", "Test description", 
                    DataTypeEnum.STRING, dataFormat);

            // Then
            assertEquals(dataFormat, claimDef.getDataFormat(), 
                    "Should correctly set data format: " + dataFormat);
        }
    }

    // Helper methods
    private ClaimDefinitionJpaEntity createTestClaimDefinition() {
        return new ClaimDefinitionJpaEntity(
                "test_claim", "Test Claim", "Test description", DataTypeEnum.STRING, null);
    }
}
