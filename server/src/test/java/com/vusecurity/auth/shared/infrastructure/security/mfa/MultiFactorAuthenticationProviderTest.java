package com.vusecurity.auth.shared.infrastructure.security.mfa;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.time.Instant;
import java.util.*;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.auth.shared.infrastructure.migration.initial.DataSeedConstants;
import com.vusecurity.auth.shared.infrastructure.security.EngagementContext;
import com.vusecurity.auth.shared.infrastructure.security.oidc.AccountDetailService;
import com.vusecurity.auth.shared.infrastructure.security.oidc.AccountUserDetails;
import com.vusecurity.business.domain.Business;

/**
 * Unit tests for MultiFactorAuthenticationProvider.
 * Tests the multi-factor authentication provider functionality.
 */
@ExtendWith(MockitoExtension.class)
class MultiFactorAuthenticationProviderTest {

    @Mock
    private FactorServerClient factorServerClient;

    @Mock
    private AccountDetailService accountDetailService;

    private MultiFactorAuthenticationProvider provider;
    private UUID testAccountId;
    private AccountUserDetails testUserDetails;
    private MockedStatic<EngagementContext> engagementContextMock;

    @BeforeEach
    void setUp() {
        provider = new MultiFactorAuthenticationProvider(factorServerClient, accountDetailService);
        testAccountId = UUID.randomUUID();
        testUserDetails = createTestUserDetails();

        // Mock EngagementContext to return null by default (will use system default)
        engagementContextMock = mockStatic(EngagementContext.class);
        engagementContextMock.when(EngagementContext::getChannelId).thenReturn(null);
    }

    @AfterEach
    void tearDown() {
        if (engagementContextMock != null) {
            engagementContextMock.close();
        }
    }

    @Test
    void shouldSupportMultiFactorAuthenticationToken() {
        // When & Then
        assertTrue(provider.supports(MultiFactorAuthenticationToken.class));
    }

    @Test
    void shouldNotSupportOtherAuthenticationTypes() {
        // When & Then
        assertFalse(provider.supports(UsernamePasswordAuthenticationToken.class));
        assertFalse(provider.supports(Authentication.class));
    }

    @Test
    void shouldReturnNullForNonMultiFactorToken() {
        // Given
        UsernamePasswordAuthenticationToken otherToken = new UsernamePasswordAuthenticationToken("user", "pass");

        // When
        Authentication result = provider.authenticate(otherToken);

        // Then
        assertNull(result);
    }

    @Test
    void shouldAuthenticateWithValidPasswordAndTotp() {
        // Given
        String password = "testPassword";
        String totp = "123456";
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(testAccountId, totp, password);

        when(accountDetailService.loadUserByUsername(testAccountId.toString())).thenReturn(testUserDetails);
        when(factorServerClient.validatePassword(eq(testAccountId), eq(password), anyString(), eq(DataSeedConstants.SYSTEM_CHANNEL_ID.toString()))).thenReturn(true);
        when(factorServerClient.validateTotp(eq(testAccountId), eq(totp), anyString(), eq(DataSeedConstants.SYSTEM_CHANNEL_ID.toString()))).thenReturn(true);

        // When
        Authentication result = provider.authenticate(token);

        // Then
        assertNotNull(result);
        assertTrue(result.isAuthenticated());
        assertInstanceOf(MultiFactorAuthenticationToken.class, result);
        assertEquals(testAccountId, ((MultiFactorAuthenticationToken) result).getAccountId());
        assertNotNull(result.getAuthorities());
    }

    @Test
    void shouldAuthenticateWithValidPasswordOnly() {
        // Given
        String password = "testPassword";
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(testAccountId, null, password);

        when(accountDetailService.loadUserByUsername(testAccountId.toString())).thenReturn(testUserDetails);
        when(factorServerClient.validatePassword(eq(testAccountId), eq(password), anyString(), eq(DataSeedConstants.SYSTEM_CHANNEL_ID.toString()))).thenReturn(true);

        // When
        Authentication result = provider.authenticate(token);

        // Then
        assertNotNull(result);
        assertTrue(result.isAuthenticated());
        verify(factorServerClient, never()).validateTotp(any(), any(), any(), any());
    }

    @Test
    void shouldAuthenticateWithValidTotpOnly() {
        // Given
        String totp = "123456";
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(testAccountId, totp, null);

        when(accountDetailService.loadUserByUsername(testAccountId.toString())).thenReturn(testUserDetails);
        when(factorServerClient.validateTotp(eq(testAccountId), eq(totp), anyString(), eq(DataSeedConstants.SYSTEM_CHANNEL_ID.toString()))).thenReturn(true);

        // When
        Authentication result = provider.authenticate(token);

        // Then
        assertNotNull(result);
        assertTrue(result.isAuthenticated());
        verify(factorServerClient, never()).validatePassword(any(), any(), any(), any());
    }

    @Test
    void shouldThrowExceptionWhenAccountIdIsNull() {
        // Given
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(null, "123456", "password");

        // When & Then
        BadCredentialsException exception = assertThrows(BadCredentialsException.class, 
            () -> provider.authenticate(token));
        assertEquals("Missing required authentication factors", exception.getMessage());
    }

    @Test
    void shouldThrowExceptionWhenBothTotpAndPasswordAreNull() {
        // Given
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(testAccountId, null, null);

        // When & Then
        BadCredentialsException exception = assertThrows(BadCredentialsException.class, 
            () -> provider.authenticate(token));
        assertEquals("Missing required authentication factors", exception.getMessage());
    }

    @Test
    void shouldThrowExceptionWhenPasswordValidationFails() {
        // Given
        String password = "wrongPassword";
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(testAccountId, null, password);

        when(accountDetailService.loadUserByUsername(testAccountId.toString())).thenReturn(testUserDetails);
        when(factorServerClient.validatePassword(eq(testAccountId), eq(password), anyString(), eq(DataSeedConstants.SYSTEM_CHANNEL_ID.toString()))).thenReturn(false);

        // When & Then
        BadCredentialsException exception = assertThrows(BadCredentialsException.class,
            () -> provider.authenticate(token));
        assertEquals("Invalid password", exception.getMessage());
    }

    @Test
    void shouldThrowExceptionWhenTotpValidationFails() {
        // Given
        String totp = "wrongTotp";
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(testAccountId, totp, null);

        when(accountDetailService.loadUserByUsername(testAccountId.toString())).thenReturn(testUserDetails);
        when(factorServerClient.validateTotp(eq(testAccountId), eq(totp), anyString(), eq(DataSeedConstants.SYSTEM_CHANNEL_ID.toString()))).thenReturn(false);

        // When & Then
        BadCredentialsException exception = assertThrows(BadCredentialsException.class,
            () -> provider.authenticate(token));
        assertEquals("Invalid TOTP", exception.getMessage());
    }

    @Test
    void shouldThrowExceptionWhenPasswordValidButTotpInvalid() {
        // Given
        String password = "validPassword";
        String totp = "invalidTotp";
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(testAccountId, totp, password);

        when(accountDetailService.loadUserByUsername(testAccountId.toString())).thenReturn(testUserDetails);
        when(factorServerClient.validatePassword(eq(testAccountId), eq(password), anyString(), eq(DataSeedConstants.SYSTEM_CHANNEL_ID.toString()))).thenReturn(true);
        when(factorServerClient.validateTotp(eq(testAccountId), eq(totp), anyString(), eq(DataSeedConstants.SYSTEM_CHANNEL_ID.toString()))).thenReturn(false);

        // When & Then
        BadCredentialsException exception = assertThrows(BadCredentialsException.class,
            () -> provider.authenticate(token));
        assertEquals("Invalid TOTP", exception.getMessage());
    }

    @Test
    void shouldThrowExceptionWhenAccountNotFound() {
        // Given
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(testAccountId, null, "password");
        
        when(accountDetailService.loadUserByUsername(testAccountId.toString()))
            .thenThrow(new UsernameNotFoundException("Account not found"));

        // When & Then
        BadCredentialsException exception = assertThrows(BadCredentialsException.class, 
            () -> provider.authenticate(token));
        assertEquals("Invalid account", exception.getMessage());
    }

    @Test
    void shouldThrowExceptionWhenUserDetailsServiceThrowsException() {
        // Given
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(testAccountId, null, "password");
        
        when(accountDetailService.loadUserByUsername(testAccountId.toString()))
            .thenThrow(new RuntimeException("Database error"));

        // When & Then
        BadCredentialsException exception = assertThrows(BadCredentialsException.class, 
            () -> provider.authenticate(token));
        assertEquals("Authentication failed", exception.getMessage());
    }

    @Test
    void shouldUseDefaultChannelIdWhenEngagementContextIsNull() {
        // Given
        String password = "testPassword";
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(testAccountId, null, password);

        when(accountDetailService.loadUserByUsername(testAccountId.toString())).thenReturn(testUserDetails);
        when(factorServerClient.validatePassword(eq(testAccountId), eq(password), anyString(), eq(DataSeedConstants.SYSTEM_CHANNEL_ID.toString()))).thenReturn(true);

        // When
        Authentication result = provider.authenticate(token);

        // Then
        assertNotNull(result);
        assertTrue(result.isAuthenticated());
        verify(factorServerClient).validatePassword(eq(testAccountId), eq(password), anyString(), eq(DataSeedConstants.SYSTEM_CHANNEL_ID.toString()));
    }

    @Test
    void shouldUseCustomChannelIdFromEngagementContext() {
        // Given
        String password = "testPassword";
        UUID customChannelId = UUID.randomUUID();
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(testAccountId, null, password);

        // Mock EngagementContext to return custom channel ID
        engagementContextMock.when(EngagementContext::getChannelId).thenReturn(customChannelId);

        when(accountDetailService.loadUserByUsername(testAccountId.toString())).thenReturn(testUserDetails);
        when(factorServerClient.validatePassword(eq(testAccountId), eq(password), anyString(), eq(customChannelId.toString()))).thenReturn(true);

        // When
        Authentication result = provider.authenticate(token);

        // Then
        assertNotNull(result);
        assertTrue(result.isAuthenticated());
        verify(factorServerClient).validatePassword(eq(testAccountId), eq(password), anyString(), eq(customChannelId.toString()));
    }

    @Test
    void shouldCopyDetailsFromOriginalAuthentication() {
        // Given
        String password = "testPassword";
        MultiFactorAuthenticationToken token = new MultiFactorAuthenticationToken(testAccountId, null, password);
        Object details = "test-details";
        token.setDetails(details);

        when(accountDetailService.loadUserByUsername(testAccountId.toString())).thenReturn(testUserDetails);
        when(factorServerClient.validatePassword(eq(testAccountId), eq(password), anyString(), eq(DataSeedConstants.SYSTEM_CHANNEL_ID.toString()))).thenReturn(true);

        // When
        Authentication result = provider.authenticate(token);

        // Then
        assertNotNull(result);
        assertEquals(details, result.getDetails());
    }

    // Helper methods
    private AccountUserDetails createTestUserDetails() {
        Business business = new Business();
        business.setId(UUID.randomUUID());
        business.setName("Test Business");

        IdentityJpaEntity identity = new IdentityJpaEntity();
        identity.setId(UUID.randomUUID());
        identity.setName("John Doe");

        IdentityProviderJpaEntity identityProvider = new IdentityProviderJpaEntity();
        identityProvider.setId(UUID.randomUUID());
        identityProvider.setName("Test Provider");

        AccountJpaEntity account = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        account.setId(testAccountId);
        account.setLifecycleState(AccountLifecycleState.ACTIVE);
        account.setUpdatedAt(Instant.now());
        account.setCreatedAt(Instant.now().minusSeconds(3600));

        Map<String, Object> claimValues = new HashMap<>();
        return new AccountUserDetails(account, claimValues);
    }


}
