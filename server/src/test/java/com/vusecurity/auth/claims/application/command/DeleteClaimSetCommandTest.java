package com.vusecurity.auth.claims.application.command;

import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class DeleteClaimSetCommandTest {

    @Test
    void validate_happy_path() {
        DeleteClaimSetCommand cmd = new DeleteClaimSetCommand(UUID.randomUUID());
        assertDoesNotThrow(cmd::validate);
    }

    @Test
    void validate_throws_when_id_null() {
        DeleteClaimSetCommand cmd = new DeleteClaimSetCommand(null);
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("id is required"));
    }
}
