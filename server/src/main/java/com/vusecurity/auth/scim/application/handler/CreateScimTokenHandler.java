package com.vusecurity.auth.scim.application.handler;

import com.vusecurity.auth.authorization.application.exception.*;
import com.vusecurity.auth.identities.application.exception.IdentityProviderNotFoundException;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityProviderRepository;
import com.vusecurity.auth.scim.application.command.CreateScimTokenCommand;
import com.vusecurity.auth.scim.application.exception.ScimTokenNameAlreadyExistsException;
import com.vusecurity.auth.scim.application.service.ScimTokensService;
import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimTokenJpaEntity;
import com.vusecurity.auth.shared.util.DatabaseConstraintUtils;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.domain.services.BusinessService;
import com.vusecurity.business.usecases.exceptions.BusinessNotFoundException;
import com.vusecurity.core.commons.Auditable.Status;

import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CreateScimTokenHandler {

    private static final Logger logger = LoggerFactory.getLogger(CreateScimTokenHandler.class);

    private final ScimTokensService scimTokensService;
    private final BusinessService businessService;
    private final IdentityProviderRepository identityProviderRepository;

    @Transactional
    public ScimTokenJpaEntity createToken(CreateScimTokenCommand command) {
        logger.debug("Creating SCIM token: {}", command.name());

        // Validate command
        command.validate();

        Business business = businessService.getById(command.businessId()).orElseThrow(() -> new BusinessNotFoundException());
        IdentityProviderJpaEntity provider = identityProviderRepository.findById(command.provierId()).orElseThrow(() -> new IdentityProviderNotFoundException(command.provierId().toString()));
        
        // Create token entity
        ScimTokenJpaEntity newScimToken = new ScimTokenJpaEntity();
        newScimToken.setName(command.name());
        newScimToken.setDescription(command.description());
        newScimToken.setBusiness(business);
        newScimToken.setIdentityProvider(provider);
        newScimToken.setStatus(Status.ACTIVE);
        newScimToken.setToken(scimTokensService.hashToken(command.token()));

        // Save token with constraint handling for unique token name
        ScimTokenJpaEntity savedToken = DatabaseConstraintUtils.executeWithConstraintHandling(
                () -> scimTokensService.save(newScimToken),
                "uk_scim_tokens_name", // Unique constraint on token name
                () -> new ScimTokenNameAlreadyExistsException("Token with name '" + command.name() + "' already exists")
        );
        logger.debug("SCIM token created successfully: {}", savedToken.getId());

        return savedToken;
    }
} 