package com.vusecurity.auth.client.interceptor;

import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;

@RequiredArgsConstructor
public class ApiKeyInterceptor implements ClientHttpRequestInterceptor {
    
    private static final Logger log = LoggerFactory.getLogger(ApiKeyInterceptor.class);
    
    private static final String API_KEY_HEADER = "x-apikey";
    
    private final String apiKey;
    
    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, 
                                       ClientHttpRequestExecution execution) throws IOException {
        log.debug("Adding API key header '{}' with value: {}", 
                API_KEY_HEADER, 
                apiKey != null ? "***masked***" : "null");
        request.getHeaders().add(API_KEY_HEADER, apiKey);
        return execution.execute(request, body);
    }
}