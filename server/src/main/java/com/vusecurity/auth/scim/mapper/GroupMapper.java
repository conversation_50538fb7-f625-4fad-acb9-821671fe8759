package com.vusecurity.auth.scim.mapper;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.directory.scim.spec.resources.GroupMembership;
import org.apache.directory.scim.spec.resources.GroupMembership.Type;
import org.apache.directory.scim.spec.resources.ScimGroup;

import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.GroupMembershipJpaEntity;

public class GroupMapper {

    private static final String EXTERNAL_ID = "externalId";

    private GroupMapper(){
        //NoOP
    }

    public static ScimGroup toGroup(AccountGroupJpaEntity entity) {
        return toGroup(entity, true);
    }

    public static ScimGroup toGroup(AccountGroupJpaEntity entity, boolean includeMembers) {
        ScimGroup groupResource = new ScimGroup();
        groupResource
        .setDisplayName(entity.getName())
        .setId(entity.getId().toString())
        .setMeta(
            groupResource.getMeta()
            .setCreated(LocalDateTime.ofInstant(entity.getCreatedAt(), ZoneId.systemDefault()))
            .setLastModified(LocalDateTime.ofInstant(entity.getUpdatedAt(), ZoneId.systemDefault()))
        );
        if (entity.getMetadata().containsKey(EXTERNAL_ID)){
            groupResource.setExternalId(entity.getMetadata().get(EXTERNAL_ID).toString());
        }
        if (includeMembers){
            groupResource.setMembers(idsToGroupMemberShip(entity.getMembers().stream().map(GroupMembershipJpaEntity::getAccountId).toList()));
        }

        return groupResource;
    }

    public static List<GroupMembership> idsToGroupMemberShip(List<UUID> uuidList){
        return uuidList.stream().map(uuid -> 
        new GroupMembership()
            .setType(Type.USER)
            .setValue(uuid.toString())
            .setRef("/scim/v2/Users/".concat(uuid.toString()))
        ).toList();
    }

    public static AccountGroupJpaEntity fromGroupWithoutMembers(ScimGroup resource){
        AccountGroupJpaEntity account = new AccountGroupJpaEntity();
        account.setName(resource.getDisplayName());
        account.setDescription("SCIM Group " + resource.getExternalId());
        if (resource.getExternalId() != null){
            account.setMetadata( Map.of(EXTERNAL_ID, resource.getExternalId()));
        }
        if (resource.getId() != null && !resource.getId().isBlank()){
            account.setId(UUID.fromString(resource.getId()));
        }

        return account;
    }

    public static List<UUID> groupMembershipToUUIDs(List<GroupMembership> groupMemberships) throws RuntimeException{
        List<UUID> directMemberList = new ArrayList<>();
        if (groupMemberships != null && !groupMemberships.isEmpty()){
            for (GroupMembership member : groupMemberships) {
                try {
                    //TODO: Add support for indirect members or sub-groups
                    //For now support Direct Members only. If no type is defined we asume is an User.
                    if(member.getType() == null || member.getType().equals(Type.USER)){
                        directMemberList.add(UUID.fromString(member.getValue()));
                    }
                } catch (Exception e){
                    throw new RuntimeException("GroupMapper - groupMembershipToUUIDs - extract GroupMember - " + e.getMessage());
                }
            }
        }
        return directMemberList;
    }

    //Static maping for groups
    //SCIM field <-> database
    public static final Map<String, List<String>> groupMapping = Map.of(
        "id", List.of("id"),
        "displayName", List.of("name"),
        EXTERNAL_ID, List.of("metadata")
    );

} 