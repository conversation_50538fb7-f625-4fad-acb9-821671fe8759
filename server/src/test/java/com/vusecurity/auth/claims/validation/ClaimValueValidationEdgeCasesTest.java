package com.vusecurity.auth.claims.validation;

import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

class ClaimValueValidationEdgeCasesTest {

    @Test
    void string_shouldHandleUnicodeCharacters() {
        String unicode = "Hello 世界 🌍 Café naïve résumé";
        assertTrue(DataTypeEnum.STRING.isValid(unicode));
        assertEquals(unicode, DataTypeEnum.STRING.normalize(unicode));
    }

    @Test
    void string_shouldHandleVeryLongStrings() {
        String longString = "a".repeat(10000);
        assertTrue(DataTypeEnum.STRING.isValid(longString));
        assertEquals(longString, DataTypeEnum.STRING.normalize(longString));
    }

    @ParameterizedTest
    @NullAndEmptySource
    void string_shouldHandleNullAndEmpty(String value) {
        if (value == null) {
            assertThrows(NullPointerException.class, () -> DataTypeEnum.STRING.isValid(value));
        } else {
            assertTrue(DataTypeEnum.STRING.isValid(value));
            assertEquals("", DataTypeEnum.STRING.normalize(value));
        }
    }

    @Test
    void numeric_shouldHandleVeryLargeNumbers() {
        assertTrue(DataTypeEnum.NUMERIC.isValid("999999999999999999999999999999"));
        assertTrue(DataTypeEnum.NUMERIC.isValid("1.7976931348623157E+308")); // Close to Double.MAX_VALUE
        assertTrue(DataTypeEnum.NUMERIC.isValid("-1.7976931348623157E+308"));
    }

    @Test
    void numeric_shouldHandleVerySmallNumbers() {
        assertTrue(DataTypeEnum.NUMERIC.isValid("0.000000000000000001"));
        assertTrue(DataTypeEnum.NUMERIC.isValid("4.9E-324")); // Close to Double.MIN_VALUE
        assertTrue(DataTypeEnum.NUMERIC.isValid("-4.9E-324"));
    }

    @Test
    void numeric_shouldHandleScientificNotationEdgeCases() {
        assertTrue(DataTypeEnum.NUMERIC.isValid("1E1"));
        assertTrue(DataTypeEnum.NUMERIC.isValid("1e+1"));
        assertTrue(DataTypeEnum.NUMERIC.isValid("1e-1"));
        assertTrue(DataTypeEnum.NUMERIC.isValid("1.0E1"));
        assertTrue(DataTypeEnum.NUMERIC.isValid("1.0e+1"));
        assertTrue(DataTypeEnum.NUMERIC.isValid("1.0e-1"));
    }

    @Test
    void numeric_shouldRejectInvalidScientificNotation() {
        assertFalse(DataTypeEnum.NUMERIC.isValid("1E"));
        assertFalse(DataTypeEnum.NUMERIC.isValid("E1"));
        assertFalse(DataTypeEnum.NUMERIC.isValid("1.E"));
        assertFalse(DataTypeEnum.NUMERIC.isValid(".E1"));
        assertFalse(DataTypeEnum.NUMERIC.isValid("1e1.5"));
    }

    @Test
    void numeric_shouldNormalizeSpecialCases() {
        assertEquals("0", DataTypeEnum.NUMERIC.normalize("0.000"));
        assertEquals("1", DataTypeEnum.NUMERIC.normalize("1.000"));
        assertEquals("-1", DataTypeEnum.NUMERIC.normalize("-1.000"));
        assertEquals("1000", DataTypeEnum.NUMERIC.normalize("1E3"));
        assertEquals("0.001", DataTypeEnum.NUMERIC.normalize("1E-3"));
    }

    @ParameterizedTest
    @ValueSource(strings = {"TRUE", "True", "tRuE", "FALSE", "False", "fAlSe"})
    void bool_shouldHandleCaseInsensitivity(String value) {
        assertTrue(DataTypeEnum.BOOL.isValid(value));
        String expected = value.toLowerCase().equals("true") ? "true" : "false";
        assertEquals(expected, DataTypeEnum.BOOL.normalize(value));
    }

    @Test
    void bool_shouldRejectPartialMatches() {
        assertFalse(DataTypeEnum.BOOL.isValid("tru"));
        assertFalse(DataTypeEnum.BOOL.isValid("fals"));
        assertFalse(DataTypeEnum.BOOL.isValid("true "));
        assertFalse(DataTypeEnum.BOOL.isValid(" false"));
        assertFalse(DataTypeEnum.BOOL.isValid("true1"));
        assertFalse(DataTypeEnum.BOOL.isValid("1true"));
    }

    @Test
    void date_shouldHandleBoundaryDates() {
        // Test boundary dates
        assertTrue(DataTypeEnum.DATE.isValid("0001-01-01")); // Minimum date
        assertTrue(DataTypeEnum.DATE.isValid("9999-12-31")); // Maximum date
        assertTrue(DataTypeEnum.DATE.isValid("1900-01-01")); // Century boundary
        assertTrue(DataTypeEnum.DATE.isValid("2000-01-01")); // Millennium boundary
    }

    @Test
    void date_shouldRejectInvalidBoundaryDates() {
        assertTrue(DataTypeEnum.DATE.isValid("0000-01-01")); // Year 0 is valid in ISO 8601
        assertFalse(DataTypeEnum.DATE.isValid("2023-00-01")); // Month 0
        assertFalse(DataTypeEnum.DATE.isValid("2023-01-00")); // Day 0
        assertFalse(DataTypeEnum.DATE.isValid("2023-13-01")); // Month 13
        assertFalse(DataTypeEnum.DATE.isValid("2023-01-32")); // Day 32
    }

    @Test
    void date_shouldHandleLeapYearEdgeCases() {
        // Leap years
        assertTrue(DataTypeEnum.DATE.isValid("2000-02-29")); // Divisible by 400
        assertTrue(DataTypeEnum.DATE.isValid("2004-02-29")); // Divisible by 4
        assertTrue(DataTypeEnum.DATE.isValid("1600-02-29")); // Divisible by 400
        
        // Non-leap years
        assertFalse(DataTypeEnum.DATE.isValid("2001-02-29")); // Not divisible by 4
        assertFalse(DataTypeEnum.DATE.isValid("1900-02-29")); // Divisible by 100 but not 400
        assertFalse(DataTypeEnum.DATE.isValid("2100-02-29")); // Divisible by 100 but not 400
    }

    @Test
    void datetime_shouldHandleTimezoneOffsets() {
        // Valid timezone offsets
        assertTrue(DataTypeEnum.DATETIME.isValid("2023-01-01T00:00:00+00:00"));
        assertTrue(DataTypeEnum.DATETIME.isValid("2023-01-01T00:00:00-12:00"));
        assertTrue(DataTypeEnum.DATETIME.isValid("2023-01-01T00:00:00+14:00"));
        assertTrue(DataTypeEnum.DATETIME.isValid("2023-01-01T00:00:00+05:30")); // India timezone
        assertTrue(DataTypeEnum.DATETIME.isValid("2023-01-01T00:00:00-09:30")); // Marquesas timezone
    }

    @Test
    void datetime_shouldRejectInvalidTimezoneOffsets() {
        assertFalse(DataTypeEnum.DATETIME.isValid("2023-01-01T00:00:00+15:00")); // Beyond +14:00
        assertTrue(DataTypeEnum.DATETIME.isValid("2023-01-01T00:00:00-13:00")); // Within -14:00 limit
        assertFalse(DataTypeEnum.DATETIME.isValid("2023-01-01T00:00:00+12:60")); // Invalid minutes
        assertFalse(DataTypeEnum.DATETIME.isValid("2023-01-01T00:00:00+24:00")); // Invalid hour
    }

    @Test
    void datetime_shouldHandleFractionalSeconds() {
        assertTrue(DataTypeEnum.DATETIME.isValid("2023-01-01T00:00:00.0Z"));
        assertTrue(DataTypeEnum.DATETIME.isValid("2023-01-01T00:00:00.123Z"));
        assertTrue(DataTypeEnum.DATETIME.isValid("2023-01-01T00:00:00.123456Z"));
        assertTrue(DataTypeEnum.DATETIME.isValid("2023-01-01T00:00:00.123456789Z"));
    }

    @Test
    void image_shouldHandleBase64EdgeCases() {
        // Valid base64 with padding
        assertTrue(DataTypeEnum.IMAGE.isValid("data:image/png;base64,AB=="));
        assertTrue(DataTypeEnum.IMAGE.isValid("data:image/png;base64,ABC="));
        
        // Valid base64 without padding
        assertTrue(DataTypeEnum.IMAGE.isValid("data:image/png;base64,ABCD"));
        
        // Different image types
        assertTrue(DataTypeEnum.IMAGE.isValid("data:image/jpeg;base64,ABCD"));
        assertTrue(DataTypeEnum.IMAGE.isValid("data:image/gif;base64,ABCD"));
        assertTrue(DataTypeEnum.IMAGE.isValid("data:image/webp;base64,ABCD"));
        assertTrue(DataTypeEnum.IMAGE.isValid("data:image/svg+xml;base64,ABCD"));
    }

    @Test
    void image_shouldRejectInvalidBase64() {
        assertFalse(DataTypeEnum.IMAGE.isValid("data:image/png;base64,ABC!")); // Invalid character
        assertFalse(DataTypeEnum.IMAGE.isValid("data:image/png;base64,AB")); // Too short
        assertFalse(DataTypeEnum.IMAGE.isValid("data:image/png;base64,")); // Empty
    }

    @Test
    void image_shouldHandleUrlEdgeCases() {
        // Different protocols
        assertTrue(DataTypeEnum.IMAGE.isValid("https://example.com/image.png"));
        assertTrue(DataTypeEnum.IMAGE.isValid("http://example.com/image.jpg"));
        
        // Different file extensions
        assertTrue(DataTypeEnum.IMAGE.isValid("https://example.com/image.jpeg"));
        assertTrue(DataTypeEnum.IMAGE.isValid("https://example.com/image.gif"));
        assertTrue(DataTypeEnum.IMAGE.isValid("https://example.com/image.bmp"));
        
        // URLs with query parameters
        assertTrue(DataTypeEnum.IMAGE.isValid("https://example.com/image.png?v=1"));
        assertTrue(DataTypeEnum.IMAGE.isValid("https://example.com/image.jpg?width=100&height=200"));
    }

    @Test
    void image_shouldRejectInvalidUrls() {
        assertFalse(DataTypeEnum.IMAGE.isValid("ftp://example.com/image.png")); // Wrong protocol
        assertFalse(DataTypeEnum.IMAGE.isValid("https://example.com/image.txt")); // Wrong extension
        assertFalse(DataTypeEnum.IMAGE.isValid("https://example.com/image")); // No extension
        assertFalse(DataTypeEnum.IMAGE.isValid("example.com/image.png")); // No protocol
    }

    @Test
    void array_shouldHandleComplexJsonArrays() {
        assertTrue(DataTypeEnum.ARRAY.isValid("[\"item1\", \"item2\", \"item3\"]"));
        assertTrue(DataTypeEnum.ARRAY.isValid("[1, 2, 3, 4, 5]"));
        assertTrue(DataTypeEnum.ARRAY.isValid("[true, false, true]"));
        assertTrue(DataTypeEnum.ARRAY.isValid("[\"mixed\", 123, true]"));
        assertTrue(DataTypeEnum.ARRAY.isValid("[{\"key\": \"value\"}, {\"key2\": \"value2\"}]"));
    }

    @Test
    void array_shouldHandleNestedArrays() {
        assertTrue(DataTypeEnum.ARRAY.isValid("[[1, 2], [3, 4]]"));
        assertTrue(DataTypeEnum.ARRAY.isValid("[\"item1\", [\"nested1\", \"nested2\"]]"));
    }

    @Test
    void array_shouldHandleCommaSeparatedValues() {
        assertTrue(DataTypeEnum.ARRAY.isValid("item1,item2,item3"));
        assertTrue(DataTypeEnum.ARRAY.isValid("value1, value2, value3")); // With spaces
        assertTrue(DataTypeEnum.ARRAY.isValid("1,2,3,4,5"));
        assertTrue(DataTypeEnum.ARRAY.isValid("true,false,true"));
    }

    @Test
    void array_shouldHandleEmptyArrays() {
        assertTrue(DataTypeEnum.ARRAY.isValid("[]"));
        assertTrue(DataTypeEnum.ARRAY.isValid(",")); // Single comma
        assertTrue(DataTypeEnum.ARRAY.isValid(",,,")); // Multiple commas
    }

    @Test
    void array_shouldRejectNonArrayFormats() {
        assertFalse(DataTypeEnum.ARRAY.isValid("single-item")); // No separator
        assertFalse(DataTypeEnum.ARRAY.isValid("item1;item2")); // Wrong separator
        assertFalse(DataTypeEnum.ARRAY.isValid("item1|item2")); // Wrong separator
        assertFalse(DataTypeEnum.ARRAY.isValid("")); // Empty string
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "  123.45  ",    // NUMERIC with whitespace
        "  true  ",      // BOOL with whitespace  
        "  2023-01-01  ", // DATE with whitespace
        "  test value  "  // STRING with whitespace
    })
    void allTypes_shouldHandleWhitespaceGracefully(String value) {
        // STRING should trim
        assertEquals("test value", DataTypeEnum.STRING.normalize("  test value  "));
        
        // Other types should handle whitespace in their validation
        String trimmed = value.trim();
        if (trimmed.equals("123.45")) {
            assertTrue(DataTypeEnum.NUMERIC.isValid(trimmed));
        } else if (trimmed.equals("true")) {
            assertTrue(DataTypeEnum.BOOL.isValid(trimmed));
        } else if (trimmed.equals("2023-01-01")) {
            assertTrue(DataTypeEnum.DATE.isValid(trimmed));
        }
    }

    @Test
    void normalization_shouldBeIdempotent() {
        // Normalizing a normalized value should return the same value
        String[] testValues = {
            "123.45", // NUMERIC
            "true",   // BOOL
            "2023-01-01", // DATE
            "test"    // STRING
        };
        
        for (String value : testValues) {
            for (DataTypeEnum type : DataTypeEnum.values()) {
                if (type.isValid(value)) {
                    String normalized = type.normalize(value);
                    String doubleNormalized = type.normalize(normalized);
                    assertEquals(normalized, doubleNormalized, 
                        "Normalization should be idempotent for " + type + " with value: " + value);
                }
            }
        }
    }

    @Test
    void validation_shouldBeConsistentWithNormalization() {
        // If a value is valid, its normalized form should also be valid
        String[] testValues = {
            "123.00",     // NUMERIC that will be normalized
            "TRUE",       // BOOL that will be normalized
            "2023-01-01", // DATE (already normalized)
            "  test  "    // STRING with whitespace
        };
        
        for (String value : testValues) {
            for (DataTypeEnum type : DataTypeEnum.values()) {
                if (type.isValid(value)) {
                    String normalized = type.normalize(value);
                    assertTrue(type.isValid(normalized), 
                        "Normalized value should still be valid for " + type + 
                        ". Original: '" + value + "', Normalized: '" + normalized + "'");
                }
            }
        }
    }

    @Test
    void errorHandling_shouldHandleNullInputsGracefully() {
        for (DataTypeEnum type : DataTypeEnum.values()) {
            assertThrows(NullPointerException.class, () -> type.isValid(null),
                "Type " + type + " should throw NPE for null validation");
            assertThrows(NullPointerException.class, () -> type.normalize(null),
                "Type " + type + " should throw NPE for null normalization");
        }
    }

    @Test
    void performance_shouldHandleLargeInputsEfficiently() {
        // Test with large strings to ensure no catastrophic backtracking in regex
        String largeString = "a".repeat(1000000); // 1MB string
        
        // These should complete quickly without hanging
        assertTimeoutPreemptively(java.time.Duration.ofSeconds(1), () -> {
            DataTypeEnum.STRING.isValid(largeString);
            DataTypeEnum.STRING.normalize(largeString);
        });
        
        // Test numeric with many digits
        String largeNumber = "1" + "0".repeat(1000);
        assertTimeoutPreemptively(java.time.Duration.ofSeconds(1), () -> {
            DataTypeEnum.NUMERIC.isValid(largeNumber);
        });
    }
}