package com.vusecurity.auth.contracts.api.v1.dto.identities;

import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BusinessBasicInfoResponse;
import com.vusecurity.auth.contracts.enums.IdentityType;
import com.vusecurity.auth.contracts.enums.IdentityLifecycleState;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "Identity information response")
public class IdentityResponse extends BaseResponse {
    @Schema(description = "Unique identifier of the identity",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    @Schema(description = "Name of the identity",
            example = "<PERSON>")
    private String name;

    @Schema(description = "Type of identity",
            example = "PERSON")
    private IdentityType identityType;

    @Schema(description = "Additional metadata for the identity as key-value pairs",
            example = "{\"email\": \"<EMAIL>\", \"phone\": \"+**********\"}")
    private Map<String, Object> metadata;

    @Schema(description = "Current lifecycle state of the identity",
            example = "ACTIVE")
    private IdentityLifecycleState lifecycleState;

    @Schema(description = "List of accounts associated with this identity")
    private List<AccountResponse> accounts;

    @Schema(description = "List of businesses associated with this identity")
    private List<BusinessBasicInfoResponse> businesses;
}
