package com.vusecurity.auth.authorization.api.handler;


import com.vusecurity.auth.authorization.application.exception.PermissionNotExistsException;
import com.vusecurity.auth.authorization.application.exception.PermissionNotFoundException;
import com.vusecurity.auth.authorization.application.exception.RoleNameAlreadyExistException;
import com.vusecurity.auth.authorization.application.exception.RoleNotFoundException;
import com.vusecurity.core.commons.ApiMessage;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@ControllerAdvice(assignableTypes = {com.vusecurity.auth.authorization.api.controller.RolesController.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class RoleErrorControllerHandler {

    @ExceptionHandler(RoleNameAlreadyExistException.class)
    public ResponseEntity<ApiMessage> handleAlreadyExistsException(RoleNameAlreadyExistException ex) {

        ApiMessage error = new ApiMessage(4000, ex.getMessage());
        return ResponseEntity.status(HttpStatus.CONFLICT).body(error);
    }

    @ExceptionHandler(RoleNotFoundException.class)
    public ResponseEntity<ApiMessage> handleNotFoundException(RoleNotFoundException ex) {

        ApiMessage error = new ApiMessage(4001, ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }

    @ExceptionHandler(PermissionNotExistsException.class)
    public ResponseEntity<ApiMessage> handlePermissionNotExistsException(PermissionNotExistsException ex) {

        ApiMessage error = new ApiMessage(4002, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }

    @ExceptionHandler(PermissionNotFoundException.class)
    public ResponseEntity<ApiMessage> handlePermissionNotFoundException(PermissionNotFoundException ex) {

        ApiMessage error = new ApiMessage(4003, ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }


}
