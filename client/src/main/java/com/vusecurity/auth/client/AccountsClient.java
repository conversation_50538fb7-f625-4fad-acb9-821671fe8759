package com.vusecurity.auth.client;

import com.vusecurity.auth.client.config.AuthServerClientConfiguration;
import com.vusecurity.auth.client.exception.AuthServerClientException;
import com.vusecurity.auth.client.interceptor.ApiKeyInterceptor;
import com.vusecurity.auth.client.interceptor.BearerTokenInterceptor;
import com.vusecurity.auth.client.util.RestTemplateErrorHandler;
import com.vusecurity.auth.contracts.api.v1.dto.identities.*;
import com.vusecurity.auth.contracts.enums.AccountType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.UUID;

public class AccountsClient {
    
    private static final Logger log = LoggerFactory.getLogger(AccountsClient.class);
    
    private final RestTemplate restTemplate;
    private final AuthServerClientConfiguration configuration;
    private final String baseUrl;
    
    public AccountsClient(AuthServerClientConfiguration configuration) {
        this.configuration = configuration;
        this.baseUrl = configuration.getFullBaseUrl();
        log.debug("AccountsClient initialized with baseUrl: {}, apiKey: {}, bearerToken: {}", 
                this.baseUrl, 
                configuration.getApiKey() != null ? "***masked***" : "null",
                configuration.getBearerToken() != null ? "***masked***" : "null");
        this.restTemplate = createRestTemplate();
    }
    
    /**
     * Constructor for API key authentication (inter-service communication only)
     * @param baseUrl Base URL of the auth server
     * @param apiKey API key for inter-service authentication
     */
    public AccountsClient(String baseUrl, String apiKey) {
        this.configuration = new AuthServerClientConfiguration();
        this.configuration.setBaseUrl(baseUrl);
        this.configuration.setApiKey(apiKey);
        this.baseUrl = configuration.getFullBaseUrl();
        this.restTemplate = createRestTemplate();
    }
    
    /**
     * Constructor with authentication type
     * @param baseUrl Base URL of the auth server
     * @param token API key or bearer token
     * @param authType Type of authentication
     */
    public AccountsClient(String baseUrl, String token, AuthenticationType authType) {
        this.configuration = new AuthServerClientConfiguration();
        this.configuration.setBaseUrl(baseUrl);
        
        if (authType == AuthenticationType.BEARER_TOKEN) {
            this.configuration.setBearerToken(token);
        } else {
            this.configuration.setApiKey(token);
        }
        
        this.baseUrl = configuration.getFullBaseUrl();
        this.restTemplate = createRestTemplate();
    }
    
    /**
     * Factory method to create AccountsClient with API key authentication for inter-service communication
     * @param baseUrl Base URL of the auth server
     * @param apiKey API key for inter-service authentication
     * @return AccountsClient instance configured for API key authentication
     */
    public static AccountsClient withApiKey(String baseUrl, String apiKey) {
        return new AccountsClient(baseUrl, apiKey);
    }
    
    /**
     * Factory method to create AccountsClient with bearer token authentication for user requests
     * @param baseUrl Base URL of the auth server  
     * @param bearerToken Bearer token for user-level authentication
     * @return AccountsClient instance configured for bearer token authentication
     */
    public static AccountsClient withBearerToken(String baseUrl, String bearerToken) {
        return new AccountsClient(baseUrl, bearerToken, AuthenticationType.BEARER_TOKEN);
    }
    
    private RestTemplate createRestTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(configuration.getConnectTimeout());
        factory.setReadTimeout(configuration.getReadTimeout());
        
        RestTemplate template = new RestTemplate(factory);
        
        // Add appropriate authentication interceptor
        if (configuration.getBearerToken() != null && !configuration.getBearerToken().isEmpty()) {
            template.getInterceptors().add(new BearerTokenInterceptor(configuration.getBearerToken()));
            log.debug("Using Bearer token authentication");
        } else if (configuration.getApiKey() != null && !configuration.getApiKey().isEmpty()) {
            template.getInterceptors().add(new ApiKeyInterceptor(configuration.getApiKey()));
            log.debug("Using API key authentication for inter-service communication");
        } else {
            log.warn("No authentication method configured - requests may fail");
        }
        
        template.setErrorHandler(new RestTemplateErrorHandler());
        
        return template;
    }
    
    /**
     * Get all accounts with pagination and optional filtering
     * 
     * @param page Page number (1-based)
     * @param pageSize Number of items per page
     * @param filter Optional filter string
     * @return List of accounts (Note: This method will return JSON response as string due to dependency limitations)
     */
    public String getAllAccounts(int page, int pageSize, String filter) {
        log.debug("Getting all accounts - page: {}, pageSize: {}, filter: {}", page, pageSize, filter);
        
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl + "/accounts")
                .queryParam("page", page)
                .queryParam("pageSize", pageSize);
        
        if (filter != null && !filter.isEmpty()) {
            builder.queryParam("filter", filter);
        }
        
        ResponseEntity<String> response = restTemplate.exchange(
                builder.toUriString(),
                HttpMethod.GET,
                null,
                String.class
        );
        
        return response.getBody();
    }
    
    /**
     * Create a new account
     * 
     * @param request Account creation request
     * @return Created account details
     */
    public AccountResponse createAccount(CreateAccountRequest request) {
        log.debug("Creating account for identity: {}", request.getIdentityId());
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<CreateAccountRequest> entity = new HttpEntity<>(request, headers);
        
        ResponseEntity<AccountResponse> response = restTemplate.exchange(
                baseUrl + "/accounts",
                HttpMethod.POST,
                entity,
                AccountResponse.class
        );
        
        return response.getBody();
    }
    
    /**
     * Get account by ID
     * 
     * @param accountId Account identifier
     * @return Account details
     */
    public AccountResponse getAccountById(String accountId) {
        log.debug("Getting account by ID: {}", accountId);
        
        ResponseEntity<AccountResponse> response = restTemplate.exchange(
                baseUrl + "/accounts/{accountId}",
                HttpMethod.GET,
                null,
                AccountResponse.class,
                accountId
        );
        
        return response.getBody();
    }
    
    /**
     * Update an account
     * 
     * @param accountId Account identifier
     * @param request Update request
     */
    public void updateAccount(UUID accountId, UpdateAccountRequest request) {
        log.debug("Updating account: {}", accountId);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<UpdateAccountRequest> entity = new HttpEntity<>(request, headers);
        
        restTemplate.exchange(
                baseUrl + "/accounts/{accountId}",
                HttpMethod.PATCH,
                entity,
                Void.class,
                accountId
        );
    }
    
    /**
     * Get accounts by identity ID
     * 
     * @param identityId Identity identifier
     * @param page Page number (1-based)
     * @param pageSize Number of items per page
     * @return JSON string response containing paginated list of accounts for the identity
     */
    public String getAccountsByIdentity(UUID identityId, int page, int pageSize) {
        log.debug("Getting accounts for identity: {}", identityId);
        
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl + "/accounts/identity/{identityId}")
                .queryParam("page", page)
                .queryParam("pageSize", pageSize);
        
        ResponseEntity<String> response = restTemplate.exchange(
                builder.buildAndExpand(identityId).toUriString(),
                HttpMethod.GET,
                null,
                String.class
        );
        
        return response.getBody();
    }
    
    /**
     * Identify account by claims
     * 
     * @param businessId Business identifier
     * @param accountType Type of account
     * @param claimIdentifiers List of claim identifiers
     * @return Identified account
     * @throws AuthServerClientException if no account found or multiple accounts found
     */
    public AccountResponse identifyAccount(UUID businessId, AccountType accountType, 
                                          List<ClaimIdentifierRequest> claimIdentifiers) {
        log.debug("Identifying account for business: {}, type: {}", businessId, accountType);
        
        if (claimIdentifiers == null || claimIdentifiers.isEmpty()) {
            throw new AuthServerClientException("At least one claim identifier is required");
        }
        
        UriComponentsBuilder builder = UriComponentsBuilder
                .fromHttpUrl(baseUrl + "/businesses/{businessId}/accounts/identify")
                .queryParam("accountType", accountType);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<List<ClaimIdentifierRequest>> entity = new HttpEntity<>(claimIdentifiers, headers);
        
        ResponseEntity<AccountResponse> response = restTemplate.exchange(
                builder.buildAndExpand(businessId).toUriString(),
                HttpMethod.POST,
                entity,
                AccountResponse.class
        );
        
        return response.getBody();
    }
    
    /**
     * Get account with claims
     * 
     * @param accountId Account identifier
     * @return Account with associated claims
     */
    public AccountWithClaimsResponse getAccountWithClaims(String accountId) {
        log.debug("Getting account with claims: {}", accountId);
        
        ResponseEntity<AccountWithClaimsResponse> response = restTemplate.exchange(
                baseUrl + "/accounts/{accountId}/claims",
                HttpMethod.GET,
                null,
                AccountWithClaimsResponse.class,
                accountId
        );
        
        return response.getBody();
    }
}