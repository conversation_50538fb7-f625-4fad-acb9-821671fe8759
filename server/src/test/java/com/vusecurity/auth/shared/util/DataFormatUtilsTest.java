package com.vusecurity.auth.shared.util;

import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class DataFormatUtilsTest {

    @Test
    void should_identify_valid_uuid_format() {
        // Given
        String validUuid = "2784994d-dc95-491b-8843-f5664dece55e";
        String validUuidUpperCase = "2784994D-DC95-491B-8843-F5664DECE55E";
        String validUuidMixed = "2784994d-DC95-491b-8843-F5664dece55e";

        // When & Then
        assertTrue(DataFormatUtils.isUuidFormat(validUuid));
        assertTrue(DataFormatUtils.isUuidFormat(validUuidUpperCase));
        assertTrue(DataFormatUtils.isUuidFormat(validUuidMixed));
    }

    @Test
    void should_reject_invalid_uuid_format() {
        // Given
        String shortUuid = "2784994d-dc95-491b-8843-f5664dece5";
        String longUuid = "2784994d-dc95-491b-8843-f5664dece55ee";
        String invalidChars = "2784994g-dc95-491b-8843-f5664dece55e";
        String missingDashes = "2784994ddc95491b8843f5664dece55e";
        String extraDashes = "2784994d-dc95-491b-8843-f5664de-ce55e";
        String regexPattern = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        String emptyString = "";
        String nullString = null;

        // When & Then
        assertFalse(DataFormatUtils.isUuidFormat(shortUuid));
        assertFalse(DataFormatUtils.isUuidFormat(longUuid));
        assertFalse(DataFormatUtils.isUuidFormat(invalidChars));
        assertFalse(DataFormatUtils.isUuidFormat(missingDashes));
        assertFalse(DataFormatUtils.isUuidFormat(extraDashes));
        assertFalse(DataFormatUtils.isUuidFormat(regexPattern));
        assertFalse(DataFormatUtils.isUuidFormat(emptyString));
        assertFalse(DataFormatUtils.isUuidFormat(nullString));
    }

    @Test
    void should_handle_uuid_with_whitespace() {
        // Given
        String uuidWithSpaces = "  2784994d-dc95-491b-8843-f5664dece55e  ";
        String uuidWithTabs = "\t2784994d-dc95-491b-8843-f5664dece55e\t";

        // When & Then
        assertTrue(DataFormatUtils.isUuidFormat(uuidWithSpaces));
        assertTrue(DataFormatUtils.isUuidFormat(uuidWithTabs));
    }

    @Test
    void should_validate_regex_patterns() {
        // Given
        String validRegex1 = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        String validRegex2 = "\\d{3}-\\d{3}-\\d{4}";
        String validRegex3 = "^\\+?[1-9]\\d{1,14}$";
        String invalidRegex = "[unclosed bracket";
        String emptyPattern = "";
        String nullPattern = null;

        // When & Then
        assertTrue(DataFormatUtils.isValidRegexPattern(validRegex1));
        assertTrue(DataFormatUtils.isValidRegexPattern(validRegex2));
        assertTrue(DataFormatUtils.isValidRegexPattern(validRegex3));
        assertTrue(DataFormatUtils.isValidRegexPattern(emptyPattern)); // Empty is considered valid
        assertTrue(DataFormatUtils.isValidRegexPattern(nullPattern)); // Null is considered valid
        assertFalse(DataFormatUtils.isValidRegexPattern(invalidRegex));
    }

    @Test
    void should_parse_uuid_safely() {
        // Given
        String validUuid = "2784994d-dc95-491b-8843-f5664dece55e";
        String validUuidWithSpaces = "  2784994d-dc95-491b-8843-f5664dece55e  ";
        String invalidUuid = "not-a-uuid";
        String nullString = null;

        // When & Then
        UUID parsedUuid = DataFormatUtils.parseUuidSafely(validUuid);
        assertNotNull(parsedUuid);
        assertEquals(UUID.fromString(validUuid), parsedUuid);

        UUID parsedUuidWithSpaces = DataFormatUtils.parseUuidSafely(validUuidWithSpaces);
        assertNotNull(parsedUuidWithSpaces);
        assertEquals(UUID.fromString(validUuid), parsedUuidWithSpaces);

        assertNull(DataFormatUtils.parseUuidSafely(invalidUuid));
        assertNull(DataFormatUtils.parseUuidSafely(nullString));
    }

    @Test
    void should_handle_edge_cases() {
        // Given
        String almostValidUuid = "2784994d-dc95-491b-8843-f5664dece55"; // Missing one character
        String tooLongUuid = "2784994d-dc95-491b-8843-f5664dece55ee"; // One extra character
        String wrongFormat = "2784994d_dc95_491b_8843_f5664dece55e"; // Underscores instead of dashes

        // When & Then
        assertFalse(DataFormatUtils.isUuidFormat(almostValidUuid));
        assertFalse(DataFormatUtils.isUuidFormat(tooLongUuid));
        assertFalse(DataFormatUtils.isUuidFormat(wrongFormat));

        assertNull(DataFormatUtils.parseUuidSafely(almostValidUuid));
        assertNull(DataFormatUtils.parseUuidSafely(tooLongUuid));
        assertNull(DataFormatUtils.parseUuidSafely(wrongFormat));
    }
}