package com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import jakarta.persistence.*;

import java.util.Objects;
import java.util.UUID;

@Entity
@Table(
    name = "claim_set_claim_value",
    indexes = {
        @Index(name = "idx_claim_set", columnList = "claim_set_id"),
        @Index(name = "idx_claim_value", columnList = "claim_value_id")
    }
)
public class ClaimSetClaimValueJpaEntity extends AbstractEntity {

    @OneToOne(optional = false)
    @JoinColumn(name = "claim_value_id", 
        unique = true, 
        nullable = false,
        referencedColumnName = "id",
        foreignKey = @ForeignKey(name = "fk_claim_value_claim_set_claim_value")
    )
    private ClaimValueJpaEntity claimValue;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
        name = "claim_set_id",
        referencedColumnName = "id",
        foreignKey = @ForeignKey(name = "fk_claim_set_claim_value_claim_set"),
        nullable = false
    )
    private ClaimSetJpaEntity claimSet;

    // Constructors
    public ClaimSetClaimValueJpaEntity() {
    }

    public ClaimSetClaimValueJpaEntity(ClaimSetJpaEntity claimSet, ClaimValueJpaEntity claimValue) {
        if (claimSet == null) {
            throw new IllegalArgumentException("ClaimSet cannot be null");
        }
        if (claimValue == null) {
            throw new IllegalArgumentException("ClaimValue cannot be null");
        }

        this.claimSet = claimSet;
        this.claimValue = claimValue;
    }

    public ClaimSetClaimValueJpaEntity(UUID id, ClaimSetJpaEntity claimSet, ClaimValueJpaEntity claimValue) {
        this(claimSet, claimValue);
        this.setId(id);
    }

    public ClaimSetJpaEntity getClaimSet() {
        return claimSet;
    }

    public void setClaimSet(ClaimSetJpaEntity claimSet) {
        this.claimSet = claimSet;
    }

    public ClaimValueJpaEntity getClaimValue() {
        return claimValue;
    }

    public void setClaimValue(ClaimValueJpaEntity claimValue) {
        this.claimValue = claimValue;
    }

    // Helper methods
    public UUID getClaimSetId() {
        return claimSet != null ? claimSet.getId() : null;
    }

    public UUID getClaimValueId() {
        return claimValue != null ? claimValue.getId() : null;
    }

    // equals() and hashCode() using id (fallback since no unique business key)
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ClaimSetClaimValueJpaEntity that = (ClaimSetClaimValueJpaEntity) o;
        return Objects.equals(getClaimSet(), that.getClaimSet()) &&
               Objects.equals(getClaimValue(), that.getClaimValue());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getClaimSet().hashCode(), getClaimValue().hashCode());
    }

    @Override
    public String toString() {
        return "ClaimSetClaimValueJpaEntity{" +
                "id=" + getId() +
                ", claimSetId=" + getClaimSetId() +
                ", claimValueId=" + getClaimValueId() +
                '}';
    }
}
