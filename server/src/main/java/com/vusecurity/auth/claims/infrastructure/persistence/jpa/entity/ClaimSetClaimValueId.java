package com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

@Embeddable
public class ClaimSetClaimValueId implements Serializable {

    @Column(name = "claim_set_id")
    private UUID claimSet;

    @Column(name = "claim_value_id")
    private UUID claimValue;

    public ClaimSetClaimValueId() {}

    public ClaimSetClaimValueId(UUID claimSet, UUID claimValue) {
        this.claimSet = claimSet;
        this.claimValue = claimValue;
    }

    // Getters, setters, equals, hashCode
    public UUID getClaimSet() { return claimSet; }
    public void setClaimSet(UUID claimSet) { this.claimSet = claimSet; }

    public UUID getClaimValue() { return claimValue; }
    public void setClaimValue(UUID claimValue) { this.claimValue = claimValue; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ClaimSetClaimValueId)) return false;
        ClaimSetClaimValueId that = (ClaimSetClaimValueId) o;
        return Objects.equals(claimSet, that.claimSet) &&
               Objects.equals(claimValue, that.claimValue);
    }

    @Override
    public int hashCode() {
        return Objects.hash(claimSet, claimValue);
    }
}