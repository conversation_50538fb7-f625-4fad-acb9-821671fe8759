package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.query.GetClaimSetQuery;
import com.vusecurity.auth.claims.application.query.GetClaimSetsPagedQuery;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class GetClaimSetHandler implements GetClaimSetQuery {

    private static final Logger logger = LoggerFactory.getLogger(GetClaimSetHandler.class);
    private static final String BUSINESS_FIELD = "business";
    
    private final ClaimSetRepository claimSetRepository;

    @Override
    public ClaimSetJpaEntity getClaimSetById(UUID id) {
        logger.debug("Getting claim set by ID: {}", id);
        
        return claimSetRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Claim set not found with ID: " + id));
    }

    @Override
    public ClaimSetJpaEntity getClaimSetByClaimSetId(UUID claimSetId) {
        logger.debug("Getting claim set by claim set ID: {}", claimSetId);
        
        return claimSetRepository.findById(claimSetId)
                .orElseThrow(() -> new IllegalArgumentException("Claim set not found with claim set ID: " + claimSetId));
    }

    @Override
    public List<ClaimSetJpaEntity> getAllClaimSetsByIds(List<UUID> claimSetIdList) {
        logger.debug("Getting all the claim sets by claim set ID: {}", claimSetIdList);
        
        return claimSetRepository.findAllById(claimSetIdList);
    }

    @Override
    public Page<ClaimSetJpaEntity> getAllClaimSets(GetClaimSetsPagedQuery query) {
        logger.debug("Getting all claim sets with page: {}, pageSize: {}, filter: {}, businessId: {}, accountType: {}, isIdentifier: {}, hasClaimDefinitions: {}, hasBusiness: {}, sortBy: {}, sortDirection: {}",
                query.getPage(), query.getPageSize(), query.getFilter(), query.getBusinessId(), query.getAccountType(), query.getIsIdentifier(), query.getHasClaimDefinitions(), query.getHasBusiness(), query.getSortBy(), query.getSortDirection());

        // Build sort if specified
        Sort sort = Sort.unsorted(); // Default: no sorting
        if (query.getSortBy() != null && !query.getSortBy().trim().isEmpty()) {
            Sort.Direction direction = Sort.Direction.fromString(query.getSortDirection().toUpperCase());
            sort = Sort.by(direction, query.getSortBy().trim());
        }

        Pageable pageable = PageRequest.of(query.getPage() - 1, query.getPageSize(), sort);

        Specification<ClaimSetJpaEntity> spec = buildSpecification(query);
        return claimSetRepository.findAll(spec, pageable);
    }

    private Specification<ClaimSetJpaEntity> buildSpecification(GetClaimSetsPagedQuery query) {
        Specification<ClaimSetJpaEntity> spec = Specification.where(null);

        // Filter by text search
        if (query.getFilter() != null && !query.getFilter().trim().isEmpty()) {
            String filter = query.getFilter().trim();
            Specification<ClaimSetJpaEntity> textFilter = (root, criteriaQuery, criteriaBuilder) ->
                    criteriaBuilder.or(
                            criteriaBuilder.like(criteriaBuilder.lower(root.get("name")),
                                    "%" + filter.toLowerCase() + "%"),
                            criteriaBuilder.like(criteriaBuilder.lower(root.get("description")),
                                    "%" + filter.toLowerCase() + "%"),
                            criteriaBuilder.like(criteriaBuilder.lower(root.get("accountType")),
                                    "%" + filter.toLowerCase() + "%")
                    );
            spec = spec.and(textFilter);
        }

        // Handle business filtering - hasBusiness takes precedence over businessId
        if (query.getHasBusiness() != null) {
            if (query.getHasBusiness().booleanValue()) {
                // Filter for claim sets WITH business (not null business)
                Specification<ClaimSetJpaEntity> withBusinessFilter = (root, criteriaQuery, criteriaBuilder) ->
                        criteriaBuilder.isNotNull(root.get(BUSINESS_FIELD));
                spec = spec.and(withBusinessFilter);
            } else {
                // Filter for claim sets WITHOUT business (null business)
                Specification<ClaimSetJpaEntity> withoutBusinessFilter = (root, criteriaQuery, criteriaBuilder) ->
                        criteriaBuilder.isNull(root.get(BUSINESS_FIELD));
                spec = spec.and(withoutBusinessFilter);
            }
        } else if (query.getBusinessId() != null) {
            // Filter by specific business ID
            Specification<ClaimSetJpaEntity> businessFilter = (root, criteriaQuery, criteriaBuilder) ->
                    criteriaBuilder.equal(root.get(BUSINESS_FIELD).get("id"), query.getBusinessId());
            spec = spec.and(businessFilter);
        }

        // Filter by account type
        if (query.getAccountType() != null && !query.getAccountType().trim().isEmpty()) {
            Specification<ClaimSetJpaEntity> accountTypeFilter = (root, criteriaQuery, criteriaBuilder) ->
                    criteriaBuilder.equal(root.get("accountType"), query.getAccountType());
            spec = spec.and(accountTypeFilter);
        }

        // Filter by isIdentifier
        if (query.getIsIdentifier() != null) {
            Specification<ClaimSetJpaEntity> isIdentifierFilter = (root, criteriaQuery, criteriaBuilder) ->
                    criteriaBuilder.equal(root.get("isIdentifier"), query.getIsIdentifier());
            spec = spec.and(isIdentifierFilter);
        }

        // Filter by claim definitions presence
        if (query.getHasClaimDefinitions() != null) {
            if (query.getHasClaimDefinitions().booleanValue()) {
                // Filter for claim sets WITH claim definitions (collection is not empty)
                Specification<ClaimSetJpaEntity> withClaimDefsFilter = (root, criteriaQuery, criteriaBuilder) ->
                    // Use size() > 0 instead of isNotEmpty for better database compatibility
                    criteriaBuilder.greaterThan(criteriaBuilder.size(root.get("claimDefinitionMappings")), 0);
                spec = spec.and(withClaimDefsFilter);
            } else {
                // Filter for claim sets WITHOUT claim definitions (collection is empty)
                Specification<ClaimSetJpaEntity> withoutClaimDefsFilter = (root, criteriaQuery, criteriaBuilder) ->
                    // Use size() = 0 instead of isEmpty for better database compatibility
                    criteriaBuilder.equal(criteriaBuilder.size(root.get("claimDefinitionMappings")), 0);
                spec = spec.and(withoutClaimDefsFilter);
            }
        }

        return spec;
    }
}
