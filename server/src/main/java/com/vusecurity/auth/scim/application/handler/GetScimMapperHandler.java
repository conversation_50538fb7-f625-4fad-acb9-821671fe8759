package com.vusecurity.auth.scim.application.handler;

import com.vusecurity.auth.scim.application.exception.ScimMapperNotFoundException;
import com.vusecurity.auth.scim.application.service.ScimToClaimMappingService;
import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimToClaimMappingJpaEntity;

import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class GetScimMapperHandler {

    private static final Logger logger = LoggerFactory.getLogger(GetScimMapperHandler.class);

    private final ScimToClaimMappingService scimToClaimMappingService;
    
    @Transactional(readOnly = true)
    public List<ScimToClaimMappingJpaEntity> getMapper(UUID businessId) {
        logger.debug("Retrieving SCIM MApper: {}", businessId.toString());

        List<ScimToClaimMappingJpaEntity> result = scimToClaimMappingService.getByBusinessId(businessId);

        if (result.isEmpty()){
            throw new ScimMapperNotFoundException("Mapper not found for business id: " + businessId.toString());
        }

        return result;
    }

    @Transactional(readOnly = true)
    public ScimToClaimMappingJpaEntity getFilter(UUID filterId) {
        return scimToClaimMappingService.findById(filterId).orElseThrow(() -> new ScimMapperNotFoundException("Mapper not found for Filter id: " + filterId.toString()));
    }
} 