package com.vusecurity.auth.authorization.application.command;

import com.vusecurity.auth.authorization.application.exception.InvalidGroupRequestException;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for UpdateGroupRolesCommand validation logic.
 */
class UpdateGroupRolesCommandTest {

    @Test
    void shouldValidateSuccessfully_WhenGroupIdIsProvided() {
        // Given
        UpdateGroupRolesCommand command = new UpdateGroupRolesCommand(
                UUID.randomUUID(),
                List.of(UUID.randomUUID())
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WhenRoleIdsAreNull() {
        // Given
        UpdateGroupRolesCommand command = new UpdateGroupRolesCommand(
                UUID.randomUUID(),
                null
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldThrowException_WhenGroupIdIsNull() {
        // Given
        UpdateGroupRolesCommand command = new UpdateGroupRolesCommand(null, List.of(UUID.randomUUID()));

        // When & Then
        var exception = assertThrows(InvalidGroupRequestException.class, command::validate);
        assertEquals("Group ID is required", exception.getMessage());
    }
} 