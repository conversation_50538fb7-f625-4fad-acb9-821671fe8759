package com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.contracts.enums.ClaimType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;

import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;

import java.util.*;

@Entity
@Table(name = "claim_definition",
        indexes = {
                @Index(name = "uk_claim_def_id", columnList = "id", unique = true),
                @Index(name = "uk_claim_def_code", columnList = "code", unique = true)
        })
@SuppressWarnings("LombokGetterMayBeUsed, LombokSetterMayBeUsed")
public class ClaimDefinitionJpaEntity extends AbstractEntity {

    @Column(nullable = false)
    @Size(max = 255)
    private String code;

    @Column(nullable = false)
    @Size(max = 255)
    private String name;

    @Column(length = 1024)
    @Size(max = 1024)
    private String description;

    @Enumerated(EnumType.STRING)
    private DataTypeEnum dataType;

    @Column(length = 1024)
    @Size(max = 1024)
    private String dataFormat;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "is_a_list_of", referencedColumnName = "id")
    private ClaimDefinitionJpaEntity isAListOf;

    @OneToMany(mappedBy = "claimDefinition", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<ClaimSetDefinitionMappingJpaEntity> claimSetMappings = new HashSet<>();

    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = "varchar(255) default 'USER_DEFINED'", nullable = false)
    private ClaimType claimType = ClaimType.USER_DEFINED;

    // Constructors
    public ClaimDefinitionJpaEntity() {
    }

        // Constructor with ID (for test scenarios)
    public ClaimDefinitionJpaEntity(UUID id, String code, String name, String description,
                                    DataTypeEnum dataType, String dataFormat) {
        this.setId(id);
        this.code = code;
        this.name = name;
        this.description = description;
        this.dataType = dataType;
        this.dataFormat = dataFormat;
        this.claimSetMappings = new HashSet<>();
    }

    // Constructor with ID (for migration scenarios)
    public ClaimDefinitionJpaEntity(UUID id, String code, String name, String description,
                                    DataTypeEnum dataType, String dataFormat, ClaimType claimType) {
        this.setId(id);
        this.code = code;
        this.name = name;
        this.description = description;
        this.dataType = dataType;
        this.dataFormat = dataFormat;
        this.claimSetMappings = new HashSet<>();
        this.claimType = claimType;
    }

    // Constructor without ID (for production use - JPA will generate ID)
    public ClaimDefinitionJpaEntity(String code, String name, String description, DataTypeEnum dataType, String dataFormat) {
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("code cannot be null or empty");
        }
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("name cannot be null or empty");
        }
        if (dataType == null) {
            throw new IllegalArgumentException("dataType cannot be null");
        }

        this.code = code.trim();
        this.name = name.trim();
        this.description = description;
        this.claimType = ClaimType.USER_DEFINED;
        this.dataType = dataType;
        this.dataFormat = dataFormat;
        this.claimSetMappings = new HashSet<>();
    }

    // Constructor without ID (for production use - JPA will generate ID)
    public ClaimDefinitionJpaEntity(String code, String name, String description, ClaimType claimType, DataTypeEnum dataType, String dataFormat) {
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("code cannot be null or empty");
        }
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("name cannot be null or empty");
        }
        if (dataType == null) {
            throw new IllegalArgumentException("dataType cannot be null");
        }
        if (claimType == null){
            claimType = ClaimType.USER_DEFINED;
        }

        this.code = code.trim();
        this.name = name.trim();
        this.description = description;
        this.claimType = claimType;
        this.dataType = dataType;
        this.dataFormat = dataFormat;
        this.claimSetMappings = new HashSet<>();
    }

    // Getters
    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public DataTypeEnum getDataType() {
        return dataType;
    }

    public String getDataFormat() {
        return dataFormat;
    }

    public ClaimDefinitionJpaEntity getIsAListOf() {
        return isAListOf;
    }

    public Set<ClaimSetDefinitionMappingJpaEntity> getClaimSetMappings() {
        return claimSetMappings != null ? Collections.unmodifiableSet(claimSetMappings) : Collections.emptySet();
    }
    
    public ClaimType getClaimType(){
        return this.claimType;
    }

    // Setters
    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setDataType(DataTypeEnum dataType) {
        this.dataType = dataType;
    }

    public void setDataFormat(String dataFormat) {
        this.dataFormat = dataFormat;
    }

    public void setIsAListOf(ClaimDefinitionJpaEntity isAListOf) {
        this.isAListOf = isAListOf;
    }

    public void setClaimSetMappings(Set<ClaimSetDefinitionMappingJpaEntity> claimSetMappings) {
        this.claimSetMappings = claimSetMappings != null ? new HashSet<>(claimSetMappings) : new HashSet<>();
    }

    public void setClaimType(ClaimType claimType){
        this.claimType = claimType;
    }


    // equals() and hashCode() using code (unique business key)
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        ClaimDefinitionJpaEntity that = (ClaimDefinitionJpaEntity) o;
        return Objects.equals(code, that.code);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), code);
    }

    @Override
    public String toString() {
        return "ClaimDefinitionJpaEntity{" +
                "id=" + getId() +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", dataType=" + dataType +
                ", dataFormat='" + dataFormat + '\'' +
                ", claimType='" + claimType + '\'' +
                '}';
    }
}
