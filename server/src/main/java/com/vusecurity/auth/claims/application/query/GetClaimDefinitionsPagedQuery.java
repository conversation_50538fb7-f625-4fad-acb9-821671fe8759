package com.vusecurity.auth.claims.application.query;

import lombok.Builder;
import lombok.Data;

import java.util.UUID;

/**
 * Query for getting paginated claim definitions with complex filtering.
 */
@Data
@Builder
public class GetClaimDefinitionsPagedQuery {
    private int page;
    private int pageSize;
    private UUID claimDefinitionId;
    private String code;
    private String name;
    private String description;
    private String dataType;
    private String filter; // General filter string
    private Boolean isAList; // Filter for array data type (list claims)
}
