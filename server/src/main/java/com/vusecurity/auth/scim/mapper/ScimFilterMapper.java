package com.vusecurity.auth.scim.mapper;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * SCIM Filter Mapper for processing SCIM filter expressions according to RFC 7644
 * Supports reading from and writing to SCIM resources using filter syntax
 */
@Slf4j
public class ScimFilterMapper {
    
    private static final Pattern ARRAY_FILTER_PATTERN = Pattern.compile("^(.+)\\[(.+)\\](?:\\.(.+))?$");
    private static final Pattern COMPARISON_PATTERN = Pattern.compile("(.+)\\s+(eq|ne|co|sw|ew|gt|ge|lt|le)\\s+(.+)");

    /**
     * Gets a value from a SCIM resource using a filter expression
     */
    public static String getValueAsString(Object resource, String filter) {
        Object value = getValue(resource, filter);

        return value != null ? value.toString() : null;
    }
    
    /**
     * Gets a value from a SCIM resource using a filter expression
     */
    public static Object getValue(Object resource, String filter) {
        if (resource == null || filter == null || filter.trim().isEmpty()) {
            return null;
        }
        
        return processGetFilter(resource, filter.trim());
    }
    
    /**
     * Gets a list of values from a SCIM resource using a filter expression
     * Useful for extracting field values from all elements in an array
     */
    public static List<String> getValueAsList(Object resource, String filter) {
        List<String> itemList = new ArrayList<>();
        if (isArray(resource, filter)){
            Collection<?> result = getArrayObjects(resource, filter);
            if (result != null){
                return result.stream().map(object -> object.toString()).collect(Collectors.toList());
            }
            return itemList;
        }
        
        // Single value - return as single-item list
        Object value = processGetFilter(resource, filter);
        if (value != null){
            itemList.add(value.toString());
        }
        return itemList;
    }
    
    /**
     * Sets a value in a SCIM resource using a filter expression
     */
    public static void setValue(Object resource, String filter, Object value) {
        if (resource == null || filter == null || filter.trim().isEmpty()) {
            return;
        }
        
        processSetFilter(resource, filter.trim(), value);
    }

    private static Boolean isArray(Object resource, String filter) {
        if (resource == null || filter == null || filter.trim().isEmpty()) {
            return false;
        }

        Matcher arrayMatcher = ARRAY_FILTER_PATTERN.matcher(filter);
        if (arrayMatcher.matches()) {
            return true;
        }

        return false;
    }

    private static Collection<?> getArrayObjects(Object resource, String filter){
        if (resource == null || filter == null || filter.trim().isEmpty()) {
            return null;
        }

        List<Object> collectionResult = new ArrayList<>();

        Matcher arrayMatcher = ARRAY_FILTER_PATTERN.matcher(filter);
        if (arrayMatcher.matches()) {
            String arrayField = arrayMatcher.group(1);
            String condition = arrayMatcher.group(2);
            String subField = arrayMatcher.group(3);
            
            Object arrayValue = getFieldValue(resource, arrayField);
            if (arrayValue instanceof Collection) {
                Collection<?> collection = (Collection<?>) arrayValue;
                for (Object item : collection) {
                    if (evaluateCondition(item, condition)) {
                        if (subField != null) {
                            collectionResult.add(getFieldValue(item, subField)); 
                        } else {
                            collectionResult.add(item);
                        }
                    }
                }
            }
        }

        return collectionResult;
    }
    
    private static Object processGetFilter(Object current, String filter) {
        // Handle array filter pattern like "emails[primary eq true].value"
        Matcher arrayMatcher = ARRAY_FILTER_PATTERN.matcher(filter);
        if (arrayMatcher.matches()) {
            String arrayField = arrayMatcher.group(1);
            String condition = arrayMatcher.group(2);
            String subField = arrayMatcher.group(3);
            
            Object arrayValue = getFieldValue(current, arrayField);
            if (arrayValue instanceof Collection) {
                Collection<?> collection = (Collection<?>) arrayValue;
                for (Object item : collection) {
                    if (evaluateCondition(item, condition)) {
                        if (subField != null) {
                            return getFieldValue(item, subField);
                        } else {
                            return item;
                        }
                    }
                }
            }
            return null;
        }
        
        // Handle dot notation like "name.givenName"
        if (filter.contains(".")) {
            String[] parts = filter.split("\\.", 2);
            String fieldName = parts[0];
            String remaining = parts[1];
            
            Object fieldValue = getFieldValue(current, fieldName);
            if (fieldValue != null) {
                // Check if this is an array/collection and we want to extract a field from all elements
                if (fieldValue instanceof Collection) {
                    Collection<?> collection = (Collection<?>) fieldValue;
                    List<Object> results = new ArrayList<>();
                    
                    for (Object item : collection) {
                        Object itemResult = processGetFilter(item, remaining);
                        if (itemResult != null) {
                            results.add(itemResult);
                        }
                    }
                    
                    return results.isEmpty() ? null : results;
                } else {
                    return processGetFilter(fieldValue, remaining);
                }
            }
            return null;
        }
        
        // Simple field access
        return getFieldValue(current, filter);
    }
    
    private static void processSetFilter(Object current, String filter, Object value) {
        // Handle array filter pattern like "emails[primary eq true]"
        Matcher arrayMatcher = ARRAY_FILTER_PATTERN.matcher(filter);
        if (arrayMatcher.matches()) {
            String arrayField = arrayMatcher.group(1);
            String condition = arrayMatcher.group(2);
            String subField = arrayMatcher.group(3);
            
            Object arrayValue = getFieldValue(current, arrayField);
            Collection<Object> collection;
            
            if (arrayValue == null) {
                collection = new ArrayList<>();
                setFieldValue(current, arrayField, collection);
            } else if (arrayValue instanceof Collection) {
                collection = (Collection<Object>) arrayValue;
            } else {
                return; // Cannot set on non-collection
            }
            
            // Find existing item that matches condition
            Object targetItem = null;
            for (Object item : collection) {
                Object itemValue = getFieldValue(item, subField);
                if (itemValue != null && itemValue.equals(value)){
                    return;
                }
            }
            
            // If no matching item found, create new one
            if (targetItem == null) {
                targetItem = createObjectFromCondition(current, arrayField, condition);
                collection.add(targetItem);
            }
            
            // Set the value
            if (subField != null) {
                setFieldValue(targetItem, subField, value);
            } else {
                // Replace entire object with value
                if (value instanceof Map) {
                    copyMapToObject(targetItem, (Map<String, Object>) value);
                }
            }
            return;
        }
        
        // Handle dot notation like "name.givenName"
        if (filter.contains(".")) {
            String[] parts = filter.split("\\.", 2);
            String fieldName = parts[0];
            String remaining = parts[1];
            
            Object fieldValue = getFieldValue(current, fieldName);
            if (fieldValue == null) {
                // Create nested object
                fieldValue = createNestedObject(current, fieldName);
                setFieldValue(current, fieldName, fieldValue);
            }
            processSetFilter(fieldValue, remaining, value);
            return;
        }
        
        // Simple field assignment
        setFieldValue(current, filter, value);
    }
    
    private static Object getFieldValue(Object obj, String fieldName) {
        try {
            // Try getter method first
            String getterName = "get" + capitalize(fieldName);
            Method getter = findMethod(obj.getClass(), getterName);
            if (getter != null) {
                return getter.invoke(obj);
            }
            
            // Try field access
            Field field = findField(obj.getClass(), fieldName);
            if (field != null) {
                field.setAccessible(true);
                return field.get(obj);
            }
        } catch (Exception e) {
            // Ignore and return null
        }
        return null;
    }
    
    private static void setFieldValue(Object obj, String fieldName, Object value) {
        try {
            // Try setter method first
            String setterName = "set" + capitalize(fieldName);
            Method[] methods = obj.getClass().getMethods();
            for (Method method : methods) {
                if (method.getName().equals(setterName) && method.getParameterCount() == 1) {
                    Class<?> paramType = method.getParameterTypes()[0];
                    Object convertedValue = convertValue(value, paramType);
                    method.invoke(obj, convertedValue);
                    return;
                }
            }
            
            // Try field access
            Field field = findField(obj.getClass(), fieldName);
            if (field != null) {
                field.setAccessible(true);
                Object convertedValue = convertValue(value, field.getType());
                field.set(obj, convertedValue);
            }
        } catch (Exception e) {
            log.error("Failed to set field: " + fieldName, e);
            //throw new RuntimeException("Failed to set field: " + fieldName, e);
        }
    }
    
    private static boolean evaluateCondition(Object item, String condition) {
        Matcher matcher = COMPARISON_PATTERN.matcher(condition.trim());
        if (!matcher.matches()) {
            return false;
        }
        
        String field = matcher.group(1).trim();
        String operator = matcher.group(2).trim();
        String expectedValue = matcher.group(3).trim().replaceAll("(?:^[\"'])|(?:[\"']$)", ""); // Remove quotes
        
        Object actualValue = getFieldValue(item, field);
        if (actualValue == null) {
            return false;
        }
        
        String actualStr = actualValue.toString();
        
        switch (operator) {
            case "eq":
                return actualStr.equals(expectedValue);
            case "ne":
                return !actualStr.equals(expectedValue);
            case "co":
                return actualStr.contains(expectedValue);
            case "sw":
                return actualStr.startsWith(expectedValue);
            case "ew":
                return actualStr.endsWith(expectedValue);
            case "gt":
                return compareValues(actualValue, expectedValue) > 0;
            case "ge":
                return compareValues(actualValue, expectedValue) >= 0;
            case "lt":
                return compareValues(actualValue, expectedValue) < 0;
            case "le":
                return compareValues(actualValue, expectedValue) <= 0;
            default:
                return false;
        }
    }
    
    private static int compareValues(Object actual, String expected) {
        if (actual instanceof Number && isNumeric(expected)) {
            double actualNum = ((Number) actual).doubleValue();
            double expectedNum = Double.parseDouble(expected);
            return Double.compare(actualNum, expectedNum);
        }
        return actual.toString().compareTo(expected);
    }
    
    private static boolean isNumeric(String str) {
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    private static Object createObjectFromCondition(Object parent, String arrayField, String condition) {
        try {
            // Get the generic type of the collection
            Field field = findField(parent.getClass(), arrayField);
            if (field != null) {
                Type genericType = field.getGenericType();
                if (genericType instanceof ParameterizedType) {
                    ParameterizedType paramType = (ParameterizedType) genericType;
                    Type[] actualTypes = paramType.getActualTypeArguments();
                    if (actualTypes.length > 0) {
                        Class<?> itemClass = (Class<?>) actualTypes[0];
                        Object newItem = itemClass.getDeclaredConstructor().newInstance();
                        
                        // Set condition fields
                        setConditionFields(newItem, condition);
                        return newItem;
                    }
                }
            }
        } catch (Exception e) {
            // Fallback: create a generic object
        }
        
        // Create a Map as fallback
        Map<String, Object> newItem = new HashMap<>();
        setConditionFields(newItem, condition);
        return newItem;
    }
    
    private static void setConditionFields(Object item, String condition) {
        Matcher matcher = COMPARISON_PATTERN.matcher(condition.trim());
        if (matcher.matches()) {
            String field = matcher.group(1).trim();
            String operator = matcher.group(2).trim();
            String value = matcher.group(3).trim().replaceAll("(?:^[\"'])|(?:[\"']$)", ""); // Remove quotes
            
            if ("eq".equals(operator)) {
                if (item instanceof Map) {
                    ((Map<String, Object>) item).put(field, convertStringValue(value));
                } else {
                    setFieldValue(item, field, convertStringValue(value));
                }
            }
        }
    }
    
    private static Object convertStringValue(String value) {
        if ("true".equalsIgnoreCase(value)) {
            return true;
        }
        if ("false".equalsIgnoreCase(value)) {
            return false;
        }
        if (isNumeric(value)) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException e) {
                return Double.parseDouble(value);
            }
        }
        return value;
    }
    
    private static Object createNestedObject(Object parent, String fieldName) {
        try {
            Field field = findField(parent.getClass(), fieldName);
            if (field != null) {
                Class<?> fieldType = field.getType();
                return fieldType.getDeclaredConstructor().newInstance();
            }
        } catch (Exception e) {
            // Fallback to Map
        }
        return new HashMap<String, Object>();
    }
    
    private static Object convertValue(Object value, Class<?> targetType) {
        if (value == null) {
            return null;
        }
        
        if (targetType.isAssignableFrom(value.getClass())) {
            return value;
        }
        
        // Handle String conversions
        if (targetType == String.class) {
            return value.toString();
        }
        
        // Handle primitive and wrapper conversions
        if (value instanceof String) {
            String str = (String) value;
            if (targetType == boolean.class || targetType == Boolean.class) {
                return Boolean.parseBoolean(str);
            }
            if (targetType == int.class || targetType == Integer.class) {
                return Integer.parseInt(str);
            }
            if (targetType == long.class || targetType == Long.class) {
                return Long.parseLong(str);
            }
            if (targetType == double.class || targetType == Double.class) {
                return Double.parseDouble(str);
            }
        }
        
        return value;
    }
    
    private static void copyMapToObject(Object target, Map<String, Object> source) {
        for (Map.Entry<String, Object> entry : source.entrySet()) {
            setFieldValue(target, entry.getKey(), entry.getValue());
        }
    }
    
    private static Field findField(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }
    
    private static Method findMethod(Class<?> clazz, String methodName) {
        try {
            return clazz.getMethod(methodName);
        } catch (NoSuchMethodException e) {
            return null;
        }
    }
    
    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return Character.toUpperCase(str.charAt(0)) + str.substring(1);
    }
}