package com.vusecurity.auth.contracts.api.v1.dto.scimConfig;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonProperty;

@Data
@Schema(description = "Request to create a new SCIM to claims mapper")
public class CreateScimMapperRequest {

    @NotNull(message = "Business Id is required")
    @Schema(description = "Id of a business",
            example = "f9fc0a64-56fc-4477-87a7-8e525505abce")
    private UUID businessId;

    @Valid
    @Schema(
            description = "List of filters to create for the business",
            implementation = ScimFilterMapperRequest.class
    )
    @JsonProperty("filters")
    List<ScimFilterMapperRequest> filters;
}
