package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.command.DeleteGroupCommand;
import com.vusecurity.auth.authorization.application.exception.GroupNotFoundException;
import com.vusecurity.auth.authorization.application.exception.InvalidGroupRequestException;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.GroupMembershipRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountGroupRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InOrder;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for DeleteGroupHandler.
 * Tests business logic with mocked repository dependencies.
 */
@ExtendWith(MockitoExtension.class)
class DeleteGroupHandlerTest {

    @Mock
    private AccountGroupRepository accountGroupRepository;

    @Mock
    private GroupMembershipRepository groupMembershipRepository;

    @InjectMocks
    private DeleteGroupHandler deleteGroupHandler;

    private UUID groupId;
    private DeleteGroupCommand validCommand;
    private DeleteGroupCommand invalidCommand;

    @BeforeEach
    void setUp() {
        groupId = UUID.randomUUID();
        validCommand = new DeleteGroupCommand(groupId);
        invalidCommand = new DeleteGroupCommand(null);
    }

    @Test
    void shouldSuccessfullyDeleteGroup_WhenGroupExists() {
        // Given
        when(accountGroupRepository.existsById(groupId)).thenReturn(true);

        // When
        deleteGroupHandler.deleteGroup(validCommand);

        // Then
        verify(accountGroupRepository).existsById(groupId);
        verify(groupMembershipRepository).deleteByGroupId(groupId);
        verify(accountGroupRepository).deleteById(groupId);
        
        // Verify order of operations - memberships deleted before group
        InOrder inOrder = inOrder(groupMembershipRepository, accountGroupRepository);
        inOrder.verify(accountGroupRepository).existsById(groupId);
        inOrder.verify(groupMembershipRepository).deleteByGroupId(groupId);
        inOrder.verify(accountGroupRepository).deleteById(groupId);
    }

    @Test
    void shouldThrowGroupNotFoundException_WhenGroupDoesNotExist() {
        // Given
        when(accountGroupRepository.existsById(groupId)).thenReturn(false);

        // When & Then
        GroupNotFoundException exception = assertThrows(
                GroupNotFoundException.class,
                () -> deleteGroupHandler.deleteGroup(validCommand)
        );

        assertEquals("Group not found with ID: " + groupId, exception.getMessage());

        // Verify that only existsById was called, no deletion operations
        verify(accountGroupRepository).existsById(groupId);
        verify(groupMembershipRepository, never()).deleteByGroupId(any());
        verify(accountGroupRepository, never()).deleteById(any());
    }

    @Test
    void shouldThrowInvalidGroupRequestException_WhenGroupIdIsNull() {
        // When & Then
        InvalidGroupRequestException exception = assertThrows(
                InvalidGroupRequestException.class,
                () -> deleteGroupHandler.deleteGroup(invalidCommand)
        );

        assertEquals("groupId is required", exception.getMessage());

        // Verify no repository calls are made when validation fails
        verifyNoInteractions(accountGroupRepository);
        verifyNoInteractions(groupMembershipRepository);
    }

    @Test
    void shouldCallValidateOnCommand() {
        // Given
        DeleteGroupCommand spyCommand = spy(validCommand);
        when(accountGroupRepository.existsById(groupId)).thenReturn(true);

        // When
        deleteGroupHandler.deleteGroup(spyCommand);

        // Then
        verify(spyCommand).validate();
    }

    @Test
    void shouldDeleteMembershipsBeforeGroup_EvenWhenNoMembershipsExist() {
        // Given
        when(accountGroupRepository.existsById(groupId)).thenReturn(true);
        doNothing().when(groupMembershipRepository).deleteByGroupId(groupId);

        // When
        deleteGroupHandler.deleteGroup(validCommand);

        // Then
        verify(groupMembershipRepository).deleteByGroupId(groupId);
        verify(accountGroupRepository).deleteById(groupId);
        
        // Verify order - memberships deleted first, even if none exist
        InOrder inOrder = inOrder(groupMembershipRepository, accountGroupRepository);
        inOrder.verify(accountGroupRepository).existsById(groupId);
        inOrder.verify(groupMembershipRepository).deleteByGroupId(groupId);
        inOrder.verify(accountGroupRepository).deleteById(groupId);
    }

    @Test
    void shouldNotCallDeleteOperations_WhenGroupNotFound() {
        // Given
        when(accountGroupRepository.existsById(groupId)).thenReturn(false);

        // When & Then
        assertThrows(GroupNotFoundException.class, () -> deleteGroupHandler.deleteGroup(validCommand));

        // Verify that no delete operations are performed
        verify(accountGroupRepository, never()).deleteById(any());
        verify(groupMembershipRepository, never()).deleteByGroupId(any());
    }
} 