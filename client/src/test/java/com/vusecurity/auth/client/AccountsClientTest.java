package com.vusecurity.auth.client;

import com.vusecurity.auth.client.config.AuthServerClientConfiguration;
import com.vusecurity.auth.client.exception.AuthServerClientException;
import com.vusecurity.auth.client.AuthenticationType;
import com.vusecurity.auth.contracts.api.v1.dto.identities.*;
import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.contracts.enums.AccountType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AccountsClientTest {
    
    @Mock
    private RestTemplate restTemplate;
    
    @Mock
    private AuthServerClientConfiguration configuration;
    
    private AccountsClient accountsClient;
    
    private static final String BASE_URL = "http://localhost:8080/api/v1";
    private static final String API_KEY = "test-api-key";
    
    @BeforeEach
    void setUp() {
        when(configuration.getFullBaseUrl()).thenReturn(BASE_URL);
        when(configuration.getApiKey()).thenReturn(API_KEY);
        when(configuration.getConnectTimeout()).thenReturn(5000);
        when(configuration.getReadTimeout()).thenReturn(10000);
        
        accountsClient = new AccountsClient(configuration);
        ReflectionTestUtils.setField(accountsClient, "restTemplate", restTemplate);
    }
    
    @Test
    void testConstructorWithBaseUrlAndApiKey() {
        // When
        AccountsClient client = new AccountsClient(BASE_URL, API_KEY);
        
        // Then
        assertNotNull(client);
    }
    
    @Test
    void testWithApiKeyFactoryMethod() {
        // When
        AccountsClient client = AccountsClient.withApiKey(BASE_URL, API_KEY);
        
        // Then
        assertNotNull(client);
    }
    
    @Test
    void testWithBearerTokenFactoryMethod() {
        // Given
        String bearerToken = "test-bearer-token";
        
        // When
        AccountsClient client = AccountsClient.withBearerToken(BASE_URL, bearerToken);
        
        // Then
        assertNotNull(client);
    }
    
    @Test
    void testConstructorWithBearerTokenType() {
        // Given
        String bearerToken = "test-bearer-token";
        
        // When
        AccountsClient client = new AccountsClient(BASE_URL, bearerToken, AuthenticationType.BEARER_TOKEN);
        
        // Then
        assertNotNull(client);
    }
    
    @Test
    void testConstructorWithApiKeyType() {
        // Given
        String apiKey = "test-api-key";
        
        // When
        AccountsClient client = new AccountsClient(BASE_URL, apiKey, AuthenticationType.API_KEY);
        
        // Then
        assertNotNull(client);
    }
    
    @Test
    void testCreateAccount() {
        // Given
        CreateAccountRequest request = new CreateAccountRequest();
        request.setIdentityId(UUID.randomUUID());
        request.setBusinessId(UUID.randomUUID());
        request.setIdentityProviderId(UUID.randomUUID());
        request.setAccountType(AccountType.WORKFORCE);
        request.setLifecycleState(AccountLifecycleState.ACTIVE);
        
        AccountResponse expectedResponse = new AccountResponse();
        expectedResponse.setId(UUID.randomUUID());
        
        ResponseEntity<AccountResponse> responseEntity = new ResponseEntity<>(expectedResponse, HttpStatus.CREATED);
        
        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(AccountResponse.class)
        )).thenReturn(responseEntity);
        
        // When
        AccountResponse result = accountsClient.createAccount(request);
        
        // Then
        assertNotNull(result);
        assertNotNull(result.getId());
        
        ArgumentCaptor<HttpEntity> entityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
        verify(restTemplate).exchange(
                eq(BASE_URL + "/accounts"),
                eq(HttpMethod.POST),
                entityCaptor.capture(),
                eq(AccountResponse.class)
        );
        
        HttpEntity<CreateAccountRequest> capturedEntity = entityCaptor.getValue();
        assertEquals(request, capturedEntity.getBody());
        assertEquals(MediaType.APPLICATION_JSON, capturedEntity.getHeaders().getContentType());
    }
    
    @Test
    void testGetAccountById() {
        // Given
        String accountId = UUID.randomUUID().toString();
        AccountResponse expectedResponse = new AccountResponse();
        expectedResponse.setId(UUID.fromString(accountId));
        
        ResponseEntity<AccountResponse> responseEntity = new ResponseEntity<>(expectedResponse, HttpStatus.OK);
        
        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.GET),
                isNull(),
                eq(AccountResponse.class),
                eq(accountId)
        )).thenReturn(responseEntity);
        
        // When
        AccountResponse result = accountsClient.getAccountById(accountId);
        
        // Then
        assertNotNull(result);
        assertEquals(accountId, result.getId().toString());
        
        verify(restTemplate).exchange(
                eq(BASE_URL + "/accounts/{accountId}"),
                eq(HttpMethod.GET),
                isNull(),
                eq(AccountResponse.class),
                eq(accountId)
        );
    }
    
    @Test
    void testUpdateAccount() {
        // Given
        UUID accountId = UUID.randomUUID();
        UpdateAccountRequest request = new UpdateAccountRequest();
        request.setLifecycleState(AccountLifecycleState.SUSPENDED);
        request.setMetadata(Map.of("key", "value"));
        
        ResponseEntity<Void> responseEntity = new ResponseEntity<>(HttpStatus.NO_CONTENT);
        
        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.PATCH),
                any(HttpEntity.class),
                eq(Void.class),
                any(UUID.class)
        )).thenReturn(responseEntity);
        
        // When
        assertDoesNotThrow(() -> accountsClient.updateAccount(accountId, request));
        
        // Then
        verify(restTemplate).exchange(
                eq(BASE_URL + "/accounts/{accountId}"),
                eq(HttpMethod.PATCH),
                any(HttpEntity.class),
                eq(Void.class),
                eq(accountId)
        );
    }
    
    @Test
    void testIdentifyAccountWithEmptyClaimsThrowsException() {
        // Given
        UUID businessId = UUID.randomUUID();
        AccountType accountType = AccountType.CUSTOMER;
        List<ClaimIdentifierRequest> emptyList = Collections.emptyList();
        
        // When & Then
        AuthServerClientException exception = assertThrows(
                AuthServerClientException.class,
                () -> accountsClient.identifyAccount(businessId, accountType, emptyList)
        );
        
        assertEquals("At least one claim identifier is required", exception.getMessage());
    }
    
    @Test
    void testGetAccountWithClaims() {
        // Given
        String accountId = UUID.randomUUID().toString();
        AccountResponse accountResponse = new AccountResponse();
        accountResponse.setId(UUID.fromString(accountId));
        AccountWithClaimsResponse expectedResponse = new AccountWithClaimsResponse(accountResponse, Collections.emptyList());
        
        ResponseEntity<AccountWithClaimsResponse> responseEntity = 
                new ResponseEntity<>(expectedResponse, HttpStatus.OK);
        
        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.GET),
                isNull(),
                eq(AccountWithClaimsResponse.class),
                eq(accountId)
        )).thenReturn(responseEntity);
        
        // When
        AccountWithClaimsResponse result = accountsClient.getAccountWithClaims(accountId);
        
        // Then
        assertNotNull(result);
        assertEquals(accountId, result.getId().toString());
        
        verify(restTemplate).exchange(
                eq(BASE_URL + "/accounts/{accountId}/claims"),
                eq(HttpMethod.GET),
                isNull(),
                eq(AccountWithClaimsResponse.class),
                eq(accountId)
        );
    }
}