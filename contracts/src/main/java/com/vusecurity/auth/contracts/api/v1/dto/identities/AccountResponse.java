package com.vusecurity.auth.contracts.api.v1.dto.identities;

import java.util.Map;
import java.util.UUID;

import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "Account information response")
public class AccountResponse extends BaseResponse {
    @Schema(description = "Unique identifier of the account",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    @Schema(description = "Unique identifier of the business this account belongs to",
            example = "987fcdeb-51a2-43d1-9c4f-123456789abc")
    private UUID businessId;

    @Schema(description = "Name of the business this account belongs to",
            example = "Test Business")
    private String businessName;

    @Schema(description = "Unique identifier of the identity this account is associated with",
            example = "456e7890-a12b-34c5-d678-901234567def")
    private UUID identityId;

    @Schema(description = "Unique identifier of the identity provider",
            example = "789abcde-f012-3456-7890-123456789abc")
    private UUID identityProviderId;

    @Schema(description = "Type of account",
            example = "WORKFORCE")
    private AccountType accountType;

    @Schema(description = "Additional metadata for the account as key-value pairs",
            example = "{\"department\": \"Engineering\", \"role\": \"Developer\"}")
    private Map<String, Object> metadata;

    @Schema(description = "Current lifecycle state of the account",
            example = "ACTIVE")
    private AccountLifecycleState lifecycleState;

    @Schema(description = "Primary account ID for merged accounts",
            example = "111e2222-e33b-44d5-a666-************")
    private UUID mergePrimary;

    @Schema(description = "Secondary account ID for merged accounts",
            example = "aaa1bbbb-c22d-33e4-f555-************")
    private UUID mergeSecondary;
}
