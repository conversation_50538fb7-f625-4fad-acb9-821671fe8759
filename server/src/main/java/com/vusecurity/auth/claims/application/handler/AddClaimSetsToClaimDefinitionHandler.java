package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.AddClaimSetsToClaimDefinitionCommand;
import com.vusecurity.auth.claims.application.command.ClaimSetAssignmentCommand;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.service.ClaimSetDefinitionMappingService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AddClaimSetsToClaimDefinitionHandler {
    private static final Logger logger = LoggerFactory.getLogger(AddClaimSetsToClaimDefinitionHandler.class);

    private final ClaimDefinitionRepository claimDefinitionRepository;
    private final ClaimSetRepository claimSetRepository;
    private final ClaimSetDefinitionMappingService mappingService;

    @Transactional
    public void addClaimSetsToClaimDefinition(AddClaimSetsToClaimDefinitionCommand command) {
        logger.debug("Adding claim sets to claim definition with command: {}", command);

        try {
            // Validate command
            command.validate();

            // Find the claim definition
            ClaimDefinitionJpaEntity claimDefinition = claimDefinitionRepository.findById(command.claimDefinitionId())
                    .orElseThrow(() -> new IllegalArgumentException("Claim definition not found with ID: " + command.claimDefinitionId()));

            // Extract claim set IDs
            List<UUID> claimSetIds = command.claimSetsAssignment().stream()
                    .map(ClaimSetAssignmentCommand::claimSetId)
                    .toList();

            // Find all claim sets
            List<ClaimSetJpaEntity> claimSets = claimSetRepository.findAllById(claimSetIds);

            // Validate that all claim sets were found
            if (claimSets.size() != claimSetIds.size()) {
                throw new IllegalArgumentException("One or more claim sets not found");
            }

            // Create a map to easily access enforceUniqueness by ID
            Map<UUID, Boolean> enforceUniquenessMap = command.claimSetsAssignment().stream()
                    .collect(Collectors.toMap(ClaimSetAssignmentCommand::claimSetId, ClaimSetAssignmentCommand::enforceUniqueness));

            // Add claim definition to each claim set using the mapping service
            claimSets.forEach(claimSet -> {
                if (Boolean.TRUE.equals(claimSet.getIsIdentifier())) {
                    Boolean enforceUniqueness = enforceUniquenessMap.get(claimSet.getId());

                    if (enforceUniqueness != null && !enforceUniqueness) {
                        throw new IllegalArgumentException("Claim set with ID " + claimSet.getId() + " is an identifier and cannot have enforceUniqueness = false");
                    }
                }
                boolean finalEnforceUniqueness = Boolean.TRUE.equals(claimSet.getIsIdentifier())
                        || enforceUniquenessMap.getOrDefault(claimSet.getId(), false);
                mappingService.addClaimDefinitionToClaimSet(
                        claimSet,
                        claimDefinition,
                        null,
                        finalEnforceUniqueness
                );
            });

            logger.debug("Successfully added claim sets to claim definition: {}", command.claimDefinitionId());

        }
        catch (Exception e) {
            logger.error("Error adding claim sets to claim definition {}: {}", command.claimDefinitionId(), e.getMessage(), e);
            throw e;
        }
    }
}
