package com.vusecurity.auth.claims.application.query;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.contracts.enums.OwnerType;

import org.springframework.data.domain.Page;

import java.util.List;
import java.util.UUID;

public interface GetClaimValueQuery {
    ClaimValueJpaEntity getClaimValueById(UUID claimValueId);
    Page<ClaimValueJpaEntity> getAllClaimValues(GetClaimValuesPagedQuery query);
    Page<ClaimValueJpaEntity> getClaimValuesByValue(GetClaimValuesByValueQuery query);
    List<ClaimValueJpaEntity> getClaimValuesByOwnerTypeAndOwnerId(OwnerType ownerType, UUID claimValueId);
    List<ClaimValueJpaEntity> getRelatedClaimValuesByValue(OwnerType ownerType, UUID ownerId, String searchValue);
}
