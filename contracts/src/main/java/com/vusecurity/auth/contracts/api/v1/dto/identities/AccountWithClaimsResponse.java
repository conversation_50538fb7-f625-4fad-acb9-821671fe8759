package com.vusecurity.auth.contracts.api.v1.dto.identities;

import java.util.List;

import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.ClaimValueResponse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "Account information with claims response")
public class AccountWithClaimsResponse extends AccountResponse {

    @Schema(description = "List of Claims values associated with this account")
    private List<ClaimValueResponse> claimValues;

    public AccountWithClaimsResponse(AccountResponse accountResponse, List<ClaimValueResponse> claimValues){
        this.setId(accountResponse.getId());
        this.setBusinessId(accountResponse.getBusinessId());
        this.setBusinessName(accountResponse.getBusinessName());
        this.setIdentityId(accountResponse.getIdentityId());
        this.setIdentityProviderId(accountResponse.getIdentityProviderId());
        this.setAccountType(accountResponse.getAccountType());
        this.setMetadata(accountResponse.getMetadata());
        this.setLifecycleState(accountResponse.getLifecycleState());
        this.setMergePrimary(accountResponse.getMergePrimary());
        this.setMergeSecondary(accountResponse.getMergeSecondary());
        this.claimValues = claimValues;
    }
}
