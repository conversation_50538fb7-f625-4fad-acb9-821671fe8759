package com.vusecurity.auth.claims.api.handler;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.vusecurity.auth.claims.application.exception.*;
import com.vusecurity.core.commons.ApiMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.server.ResponseStatusException;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.stream.Collectors;

/**
 * Exception handler for ClaimDefinitionController.
 * Handles specific exceptions thrown by claim definition operations and converts them to appropriate HTTP responses.
 */
@ControllerAdvice(assignableTypes = {com.vusecurity.auth.claims.api.controller.ClaimDefinitionController.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ClaimDefinitionErrorControllerHandler {

    private static final Logger logger = LoggerFactory.getLogger(ClaimDefinitionErrorControllerHandler.class);

    /**
     * Handle ResponseStatusException thrown by claim definition controller.
     * This ensures that the HTTP status code from the ResponseStatusException is preserved.
     */
    @ExceptionHandler(ResponseStatusException.class)
    public ResponseEntity<ApiMessage> handleResponseStatusException(ResponseStatusException ex) {
        logger.debug("Handling ResponseStatusException: {} - {}", ex.getStatusCode(), ex.getReason());
        
        // Extract the status code and reason from the ResponseStatusException
        int statusCode = ex.getStatusCode().value();
        String message = ex.getReason() != null ? ex.getReason() : ex.getMessage();
        
        // Create appropriate error code based on status
        int errorCode = switch (statusCode) {
            case 400 -> 5200; // Bad Request
            case 404 -> 5204; // Not Found
            case 500 -> 5250; // Internal Server Error
            default -> 5200; // Generic error
        };
        
        ApiMessage error = new ApiMessage(errorCode, message);
        return ResponseEntity.status(ex.getStatusCode()).body(error);
    }

    /**
     * Handle NoSuchElementException thrown by claim definition services.
     * This is a fallback in case the service throws NoSuchElementException directly.
     */
    @ExceptionHandler(NoSuchElementException.class)
    public ResponseEntity<ApiMessage> handleNoSuchElementException(NoSuchElementException ex) {
        logger.debug("Handling NoSuchElementException: {}", ex.getMessage());
        
        ApiMessage error = new ApiMessage(5204, ex.getMessage());
        return ResponseEntity.status(404).body(error);
    }

    /**
     * Handle IllegalArgumentException thrown by claim definition services.
     * This is a fallback in case the service throws IllegalArgumentException directly.
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiMessage> handleIllegalArgumentException(IllegalArgumentException ex) {
        logger.debug("Handling IllegalArgumentException: {}", ex.getMessage());
        
        ApiMessage error = new ApiMessage(5200, ex.getMessage());
        return ResponseEntity.status(400).body(error);
    }

    @ExceptionHandler(InvalidFormatException.class)
    public ResponseEntity<ApiMessage> handleInvalidFormatException(InvalidFormatException ex) {
        logger.debug("Handling InvalidFormatException: {}", ex.getMessage());

        if (ex.getTargetType().isEnum()) {
            String fieldName = ex.getPath().stream()
                    .map(JsonMappingException.Reference::getFieldName)
                    .collect(Collectors.joining("."));

            Object[] enumConstants = ex.getTargetType().getEnumConstants();
            String allowedValues = String.join(", ",
                    Arrays.stream(enumConstants)
                            .map(Object::toString)
                            .toArray(String[]::new)
            );

            String message = String.format(
                    "Invalid value '%s' for field '%s'. Allowed values are: [%s]",
                    ex.getValue(), fieldName, allowedValues
            );

            ApiMessage error = new ApiMessage(5200, message);
            return ResponseEntity.status(400).body(error);
        }

        ApiMessage error = new ApiMessage(5200, "Invalid format in the application.");
        return ResponseEntity.status(400).body(error);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiMessage> handleValidationException(MethodArgumentNotValidException ex) {
        logger.debug("Handling MethodArgumentNotValidException");

        FieldError fieldError = ex.getBindingResult().getFieldError();
        String field = fieldError != null ? fieldError.getField() : "unknown";
        String defaultMessage = fieldError != null ? fieldError.getDefaultMessage() : "Validation error";

        String message = String.format("Error in the field '%s': %s", field, defaultMessage);

        ApiMessage error = new ApiMessage(5200, message);
        return ResponseEntity.status(400).body(error);
    }

    /**
     * Handle DeleteClaimTypeSystemException thrown by claim definition services.
     * This is a fallback in case the service throws DeleteClaimTypeSystemException directly.
     */
    @ExceptionHandler(DeleteClaimTypeSystemException.class)
    public ResponseEntity<ApiMessage> handleDeleteSystemClaimDefinitionException(DeleteClaimTypeSystemException ex) {
        logger.debug("Handling DeleteClaimTypeSystemException: {}", ex.getMessage());

        ApiMessage error = new ApiMessage(5200, ex.getMessage());
        return ResponseEntity.status(403).body(error);
    }

    /**
     * Handle UpdateClaimTypeSystemException thrown by claim definition services.
     * This is a fallback in case the service throws UpdateClaimTypeSystemException directly.
     */
    @ExceptionHandler(UpdateClaimTypeSystemException.class)
    public ResponseEntity<ApiMessage> handleAlterSystemClaimDefinitionException(UpdateClaimTypeSystemException ex) {
        logger.debug("Handling UpdateClaimTypeSystemException: {}", ex.getMessage());

        ApiMessage error = new ApiMessage(5200, ex.getMessage());
        return ResponseEntity.status(403).body(error);
    }

    /**
     * Handle ClaimDefinitionNotFoundException thrown by claim definition handlers.
     */
    @ExceptionHandler(ClaimDefinitionNotFoundException.class)
    public ResponseEntity<ApiMessage> handleClaimDefinitionNotFoundException(ClaimDefinitionNotFoundException ex) {
        logger.debug("Handling ClaimDefinitionNotFoundException: {}", ex.getMessage());

        ApiMessage error = new ApiMessage(5204, ex.getMessage());
        return ResponseEntity.status(404).body(error);
    }

    /**
     * Handle ClaimSetNotFoundException thrown by claim set operations.
     */
    @ExceptionHandler(ClaimSetNotFoundException.class)
    public ResponseEntity<ApiMessage> handleClaimSetNotFoundException(ClaimSetNotFoundException ex) {
        logger.debug("Handling ClaimSetNotFoundException: {}", ex.getMessage());

        ApiMessage error = new ApiMessage(5205, ex.getMessage());
        return ResponseEntity.status(404).body(error);
    }

    /**
     * Handle PatchClaimSetsException thrown by patch operations.
     */
    @ExceptionHandler(PatchClaimSetsException.class)
    public ResponseEntity<ApiMessage> handlePatchClaimSetsException(PatchClaimSetsException ex) {
        logger.debug("Handling PatchClaimSetsException: {}", ex.getMessage());

        ApiMessage error = new ApiMessage(5206, ex.getMessage());
        return ResponseEntity.status(400).body(error);
    }

    @ExceptionHandler(DuplicateClaimDefinitionException.class)
    public ResponseEntity<com.vusecurity.core.commons.ApiMessage> handleDuplicateClaimDefinitionException(DuplicateClaimDefinitionException ex) {
        logger.debug("Handling DuplicateClaimDefinitionException: {}", ex.getMessage());
        ApiMessage error = new ApiMessage(5201, ex.getMessage());
        return ResponseEntity.status(409).body(error);
    }
}
