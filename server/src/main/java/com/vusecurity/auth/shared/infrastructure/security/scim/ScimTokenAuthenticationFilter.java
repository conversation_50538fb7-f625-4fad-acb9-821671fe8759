package com.vusecurity.auth.shared.infrastructure.security.scim;

import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.context.SecurityContextHolderStrategy;
import org.springframework.security.web.context.RequestAttributeSecurityContextRepository;
import org.springframework.security.web.context.SecurityContextRepository;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * Authentication filter for scim authentication.
 * This filter intercepts requests to the sicm endpoints,
 * the token credentials from the header, creates a
 * ScimToken,
 * and delegates authentication to the ScimTokenAuthenticationProvider.
 *
 * Similar to BasicAuthenticationFilter, this filter allows the chain to
 * continue
 * after processing authentication.
 */
public class ScimTokenAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(ScimTokenAuthenticationFilter.class);

    private SecurityContextHolderStrategy securityContextHolderStrategy = SecurityContextHolder
            .getContextHolderStrategy();
    private final AuthenticationManager authenticationManager;
    private SecurityContextRepository securityContextRepository = new RequestAttributeSecurityContextRepository();

    /**
     * Constructor that sets up the filter
     */
    public ScimTokenAuthenticationFilter(AuthenticationManager authenticationManager) {
        this.authenticationManager = authenticationManager;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // Skip if already authenticated
        if (SecurityContextHolder.getContext().getAuthentication() != null &&
                SecurityContextHolder.getContext().getAuthentication().isAuthenticated()) {
            filterChain.doFilter(request, response);
            return;
        }

        logger.debug("Processing scim authentication request");

        try {
            Authentication authResult = attemptAuthentication(request);

            SecurityContext context = this.securityContextHolderStrategy.createEmptyContext();
            context.setAuthentication(authResult);
            this.securityContextHolderStrategy.setContext(context);
            logger.debug("Set SecurityContextHolder to {}", authResult);

            this.securityContextRepository.saveContext(context, request, response);

        } catch (AuthenticationException e) {
            this.securityContextHolderStrategy.clearContext();
            logger.debug("Scim authentication failed: {}", e.getMessage());
        }

        // Continue filter chain for non-auth requests
        filterChain.doFilter(request, response);
    }

    /**
     * Attempt authentication similar to the original method
     */
    private Authentication attemptAuthentication(HttpServletRequest request)
            throws AuthenticationException, BadCredentialsException {
        logger.debug("Attempting scim authentication from filter");

        // Read and parse the request
        String authorizationHeader = request.getHeader("Authorization");
        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            String token = authorizationHeader.substring("Bearer ".length());
            
            ScimToken authToken = new ScimToken(token);
            // Delegate to the authentication manager
            return authenticationManager.authenticate(authToken);
        }
        
        logger.debug("Not header for Authorization was found");
        throw new BadCredentialsException("Invalid request format");
    }
}
