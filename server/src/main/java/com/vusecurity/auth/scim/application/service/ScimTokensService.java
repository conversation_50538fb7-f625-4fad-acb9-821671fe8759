package com.vusecurity.auth.scim.application.service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Collection;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimTokenJpaEntity;
import com.vusecurity.auth.scim.infrastructure.persistence.jpa.repository.ScimTokensRepository;

import lombok.extern.slf4j.Slf4j;

@Service
@Transactional
@Slf4j
public class ScimTokensService {

    private final ScimTokensRepository scimTokensRepository;

    public ScimTokensService(ScimTokensRepository scimTokensRepository){
        this.scimTokensRepository = scimTokensRepository;
    }

    public List<ScimTokenJpaEntity> getByBusinessId(UUID businessId){
        return this.scimTokensRepository.findAllByBusinessId(businessId);
    }

    public Page<ScimTokenJpaEntity> getAll(Pageable page){
        return this.getAll(null, page);
    }

    public Page<ScimTokenJpaEntity> getAll(Specification<ScimTokenJpaEntity> spec, Pageable page){
        return this.scimTokensRepository.findAll(spec, page);
    }

    public Optional<ScimTokenJpaEntity> get(UUID id){
        return this.scimTokensRepository.findById(id);
    }

    public ScimTokenJpaEntity save(ScimTokenJpaEntity entity){
        return this.scimTokensRepository.saveAndFlush(entity);
    }

    public void saveAll(Collection<ScimTokenJpaEntity> entityList){
        this.scimTokensRepository.saveAllAndFlush(entityList);
    }

    public ScimTokenJpaEntity activateToken(ScimTokenJpaEntity token){
        token.activate();
        return save(token);
    }

    public ScimTokenJpaEntity deactivateToken(ScimTokenJpaEntity token){
        token.deactivate();
        return save(token);
    }

    public ScimTokenJpaEntity deleteToken(ScimTokenJpaEntity token){
        token.delete();
        return save(token);
    }

    public ScimTokenJpaEntity findByPlainToken(String token) throws NoSuchElementException {
            String hashedToken = this.hashToken(token);
            return this.scimTokensRepository.findByHashedTokenAndIsActive(hashedToken).orElseThrow();
    }

    public String hashToken(String token){
        
        try {
			MessageDigest md = MessageDigest.getInstance("SHA-512");
			byte[] bytes = md.digest(token.toString().getBytes(StandardCharsets.UTF_8));
			StringBuilder sb = new StringBuilder();
			for(int i=0; i< bytes.length ;i++){
				sb.append(Integer.toString((bytes[i] & 0xff) + 0x100, 16).substring(1));
			}
			return sb.toString();
		} catch (NoSuchAlgorithmException e) {
			log.error("ScimTokensService - hashToken - NoSuchAlgorithmException", e);
		}

        return null;
    }

}
