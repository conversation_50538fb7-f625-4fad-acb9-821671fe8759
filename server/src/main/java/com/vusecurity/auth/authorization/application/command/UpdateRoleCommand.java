package com.vusecurity.auth.authorization.application.command;

import java.util.Set;
import java.util.UUID;

public record UpdateRoleCommand(
        UUID roleId,
        String name,
        String description,
        UUID businessId,
        String status,
        Set<UUID> permissionIds
) {
    public void validate() {
        if (roleId == null) {
            throw new IllegalArgumentException("roleId is required");
        }
    }
}
