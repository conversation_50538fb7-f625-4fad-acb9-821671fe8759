package com.vusecurity.auth.identities.application.handler;

import com.vusecurity.auth.contracts.enums.IdentityType;
import com.vusecurity.auth.identities.application.query.GetIdentitiesPagedQuery;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GetIdentityHandlerTest {

    @Mock
    private IdentityRepository identityRepository;

    @InjectMocks
    private GetIdentityHandler getIdentityHandler;

    private IdentityJpaEntity identity;
    private AccountJpaEntity account;
    private UUID identityId;
    private UUID businessId;

    @BeforeEach
    void setUp() {
        identityId = UUID.randomUUID();
        businessId = UUID.randomUUID();
        identity = new IdentityJpaEntity(IdentityType.PERSON, "Test Identity");
        identity.setId(identityId);
        account = mock(AccountJpaEntity.class);
        identity.setAccounts(List.of(account));
    }

    @Test
    void shouldReturnIdentity_whenIdentityIdExists() {
        // Given
        when(identityRepository.findByIdentityId(identityId)).thenReturn(java.util.Optional.of(identity));

        // When
        IdentityJpaEntity result = getIdentityHandler.getIdentityById(identityId.toString());

        // Then
        assertNotNull(result);
        assertEquals(identityId, result.getId());
        verify(identityRepository).findByIdentityId(identityId);
    }

    @Test
    void shouldThrowIdentityNotFoundException_whenIdentityIdDoesNotExist() {
        // Given
        when(identityRepository.findByIdentityId(identityId)).thenReturn(java.util.Optional.empty());

        // When & Then
        assertThrows(
                com.vusecurity.auth.identities.application.exception.IdentityNotFoundException.class,
                () -> getIdentityHandler.getIdentityById(identityId.toString())
        );
        verify(identityRepository).findByIdentityId(identityId);
    }

    @Test
    void shouldThrowIllegalArgumentException_whenIdentityIdIsInvalid() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> getIdentityHandler.getIdentityById("not-a-uuid"));
        verifyNoInteractions(identityRepository);
    }

    @Test
    void shouldReturnPagedIdentities_whenNameFilterIsProvided() {
        // Given
        GetIdentitiesPagedQuery query = GetIdentitiesPagedQuery.builder().page(1).pageSize(10).name("Test").build();
        Page<IdentityJpaEntity> expectedPage = new PageImpl<>(List.of(identity));
        when(identityRepository.findByNameContaining(eq("Test"), any(PageRequest.class))).thenReturn(expectedPage);

        // When
        Page<IdentityJpaEntity> result = getIdentityHandler.getAllIdentities(query);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(identityRepository).findByNameContaining(eq("Test"), any(PageRequest.class));
    }

    @Test
    void shouldReturnPagedIdentities_whenIdentityTypeFilterIsProvided() {
        // Given
        GetIdentitiesPagedQuery query = GetIdentitiesPagedQuery.builder().page(1).pageSize(10).identityType("PERSON").build();
        Page<IdentityJpaEntity> expectedPage = new PageImpl<>(List.of(identity));
        when(identityRepository.findByIdentityType(eq(IdentityType.PERSON), any(PageRequest.class))).thenReturn(expectedPage);

        // When
        Page<IdentityJpaEntity> result = getIdentityHandler.getAllIdentities(query);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(identityRepository).findByIdentityType(eq(IdentityType.PERSON), any(PageRequest.class));
    }

    @Test
    void shouldReturnPagedIdentities_whenLifecycleStateFilterIsProvided() {
        // Given
        GetIdentitiesPagedQuery query = GetIdentitiesPagedQuery.builder().page(1).pageSize(10).lifecycleState("ACTIVE").build();
        Page<IdentityJpaEntity> expectedPage = new PageImpl<>(List.of(identity));
        when(identityRepository.findByLifecycleState(eq("ACTIVE"), any(PageRequest.class))).thenReturn(expectedPage);

        // When
        Page<IdentityJpaEntity> result = getIdentityHandler.getAllIdentities(query);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(identityRepository).findByLifecycleState(eq("ACTIVE"), any(PageRequest.class));
    }

    @Test
    void shouldReturnPagedIdentities_whenNoFilterIsProvided() {
        // Given
        GetIdentitiesPagedQuery query = GetIdentitiesPagedQuery.builder().page(1).pageSize(10).build();
        Page<IdentityJpaEntity> expectedPage = new PageImpl<>(List.of(identity));
        when(identityRepository.findAll(any(PageRequest.class))).thenReturn(expectedPage);

        // When
        Page<IdentityJpaEntity> result = getIdentityHandler.getAllIdentities(query);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(identityRepository).findAll(any(PageRequest.class));
    }
} 