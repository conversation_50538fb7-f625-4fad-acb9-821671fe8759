package com.vusecurity.auth.identities.application.handler;

import com.vusecurity.auth.claims.application.service.ClaimVerificationHelper;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.identities.application.query.GetAccountClaimsQuery;
import com.vusecurity.auth.shared.enums.ResultType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Handler for retrieving claim values and definitions for accounts.
 * Implements the GetAccountClaimsQuery following the CQRS pattern.
 */
@Service
@Transactional(readOnly = true)
public class GetAccountClaimsHandler implements GetAccountClaimsQuery {

    private static final Logger logger = LoggerFactory.getLogger(GetAccountClaimsHandler.class);

    private final ClaimValueRepository claimValueRepository;
    private final ClaimVerificationHelper verificationHelper;

    public GetAccountClaimsHandler(ClaimValueRepository claimValueRepository,
                                  ClaimVerificationHelper verificationHelper) {
        this.claimValueRepository = claimValueRepository;
        this.verificationHelper = verificationHelper;
    }

    @Override
    public Map<String, Object> getClaimValuesByAccountId(UUID accountId) {
        logger.debug("Getting claim values with verification data for account: {}", accountId);

        // Use the new method that loads verification data in a single query
        List<ClaimValueJpaEntity> claimValues = verificationHelper.getClaimValuesWithVerification(accountId);

        Map<String, Object> claimMap = new HashMap<>();
        for (ClaimValueJpaEntity claimValue : claimValues) {
            if (claimValue.getClaimDefinition() != null) {
                String claimCode = claimValue.getClaimDefinition().getCode();

                // Handle multiple values for the same claim (e.g., multiple email addresses)
                if (claimMap.containsKey(claimCode)) {
                    handleExistingClaim(claimMap, claimValue, claimCode);
                } else {
                    handleNewClaim(claimMap, claimValue, claimCode);
                }
            }
        }

        logger.debug("Retrieved {} claim values for account {}", claimMap.size(), accountId);
        return claimMap;
    }

    @Override
    public Map<String, String> getClaimDefinitionNamesByAccountId(UUID accountId) {
        logger.debug("Getting claim definition names for account: {}", accountId);

        // We can use the regular method here since we don't need verification data for this operation
        List<ClaimValueJpaEntity> claimValues = claimValueRepository.findByAccountIdWithDefinition(accountId);

        Map<String, String> definitionMap = new HashMap<>();
        for (ClaimValueJpaEntity claimValue : claimValues) {
            if (claimValue.getClaimDefinition() != null) {
                String claimId = claimValue.getClaimDefinition().getId().toString();
                String claimCode = claimValue.getClaimDefinition().getCode();
                definitionMap.put(claimId, claimCode);
            }
        }

        logger.debug("Retrieved {} claim definition names for account {}", definitionMap.size(), accountId);
        return definitionMap;
    }


    @Override
    public boolean hasClaimValue(UUID accountId, String claimCode, String expectedValue) {
        logger.debug("Checking if account {} has claim {} with value {}", accountId, claimCode, expectedValue);

        // Use verification helper to get claims with verification data
        List<ClaimValueJpaEntity> claimValues = verificationHelper.getClaimValuesWithVerification(accountId);

        for (ClaimValueJpaEntity claimValue : claimValues) {
            if (claimValue.getClaimDefinition() != null &&
                    claimCode.equals(claimValue.getClaimDefinition().getCode()) &&
                    expectedValue.equals(claimValue.getValue())) {

                logger.debug("Found matching claim value. Verified: {}, Verification result: {}",
                           claimValue.isVerified(), claimValue.getVerificationResult());
                return true;
            }
        }

        return false;
    }

    /**
     * Enhanced version of hasClaimValue that also checks if the claim is verified.
     *
     * @param accountId the account ID
     * @param claimCode the claim code to check
     * @param expectedValue the expected value
     * @param requireVerified whether the claim must be verified
     * @return true if the claim exists with the expected value and meets verification requirements
     */
    public boolean hasVerifiedClaimValue(UUID accountId, String claimCode, String expectedValue, boolean requireVerified) {
        logger.debug("Checking if account {} has {} claim {} with value {} (require verified: {})",
                   accountId, requireVerified ? "verified" : "any", claimCode, expectedValue, requireVerified);

        List<ClaimValueJpaEntity> claimValues = verificationHelper.getClaimValuesWithVerification(accountId);

        for (ClaimValueJpaEntity claimValue : claimValues) {
            if (claimValue.getClaimDefinition() != null &&
                    claimCode.equals(claimValue.getClaimDefinition().getCode()) &&
                    expectedValue.equals(claimValue.getValue())) {

                boolean isVerified = claimValue.isVerified();
                logger.debug("Found matching claim value. Verified: {}, Verification result: {}",
                           isVerified, claimValue.getVerificationResult());

                // If verification is required, check if the claim is verified
                if (requireVerified) {
                    return isVerified;
                }

                // If verification is not required, just return true for any match
                return true;
            }
        }

        return false;
    }

    /**
     * Handles processing of a claim that already exists in the result map.
     * Updates existing claim with additional values and verification information.
     */
    private void handleExistingClaim(Map<String, Object> claimMap, ClaimValueJpaEntity claimValue, String claimCode) {
        if (claimValue.getClaimDefinition().getIsAListOf() != null
                && claimMap.get(claimCode) instanceof Map) {
            claimMap.computeIfPresent(claimCode, (key, existingValue) -> {
                @SuppressWarnings("unchecked")
                Map<String, Object> valueMap = (Map<String, Object>) existingValue;

                // Add the new value
                String subClaimCode = claimValue.getClaimDefinition().getIsAListOf().getCode();
                valueMap.put(subClaimCode, claimValue.getValue());

                // Add metadata for this specific value
                addClaimMetadata(valueMap, claimValue);

                return valueMap;
            });
        }
    }

    /**
     * Handles processing of a new claim that doesn't exist in the result map yet.
     * Creates appropriate structure based on whether it's a list claim or single value.
     */
    private void handleNewClaim(Map<String, Object> claimMap, ClaimValueJpaEntity claimValue, String claimCode) {
        if (claimValue.getClaimDefinition().getIsAListOf() != null) {
            // This is a list-type claim (e.g., multiple emails)
            Map<String, Object> valueMap = new HashMap<>();
            String subClaimCode = claimValue.getClaimDefinition().getIsAListOf().getCode();
            valueMap.put(subClaimCode, claimValue.getValue());

            // Add metadata
            addClaimMetadata(valueMap, claimValue);

            claimMap.put(claimCode, valueMap);
        } else {
            // This is a simple single-value claim
            // For backward compatibility, we can either return just the value or an enriched object
            // Let's create an enriched object that includes verification info
            Map<String, Object> claimInfo = createClaimInfo(claimValue);
            claimMap.put(claimCode, claimInfo);
        }
    }

    /**
     * Creates a comprehensive claim information object with value and verification data.
     */
    private Map<String, Object> createClaimInfo(ClaimValueJpaEntity claimValue) {
        Map<String, Object> claimInfo = new HashMap<>();

        // Basic value
        claimInfo.put("value", claimValue.getValue());

        // Add metadata
        addClaimMetadata(claimInfo, claimValue);

        return claimInfo;
    }

    /**
     * Adds metadata (primary, verification info) to a claim value map.
     */
    private void addClaimMetadata(Map<String, Object> valueMap, ClaimValueJpaEntity claimValue) {
        // Primary flag
        if (claimValue.isPrimary()) {
            valueMap.put("primary", true);
        }

        // Verification information
        addVerificationInfo(valueMap, claimValue);
    }

    /**
     * Adds comprehensive verification information to a claim value map.
     */
    private void addVerificationInfo(Map<String, Object> valueMap, ClaimValueJpaEntity claimValue) {
        // Basic verification status
        boolean isVerified = claimValue.isVerified();       

        // Legacy field for backward compatibility
        valueMap.put("isVerified", isVerified);
    }

}
