package com.vusecurity.auth.business.api.controller;

import static com.vusecurity.auth.authorization.domain.PermissionNames.BUSINESS_CREATE;
import static com.vusecurity.auth.authorization.domain.PermissionNames.BUSINESS_DELETE;
import static com.vusecurity.auth.authorization.domain.PermissionNames.BUSINESS_READ;
import static com.vusecurity.auth.authorization.domain.PermissionNames.BUSINESS_UPDATE;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.vusecurity.auth.shared.config.aspect.Oauth2Authorize;
import com.vusecurity.business.commons.api.model.request.CreateBusinessRequest;
import com.vusecurity.business.commons.api.model.request.UpdateBusinessRequest;
import com.vusecurity.business.commons.api.model.response.BusinessResponse;
import com.vusecurity.business.commons.api.model.response.BusinessResponseBuilder;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.usecases.interfaces.ICreateBusiness;
import com.vusecurity.business.usecases.interfaces.IDeleteBusiness;
import com.vusecurity.business.usecases.interfaces.IRetrieveBusiness;
import com.vusecurity.business.usecases.interfaces.IUpdateBusiness;
import com.vusecurity.core.commons.models.PageableResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

@RestController
@SecurityRequirement(name = "oauth2 client credentials")
@Tag(name = "Business", description = "Business management operations")
public class BusinessController {
        private final IRetrieveBusiness retrieveBusiness;
        private final ICreateBusiness createBusiness;
        private final IUpdateBusiness updateBusiness;
        private final IDeleteBusiness deleteBusiness;

        public BusinessController(IRetrieveBusiness retrieveBusiness, ICreateBusiness createBusiness,
                        IUpdateBusiness updateBusiness, IDeleteBusiness deleteBusiness) {
                this.retrieveBusiness = retrieveBusiness;
                this.createBusiness = createBusiness;
                this.updateBusiness = updateBusiness;
                this.deleteBusiness = deleteBusiness;
        }

        @Operation(summary = "Get all businesses", description = "Retrieves a paginated list of businesses with optional filtering by business type. Business types include TENANT, LEGAL_ENTITY, and BUSINESS_UNIT.")
        @ApiResponses({
                        @ApiResponse(responseCode = "200", description = "Successful retrieval", content = @Content(schema = @Schema(implementation = PageableResponse.class))),
                        @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
                        @ApiResponse(responseCode = "401", description = "Authentication required"),
                        @ApiResponse(responseCode = "403", description = "Access denied")
        })
        @GetMapping("${app.business.context-path}/businesses")
        @Oauth2Authorize(permission = BUSINESS_READ)
        public PageableResponse<BusinessResponse> get(
                        @RequestParam(name = "page", defaultValue = "1") int page,
                        @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                        @RequestParam(name = "businessType", required = false) String businessType) {
                Page<Business> businesses = retrieveBusiness.retrieveAll(page, pageSize,
                                Optional.ofNullable(businessType));

                return new PageableResponse<>(businesses.getNumber() + 1, businesses.getSize(),
                                businesses.getTotalElements(),
                                businesses.map(BusinessController::getBusinessResponse).toList());
        }

        @Operation(summary = "Create a new business", description = "Allows you to create a new business with its corresponding details. The name is required and must be unique")
        @ApiResponse(responseCode = "201", description = "Business created successfully", content = {
                        @Content(mediaType = "application/json", schema = @Schema(implementation = BusinessResponse.class)) })
        @ApiResponse(responseCode = "400", description = "Incorrect request. Some attribute sent is invalid")
        @ApiResponse(responseCode = "409", description = "A unique field is already registered in another entity")
        @PostMapping("${app.business.context-path}/businesses")
        @ResponseStatus(HttpStatus.CREATED)
        @Oauth2Authorize(permission = BUSINESS_CREATE)
        public BusinessResponse create(@Valid @RequestBody CreateBusinessRequest request) {
                Business business = createBusiness.create(
                                request.getName(),
                                request.getDescription(),
                                request.getBusinessType(),
                                request.getLogo(),
                                request.getAccentColor(),
                                request.getMetadata(),
                                "SYSTEM");

                return getBusinessResponse(business);
        }

        @Operation(summary = "Get a business by id", description = "Retrieves a single business by its id")
        @ApiResponse(responseCode = "200", description = "Successful retrieval", content = {
                        @Content(mediaType = "application/json", schema = @Schema(implementation = BusinessResponse.class)) })
        @ApiResponse(responseCode = "404", description = "Business not found")
        @GetMapping("${app.business.context-path}/businesses/{businessId}")
        @Oauth2Authorize(permission = BUSINESS_READ)
        public BusinessResponse get(@PathVariable(value = "businessId") UUID businessId) {
                Business business = retrieveBusiness.retrieveById(businessId);

                return getBusinessResponse(business);
        }

        @Operation(summary = "Partially update a business", description = "Allows you to update one or more properties of a business by its id")
        @ApiResponse(responseCode = "204", description = "Business updated successfully")
        @ApiResponse(responseCode = "400", description = "Validation error")
        @ApiResponse(responseCode = "404", description = "Business not found")
        @ApiResponse(responseCode = "409", description = "A unique field is already registered in another entity")
        @PatchMapping("${app.business.context-path}/businesses/{businessId}")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        @Oauth2Authorize(permission = BUSINESS_UPDATE)
        public void update(@PathVariable(value = "businessId") UUID businessId,
                        @Valid @RequestBody UpdateBusinessRequest request) {
                updateBusiness.update(
                                businessId,
                                request.getName(),
                                request.getDescription(),
                                request.getBusinessType(),
                                request.getLogo(),
                                request.getAccentColor(),
                                request.getMetadata(),
                                "SYSTEM");
        }

        @Operation(summary = "Delete a business", description = "Allows you to delete a business by its id")
        @ApiResponse(responseCode = "204", description = "Business deleted successfully")
        @ApiResponse(responseCode = "400", description = "Validation error")
        @ApiResponse(responseCode = "404", description = "Business not found")
        @DeleteMapping("${app.business.context-path}/businesses/{businessId}")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        @Oauth2Authorize(permission = BUSINESS_DELETE)
        public void delete(@PathVariable(value = "businessId") UUID businessId) {
                deleteBusiness.deleteById(businessId);
        }

        private static BusinessResponse getBusinessResponse(Business business) {
                return new BusinessResponseBuilder()
                                .setUuid(business.getId())
                                .setName(business.getName())
                                .setDescription(business.getDescription())
                                .setBusinessType(business.getBusinessType().name())
                                .setLogo(business.getLogo())
                                .setAccentColor(business.getAccentColor())
                                .setMetadata(business.getMetadata())
                                .setCreatedAt(Date.from(business.getCreatedAt()))
                                .setCreatedBy(business.getCreatedBy())
                                .setUpdatedAt(Date.from(business.getUpdatedAt()))
                                .setUpdatedBy(business.getUpdatedBy())
                                .createBusinessResponse();
        }

        @Operation(summary = "Search businesses by partial name", description = "Retrieves a list of businesses whose names contain the provided query string. The search is partial and case insensitive. Returns an empty list if no matches are found")
        @ApiResponse(responseCode = "200", description = "Successful retrieval", content = {
                        @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = BusinessResponse.class))) })
        @ApiResponse(responseCode = "400", description = "Validation error")
        @GetMapping("${app.business.context-path}/businesses/search")
        @Oauth2Authorize(permission = BUSINESS_READ)
        public List<BusinessResponse> searchByName(@RequestParam(value = "name") String name) {
                List<Business> businessList = retrieveBusiness.retrieveBusinessesByName(name);

                return businessList.stream()
                                .map(BusinessController::getBusinessResponse)
                                .toList();

        }
}