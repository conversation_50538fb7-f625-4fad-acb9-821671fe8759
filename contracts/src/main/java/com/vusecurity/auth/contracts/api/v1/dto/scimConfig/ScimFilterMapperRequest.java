package com.vusecurity.auth.contracts.api.v1.dto.scimConfig;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.UUID;

@Data
@Schema(description = "Request to create a new SCIM to claims mapper")
public class ScimFilterMapperRequest {

    @NotNull(message = "claimSetId is required")
    @Schema(description = "Origin claimSetId",
            example = "f9fc0a64-56fc-4477-87a7-8e525505abce")
    private UUID claimSetIdOrigin;

    @Schema(description = "Referenced claimSetId.", example = "f9fc0a64-56fc-4477-87a7-8e525505abce", defaultValue = "null")
    private UUID claimSetIdReferenced;

    @NotNull(message = "claimDefinitionId is required")
    @Schema(description = "Unique identifier of the claim definition",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID claimDefinitionId;

    @NotNull(message = "filter is required")
    @Schema(description = "SCIM Filter that represents the extracted value to be saved on a claim. e.g.",
            examples = {
                "userName",
                "name.giuveName",
                "emails[primary eq true].value"
            })
    private String filter;

    @Schema(description = "Order of the secuence", example = "1", defaultValue = "0")
    private Integer sequenceOrder;

    @Size(max = 255, message = "Description must not exceed 255 characters")
    @Schema(description = "Description of the filter",
            example = "Retrieves the primary email",
            defaultValue = "")
    private String description;
}
