package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.UpdateClaimDefinitionCommand;
import com.vusecurity.auth.claims.application.exception.ClaimDefinitionNotFoundException;
import com.vusecurity.auth.claims.application.exception.DuplicateClaimDefinitionException;
import com.vusecurity.auth.claims.application.exception.UpdateClaimTypeSystemException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.contracts.enums.ClaimType;

import com.vusecurity.auth.shared.util.DatabaseConstraintUtils;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Transactional
public class UpdateClaimDefinitionHandler {

    private static final Logger logger = LoggerFactory.getLogger(UpdateClaimDefinitionHandler.class);
    
    private final ClaimDefinitionRepository claimDefinitionRepository;

    public void updateClaimDefinition(UpdateClaimDefinitionCommand command) {
        this.updateClaimDefinition(command, false);
    }

    public void updateClaimDefinition(UpdateClaimDefinitionCommand command, boolean allowToEditSystemClaim) {
        logger.debug("Updating claim definition: {}", command.claimDefinitionId());

        // Validate command
        command.validate();

        // Find existing claim definition
        ClaimDefinitionJpaEntity claimDefinition = claimDefinitionRepository.findById(command.claimDefinitionId())
                .orElseThrow(() -> new ClaimDefinitionNotFoundException(command.claimDefinitionId()));
        
        if (claimDefinition.getClaimType().equals(ClaimType.SYSTEM_DEFINED) && !allowToEditSystemClaim){
            throw new UpdateClaimTypeSystemException();
        }

        // Update code if provided and different
        if (command.code() != null && !command.code().equals(claimDefinition.getCode())) {
            claimDefinition.setCode(command.code());
        }

        // Update fields if provided
        if (command.name() != null) {
            claimDefinition.setName(command.name());
        }

        if (command.description() != null) {
            claimDefinition.setDescription(command.description());
        }

        if (command.dataFormat() != null) {
            claimDefinition.setDataType(command.dataFormat());
        }

        if (command.format() != null) {
            claimDefinition.setDataFormat(command.format());
        }

        // Save the updated entity with constraint handling for unique code
        DatabaseConstraintUtils.executeWithConstraintHandling(
            () -> claimDefinitionRepository.saveAndFlush(claimDefinition),
            "uk_claim_def_code",
            () -> new DuplicateClaimDefinitionException(
                String.format("Claim definition with code '%s' already exists", command.code())
            )
        );
        
        logger.debug("Claim definition updated successfully: {}", command.claimDefinitionId());
    }
}
