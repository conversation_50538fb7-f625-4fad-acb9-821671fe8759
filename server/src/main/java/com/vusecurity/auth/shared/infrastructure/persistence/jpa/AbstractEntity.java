package com.vusecurity.auth.shared.infrastructure.persistence.jpa;

import com.vusecurity.core.commons.Auditable;
import jakarta.persistence.*;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Objects;
import java.util.UUID;

/**
 * Every table shares:
 * • A UUID primary key that serves as both internal and external identifier
 * • Stable UUID that never changes and is unique
 * Enhanced with application-layer audit user management from SecurityContext.
 */
@MappedSuperclass
@SuppressWarnings("LombokGetterMayBeUsed, LombokSetterMayBeUsed")
public abstract class AbstractEntity extends Auditable {

    private static final String SYSTEM_USER = "SYSTEM";

    @Id
    @Column(name = "id", nullable = false, updatable = false)
    private UUID id = UUID.randomUUID(); // PRIMARY KEY – used for every FK and public access

    /**
     * Override parent's prePersist to set user from SecurityContext.
     * Timestamps are still handled by parent class.
     */
    @Override
    @PrePersist
    public void prePersist() {
        // Call parent to handle timestamps
        super.prePersist();

        // Set user from SecurityContext
        String currentUser = getCurrentUser();
        this.setCreatedBy(currentUser);
        this.setUpdatedBy(currentUser);
    }

    /**
     * Override parent's preUpdate to set user from SecurityContext.
     * Timestamps are still handled by parent class.
     */
    @Override
    @PreUpdate
    public void preUpdate() {
        // Call parent to handle timestamps
        super.preUpdate();

        // Set user from SecurityContext
        String currentUser = getCurrentUser();
        this.setUpdatedBy(currentUser);
    }

    /**
     * Gets current user from SecurityContext with fallback to SYSTEM.
     */
    private String getCurrentUser() {
        return SecurityContextHolder.getContext().getAuthentication() != null
                ? SecurityContextHolder.getContext().getAuthentication().getName()
                : SYSTEM_USER;
    }

    // Constructors
    protected AbstractEntity() {
    }

    protected AbstractEntity(UUID id) {
        this.id = id;
    }

    // Getters and Setters
    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    // equals() and hashCode() using id
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        AbstractEntity that = (AbstractEntity) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), id);
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + "{" +
                "id=" + id +
                '}';
    }
}
