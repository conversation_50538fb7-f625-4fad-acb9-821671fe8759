package com.vusecurity.auth.shared.application.service;

import com.nimbusds.jose.jwk.RSAKey;
import com.vusecurity.auth.shared.infrastructure.persistence.jpa.entity.JwkJpaEntity;
import com.vusecurity.auth.shared.infrastructure.persistence.jpa.repository.JwkRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class JwkManagementServiceTest {

    @Mock
    private JwkRepository jwkRepository;

    @InjectMocks
    private JwkManagementService jwkManagementService;

    @Test
    void getOrCreateRsaKey_WhenNoExistingKey_ShouldGenerateNewKey() {
        // Given
        when(jwkRepository.findFirstActive()).thenReturn(Optional.empty());
        when(jwkRepository.save(any(JwkJpaEntity.class))).thenReturn(new JwkJpaEntity());

        // When
        RSAKey result = jwkManagementService.getOrCreateRsaKey();

        // Then
        assertNotNull(result);
        assertNotNull(result.getKeyID());
        verify(jwkRepository).findFirstActive();
        verify(jwkRepository).save(any(JwkJpaEntity.class));
    }

    @Test
    void getOrCreateRsaKey_WhenExistingKey_ShouldLoadFromDatabase() {
        // Given
        JwkJpaEntity existingEntity = createValidJwkEntity();
        when(jwkRepository.findFirstActive()).thenReturn(Optional.of(existingEntity));

        // When
        RSAKey result = jwkManagementService.getOrCreateRsaKey();

        // Then
        assertNotNull(result);
        assertEquals("test-key-id", result.getKeyID());
        verify(jwkRepository).findFirstActive();
        verify(jwkRepository, never()).save(any(JwkJpaEntity.class));
    }

    @Test
    void invalidateCache_ShouldClearCache() {
        // Given - First call to populate cache
        when(jwkRepository.findFirstActive()).thenReturn(Optional.empty());
        when(jwkRepository.save(any(JwkJpaEntity.class))).thenReturn(new JwkJpaEntity());
        
        RSAKey firstCall = jwkManagementService.getOrCreateRsaKey();
        
        // When
        jwkManagementService.invalidateCache();
        
        // Reset mock to verify new call
        reset(jwkRepository);
        when(jwkRepository.findFirstActive()).thenReturn(Optional.empty());
        when(jwkRepository.save(any(JwkJpaEntity.class))).thenReturn(new JwkJpaEntity());
        
        RSAKey secondCall = jwkManagementService.getOrCreateRsaKey();

        // Then
        assertNotNull(firstCall);
        assertNotNull(secondCall);
        // Should have made a new database call after cache invalidation
        verify(jwkRepository).findFirstActive();
    }

    private JwkJpaEntity createValidJwkEntity() {
        // Create a simple RSA key for testing
        RSAKey testKey = com.vusecurity.auth.shared.util.Jwks.generateRsa();
        
        try {
            String publicKeyPem = java.util.Base64.getEncoder().encodeToString(
                testKey.toRSAPublicKey().getEncoded());
            String privateKeyPem = java.util.Base64.getEncoder().encodeToString(
                testKey.toRSAPrivateKey().getEncoded());

            return new JwkJpaEntity(
                UUID.randomUUID(),
                "test-key-id",
                publicKeyPem,
                privateKeyPem,
                "RS256",
                "sig"
            );
        } catch (Exception e) {
            throw new RuntimeException("Failed to create test entity", e);
        }
    }
}