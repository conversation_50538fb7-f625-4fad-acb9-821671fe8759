package com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

/**
 * Spring Data JPA repository for AccountGroupJpaEntity.
 * Infrastructure-only interface for database operations.
 */
@Repository
public interface AccountGroupRepository extends JpaRepository<AccountGroupJpaEntity, UUID>, JpaSpecificationExecutor<AccountGroupJpaEntity> {
    
    /**
     * Find account group by name.
     * @param name the account group name
     * @return the account group if found
     */
    @Query("SELECT ag FROM AccountGroupJpaEntity ag WHERE ag.name = :name")
    Optional<AccountGroupJpaEntity> findByName(@Param("name") String name);

    /**
     * Check if account group exists by name.
     * @param name the account group name
     * @return true if exists, false otherwise
     */
    @Query("SELECT COUNT(ag) > 0 FROM AccountGroupJpaEntity ag WHERE ag.name = :name")
    boolean existsByName(@Param("name") String name);

    @Query("SELECT g FROM AccountGroupJpaEntity g LEFT JOIN FETCH g.roles WHERE g.id = :id")
    Optional<AccountGroupJpaEntity> findByIdWithRoles(@Param("id") UUID id);

    @Query("SELECT ag FROM AccountGroupJpaEntity ag WHERE ag.id = :groupId AND ag.status = 'ACTIVE'")
    Optional<AccountGroupJpaEntity> findByIdAndStatusIsActive(UUID groupId);
}
