/*
 * Flyway Migration: V2__seed_static_foundation.sql
 * Purpose: Insert shared reference data (system business, identity providers, channels, claims).
 * Note: Replaces cross-tenant, non-secret portions of legacy Java seeders with SQL-based data provisioning.
 */

SET NOCOUNT ON;
SET XACT_ABORT ON; -- Automatically rollback on any error

BEGIN TRANSACTION;

-- Capture initial identity provider count once at the top for consistent logic throughout
DECLARE @initial_provider_count INT = 0;
IF OBJECT_ID('dbo.identity_provider','U') IS NOT NULL 
  SET @initial_provider_count = (SELECT COUNT(*) FROM dbo.identity_provider);

----------------------------
-- Identity Providers (replicating Java SystemSetupService logic)
----------------------------
IF OBJECT_ID('dbo.identity_provider','U') IS NOT NULL
BEGIN
  
  -- Replicate Java SystemSetupService identity provider creation logic:
  IF @initial_provider_count = 0
  BEGIN
    -- Create LOCAL Identity Provider when count = 0
    INSERT INTO dbo.identity_provider (id, name, description, created_by, status, created_at, updated_at)
    VALUES ('b3c1c3b0-303c-4a7a-8c43-7bdeb96ef558', 'LOCAL', 'Local identity provider', 'SYSTEM', 'ACTIVE', SYSUTCDATETIME(), SYSUTCDATETIME());
  END
  ELSE IF @initial_provider_count = 1
  BEGIN
    -- Create SCIM Identity Provider when count = 1 (matches Java lines 61-65)
    IF NOT EXISTS (SELECT 1 FROM dbo.identity_provider WHERE id = '55896ef7-303c-4a7a-8c43-bdc3b0b3c1eb')
    BEGIN
      INSERT INTO dbo.identity_provider (id, name, description, created_by, status, created_at, updated_at)
      VALUES ('55896ef7-303c-4a7a-8c43-bdc3b0b3c1eb', 'SCIM', 'SCIM identity provider', 'SYSTEM', 'ACTIVE', SYSUTCDATETIME(), SYSUTCDATETIME());
    END;
  END;
  -- When count > 1: create no providers (matches Java lines 69-75)
END;

----------------------------
-- System Business
----------------------------
IF OBJECT_ID('business','U') IS NOT NULL
AND NOT EXISTS (SELECT 1 FROM business WHERE id = 'd37ee3d4-22c6-41f7-819d-1464b3b9c454')
BEGIN
  INSERT INTO business (id, name, business_type, description, created_by, status, created_at, updated_at)
  VALUES ('d37ee3d4-22c6-41f7-819d-1464b3b9c454', 'Default', 'BUSINESS_UNIT', 'Default system business', 'SYSTEM', 'ACTIVE', SYSUTCDATETIME(), SYSUTCDATETIME());
END;

----------------------------
-- System Channel
----------------------------
IF OBJECT_ID('dbo.channel','U') IS NOT NULL
AND NOT EXISTS (SELECT 1 FROM dbo.channel WHERE id = '2701e2f0-8631-4561-a3c6-2b4af8e269ae')
BEGIN
  INSERT INTO dbo.channel (id, name, channel_type, description, created_by, status, created_at, updated_at)
  VALUES ('2701e2f0-8631-4561-a3c6-2b4af8e269ae', 'Default', 'WEB', 'Default system channel', 'SYSTEM', 'ACTIVE', SYSUTCDATETIME(), SYSUTCDATETIME());
END;

----------------------------
-- System Identity 
----------------------------
IF OBJECT_ID('dbo.identities','U') IS NOT NULL
AND NOT EXISTS (SELECT 1 FROM dbo.identities WHERE id = '509dde79-20d8-4ccb-92f7-f659927b5531')
BEGIN
  INSERT INTO dbo.identities (id, identity_type, name, lifecycle_state, created_by, status, created_at, updated_at)
  VALUES ('509dde79-20d8-4ccb-92f7-f659927b5531', 'SERVICE', 'SYSTEM', 'ACTIVE', 'SYSTEM', 'ACTIVE', SYSUTCDATETIME(), SYSUTCDATETIME());
END;

----------------------------
-- System Account (with identity provider selection logic matching Java SystemSetupService)
----------------------------
IF OBJECT_ID('dbo.account','U') IS NOT NULL
AND NOT EXISTS (SELECT 1 FROM dbo.account WHERE id = 'a1b2c3d4-e5f6-7890-abcd-ef1234567890')
BEGIN
  -- Use the initial provider count captured at the top
  DECLARE @selected_provider_id UNIQUEIDENTIFIER;
  
  -- Replicate Java SystemSetupService logic using initial count:
  IF @initial_provider_count = 0
  BEGIN
    -- Use LOCAL provider (created above)
    SET @selected_provider_id = 'b3c1c3b0-303c-4a7a-8c43-7bdeb96ef558';
  END
  ELSE IF @initial_provider_count = 1
  BEGIN
    -- Get oldest provider (ORDER BY created_at ASC) - matches Java PageRequest.of(0, 1, Sort.by("createdAt"))
    -- This selects from the original provider plus the newly created SCIM provider
    SET @selected_provider_id = (SELECT TOP (1) id FROM dbo.identity_provider ORDER BY created_at ASC);
  END
  ELSE
  BEGIN
    -- Get first provider (no specific ordering) - matches Java PageRequest.of(0, 1)
    SET @selected_provider_id = (SELECT TOP (1) id FROM dbo.identity_provider);
  END
  
  INSERT INTO dbo.account (id, business_id, identity_id, identity_provider_id, account_type, lifecycle_state, created_by, status, created_at, updated_at)
  VALUES ('a1b2c3d4-e5f6-7890-abcd-ef1234567890', 'd37ee3d4-22c6-41f7-819d-1464b3b9c454', '509dde79-20d8-4ccb-92f7-f659927b5531', @selected_provider_id, 'SERVICE', 'ACTIVE', 'SYSTEM', 'ACTIVE', SYSUTCDATETIME(), SYSUTCDATETIME());
END;

----------------------------
-- Claim Definitions (complete catalog from ClaimsSetupService)
----------------------------
IF OBJECT_ID('dbo.claim_definition','U') IS NOT NULL
BEGIN
  -- Update existing claim definitions
  UPDATE cd SET 
    name = v.name,
    description = v.description,
    claim_type = v.claim_type,
    data_type = v.data_type,
    data_format = v.data_format,
    updated_by = 'SYSTEM',
    updated_at = SYSUTCDATETIME()
  FROM dbo.claim_definition cd
  INNER JOIN (
    VALUES
      ('ac0de313-0d86-49a4-97bf-03da1943671b', 'email_addresses', 'Email Addresses', 'List of user email addresses.', 'SYSTEM_DEFINED', 'ARRAY', 'beb4da69-6685-481b-a940-87823788b02a'),
      ('beb4da69-6685-481b-a940-87823788b02a', 'email_address', 'Email', 'An email address.', 'SYSTEM_DEFINED', 'STRING', '^[a-zA-Z0-9_+&*-]+(?:\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$'),
      ('fd9c993f-d544-43a1-9fd8-b77d19de1635', 'first_name', 'First Name', 'User''s first name.', 'SYSTEM_DEFINED', 'STRING', '^[a-zA-Z\s]+$'),
      ('db76198f-9866-41d5-82b3-856a6a86d157', 'last_name', 'Last Name', 'User''s last name.', 'SYSTEM_DEFINED', 'STRING', '^[a-zA-Z\s]+$'),
      ('6dffb4ad-1300-4770-a5c3-7c0913e7a624', 'phone_numbers', 'Phone Numbers', 'List of user phone numbers.', 'SYSTEM_DEFINED', 'ARRAY', '2784994d-dc95-491b-8843-f5664dece55e'),
      ('aa087964-b788-44c4-9078-3e53e50fe853', 'addresses', 'Addresses', 'List of user postal addresses.', 'SYSTEM_DEFINED', 'ARRAY', 'e78db996-7a30-46b0-8a47-6d86c0a91f49'),
      ('ed926e0f-4d85-4f3d-b9f6-971d566a8c59', 'last_login', 'Last Login', 'Date and time of user''s last login.', 'SYSTEM_DEFINED', 'DATETIME', '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$'),
      ('2784994d-dc95-491b-8843-f5664dece55e', 'phone_number', 'Individual Phone Number', 'Individual phone number.', 'SYSTEM_DEFINED', 'STRING', '^(\+\d{1,3})?\d{7,15}$'),
      ('e78db996-7a30-46b0-8a47-6d86c0a91f49', 'address', 'Individual Mailing Address', 'Individual postal address.', 'SYSTEM_DEFINED', 'STRING', '^[a-zA-Z0-9\s.,#-]+$'),
      ('d31ff644-6a74-41da-b81c-13ad894a189a', 'id_number', 'ID Number', 'User''s official identification number.', 'SYSTEM_DEFINED', 'STRING', '^[a-zA-Z0-9-]+$'),
      ('12afd933-ede5-4ac2-8d2b-e8978642965c', 'scim_username', 'SCIM Username', 'SCIM only identification.', 'SYSTEM_DEFINED', 'STRING', '^[\S]{1,}[\s]*$'),
      ('e8978642-8d2b-4ac2-ede5-965c12afd933', 'scim_external_id', 'SCIM External Id', 'SCIM external identification.', 'SYSTEM_DEFINED', 'STRING', '^[\S]{1,}[\s]*$'),
      -- Workforce-specific claim definitions
      ('f1a2b3c4-d5e6-7f8a-9b0c-1d2e3f4a5b6c', 'employee_id', 'Employee ID', 'Unique employee identifier.', 'SYSTEM_DEFINED', 'STRING', '^EMP[0-9]{6}$'),
      ('a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d', 'department', 'Department', 'Employee''s department.', 'SYSTEM_DEFINED', 'STRING', '^[a-zA-Z\s&-]+$'),
      ('b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e', 'job_title', 'Job Title', 'Employee''s job title.', 'SYSTEM_DEFINED', 'STRING', '^[a-zA-Z\s&-]+$'),
      ('c3d4e5f6-a7b8-9c0d-1e2f-3a4b5c6d7e8f', 'manager_email', 'Manager Email', 'Email address of employee''s manager.', 'SYSTEM_DEFINED', 'STRING', '^[a-zA-Z0-9_+&*-]+(?:\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$'),
      ('d4e5f6a7-b8c9-0d1e-2f3a-4b5c6d7e8f9a', 'hire_date', 'Hire Date', 'Employee''s hire date.', 'SYSTEM_DEFINED', 'DATE', '^\d{4}-\d{2}-\d{2}$')
  ) AS v(id, code, name, description, claim_type, data_type, data_format)
  ON cd.id = v.id;

  -- Insert new claim definitions
  INSERT INTO dbo.claim_definition (id, code, name, description, claim_type, data_type, data_format, created_by, status, created_at, updated_at)
  SELECT v.id, v.code, v.name, v.description, v.claim_type, v.data_type, v.data_format, 'SYSTEM', 'ACTIVE', SYSUTCDATETIME(), SYSUTCDATETIME()
  FROM (
    VALUES
      ('ac0de313-0d86-49a4-97bf-03da1943671b', 'email_addresses', 'Email Addresses', 'List of user email addresses.', 'SYSTEM_DEFINED', 'ARRAY', 'beb4da69-6685-481b-a940-87823788b02a'),
      ('beb4da69-6685-481b-a940-87823788b02a', 'email_address', 'Email', 'An email address.', 'SYSTEM_DEFINED', 'STRING', '^[a-zA-Z0-9_+&*-]+(?:\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$'),
      ('fd9c993f-d544-43a1-9fd8-b77d19de1635', 'first_name', 'First Name', 'User''s first name.', 'SYSTEM_DEFINED', 'STRING', '^[a-zA-Z\s]+$'),
      ('db76198f-9866-41d5-82b3-856a6a86d157', 'last_name', 'Last Name', 'User''s last name.', 'SYSTEM_DEFINED', 'STRING', '^[a-zA-Z\s]+$'),
      ('6dffb4ad-1300-4770-a5c3-7c0913e7a624', 'phone_numbers', 'Phone Numbers', 'List of user phone numbers.', 'SYSTEM_DEFINED', 'ARRAY', '2784994d-dc95-491b-8843-f5664dece55e'),
      ('aa087964-b788-44c4-9078-3e53e50fe853', 'addresses', 'Addresses', 'List of user postal addresses.', 'SYSTEM_DEFINED', 'ARRAY', 'e78db996-7a30-46b0-8a47-6d86c0a91f49'),
      ('ed926e0f-4d85-4f3d-b9f6-971d566a8c59', 'last_login', 'Last Login', 'Date and time of user''s last login.', 'SYSTEM_DEFINED', 'DATETIME', '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$'),
      ('2784994d-dc95-491b-8843-f5664dece55e', 'phone_number', 'Individual Phone Number', 'Individual phone number.', 'SYSTEM_DEFINED', 'STRING', '^(\+\d{1,3})?\d{7,15}$'),
      ('e78db996-7a30-46b0-8a47-6d86c0a91f49', 'address', 'Individual Mailing Address', 'Individual postal address.', 'SYSTEM_DEFINED', 'STRING', '^[a-zA-Z0-9\s.,#-]+$'),
      ('d31ff644-6a74-41da-b81c-13ad894a189a', 'id_number', 'ID Number', 'User''s official identification number.', 'SYSTEM_DEFINED', 'STRING', '^[a-zA-Z0-9-]+$'),
      ('12afd933-ede5-4ac2-8d2b-e8978642965c', 'scim_username', 'SCIM Username', 'SCIM only identification.', 'SYSTEM_DEFINED', 'STRING', '^[\S]{1,}[\s]*$'),
      ('e8978642-8d2b-4ac2-ede5-965c12afd933', 'scim_external_id', 'SCIM External Id', 'SCIM external identification.', 'SYSTEM_DEFINED', 'STRING', '^[\S]{1,}[\s]*$'),
      -- Workforce-specific claim definitions
      ('f1a2b3c4-d5e6-7f8a-9b0c-1d2e3f4a5b6c', 'employee_id', 'Employee ID', 'Unique employee identifier.', 'SYSTEM_DEFINED', 'STRING', '^EMP[0-9]{6}$'),
      ('a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d', 'department', 'Department', 'Employee''s department.', 'SYSTEM_DEFINED', 'STRING', '^[a-zA-Z\s&-]+$'),
      ('b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e', 'job_title', 'Job Title', 'Employee''s job title.', 'SYSTEM_DEFINED', 'STRING', '^[a-zA-Z\s&-]+$'),
      ('c3d4e5f6-a7b8-9c0d-1e2f-3a4b5c6d7e8f', 'manager_email', 'Manager Email', 'Email address of employee''s manager.', 'SYSTEM_DEFINED', 'STRING', '^[a-zA-Z0-9_+&*-]+(?:\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$'),
      ('d4e5f6a7-b8c9-0d1e-2f3a-4b5c6d7e8f9a', 'hire_date', 'Hire Date', 'Employee''s hire date.', 'SYSTEM_DEFINED', 'DATE', '^\d{4}-\d{2}-\d{2}$')
  ) AS v(id, code, name, description, claim_type, data_type, data_format)
  WHERE NOT EXISTS (SELECT 1 FROM dbo.claim_definition WHERE dbo.claim_definition.id = v.id);
END;

----------------------------
-- Claim Sets (identifier/profile sets for all account types)
----------------------------
IF OBJECT_ID('dbo.claim_set','U') IS NOT NULL
BEGIN
  DECLARE @business_id UNIQUEIDENTIFIER = 'd37ee3d4-22c6-41f7-819d-1464b3b9c454';

  -- Update existing identifier claim sets
  UPDATE cs SET 
    name = v.name,
    description = v.description,
    updated_by = 'SYSTEM',
    updated_at = SYSUTCDATETIME()
  FROM dbo.claim_set cs
  INNER JOIN (
    VALUES
      ('4307e527-1cf4-4d10-8156-f913916c0e3e', 'NONE Identifier Set', 'Claims that uniquely identify an account', 1, 'ANY_CLAIM_CAN_MATCH', 'NONE'),
      ('d95bb76c-13aa-4587-b3ef-8dc88f2f03bb', 'CUSTOMER Identifier Set', 'Claims that uniquely identify an account', 1, 'ANY_CLAIM_CAN_MATCH', 'CUSTOMER'),
      ('6b9810c2-50fb-4b02-8461-19eb90b61ccd', 'FEDERATED Identifier Set', 'Claims that uniquely identify an account', 1, 'ANY_CLAIM_CAN_MATCH', 'FEDERATED'),
      ('f427be74-df33-451a-82b8-a09f30cec78e', 'WORKFORCE Identifier Set', 'Claims that uniquely identify an account', 1, 'ANY_CLAIM_CAN_MATCH', 'WORKFORCE'),
      ('5e1e40af-3736-4f75-bf53-e0e9ce9adac0', 'SERVICE Identifier Set', 'Claims that uniquely identify an account', 1, 'ANY_CLAIM_CAN_MATCH', 'SERVICE'),
      ('9dced845-4d92-4bc7-a6e8-7fec81d50a0d', 'SOCIAL Identifier Set', 'Claims that uniquely identify an account', 1, 'ANY_CLAIM_CAN_MATCH', 'SOCIAL'),
      ('b82bae6a-ee4d-4488-8136-dcdb49ef2fef', 'LOCAL Identifier Set', 'Claims that uniquely identify an account', 1, 'ANY_CLAIM_CAN_MATCH', 'LOCAL')
  ) AS v(id, name, description, is_identifier, lookup_strategy, account_type)
  ON cs.id = v.id;

  -- Insert new identifier claim sets
  INSERT INTO dbo.claim_set (id, business_id, name, description, is_identifier, lookup_strategy, account_type, created_by, status, created_at, updated_at)
  SELECT v.id, @business_id, v.name, v.description, v.is_identifier, v.lookup_strategy, v.account_type, 'SYSTEM', 'ACTIVE', SYSUTCDATETIME(), SYSUTCDATETIME()
  FROM (
    VALUES
      ('4307e527-1cf4-4d10-8156-f913916c0e3e', 'NONE Identifier Set', 'Claims that uniquely identify an account', 1, 'ANY_CLAIM_CAN_MATCH', 'NONE'),
      ('d95bb76c-13aa-4587-b3ef-8dc88f2f03bb', 'CUSTOMER Identifier Set', 'Claims that uniquely identify an account', 1, 'ANY_CLAIM_CAN_MATCH', 'CUSTOMER'),
      ('6b9810c2-50fb-4b02-8461-19eb90b61ccd', 'FEDERATED Identifier Set', 'Claims that uniquely identify an account', 1, 'ANY_CLAIM_CAN_MATCH', 'FEDERATED'),
      ('f427be74-df33-451a-82b8-a09f30cec78e', 'WORKFORCE Identifier Set', 'Claims that uniquely identify an account', 1, 'ANY_CLAIM_CAN_MATCH', 'WORKFORCE'),
      ('5e1e40af-3736-4f75-bf53-e0e9ce9adac0', 'SERVICE Identifier Set', 'Claims that uniquely identify an account', 1, 'ANY_CLAIM_CAN_MATCH', 'SERVICE'),
      ('9dced845-4d92-4bc7-a6e8-7fec81d50a0d', 'SOCIAL Identifier Set', 'Claims that uniquely identify an account', 1, 'ANY_CLAIM_CAN_MATCH', 'SOCIAL'),
      ('b82bae6a-ee4d-4488-8136-dcdb49ef2fef', 'LOCAL Identifier Set', 'Claims that uniquely identify an account', 1, 'ANY_CLAIM_CAN_MATCH', 'LOCAL')
  ) AS v(id, name, description, is_identifier, lookup_strategy, account_type)
  WHERE NOT EXISTS (SELECT 1 FROM dbo.claim_set WHERE dbo.claim_set.id = v.id);

  -- Update existing profile claim sets
  UPDATE cs SET 
    name = v.name,
    description = v.description,
    updated_by = 'SYSTEM',
    updated_at = SYSUTCDATETIME()
  FROM dbo.claim_set cs
  INNER JOIN (
    VALUES
      ('159c5b08-bdb2-4d49-9256-197ff0d2c761', 'NONE Profile Set', 'Non-identifier profile / preference claims', 0, 'ANY_CLAIM_CAN_MATCH', 'NONE'),
      ('3c416395-5ac9-40d6-98c7-b347b09ca21b', 'CUSTOMER Profile Set', 'Non-identifier profile / preference claims', 0, 'ANY_CLAIM_CAN_MATCH', 'CUSTOMER'),
      ('73bbc97a-1733-475b-bd9e-10635a8b5cb6', 'FEDERATED Profile Set', 'Non-identifier profile / preference claims', 0, 'ANY_CLAIM_CAN_MATCH', 'FEDERATED'),
      ('33379d68-f824-4eb0-b8d4-a713f0b8d79f', 'WORKFORCE Profile Set', 'Non-identifier profile / preference claims', 0, 'ANY_CLAIM_CAN_MATCH', 'WORKFORCE'),
      ('d2d9ba77-177d-486a-a0b4-851e104a20ab', 'SERVICE Profile Set', 'Non-identifier profile / preference claims', 0, 'ANY_CLAIM_CAN_MATCH', 'SERVICE'),
      ('1855d37f-c794-466f-9087-913f4dac0e39', 'SOCIAL Profile Set', 'Non-identifier profile / preference claims', 0, 'ANY_CLAIM_CAN_MATCH', 'SOCIAL'),
      ('ddb920ef-caa4-4193-ade5-908e8c61c931', 'LOCAL Profile Set', 'Non-identifier profile / preference claims', 0, 'ANY_CLAIM_CAN_MATCH', 'LOCAL')
  ) AS v(id, name, description, is_identifier, lookup_strategy, account_type)
  ON cs.id = v.id;

  -- Insert new profile claim sets
  INSERT INTO dbo.claim_set (id, business_id, name, description, is_identifier, lookup_strategy, account_type, created_by, status, created_at, updated_at)
  SELECT v.id, @business_id, v.name, v.description, v.is_identifier, v.lookup_strategy, v.account_type, 'SYSTEM', 'ACTIVE', SYSUTCDATETIME(), SYSUTCDATETIME()
  FROM (
    VALUES
      ('159c5b08-bdb2-4d49-9256-197ff0d2c761', 'NONE Profile Set', 'Non-identifier profile / preference claims', 0, 'ANY_CLAIM_CAN_MATCH', 'NONE'),
      ('3c416395-5ac9-40d6-98c7-b347b09ca21b', 'CUSTOMER Profile Set', 'Non-identifier profile / preference claims', 0, 'ANY_CLAIM_CAN_MATCH', 'CUSTOMER'),
      ('73bbc97a-1733-475b-bd9e-10635a8b5cb6', 'FEDERATED Profile Set', 'Non-identifier profile / preference claims', 0, 'ANY_CLAIM_CAN_MATCH', 'FEDERATED'),
      ('33379d68-f824-4eb0-b8d4-a713f0b8d79f', 'WORKFORCE Profile Set', 'Non-identifier profile / preference claims', 0, 'ANY_CLAIM_CAN_MATCH', 'WORKFORCE'),
      ('d2d9ba77-177d-486a-a0b4-851e104a20ab', 'SERVICE Profile Set', 'Non-identifier profile / preference claims', 0, 'ANY_CLAIM_CAN_MATCH', 'SERVICE'),
      ('1855d37f-c794-466f-9087-913f4dac0e39', 'SOCIAL Profile Set', 'Non-identifier profile / preference claims', 0, 'ANY_CLAIM_CAN_MATCH', 'SOCIAL'),
      ('ddb920ef-caa4-4193-ade5-908e8c61c931', 'LOCAL Profile Set', 'Non-identifier profile / preference claims', 0, 'ANY_CLAIM_CAN_MATCH', 'LOCAL')
  ) AS v(id, name, description, is_identifier, lookup_strategy, account_type)
  WHERE NOT EXISTS (SELECT 1 FROM dbo.claim_set WHERE dbo.claim_set.id = v.id);
END;

----------------------------
-- Claim Set Definition Mappings (examples based on ClaimsSetupService logic)
----------------------------
IF OBJECT_ID('dbo.claim_set_definition_mapping','U') IS NOT NULL
BEGIN
  -- Insert identifier claim set mappings (all account types use email_address as identifier)
  INSERT INTO dbo.claim_set_definition_mapping (id, claim_set_id, claim_definition_id, enforce_uniqueness, claim_definition_order, created_by, status, created_at, updated_at)
  SELECT NEWID(), v.claim_set_id, v.claim_definition_id, v.enforce_uniqueness, v.claim_definition_order, 'SYSTEM', 'ACTIVE', SYSUTCDATETIME(), SYSUTCDATETIME()
  FROM (
    VALUES
      -- CUSTOMER identifier: email_address
      ('d95bb76c-13aa-4587-b3ef-8dc88f2f03bb', 'beb4da69-6685-481b-a940-87823788b02a', 1, 1),
      -- FEDERATED identifier: email_address  
      ('6b9810c2-50fb-4b02-8461-19eb90b61ccd', 'beb4da69-6685-481b-a940-87823788b02a', 1, 1),
      -- WORKFORCE identifier: email_address
      ('f427be74-df33-451a-82b8-a09f30cec78e', 'beb4da69-6685-481b-a940-87823788b02a', 1, 1),
      -- SERVICE identifier: email_address
      ('5e1e40af-3736-4f75-bf53-e0e9ce9adac0', 'beb4da69-6685-481b-a940-87823788b02a', 1, 1),
      -- SOCIAL identifier: email_address
      ('9dced845-4d92-4bc7-a6e8-7fec81d50a0d', 'beb4da69-6685-481b-a940-87823788b02a', 1, 1),
      -- LOCAL identifier: email_address
      ('b82bae6a-ee4d-4488-8136-dcdb49ef2fef', 'beb4da69-6685-481b-a940-87823788b02a', 1, 1)
  ) AS v(claim_set_id, claim_definition_id, enforce_uniqueness, claim_definition_order)
  WHERE NOT EXISTS (SELECT 1 FROM dbo.claim_set_definition_mapping 
                   WHERE claim_set_id = v.claim_set_id AND claim_definition_id = v.claim_definition_id);

  -- Insert profile claim mappings (selected examples)
  INSERT INTO dbo.claim_set_definition_mapping (id, claim_set_id, claim_definition_id, enforce_uniqueness, claim_definition_order, created_by, status, created_at, updated_at)
  SELECT NEWID(), v.claim_set_id, v.claim_definition_id, v.enforce_uniqueness, v.claim_definition_order, 'SYSTEM', 'ACTIVE', SYSUTCDATETIME(), SYSUTCDATETIME()
  FROM (
    VALUES
      -- CUSTOMER profile: first_name, last_name, phone_number
      ('3c416395-5ac9-40d6-98c7-b347b09ca21b', 'fd9c993f-d544-43a1-9fd8-b77d19de1635', 0, 1), -- first_name
      ('3c416395-5ac9-40d6-98c7-b347b09ca21b', 'db76198f-9866-41d5-82b3-856a6a86d157', 0, 2), -- last_name
      ('3c416395-5ac9-40d6-98c7-b347b09ca21b', '2784994d-dc95-491b-8843-f5664dece55e', 0, 3), -- phone_number
      -- FEDERATED profile: first_name, last_name, email_address, scim_username, scim_external_id
      ('73bbc97a-1733-475b-bd9e-10635a8b5cb6', 'fd9c993f-d544-43a1-9fd8-b77d19de1635', 0, 1), -- first_name
      ('73bbc97a-1733-475b-bd9e-10635a8b5cb6', 'db76198f-9866-41d5-82b3-856a6a86d157', 0, 2), -- last_name
      ('73bbc97a-1733-475b-bd9e-10635a8b5cb6', 'beb4da69-6685-481b-a940-87823788b02a', 0, 3), -- email_address
      ('73bbc97a-1733-475b-bd9e-10635a8b5cb6', '12afd933-ede5-4ac2-8d2b-e8978642965c', 0, 4), -- scim_username
      ('73bbc97a-1733-475b-bd9e-10635a8b5cb6', 'e8978642-8d2b-4ac2-ede5-965c12afd933', 0, 5), -- scim_external_id
      -- WORKFORCE profile: first_name, last_name, phone_numbers, department, job_title, manager_email, hire_date
      ('33379d68-f824-4eb0-b8d4-a713f0b8d79f', 'fd9c993f-d544-43a1-9fd8-b77d19de1635', 0, 1), -- first_name
      ('33379d68-f824-4eb0-b8d4-a713f0b8d79f', 'db76198f-9866-41d5-82b3-856a6a86d157', 0, 2), -- last_name
      ('33379d68-f824-4eb0-b8d4-a713f0b8d79f', '6dffb4ad-1300-4770-a5c3-7c0913e7a624', 0, 3), -- phone_numbers
      ('33379d68-f824-4eb0-b8d4-a713f0b8d79f', 'a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d', 0, 4), -- department
      ('33379d68-f824-4eb0-b8d4-a713f0b8d79f', 'b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e', 0, 5), -- job_title
      ('33379d68-f824-4eb0-b8d4-a713f0b8d79f', 'c3d4e5f6-a7b8-9c0d-1e2f-3a4b5c6d7e8f', 0, 6), -- manager_email
      ('33379d68-f824-4eb0-b8d4-a713f0b8d79f', 'd4e5f6a7-b8c9-0d1e-2f3a-4b5c6d7e8f9a', 0, 7), -- hire_date
      -- SERVICE profile: id_number
      ('d2d9ba77-177d-486a-a0b4-851e104a20ab', 'd31ff644-6a74-41da-b81c-13ad894a189a', 0, 1), -- id_number
      -- SOCIAL profile: first_name, last_name, phone_number
      ('1855d37f-c794-466f-9087-913f4dac0e39', 'fd9c993f-d544-43a1-9fd8-b77d19de1635', 0, 1), -- first_name
      ('1855d37f-c794-466f-9087-913f4dac0e39', 'db76198f-9866-41d5-82b3-856a6a86d157', 0, 2), -- last_name
      ('1855d37f-c794-466f-9087-913f4dac0e39', '2784994d-dc95-491b-8843-f5664dece55e', 0, 3), -- phone_number
      -- LOCAL profile: first_name, last_name, last_login
      ('ddb920ef-caa4-4193-ade5-908e8c61c931', 'fd9c993f-d544-43a1-9fd8-b77d19de1635', 0, 1), -- first_name
      ('ddb920ef-caa4-4193-ade5-908e8c61c931', 'db76198f-9866-41d5-82b3-856a6a86d157', 0, 2), -- last_name
      ('ddb920ef-caa4-4193-ade5-908e8c61c931', 'ed926e0f-4d85-4f3d-b9f6-971d566a8c59', 0, 3)  -- last_login
  ) AS v(claim_set_id, claim_definition_id, enforce_uniqueness, claim_definition_order)
  WHERE NOT EXISTS (SELECT 1 FROM dbo.claim_set_definition_mapping 
                   WHERE claim_set_id = v.claim_set_id AND claim_definition_id = v.claim_definition_id);
END;

----------------------------
-- Permissions (complete catalog from RolesAndPermissionsSetupService)
----------------------------
IF OBJECT_ID('dbo.permission','U') IS NOT NULL
BEGIN
  -- Update existing permissions
  UPDATE p SET 
    name = v.name,
    description = v.description,
    updated_by = 'SYSTEM',
    updated_at = SYSUTCDATETIME()
  FROM dbo.permission p
  INNER JOIN (
    VALUES
      -- Identity permissions
      ('0b5a1b36-4eb8-476b-8cfe-0f152822a5cb', 'sys_identity:create', 'Allow to create identities'),
      ('5e3e4043-9ca3-44b5-9953-4ac4e1045716', 'sys_identity:read', 'Allow to read identities'),
      ('b2ab0b5b-96ba-4e9f-9ae0-9f98b4fae309', 'sys_identity:update', 'Allow to update identities'),
      ('5a6960d5-a7e7-44ab-bbe9-06ab1e76bf7b', 'sys_identity:delete', 'Allow to delete identities'),
      ('ddef4592-cdce-4158-800d-00fb06c24109', 'sys_identity:all', 'Allow all identity operations'),
      -- Account permissions
      ('cdfb0bc3-e6fe-472b-bb03-eaf619a51f96', 'sys_account:create', 'Allow to create accounts entities'),
      ('02b5b45f-7169-4eb3-9ecf-23a993e36d34', 'sys_account:read', 'Allow to read accounts entities'),
      ('a8a4eb8f-0beb-4a77-92d6-1d6649a46568', 'sys_account:update', 'Allow to update accounts entities'),
      ('e653f407-2fad-4250-888f-d7465f83e8ce', 'sys_account:delete', 'Allow to delete accounts entities'),
      ('5e1b9351-2f1d-4f93-871d-a3e27ce8d97d', 'sys_account:all', 'Allow all account operations'),
      -- Business permissions
      ('504719a0-fdc1-448d-b58c-ec45b594893d', 'sys_business:create', 'Allow to create business'),
      ('5e9b0f7f-8c50-4613-8991-a7b4455ce5f5', 'sys_business:read', 'Allow to read business'),
      ('4213292d-f965-4d00-9a1d-57a3615c351f', 'sys_business:update', 'Allow to update business'),
      ('b9338fd5-30b8-4a6e-ac51-4a7aeca65301', 'sys_business:delete', 'Allow to delete business'),
      ('83040f1d-fb72-474f-b856-3d6f5270b543', 'sys_business:all', 'Allow all business operations'),
      -- Claim permissions
      ('84510add-1c13-47ab-98bb-7094c23bfc56', 'sys_claim:create', 'Allow to create claims'),
      ('bec3b3ea-cfe4-4465-b896-ec3047d20232', 'sys_claim:read', 'Allow to read claims'),
      ('ed4d7659-e9e9-4d29-bbce-166c729f36b9', 'sys_claim:update', 'Allow to update claims'),
      ('316cc403-d3c8-4a19-b115-fe5b64e376fd', 'sys_claim:delete', 'Allow to delete claims'),
      ('04a610e6-48bc-444f-9ee7-27bef5b26fe9', 'sys_claim:all', 'Allow all claim operations'),
      -- Consent permissions
      ('b397c3ad-6428-4d7b-8cc9-13b8732ce9e4', 'sys_consent:create', 'Allow to create consents'),
      ('debdd7a0-a3fc-46f5-8f12-b731bb8421e4', 'sys_consent:read', 'Allow to read consents'),
      ('dad173eb-4496-4b40-b6c7-693f0ea7f5e4', 'sys_consent:update', 'Allow to update consents'),
      ('5ca9d48f-2097-4bd1-a6a4-05df06805a9a', 'sys_consent:delete', 'Allow to delete consents'),
      ('7f81915c-161c-4eb8-b72f-a02839c04e69', 'sys_consent:all', 'Allow all consent operations'),
      -- Role permissions
      ('79c8e073-4825-4ccb-9621-7b7ae127d03d', 'sys_role:create', 'Allow to create roles'),
      ('410db433-5ebc-48fa-b0ce-d313ebf3a8c6', 'sys_role:read', 'Allow to read roles'),
      ('010f7d25-44e2-4602-ba18-9b81096d50ef', 'sys_role:update', 'Allow to update roles'),
      ('f086d3fd-a216-4e1a-8381-049fae470719', 'sys_role:delete', 'Allow to delete roles'),
      ('b3077820-bc48-4a89-be3c-bf1374b7eb51', 'sys_role:all', 'Allow all role operations'),
      -- Permission permissions
      ('ab4fd567-3d79-4fc1-8fe7-9e55695dfb2a', 'sys_permission:create', 'Allow to create permissions'),
      ('9494aa9f-f0ac-4098-847c-57086568aab3', 'sys_permission:read', 'Allow to read permissions'),
      ('3f8965a3-a7b9-4074-b8bf-33824d8e8ad7', 'sys_permission:update', 'Allow to update permissions'),
      ('7372b969-56e7-43fe-bade-8d992630a173', 'sys_permission:delete', 'Allow to delete permissions'),
      ('05f9c70b-df37-4dad-8180-c03fdaa31b57', 'sys_permission:all', 'Allow all permission operations'),
      -- Group permissions
      ('e4b8c9d1-2f35-46a7-8e90-12c3d4e5f678', 'sys_group:create', 'Allow to create groups'),
      ('f5c9d0e2-3f46-57b8-9f01-23d4e5f6a789', 'sys_group:read', 'Allow to read groups'),
      ('a6d0e1f3-4f57-68c9-0f12-34e5f6a7b890', 'sys_group:update', 'Allow to update groups'),
      ('b7e1f2a4-5f68-79d0-1f23-45f6a7b8c901', 'sys_group:delete', 'Allow to delete groups'),
      ('63ff405d-100f-4955-b818-454e13d5b171', 'sys_group:all', 'Allow all group operations'),
      -- Channel permissions
      ('8f52f06e-e52c-47e0-81fd-d8e568ed34cb', 'sys_channel:create', 'Allow to create channels'),
      ('359ee229-e09c-46b1-8c55-90ac0ae1a9e8', 'sys_channel:read', 'Allow to read channels'),
      ('3b1f7511-bd15-47b8-9053-7d9bf4e99155', 'sys_channel:update', 'Allow to update channels'),
      ('b55af4fd-f1c0-4a08-9d02-0c70859f0e68', 'sys_channel:delete', 'Allow to delete channels'),
      ('50835799-dad9-4bf2-8b41-5cd922c0d04d', 'sys_channel:all', 'Allow all channel operations'),
      -- Factor TOTP permissions
      ('a29e8a6e-9d50-434a-857e-6c111c8172c9', 'sys_factor_totp:create', 'Allow to create TOTP factors'),
      ('46b76be4-a78e-4d94-9f94-53e51c8502d0', 'sys_factor_totp:read', 'Allow to read TOTP factors'),
      ('d01ac2ad-120f-4272-96b6-e280f6345165', 'sys_factor_totp:update', 'Allow to update TOTP factors'),
      ('2b76862c-3d48-497c-8c2e-24fb3d66da00', 'sys_factor_totp:delete', 'Allow to delete TOTP factors'),
      ('28657e33-e179-4ced-8a92-457fce997fae', 'sys_factor_totp:all', 'Allow all TOTP factor operations'),
      -- Factor SMS permissions
      ('adeb7443-b028-4564-992b-3edaa9a28452', 'sys_factor_sms:create', 'Allow to create SMS factors'),
      ('df4a93eb-466a-4217-bef5-0c2aafe3adeb', 'sys_factor_sms:read', 'Allow to read SMS factors'),
      ('d80aec72-f433-4259-bbf5-00b587ff90d9', 'sys_factor_sms:update', 'Allow to update SMS factors'),
      ('38720e22-ea55-4a81-b819-2eca45202db1', 'sys_factor_sms:delete', 'Allow to delete SMS factors'),
      ('1a36d1d6-7eba-427a-b097-4e6e881fb558', 'sys_factor_sms:all', 'Allow all SMS factor operations'),
      -- Factor Email permissions
      ('365c9b68-5ea9-4912-b6b4-588923143b60', 'sys_factor_email:create', 'Allow to create Email factors'),
      ('3e9da38f-6fb2-431c-9666-a89286ec6e61', 'sys_factor_email:read', 'Allow to read Email factors'),
      ('446bf8b8-3abd-414e-89b0-49fbae82f3d8', 'sys_factor_email:update', 'Allow to update Email factors'),
      ('a95d38f1-07ef-4d88-8b8c-36c86d9ad7c2', 'sys_factor_email:delete', 'Allow to delete Email factors'),
      ('6cc3d3f8-5cd9-4372-ab55-841e4f047074', 'sys_factor_email:all', 'Allow all Email factor operations'),
      -- Factor Password permissions
      ('83ff93f4-1879-4e5d-8f1a-6ffff7d92800', 'sys_factor_password:create', 'Allow to create Password factors'),
      ('cdc88324-602e-4461-ba65-79d6f5aa9727', 'sys_factor_password:read', 'Allow to read Password factors'),
      ('38febaf5-e637-40a9-b42f-631978980b05', 'sys_factor_password:update', 'Allow to update Password factors'),
      ('a5a482be-0142-420e-822a-f4091d22b236', 'sys_factor_password:delete', 'Allow to delete Password factors'),
      ('ac02fd0f-fdbd-4295-bee2-ddd49a2335d2', 'sys_factor_password:all', 'Allow all Password factor operations'),
      -- Factor Policies permissions
      ('4de0b8dd-3e0c-4055-9177-c13d901f18f1', 'sys_factor_policies:create', 'Allow to create Factor policies'),
      ('4212c99e-de64-4e6e-9478-5655c53e9b18', 'sys_factor_policies:read', 'Allow to read Factor policies'),
      ('d78738b0-50f2-44ec-9c4a-3bf0d712016e', 'sys_factor_policies:update', 'Allow to update Factor policies'),
      ('1c98dfd4-57ca-4045-b5a0-022cb251af87', 'sys_factor_policies:delete', 'Allow to delete Factor policies'),
      ('4bc58115-4c53-43d7-b168-009f75896f5a', 'sys_factor_policies:all', 'Allow all Factor policies operations')
  ) AS v(id, name, description)
  ON p.id = v.id;

  -- Insert new permissions
  INSERT INTO dbo.permission (id, name, description, created_by, status, created_at, updated_at)
  SELECT v.id, v.name, v.description, 'SYSTEM', 'ACTIVE', SYSUTCDATETIME(), SYSUTCDATETIME()
  FROM (
    VALUES
      -- Identity permissions
      ('0b5a1b36-4eb8-476b-8cfe-0f152822a5cb', 'sys_identity:create', 'Allow to create identities'),
      ('5e3e4043-9ca3-44b5-9953-4ac4e1045716', 'sys_identity:read', 'Allow to read identities'),
      ('b2ab0b5b-96ba-4e9f-9ae0-9f98b4fae309', 'sys_identity:update', 'Allow to update identities'),
      ('5a6960d5-a7e7-44ab-bbe9-06ab1e76bf7b', 'sys_identity:delete', 'Allow to delete identities'),
      ('ddef4592-cdce-4158-800d-00fb06c24109', 'sys_identity:all', 'Allow all identity operations'),
      -- Account permissions
      ('cdfb0bc3-e6fe-472b-bb03-eaf619a51f96', 'sys_account:create', 'Allow to create accounts entities'),
      ('02b5b45f-7169-4eb3-9ecf-23a993e36d34', 'sys_account:read', 'Allow to read accounts entities'),
      ('a8a4eb8f-0beb-4a77-92d6-1d6649a46568', 'sys_account:update', 'Allow to update accounts entities'),
      ('e653f407-2fad-4250-888f-d7465f83e8ce', 'sys_account:delete', 'Allow to delete accounts entities'),
      ('5e1b9351-2f1d-4f93-871d-a3e27ce8d97d', 'sys_account:all', 'Allow all account operations'),
      -- Business permissions
      ('504719a0-fdc1-448d-b58c-ec45b594893d', 'sys_business:create', 'Allow to create business'),
      ('5e9b0f7f-8c50-4613-8991-a7b4455ce5f5', 'sys_business:read', 'Allow to read business'),
      ('4213292d-f965-4d00-9a1d-57a3615c351f', 'sys_business:update', 'Allow to update business'),
      ('b9338fd5-30b8-4a6e-ac51-4a7aeca65301', 'sys_business:delete', 'Allow to delete business'),
      ('83040f1d-fb72-474f-b856-3d6f5270b543', 'sys_business:all', 'Allow all business operations'),
      -- Claim permissions
      ('84510add-1c13-47ab-98bb-7094c23bfc56', 'sys_claim:create', 'Allow to create claims'),
      ('bec3b3ea-cfe4-4465-b896-ec3047d20232', 'sys_claim:read', 'Allow to read claims'),
      ('ed4d7659-e9e9-4d29-bbce-166c729f36b9', 'sys_claim:update', 'Allow to update claims'),
      ('316cc403-d3c8-4a19-b115-fe5b64e376fd', 'sys_claim:delete', 'Allow to delete claims'),
      ('04a610e6-48bc-444f-9ee7-27bef5b26fe9', 'sys_claim:all', 'Allow all claim operations'),
      -- Consent permissions
      ('b397c3ad-6428-4d7b-8cc9-13b8732ce9e4', 'sys_consent:create', 'Allow to create consents'),
      ('debdd7a0-a3fc-46f5-8f12-b731bb8421e4', 'sys_consent:read', 'Allow to read consents'),
      ('dad173eb-4496-4b40-b6c7-693f0ea7f5e4', 'sys_consent:update', 'Allow to update consents'),
      ('5ca9d48f-2097-4bd1-a6a4-05df06805a9a', 'sys_consent:delete', 'Allow to delete consents'),
      ('7f81915c-161c-4eb8-b72f-a02839c04e69', 'sys_consent:all', 'Allow all consent operations'),
      -- Role permissions
      ('79c8e073-4825-4ccb-9621-7b7ae127d03d', 'sys_role:create', 'Allow to create roles'),
      ('410db433-5ebc-48fa-b0ce-d313ebf3a8c6', 'sys_role:read', 'Allow to read roles'),
      ('010f7d25-44e2-4602-ba18-9b81096d50ef', 'sys_role:update', 'Allow to update roles'),
      ('f086d3fd-a216-4e1a-8381-049fae470719', 'sys_role:delete', 'Allow to delete roles'),
      ('b3077820-bc48-4a89-be3c-bf1374b7eb51', 'sys_role:all', 'Allow all role operations'),
      -- Permission permissions
      ('ab4fd567-3d79-4fc1-8fe7-9e55695dfb2a', 'sys_permission:create', 'Allow to create permissions'),
      ('9494aa9f-f0ac-4098-847c-57086568aab3', 'sys_permission:read', 'Allow to read permissions'),
      ('3f8965a3-a7b9-4074-b8bf-33824d8e8ad7', 'sys_permission:update', 'Allow to update permissions'),
      ('7372b969-56e7-43fe-bade-8d992630a173', 'sys_permission:delete', 'Allow to delete permissions'),
      ('05f9c70b-df37-4dad-8180-c03fdaa31b57', 'sys_permission:all', 'Allow all permission operations'),
      -- Group permissions
      ('e4b8c9d1-2f35-46a7-8e90-12c3d4e5f678', 'sys_group:create', 'Allow to create groups'),
      ('f5c9d0e2-3f46-57b8-9f01-23d4e5f6a789', 'sys_group:read', 'Allow to read groups'),
      ('a6d0e1f3-4f57-68c9-0f12-34e5f6a7b890', 'sys_group:update', 'Allow to update groups'),
      ('b7e1f2a4-5f68-79d0-1f23-45f6a7b8c901', 'sys_group:delete', 'Allow to delete groups'),
      ('63ff405d-100f-4955-b818-454e13d5b171', 'sys_group:all', 'Allow all group operations'),
      -- Channel permissions
      ('8f52f06e-e52c-47e0-81fd-d8e568ed34cb', 'sys_channel:create', 'Allow to create channels'),
      ('359ee229-e09c-46b1-8c55-90ac0ae1a9e8', 'sys_channel:read', 'Allow to read channels'),
      ('3b1f7511-bd15-47b8-9053-7d9bf4e99155', 'sys_channel:update', 'Allow to update channels'),
      ('b55af4fd-f1c0-4a08-9d02-0c70859f0e68', 'sys_channel:delete', 'Allow to delete channels'),
      ('50835799-dad9-4bf2-8b41-5cd922c0d04d', 'sys_channel:all', 'Allow all channel operations'),
      -- Factor TOTP permissions
      ('a29e8a6e-9d50-434a-857e-6c111c8172c9', 'sys_factor_totp:create', 'Allow to create TOTP factors'),
      ('46b76be4-a78e-4d94-9f94-53e51c8502d0', 'sys_factor_totp:read', 'Allow to read TOTP factors'),
      ('d01ac2ad-120f-4272-96b6-e280f6345165', 'sys_factor_totp:update', 'Allow to update TOTP factors'),
      ('2b76862c-3d48-497c-8c2e-24fb3d66da00', 'sys_factor_totp:delete', 'Allow to delete TOTP factors'),
      ('28657e33-e179-4ced-8a92-457fce997fae', 'sys_factor_totp:all', 'Allow all TOTP factor operations'),
      -- Factor SMS permissions
      ('adeb7443-b028-4564-992b-3edaa9a28452', 'sys_factor_sms:create', 'Allow to create SMS factors'),
      ('df4a93eb-466a-4217-bef5-0c2aafe3adeb', 'sys_factor_sms:read', 'Allow to read SMS factors'),
      ('d80aec72-f433-4259-bbf5-00b587ff90d9', 'sys_factor_sms:update', 'Allow to update SMS factors'),
      ('38720e22-ea55-4a81-b819-2eca45202db1', 'sys_factor_sms:delete', 'Allow to delete SMS factors'),
      ('1a36d1d6-7eba-427a-b097-4e6e881fb558', 'sys_factor_sms:all', 'Allow all SMS factor operations'),
      -- Factor Email permissions
      ('365c9b68-5ea9-4912-b6b4-588923143b60', 'sys_factor_email:create', 'Allow to create Email factors'),
      ('3e9da38f-6fb2-431c-9666-a89286ec6e61', 'sys_factor_email:read', 'Allow to read Email factors'),
      ('446bf8b8-3abd-414e-89b0-49fbae82f3d8', 'sys_factor_email:update', 'Allow to update Email factors'),
      ('a95d38f1-07ef-4d88-8b8c-36c86d9ad7c2', 'sys_factor_email:delete', 'Allow to delete Email factors'),
      ('6cc3d3f8-5cd9-4372-ab55-841e4f047074', 'sys_factor_email:all', 'Allow all Email factor operations'),
      -- Factor Password permissions
      ('83ff93f4-1879-4e5d-8f1a-6ffff7d92800', 'sys_factor_password:create', 'Allow to create Password factors'),
      ('cdc88324-602e-4461-ba65-79d6f5aa9727', 'sys_factor_password:read', 'Allow to read Password factors'),
      ('38febaf5-e637-40a9-b42f-631978980b05', 'sys_factor_password:update', 'Allow to update Password factors'),
      ('a5a482be-0142-420e-822a-f4091d22b236', 'sys_factor_password:delete', 'Allow to delete Password factors'),
      ('ac02fd0f-fdbd-4295-bee2-ddd49a2335d2', 'sys_factor_password:all', 'Allow all Password factor operations'),
      -- Factor Policies permissions
      ('4de0b8dd-3e0c-4055-9177-c13d901f18f1', 'sys_factor_policies:create', 'Allow to create Factor policies'),
      ('4212c99e-de64-4e6e-9478-5655c53e9b18', 'sys_factor_policies:read', 'Allow to read Factor policies'),
      ('d78738b0-50f2-44ec-9c4a-3bf0d712016e', 'sys_factor_policies:update', 'Allow to update Factor policies'),
      ('1c98dfd4-57ca-4045-b5a0-022cb251af87', 'sys_factor_policies:delete', 'Allow to delete Factor policies'),
      ('4bc58115-4c53-43d7-b168-009f75896f5a', 'sys_factor_policies:all', 'Allow all Factor policies operations')
  ) AS v(id, name, description)
  WHERE NOT EXISTS (SELECT 1 FROM dbo.permission WHERE dbo.permission.id = v.id);
END;

----------------------------
-- Roles (ADMIN and OPERATOR from RolesAndPermissionsSetupService)
----------------------------
IF OBJECT_ID('dbo.role','U') IS NOT NULL
BEGIN
  DECLARE @role_business_id UNIQUEIDENTIFIER = 'd37ee3d4-22c6-41f7-819d-1464b3b9c454';

  -- Update existing roles
  UPDATE r SET 
    name = v.name,
    description = v.description,
    updated_by = 'SYSTEM',
    updated_at = SYSUTCDATETIME()
  FROM dbo.role r
  INNER JOIN (
    VALUES
      ('9fa7a7f6-b8d4-4db4-a8c7-1642a80e863a', 'ROLE_ADMIN', 'Admin role'),
      ('779f520a-bb5f-45ef-95b8-3f2ea204023d', 'ROLE_OPERATOR', 'Operator role')
  ) AS v(id, name, description)
  ON r.id = v.id;

  -- Insert new roles
  INSERT INTO dbo.role (id, business_id, name, description, created_by, status, created_at, updated_at)
  SELECT v.id, @role_business_id, v.name, v.description, 'SYSTEM', 'ACTIVE', SYSUTCDATETIME(), SYSUTCDATETIME()
  FROM (
    VALUES
      ('9fa7a7f6-b8d4-4db4-a8c7-1642a80e863a', 'ROLE_ADMIN', 'Admin role'),
      ('779f520a-bb5f-45ef-95b8-3f2ea204023d', 'ROLE_OPERATOR', 'Operator role')
  ) AS v(id, name, description)
  WHERE NOT EXISTS (SELECT 1 FROM dbo.role WHERE dbo.role.id = v.id);
END;

----------------------------
-- Role Permission Mappings
----------------------------
IF OBJECT_ID('dbo.role_permission','U') IS NOT NULL
BEGIN
  -- Admin role gets all permissions
  INSERT INTO dbo.role_permission (role_id, permission_name)
  SELECT '9fa7a7f6-b8d4-4db4-a8c7-1642a80e863a', p.id
  FROM dbo.permission p
  WHERE NOT EXISTS (SELECT 1 FROM dbo.role_permission rp 
                   WHERE rp.role_id = '9fa7a7f6-b8d4-4db4-a8c7-1642a80e863a' AND rp.permission_name = p.id);

  -- Operator role gets only read permissions (permissions with 'read' in the name)
  INSERT INTO dbo.role_permission (role_id, permission_name)
  SELECT '779f520a-bb5f-45ef-95b8-3f2ea204023d', p.id
  FROM dbo.permission p
  WHERE p.name LIKE '%:read%'
    AND NOT EXISTS (SELECT 1 FROM dbo.role_permission rp 
                   WHERE rp.role_id = '779f520a-bb5f-45ef-95b8-3f2ea204023d' AND rp.permission_name = p.id);
END;

-- Note: OAuth clients and admin users remain handled by existing Java seeders
-- This migration now covers cross-tenant, non-secret foundation data including roles and permissions

COMMIT TRANSACTION;

-- Restore default NOCOUNT behavior to prevent issues with JPA/Hibernate
-- When NOCOUNT is ON, INSERT/UPDATE/DELETE statements return -1 instead of actual row count
-- This causes Hibernate's StaleStateException when it expects row count = 1
SET NOCOUNT OFF;