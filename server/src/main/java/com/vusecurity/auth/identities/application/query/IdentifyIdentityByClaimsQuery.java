package com.vusecurity.auth.identities.application.query;

import com.vusecurity.auth.contracts.api.v1.dto.identities.ClaimIdentifierRequest;
import com.vusecurity.auth.contracts.enums.IdentityType;

import lombok.Builder;
import lombok.Value;

import java.util.List;

@Value
@Builder
public class IdentifyIdentityByClaimsQuery {
    IdentityType identityType;
    List<ClaimIdentifierRequest> claimIdentifiers;

    public void validate() {
        if (claimIdentifiers == null || claimIdentifiers.isEmpty()) {
            throw new IllegalArgumentException("At least one claim identifier is required");
        }
        
        // Validate each claim identifier
        for (ClaimIdentifierRequest claimIdentifier : claimIdentifiers) {
            if (claimIdentifier.getClaimDefinitionId() == null) {
                throw new IllegalArgumentException("Claim definition ID is required for all claim identifiers");
            }
            if (claimIdentifier.getValue() == null || claimIdentifier.getValue().trim().isEmpty()) {
                throw new IllegalArgumentException("Claim value is required and cannot be blank");
            }
        }
    }
}
