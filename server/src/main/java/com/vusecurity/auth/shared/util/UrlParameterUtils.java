package com.vusecurity.auth.shared.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

/**
 * Utility class for handling URL parameter decoding and sanitization.
 * Ensures that URL-encoded parameters are properly decoded before being used in queries.
 */
public final class UrlParameterUtils {

    private static final Logger logger = LoggerFactory.getLogger(UrlParameterUtils.class);

    private UrlParameterUtils() {
        // Utility class - prevent instantiation
    }

    /**
     * Safely decodes a URL-encoded parameter string.
     * Returns null if the input is null, empty string if input is empty after trimming.
     * 
     * @param encodedParam the URL-encoded parameter
     * @return the decoded parameter, or null if input was null
     */
    public static String decodeParameter(String encodedParam) {
        if (encodedParam == null) {
            return null;
        }

        String trimmed = encodedParam.trim();
        if (trimmed.isEmpty()) {
            return "";
        }

        try {
            String decoded = URLDecoder.decode(trimmed, StandardCharsets.UTF_8);
            logger.trace("Decoded parameter: '{}' -> '{}'", encodedParam, decoded);
            return decoded;
        } catch (Exception e) {
            logger.warn("Failed to decode URL parameter '{}': {}", encodedParam, e.getMessage());
            // Return the original parameter if decoding fails
            return trimmed;
        }
    }

    /**
     * Safely decodes a URL-encoded parameter and performs basic sanitization.
     * Removes potentially dangerous characters while preserving legitimate search terms.
     * 
     * @param encodedParam the URL-encoded parameter
     * @return the decoded and sanitized parameter, or null if input was null
     */
    public static String decodeAndSanitizeParameter(String encodedParam) {
        String decoded = decodeParameter(encodedParam);
        
        if (decoded == null || decoded.isEmpty()) {
            return decoded;
        }

        // Basic sanitization - remove control characters but preserve legitimate special chars
        String sanitized = decoded.replaceAll("[\\p{Cntrl}]", "");
        
        if (!decoded.equals(sanitized)) {
            logger.debug("Sanitized parameter: '{}' -> '{}'", decoded, sanitized);
        }
        
        return sanitized;
    }
}
