package com.vusecurity.auth.shared;

import com.vusecurity.auth.authorization.application.command.CreateRoleCommand;
import com.vusecurity.auth.claims.application.command.CreateClaimDefinitionCommand;
import com.vusecurity.auth.claims.application.command.CreateClaimValueCommand;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.contracts.enums.IdentityLifecycleState;
import com.vusecurity.auth.contracts.enums.LookupStrategy;
import com.vusecurity.auth.contracts.enums.IdentityType;
import com.vusecurity.auth.identities.application.command.CreateAccountCommand;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.domain.enums.BusinessTypeEnum;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * Test data builder utility class for creating test objects with valid defaults.
 * Provides fluent API for building test data with sensible defaults.
 */
public class TestDataBuilder {

    // Command Builders
    public static CreateAccountCommandBuilder validCreateAccountCommand() {
        return new CreateAccountCommandBuilder();
    }

    public static CreateClaimDefinitionCommandBuilder validCreateClaimDefinitionCommand() {
        return new CreateClaimDefinitionCommandBuilder();
    }

    public static CreateClaimValueCommandBuilder validCreateClaimValueCommand() {
        return new CreateClaimValueCommandBuilder();
    }

    public static CreateRoleCommandBuilder validCreateRoleCommand() {
        return new CreateRoleCommandBuilder();
    }

    // Entity Builders
    public static BusinessBuilder validBusiness() {
        return new BusinessBuilder();
    }

    public static IdentityJpaEntityBuilder validIdentity() {
        return new IdentityJpaEntityBuilder();
    }

    public static AccountJpaEntityBuilder validAccount() {
        return new AccountJpaEntityBuilder();
    }

    public static ClaimDefinitionJpaEntityBuilder validClaimDefinition() {
        return new ClaimDefinitionJpaEntityBuilder();
    }

    public static ClaimValueJpaEntityBuilder validClaimValue() {
        return new ClaimValueJpaEntityBuilder();
    }

    public static ClaimSetJpaEntityBuilder validClaimSet() {
        return new ClaimSetJpaEntityBuilder();
    }

    // Command Builder Classes
    public static class CreateAccountCommandBuilder {
        private UUID identityId = UUID.randomUUID();
        private UUID businessId = UUID.randomUUID();
        private UUID identityProviderId = UUID.randomUUID();
        private AccountType accountType = AccountType.WORKFORCE;
        private AccountLifecycleState lifecycleState = AccountLifecycleState.ACTIVE;
        private Map<String, Object> metadata = new HashMap<>();

        public CreateAccountCommandBuilder identityId(UUID identityId) {
            this.identityId = identityId;
            return this;
        }

        public CreateAccountCommandBuilder businessId(UUID businessId) {
            this.businessId = businessId;
            return this;
        }

        public CreateAccountCommandBuilder identityProviderId(UUID identityProviderId) {
            this.identityProviderId = identityProviderId;
            return this;
        }

        public CreateAccountCommandBuilder accountType(AccountType accountType) {
            this.accountType = accountType;
            return this;
        }

        public CreateAccountCommandBuilder lifecycleState(AccountLifecycleState lifecycleState) {
            this.lifecycleState = lifecycleState;
            return this;
        }

        public CreateAccountCommandBuilder metadata(Map<String, Object> metadata) {
            this.metadata = metadata;
            return this;
        }

        public CreateAccountCommandBuilder addMetadata(String key, Object value) {
            this.metadata.put(key, value);
            return this;
        }

        public CreateAccountCommand build() {
            return new CreateAccountCommand(identityId, businessId, identityProviderId,
                    accountType, lifecycleState, metadata, null);
        }
    }

    public static class CreateClaimDefinitionCommandBuilder {
        private String code = "test_claim";
        private String name = "Test Claim";
        private String description = "Test claim description";
        private DataTypeEnum dataType = DataTypeEnum.STRING;
        private String dataFormat = null;

        public CreateClaimDefinitionCommandBuilder code(String code) {
            this.code = code;
            return this;
        }

        public CreateClaimDefinitionCommandBuilder name(String name) {
            this.name = name;
            return this;
        }

        public CreateClaimDefinitionCommandBuilder description(String description) {
            this.description = description;
            return this;
        }

        public CreateClaimDefinitionCommandBuilder dataType(DataTypeEnum dataType) {
            this.dataType = dataType;
            return this;
        }

        public CreateClaimDefinitionCommandBuilder dataFormat(String dataFormat) {
            this.dataFormat = dataFormat;
            return this;
        }

        public CreateClaimDefinitionCommand build() {
            return new CreateClaimDefinitionCommand(code, name, description, dataType, dataFormat);
        }
    }

    public static class CreateClaimValueCommandBuilder {
        private UUID claimDefinitionId = UUID.randomUUID();
        private UUID identityId = UUID.randomUUID();
        private UUID accountId = null;
        private String value = "test-value";
        private boolean isPrimary = true;
        private boolean isComputed = false;
        private String source = "USER_INPUT";

        public CreateClaimValueCommandBuilder claimDefinitionId(UUID claimDefinitionId) {
            this.claimDefinitionId = claimDefinitionId;
            return this;
        }

        public CreateClaimValueCommandBuilder identityId(UUID identityId) {
            this.identityId = identityId;
            this.accountId = null; // Clear accountId when setting identityId
            return this;
        }

        public CreateClaimValueCommandBuilder accountId(UUID accountId) {
            this.accountId = accountId;
            this.identityId = null; // Clear identityId when setting accountId
            return this;
        }

        public CreateClaimValueCommandBuilder value(String value) {
            this.value = value;
            return this;
        }

        public CreateClaimValueCommandBuilder isPrimary(boolean isPrimary) {
            this.isPrimary = isPrimary;
            return this;
        }

        public CreateClaimValueCommandBuilder isComputed(boolean isComputed) {
            this.isComputed = isComputed;
            return this;
        }

        public CreateClaimValueCommandBuilder source(String source) {
            this.source = source;
            return this;
        }

        public CreateClaimValueCommand build() {
            return new CreateClaimValueCommand(claimDefinitionId, identityId, accountId, 
                    value, isPrimary, isComputed, source);
        }
    }

    public static class CreateRoleCommandBuilder {
        private String name = "TEST_ROLE";
        private String description = "Test role description";
        private UUID businessId = UUID.randomUUID();
        private Set<UUID> permissionIds = Set.of(UUID.randomUUID());

        public CreateRoleCommandBuilder name(String name) {
            this.name = name;
            return this;
        }

        public CreateRoleCommandBuilder description(String description) {
            this.description = description;
            return this;
        }

        public CreateRoleCommandBuilder businessId(UUID businessId) {
            this.businessId = businessId;
            return this;
        }

        public CreateRoleCommandBuilder permissionIds(Set<UUID> permissionIds) {
            this.permissionIds = permissionIds;
            return this;
        }

        public CreateRoleCommand build() {
            return new CreateRoleCommand(name, description, businessId, permissionIds);
        }
    }

    // Entity Builder Classes
    public static class BusinessBuilder {
        private UUID id = UUID.randomUUID();
        private String name = "Test Business";
        private BusinessTypeEnum businessType = BusinessTypeEnum.BUSINESS_UNIT;
        private String createdBy = "TEST_USER";

        public BusinessBuilder id(UUID id) {
            this.id = id;
            return this;
        }

        public BusinessBuilder name(String name) {
            this.name = name;
            return this;
        }

        public BusinessBuilder businessType(BusinessTypeEnum businessType) {
            this.businessType = businessType;
            return this;
        }

        public BusinessBuilder createdBy(String createdBy) {
            this.createdBy = createdBy;
            return this;
        }

        public Business build() {
            Business business = new Business();
            business.setId(id);
            business.setName(name);
            business.setBusinessType(businessType);
            business.setCreatedBy(createdBy);
            return business;
        }
    }

    public static class IdentityJpaEntityBuilder {
        private UUID id = UUID.randomUUID();
        private IdentityType identityType = IdentityType.PERSON;
        private String name = "Test Identity";
        private IdentityLifecycleState lifecycleState = IdentityLifecycleState.ACTIVE;
        private Map<String, Object> metadata = new HashMap<>();

        public IdentityJpaEntityBuilder id(UUID id) {
            this.id = id;
            return this;
        }

        public IdentityJpaEntityBuilder identityType(IdentityType identityType) {
            this.identityType = identityType;
            return this;
        }

        public IdentityJpaEntityBuilder name(String name) {
            this.name = name;
            return this;
        }

        public IdentityJpaEntityBuilder lifecycleState(IdentityLifecycleState lifecycleState) {
            this.lifecycleState = lifecycleState;
            return this;
        }

        public IdentityJpaEntity build() {
            IdentityJpaEntity identity = new IdentityJpaEntity(identityType, name);
            identity.setId(id);
            identity.setLifecycleState(lifecycleState);
            identity.setMetadata(metadata);
            return identity;
        }
    }

    public static class AccountJpaEntityBuilder {
        private UUID id = UUID.randomUUID();
        private Business business = validBusiness().build();
        private IdentityJpaEntity identity = validIdentity().build();
        private IdentityProviderJpaEntity identityProvider = createTestIdentityProvider();
        private AccountType accountType = AccountType.WORKFORCE;
        private AccountLifecycleState lifecycleState = AccountLifecycleState.ACTIVE;
        private Map<String, Object> metadata = new HashMap<>();

        public AccountJpaEntityBuilder id(UUID id) {
            this.id = id;
            return this;
        }

        public AccountJpaEntityBuilder business(Business business) {
            this.business = business;
            return this;
        }

        public AccountJpaEntityBuilder identity(IdentityJpaEntity identity) {
            this.identity = identity;
            return this;
        }

        public AccountJpaEntityBuilder accountType(AccountType accountType) {
            this.accountType = accountType;
            return this;
        }

        public AccountJpaEntityBuilder lifecycleState(AccountLifecycleState lifecycleState) {
            this.lifecycleState = lifecycleState;
            return this;
        }

        public AccountJpaEntity build() {
            AccountJpaEntity account = new AccountJpaEntity(business, identity, identityProvider, accountType);
            account.setId(id);
            account.setLifecycleState(lifecycleState);
            account.setMetadata(metadata);
            return account;
        }

        private IdentityProviderJpaEntity createTestIdentityProvider() {
            IdentityProviderJpaEntity provider = new IdentityProviderJpaEntity();
            provider.setId(UUID.randomUUID());
            return provider;
        }
    }

    public static class ClaimDefinitionJpaEntityBuilder {
        private UUID id = UUID.randomUUID();
        private String code = "test_claim";
        private String name = "Test Claim";
        private String description = "Test claim description";
        private DataTypeEnum dataType = DataTypeEnum.STRING;
        private String dataFormat = null;

        public ClaimDefinitionJpaEntityBuilder id(UUID id) {
            this.id = id;
            return this;
        }

        public ClaimDefinitionJpaEntityBuilder code(String code) {
            this.code = code;
            return this;
        }

        public ClaimDefinitionJpaEntityBuilder name(String name) {
            this.name = name;
            return this;
        }

        public ClaimDefinitionJpaEntityBuilder description(String description) {
            this.description = description;
            return this;
        }

        public ClaimDefinitionJpaEntityBuilder dataType(DataTypeEnum dataType) {
            this.dataType = dataType;
            return this;
        }

        public ClaimDefinitionJpaEntityBuilder dataFormat(String dataFormat) {
            this.dataFormat = dataFormat;
            return this;
        }

        public ClaimDefinitionJpaEntity build() {
            ClaimDefinitionJpaEntity claimDef = new ClaimDefinitionJpaEntity(code, name, description, dataType, dataFormat);
            claimDef.setId(id);
            return claimDef;
        }
    }

    public static class ClaimValueJpaEntityBuilder {
        private UUID id = UUID.randomUUID();
        private ClaimDefinitionJpaEntity claimDefinition = validClaimDefinition().build();
        private OwnerType ownerType = OwnerType.ACCOUNT;
        private UUID ownerId = UUID.randomUUID();
        private String value = "test-value";
        private boolean isPrimary = false;
        private boolean isComputed = false;
        private String source = "TEST";

        public ClaimValueJpaEntityBuilder id(UUID id) {
            this.id = id;
            return this;
        }

        public ClaimValueJpaEntityBuilder claimDefinition(ClaimDefinitionJpaEntity claimDefinition) {
            this.claimDefinition = claimDefinition;
            return this;
        }

        public ClaimValueJpaEntityBuilder ownerType(OwnerType ownerType) {
            this.ownerType = ownerType;
            return this;
        }

        public ClaimValueJpaEntityBuilder ownerId(UUID ownerId) {
            this.ownerId = ownerId;
            return this;
        }

        public ClaimValueJpaEntityBuilder value(String value) {
            this.value = value;
            return this;
        }

        public ClaimValueJpaEntityBuilder isPrimary(boolean isPrimary) {
            this.isPrimary = isPrimary;
            return this;
        }

        public ClaimValueJpaEntityBuilder isComputed(boolean isComputed) {
            this.isComputed = isComputed;
            return this;
        }

        public ClaimValueJpaEntityBuilder source(String source) {
            this.source = source;
            return this;
        }

        public ClaimValueJpaEntity build() {
            ClaimValueJpaEntity claimValue = new ClaimValueJpaEntity(claimDefinition, ownerType, ownerId);
            claimValue.setId(id);
            claimValue.setValue(value);
            claimValue.setPrimary(isPrimary);
            claimValue.setComputed(isComputed);
            claimValue.setSource(source);
            return claimValue;
        }
    }

    public static class ClaimSetJpaEntityBuilder {
        private UUID id = UUID.randomUUID();
        private Business business = validBusiness().build();
        private AccountType accountType = AccountType.WORKFORCE;
        private Boolean isIdentifier = false;
        private String name = "Test ClaimSet";
        private String description = "Test ClaimSet Description";
        private LookupStrategy lookupStrategy = LookupStrategy.ALL_CLAIMS_MUST_MATCH;

        public ClaimSetJpaEntityBuilder id(UUID id) {
            this.id = id;
            return this;
        }

        public ClaimSetJpaEntityBuilder business(Business business) {
            this.business = business;
            return this;
        }

        public ClaimSetJpaEntityBuilder accountType(AccountType accountType) {
            this.accountType = accountType;
            return this;
        }

        public ClaimSetJpaEntityBuilder isIdentifier(Boolean isIdentifier) {
            this.isIdentifier = isIdentifier;
            return this;
        }

        public ClaimSetJpaEntityBuilder name(String name) {
            this.name = name;
            return this;
        }

        public ClaimSetJpaEntityBuilder description(String description) {
            this.description = description;
            return this;
        }

        public ClaimSetJpaEntityBuilder lookupStrategy(LookupStrategy lookupStrategy) {
            this.lookupStrategy = lookupStrategy;
            return this;
        }

        public ClaimSetJpaEntity build() {
            ClaimSetJpaEntity claimSet = new ClaimSetJpaEntity(business, accountType, isIdentifier);
            claimSet.setId(id);
            claimSet.setName(name);
            claimSet.setDescription(description);
            claimSet.setLookupStrategy(lookupStrategy);
            return claimSet;
        }
    }
}
