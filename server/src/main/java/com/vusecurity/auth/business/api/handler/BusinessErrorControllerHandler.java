package com.vusecurity.auth.business.api.handler;

import com.vusecurity.business.usecases.exceptions.BusinessAlreadyExistException;
import com.vusecurity.business.usecases.exceptions.BusinessNameRequiredException;
import com.vusecurity.business.usecases.exceptions.BusinessNotFoundException;
import com.vusecurity.business.usecases.exceptions.BusinessTypeInvalidException;
import com.vusecurity.core.commons.ApiMessage;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@ControllerAdvice(assignableTypes = {com.vusecurity.auth.business.api.controller.BusinessController.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class BusinessErrorControllerHandler {

    @ExceptionHandler(BusinessNotFoundException.class)
    public ResponseEntity<ApiMessage> handleNotFoundException(BusinessNotFoundException ex) {
        ApiMessage error = new ApiMessage(3001, ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }

    @ExceptionHandler(BusinessAlreadyExistException.class)
    public ResponseEntity<ApiMessage> handleAlreadyExistsException(BusinessAlreadyExistException ex) {
        ApiMessage error = new ApiMessage(3002, ex.getMessage());
        return ResponseEntity.status(HttpStatus.CONFLICT).body(error);
    }

    @ExceptionHandler(BusinessNameRequiredException.class)
    public ResponseEntity<ApiMessage> handleNameRequiredException(BusinessNameRequiredException ex) {
        ApiMessage error = new ApiMessage(3003, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }

    @ExceptionHandler(BusinessTypeInvalidException.class)
    public ResponseEntity<ApiMessage> handleTypeInvalidException(BusinessTypeInvalidException ex) {
        ApiMessage error = new ApiMessage(3004, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }
}
