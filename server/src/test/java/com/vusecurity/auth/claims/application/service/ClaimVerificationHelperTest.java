package com.vusecurity.auth.claims.application.service;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimVerificationJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimVerificationRepository;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.shared.enums.ResultType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class ClaimVerificationHelperTest {

    private ClaimValueRepository claimValueRepository;
    private ClaimVerificationRepository claimVerificationRepository;
    private ClaimVerificationHelper helper;

    @BeforeEach
    void setUp() {
        claimValueRepository = mock(ClaimValueRepository.class);
        claimVerificationRepository = mock(ClaimVerificationRepository.class);
        helper = new ClaimVerificationHelper(claimValueRepository, claimVerificationRepository);
    }

    @Test
    void getClaimValuesWithVerification_marksLoaded() {
        UUID accountId = UUID.randomUUID();
        ClaimValueJpaEntity cv1 = new ClaimValueJpaEntity();
        ClaimValueJpaEntity cv2 = new ClaimValueJpaEntity();
        when(claimValueRepository.findByAccountIdWithDefinitionAndVerification(accountId))
                .thenReturn(List.of(cv1, cv2));

        List<ClaimValueJpaEntity> result = helper.getClaimValuesWithVerification(accountId);

        assertEquals(2, result.size());
        assertTrue(cv1.isVerificationLoaded());
        assertTrue(cv2.isVerificationLoaded());
    }

    @Test
    void getClaimValueWithVerification_marksLoadedIfPresent() {
        UUID id = UUID.randomUUID();
        ClaimValueJpaEntity cv = new ClaimValueJpaEntity();
        when(claimValueRepository.findByIdWithVerification(id)).thenReturn(Optional.of(cv));

        Optional<ClaimValueJpaEntity> out = helper.getClaimValueWithVerification(id);
        assertTrue(out.isPresent());
        assertTrue(cv.isVerificationLoaded());
    }

    @Test
    void isClaimValueVerified_delegatesToRepository() {
        UUID id = UUID.randomUUID();
        when(claimVerificationRepository.hasSuccessfulVerification(id)).thenReturn(true);
        assertTrue(helper.isClaimValueVerified(id));
    }

    @Test
    void getLatestVerification_returnsRepoValue() {
        UUID id = UUID.randomUUID();
        ClaimVerificationJpaEntity ver = new ClaimVerificationJpaEntity();
        when(claimVerificationRepository.findLatestByClaimValueId(id)).thenReturn(Optional.of(ver));
        assertTrue(helper.getLatestVerification(id).isPresent());
    }

    @Test
    void getAllVerifications_returnsRepoList() {
        UUID id = UUID.randomUUID();
        when(claimVerificationRepository.findByClaimValueId(id)).thenReturn(List.of(new ClaimVerificationJpaEntity()));
        assertFalse(helper.getAllVerifications(id).isEmpty());
    }

    @Test
    void getVerificationsByResult_returnsRepoList() {
        UUID id = UUID.randomUUID();
        when(claimVerificationRepository.findByClaimValueIdAndResult(id, ResultType.OK))
                .thenReturn(List.of(new ClaimVerificationJpaEntity()));
        assertFalse(helper.getVerificationsByResult(id, ResultType.OK).isEmpty());
    }

    @Test
    void getVerificationsByType_returnsRepoList() {
        UUID id = UUID.randomUUID();
        when(claimVerificationRepository.findByClaimValueIdAndVerificationType(id, "EMAIL"))
                .thenReturn(List.of(new ClaimVerificationJpaEntity()));
        assertFalse(helper.getVerificationsByType(id, "EMAIL").isEmpty());
    }

    @Test
    void getAccountVerifications_returnsRepoList() {
        UUID id = UUID.randomUUID();
        when(claimVerificationRepository.findByAccountId(id)).thenReturn(List.of(new ClaimVerificationJpaEntity()));
        assertFalse(helper.getAccountVerifications(id).isEmpty());
    }

    @Test
    void isVerified_usesLoadedVerificationFlag() {
        ClaimValueJpaEntity cv = new ClaimValueJpaEntity();
        ClaimVerificationJpaEntity ver = new ClaimVerificationJpaEntity();
        ver.setResult(ResultType.OK);
        cv.setVerification(ver);
        cv.setVerificationLoaded(true);
        assertTrue(helper.isVerified(cv));
    }

    @Test
    void isVerified_queriesRepositoryWhenNotLoaded() {
        UUID id = UUID.randomUUID();
        ClaimValueJpaEntity cv = new ClaimValueJpaEntity();
        cv.setId(id);
        cv.setVerificationLoaded(false);
        when(claimVerificationRepository.hasSuccessfulVerification(id)).thenReturn(true);
        assertTrue(helper.isVerified(cv));
    }

    @Test
    void getVerificationStatus_usesLoadedVerification() {
        ClaimValueJpaEntity cv = new ClaimValueJpaEntity();
        ClaimVerificationJpaEntity ver = new ClaimVerificationJpaEntity();
        ver.setResult(ResultType.OK);
        cv.setVerification(ver);
        cv.setVerificationLoaded(true);
        assertEquals(ResultType.OK, helper.getVerificationStatus(cv));
    }

    @Test
    void getVerificationStatus_queriesLatestWhenNotLoaded() {
        UUID id = UUID.randomUUID();
        ClaimValueJpaEntity cv = new ClaimValueJpaEntity();
        cv.setId(id);
        cv.setVerificationLoaded(false);
        ClaimVerificationJpaEntity latest = new ClaimVerificationJpaEntity();
        latest.setResult(ResultType.NONE);
        latest.setVerifiedAt(Instant.now());
        when(claimVerificationRepository.findLatestByClaimValueId(id)).thenReturn(Optional.of(latest));
        assertEquals(ResultType.NONE, helper.getVerificationStatus(cv));
    }

    @Test
    void ensureVerificationLoaded_fetches_when_not_loaded() {
        UUID id = UUID.randomUUID();
        ClaimValueJpaEntity cv = new ClaimValueJpaEntity();
        cv.setId(id);
        cv.setVerificationLoaded(false);

        ClaimValueJpaEntity loaded = new ClaimValueJpaEntity();
        ClaimVerificationJpaEntity v = new ClaimVerificationJpaEntity();
        v.setResult(ResultType.OK);
        loaded.setVerification(v);

        when(claimValueRepository.findByIdWithVerification(id)).thenReturn(Optional.of(loaded));

        helper.ensureVerificationLoaded(cv);
        assertTrue(cv.isVerificationLoaded());
        assertNotNull(cv.getVerification());
        assertEquals(ResultType.OK, cv.getVerification().getResult());
    }

    @Test
    void ensureVerificationLoaded_list_noop_on_empty() {
        helper.ensureVerificationLoaded(List.of());
    }
}

