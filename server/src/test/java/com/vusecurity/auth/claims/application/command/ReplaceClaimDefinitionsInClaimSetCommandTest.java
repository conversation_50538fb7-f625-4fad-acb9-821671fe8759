package com.vusecurity.auth.claims.application.command;

import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class ReplaceClaimDefinitionsInClaimSetCommandTest {

    @Test
    void validate_happy_path() {
        ReplaceClaimDefinitionsInClaimSetCommand.ClaimDefinitionsToClaimSetCommand item =
                new ReplaceClaimDefinitionsInClaimSetCommand.ClaimDefinitionsToClaimSetCommand(UUID.randomUUID(), 1, true);
        ReplaceClaimDefinitionsInClaimSetCommand cmd = new ReplaceClaimDefinitionsInClaimSetCommand(
                UUID.randomUUID(), List.of(item));
        assertDoesNotThrow(cmd::validate);
    }

    @Test
    void validate_throws_when_claimSetId_null() {
        ReplaceClaimDefinitionsInClaimSetCommand.ClaimDefinitionsToClaimSetCommand item =
                new ReplaceClaimDefinitionsInClaimSetCommand.ClaimDefinitionsToClaimSetCommand(UUID.randomUUID(), 1, true);
        ReplaceClaimDefinitionsInClaimSetCommand cmd = new ReplaceClaimDefinitionsInClaimSetCommand(
                null, List.of(item));
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("claimSetId"));
    }

    @Test
    void validate_throws_when_claimDefinitions_null() {
        ReplaceClaimDefinitionsInClaimSetCommand cmd = new ReplaceClaimDefinitionsInClaimSetCommand(
                UUID.randomUUID(), null);
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("claimDefinitionIds"));
    }

    @Test
    void validate_throws_when_claimDefinitions_empty() {
        ReplaceClaimDefinitionsInClaimSetCommand cmd = new ReplaceClaimDefinitionsInClaimSetCommand(
                UUID.randomUUID(), List.of());
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("At least one"));
    }

    @Test
    void validate_throws_when_claimDefinitions_contains_null() {
        List<ReplaceClaimDefinitionsInClaimSetCommand.ClaimDefinitionsToClaimSetCommand> claimDefinitions = new java.util.ArrayList<>();
        claimDefinitions.add(null);
        ReplaceClaimDefinitionsInClaimSetCommand cmd = new ReplaceClaimDefinitionsInClaimSetCommand(
                UUID.randomUUID(), claimDefinitions);
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, cmd::validate);
        assertTrue(ex.getMessage().contains("cannot contain null"));
    }
}
