package com.vusecurity.auth.claims.application.command;

import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for CreateClaimDefinitionCommand validation logic.
 * Tests pure domain logic with no external dependencies.
 */
class CreateClaimDefinitionCommandTest {

    @Test
    void shouldValidateSuccessfully_WhenAllRequiredFieldsProvided() {
        // Given
        CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                "email",
                "Email Address",
                "User's email address",
                DataTypeEnum.STRING,
                "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WhenOptionalFieldsAreNull() {
        // Given
        CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                "username",
                "Username",
                null, // description is optional
                DataTypeEnum.STRING,
                null  // dataFormat is optional
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldThrowException_WhenCodeIsNull() {
        // Given
        CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                null, // code is null
                "Email Address",
                "User's email address",
                DataTypeEnum.STRING,
                "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("Code cannot be null or empty", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenCodeIsEmpty() {
        // Given
        CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                "", // code is empty
                "Email Address",
                "User's email address",
                DataTypeEnum.STRING,
                "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("Code cannot be null or empty", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenCodeIsWhitespace() {
        // Given
        CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                "   ", // code is whitespace
                "Email Address",
                "User's email address",
                DataTypeEnum.STRING,
                "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("Code cannot be null or empty", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenNameIsNull() {
        // Given
        CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                "email",
                null, // name is null
                "User's email address",
                DataTypeEnum.STRING,
                "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("Name cannot be null or empty", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenNameIsEmpty() {
        // Given
        CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                "email",
                "", // name is empty
                "User's email address",
                DataTypeEnum.STRING,
                "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("Name cannot be null or empty", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenNameIsWhitespace() {
        // Given
        CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                "email",
                "   ", // name is whitespace
                "User's email address",
                DataTypeEnum.STRING,
                "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("Name cannot be null or empty", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenDataTypeIsNull() {
        // Given
        CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                "email",
                "Email Address",
                "User's email address",
                null, // dataType is null
                "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("Data dataFormat cannot be null", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenCodeExceedsMaxLength() {
        // Given
        String longCode = "a".repeat(256); // 256 characters, exceeds 255 limit
        CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                longCode,
                "Email Address",
                "User's email address",
                DataTypeEnum.STRING,
                "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("Code must not exceed 255 characters", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenNameExceedsMaxLength() {
        // Given
        String longName = "a".repeat(256); // 256 characters, exceeds 255 limit
        CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                "email",
                longName,
                "User's email address",
                DataTypeEnum.STRING,
                "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("Name must not exceed 255 characters", exception.getMessage());
    }

    @Test
    void shouldValidateSuccessfully_WhenCodeIsAtMaxLength() {
        // Given
        String maxLengthCode = "a".repeat(255); // Exactly 255 characters
        CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                maxLengthCode,
                "Email Address",
                "User's email address",
                DataTypeEnum.STRING,
                "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WhenNameIsAtMaxLength() {
        // Given
        String maxLengthName = "a".repeat(255); // Exactly 255 characters
        CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                "email",
                maxLengthName,
                "User's email address",
                DataTypeEnum.STRING,
                "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithAllDataTypes() {
        // Test all enum values
        for (DataTypeEnum dataType : DataTypeEnum.values()) {
            // Given
            CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                    "test_claim_" + dataType.name().toLowerCase(),
                    "Test Claim " + dataType.name(),
                    "Test description",
                    dataType,
                    null
            );

            // When & Then
            assertDoesNotThrow(command::validate, 
                    "Validation should pass for DataType: " + dataType);
        }
    }

    @Test
    void shouldValidateSuccessfully_WithLongDescription() {
        // Given
        String longDescription = "a".repeat(1000); // Long but valid description
        CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                "email",
                "Email Address",
                longDescription,
                DataTypeEnum.STRING,
                "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithLongDataFormat() {
        // Given
        String longDataFormat = "a".repeat(1000); // Long but valid data format
        CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                "email",
                "Email Address",
                "User's email address",
                DataTypeEnum.STRING,
                longDataFormat
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }
}
