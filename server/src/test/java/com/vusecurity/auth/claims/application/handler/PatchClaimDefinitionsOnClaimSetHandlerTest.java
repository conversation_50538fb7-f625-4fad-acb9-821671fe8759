package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.ClaimSetAssignmentCommand;
import com.vusecurity.auth.claims.application.command.PatchClaimDefinitionsOnClaimSetCommand;
import com.vusecurity.auth.claims.application.exception.ClaimDefinitionNotFoundException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.service.ClaimSetDefinitionMappingService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PatchClaimDefinitionsOnClaimSetHandlerTest {

    @Mock
    private ClaimDefinitionRepository claimDefinitionRepository;

    @Mock
    private ClaimSetRepository claimSetRepository;

    @Mock
    private ClaimSetDefinitionMappingService mappingService;

    private PatchClaimDefinitionsOnClaimSetHandler handler;

    private UUID claimSetId;
    private UUID claimDefinitionId1;
    private UUID claimDefinitionId2;
    private ClaimSetJpaEntity testClaimSet;
    private ClaimDefinitionJpaEntity testClaimDefinition1;
    private ClaimDefinitionJpaEntity testClaimDefinition2;

    @BeforeEach
    void setUp() {
        handler = new PatchClaimDefinitionsOnClaimSetHandler(
                claimDefinitionRepository,
                claimSetRepository,
                mappingService
        );

        claimSetId = UUID.randomUUID();
        claimDefinitionId1 = UUID.randomUUID();
        claimDefinitionId2 = UUID.randomUUID();

        testClaimSet = new ClaimSetJpaEntity();
        testClaimSet.setId(claimSetId);

        testClaimDefinition1 = new ClaimDefinitionJpaEntity();
        testClaimDefinition1.setId(claimDefinitionId1);

        testClaimDefinition2 = new ClaimDefinitionJpaEntity();
        testClaimDefinition2.setId(claimDefinitionId2);
    }

    @Test
    void shouldPatchClaimDefinitionsSuccessfully() {
        // Given
        PatchClaimDefinitionsOnClaimSetCommand command = new PatchClaimDefinitionsOnClaimSetCommand(
                claimSetId,
                List.of(
                        new PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand(claimDefinitionId1, 1, true),
                        new PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand(claimDefinitionId2, 2, false)
                )
        );

        when(claimSetRepository.getReferenceById(claimSetId)).thenReturn(testClaimSet);
        when(claimDefinitionRepository.countByIdIn(Set.of(claimDefinitionId1, claimDefinitionId2))).thenReturn(2L);

        // When
        handler.patchClaimDefinitionsOnClaimSet(command);

        // Then
        verify(claimSetRepository).getReferenceById(claimSetId);
        verify(claimDefinitionRepository).countByIdIn(Set.of(claimDefinitionId1, claimDefinitionId2));
        // Use argThat for unordered comparison
        verify(mappingService).batchAddClaimDefinitionsToClaimSet(
                eq(testClaimSet),
                argThat(actualIds -> new HashSet<>(actualIds).equals(Set.of(claimDefinitionId1, claimDefinitionId2))),
                argThat(actualAssignments -> new HashSet<>(actualAssignments).equals(new HashSet<>(command.claimDefinitionsAssignment())))
        );
    }

    @Test
    void shouldHandleEmptyClaimDefinitions() {
        // Given
        PatchClaimDefinitionsOnClaimSetCommand command = new PatchClaimDefinitionsOnClaimSetCommand(
                claimSetId,
                List.of()
        );

        when(claimSetRepository.getReferenceById(claimSetId)).thenReturn(testClaimSet);

        // When
        handler.patchClaimDefinitionsOnClaimSet(command);

        // Then
        verify(claimSetRepository).getReferenceById(claimSetId);
        verifyNoInteractions(claimDefinitionRepository);
        verifyNoInteractions(mappingService);
    }

    @Test
    void shouldThrowException_WhenClaimDefinitionsNotFound() {
        // Given
        PatchClaimDefinitionsOnClaimSetCommand command = new PatchClaimDefinitionsOnClaimSetCommand(
                claimSetId,
                List.of(
                        new PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand(claimDefinitionId1, 1, true),
                        new PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand(claimDefinitionId2, 2, false)
                )
        );

        when(claimSetRepository.getReferenceById(claimSetId)).thenReturn(testClaimSet);
        when(claimDefinitionRepository.countByIdIn(Set.of(claimDefinitionId1, claimDefinitionId2))).thenReturn(1L); // Only 1 found, but 2 requested

        // When & Then
        ClaimDefinitionNotFoundException exception = assertThrows(
                ClaimDefinitionNotFoundException.class,
                () -> handler.patchClaimDefinitionsOnClaimSet(command)
        );

        assertEquals("One or more claim definitions not found", exception.getMessage());
        verify(claimSetRepository).getReferenceById(claimSetId);
        verify(claimDefinitionRepository).countByIdIn(Set.of(claimDefinitionId1, claimDefinitionId2));
        verifyNoMoreInteractions(claimDefinitionRepository);
        verifyNoInteractions(mappingService);
    }

    @Test
    void shouldHandleDuplicateClaimDefinitionIds() {
        // Given
        PatchClaimDefinitionsOnClaimSetCommand command = new PatchClaimDefinitionsOnClaimSetCommand(
                claimSetId,
                List.of(
                        new PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand(claimDefinitionId1, 1, true),
                        new PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand(claimDefinitionId1, 2, false) // Same ID, different order and enforceUniqueness
                )
        );

        when(claimSetRepository.getReferenceById(claimSetId)).thenReturn(testClaimSet);
        when(claimDefinitionRepository.countByIdIn(Set.of(claimDefinitionId1))).thenReturn(1L);

        // When
        handler.patchClaimDefinitionsOnClaimSet(command);

        // Then
        verify(claimSetRepository).getReferenceById(claimSetId);
        verify(claimDefinitionRepository).countByIdIn(Set.of(claimDefinitionId1));
        // The batch operation will handle the duplicate IDs internally
        verify(mappingService).batchAddClaimDefinitionsToClaimSet(testClaimSet, Set.of(claimDefinitionId1), deduplicate(command.claimDefinitionsAssignment()));
    }

    // Helper methods
    private static List<PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand> deduplicate(List<PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand> assignments) {
        Map<UUID, PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand> deduped = new HashMap<>();
        for (PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand assignment : assignments) {
            deduped.put(assignment.claimDefinitionId(), assignment);
        }
        return new ArrayList<>(deduped.values());
    }
} 