package com.vusecurity.auth.authorization.api.controller;

import static com.vusecurity.auth.authorization.domain.PermissionNames.GROUP_CREATE;
import static com.vusecurity.auth.authorization.domain.PermissionNames.GROUP_DELETE;
import static com.vusecurity.auth.authorization.domain.PermissionNames.GROUP_READ;
import static com.vusecurity.auth.authorization.domain.PermissionNames.GROUP_UPDATE;

import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.vusecurity.auth.authorization.application.command.CreateGroupCommand;
import com.vusecurity.auth.authorization.application.command.DeleteGroupCommand;
import com.vusecurity.auth.authorization.application.command.UpdateGroupCommand;
import com.vusecurity.auth.authorization.application.command.UpdateGroupMembersCommand;
import com.vusecurity.auth.authorization.application.command.UpdateGroupRolesCommand;
import com.vusecurity.auth.authorization.application.handler.CreateGroupHandler;
import com.vusecurity.auth.authorization.application.handler.DeleteGroupHandler;
import com.vusecurity.auth.authorization.application.handler.UpdateGroupHandler;
import com.vusecurity.auth.authorization.application.handler.UpdateGroupMembersHandler;
import com.vusecurity.auth.authorization.application.handler.UpdateGroupRolesHandler;
import com.vusecurity.auth.authorization.application.query.GetGroupQuery;
import com.vusecurity.auth.authorization.application.query.GetGroupsPagedQuery;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import com.vusecurity.auth.authorization.mapper.GroupDtoMapper;
import com.vusecurity.auth.contracts.api.v1.dto.authorization.CreateGroupRequest;
import com.vusecurity.auth.contracts.api.v1.dto.authorization.GroupResponse;
import com.vusecurity.auth.contracts.api.v1.dto.authorization.UpdateGroupMembersRequest;
import com.vusecurity.auth.contracts.api.v1.dto.authorization.UpdateGroupRequest;
import com.vusecurity.auth.contracts.api.v1.dto.authorization.UpdateGroupRolesRequest;
import com.vusecurity.auth.shared.config.aspect.Oauth2Authorize;
import com.vusecurity.auth.shared.util.UrlParameterUtils;
import com.vusecurity.core.commons.models.PageableResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@SecurityRequirement(name = "oauth2 client credentials")
@Tag(name = "Groups", description = "Account group management operations")
public class GroupController {

        private final CreateGroupHandler createGroupHandler;
        private final UpdateGroupHandler updateGroupHandler;
        private final DeleteGroupHandler deleteGroupHandler;
        private final UpdateGroupMembersHandler updateGroupMembersHandler;
        private final UpdateGroupRolesHandler updateGroupRolesHandler;
        private final GetGroupQuery getGroupQuery;
        private final GroupDtoMapper groupDtoMapper;

        @Operation(summary = "Get all groups", description = "Retrieves a paginated list of account groups with optional filtering. Supports filtering by group name and description.")
        @ApiResponse(responseCode = "200", description = "Successful retrieval", content = @Content(schema = @Schema(implementation = PageableResponse.class)))
        @ApiResponse(responseCode = "400", description = "Invalid request parameters")
        @ApiResponse(responseCode = "401", description = "Authentication required")
        @ApiResponse(responseCode = "403", description = "Access denied")

        @GetMapping("${app.authorization.context-path}/groups")
        @Oauth2Authorize(permission = GROUP_READ)
        public PageableResponse<GroupResponse> get(@RequestParam(name = "page", defaultValue = "1") int page,
                        @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                        @RequestParam(name = "filter", required = false) String filter,
                        @RequestParam(name = "sortBy", required = false) String sortBy,
                        @RequestParam(name = "sortDirection", defaultValue = "asc") String sortDirection,
                        @RequestParam(name = "includeDetails", defaultValue = "false") boolean includeDetails) {

                // Decode URL-encoded parameter to handle special characters properly
                String decodedFilter = UrlParameterUtils.decodeAndSanitizeParameter(filter);

                GetGroupsPagedQuery query = GetGroupsPagedQuery.builder()
                                .page(page)
                                .pageSize(pageSize)
                                .filter(decodedFilter)
                                .sortBy(sortBy)
                                .sortDirection(sortDirection)
                                .build();

                Page<AccountGroupJpaEntity> groups = getGroupQuery.getAllGroups(query);

                return new PageableResponse<>(groups.getNumber() + 1, groups.getSize(), groups.getTotalElements(),
                                groups.get().map(group -> groupDtoMapper.toResponse(group, includeDetails)).toList());
        }

        @Operation(summary = "Create a new group", description = "Creates a new account group with the specified name, description, roles, and optionally member accounts. At least one role is required, but accounts are optional.")
        @ApiResponse(responseCode = "201", description = "Group created successfully", content = @Content(schema = @Schema(implementation = GroupResponse.class)))
        @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors")
        @ApiResponse(responseCode = "401", description = "Authentication required")
        @ApiResponse(responseCode = "403", description = "Access denied")
        @ApiResponse(responseCode = "409", description = "Group with same name already exists")

        @PostMapping("${app.authorization.context-path}/groups")
        @ResponseStatus(HttpStatus.CREATED)
        @Oauth2Authorize(permission = GROUP_CREATE)
        public GroupResponse create(@RequestBody @Valid CreateGroupRequest request) {

                CreateGroupCommand command = new CreateGroupCommand(
                                request.getName(),
                                request.getDescription(),
                                request.getRoleIds(),
                                request.getAccountIds(),
                                request.getOwnerAccountIds(),
                                request.getMetadata());

                AccountGroupJpaEntity group = createGroupHandler.createGroup(command);
                return groupDtoMapper.toResponse(group, true);
        }

        @Operation(summary = "Get group by ID", description = "Retrieves a specific account group by its unique identifier.")
        @ApiResponse(responseCode = "200", description = "Group found", content = @Content(schema = @Schema(implementation = GroupResponse.class)))
        @ApiResponse(responseCode = "404", description = "Group not found")
        @ApiResponse(responseCode = "401", description = "Authentication required")
        @ApiResponse(responseCode = "403", description = "Access denied")

        @GetMapping("${app.authorization.context-path}/groups/{id}")
        @Oauth2Authorize(permission = GROUP_READ)
        public GroupResponse get(@PathVariable(value = "id") UUID id,
                        @RequestParam(name = "includeDetails", defaultValue = "false") boolean includeDetails) {
                AccountGroupJpaEntity group = getGroupQuery.getGroupById(id);
                return groupDtoMapper.toResponse(group, includeDetails);
        }

        @Operation(summary = "Update a group", description = "Updates an existing account group with new information, roles, and optionally member accounts. This is a full update (PUT semantics). At least one role is required, but accounts are optional.")
        @ApiResponse(responseCode = "200", description = "Group updated successfully", content = @Content(schema = @Schema(implementation = GroupResponse.class)))
        @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors")
        @ApiResponse(responseCode = "401", description = "Authentication required")
        @ApiResponse(responseCode = "403", description = "Access denied")
        @ApiResponse(responseCode = "404", description = "Group not found")
        @ApiResponse(responseCode = "409", description = "Group name already exists")

        @PutMapping("${app.authorization.context-path}/groups/{id}")
        @Oauth2Authorize(permission = GROUP_UPDATE)
        public GroupResponse update(@PathVariable(value = "id") UUID id,
                        @RequestBody @Valid UpdateGroupRequest request) {

                UpdateGroupCommand command = new UpdateGroupCommand(
                                id,
                                request.getName(),
                                request.getDescription(),
                                request.getRoleIds(),
                                request.getAccountIds(),
                                request.getOwnerAccountIds(),
                                request.getMetadata());

                AccountGroupJpaEntity group = updateGroupHandler.updateGroup(command);
                return groupDtoMapper.toResponse(group, true);
        }

        @Operation(summary = "Delete a group", description = "Permanently deletes an account group and all its memberships.")
        @ApiResponse(responseCode = "204", description = "Group deleted successfully")
        @ApiResponse(responseCode = "401", description = "Authentication required")
        @ApiResponse(responseCode = "403", description = "Access denied")
        @ApiResponse(responseCode = "404", description = "Group not found")

        @DeleteMapping("${app.authorization.context-path}/groups/{id}")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        @Oauth2Authorize(permission = GROUP_DELETE)
        public void delete(@PathVariable(value = "id") UUID id) {

                DeleteGroupCommand command = new DeleteGroupCommand(id);
                deleteGroupHandler.deleteGroup(command);
        }

        @Operation(summary = "Partially update a group's members", description = "Adds or removes members from an existing account group.")
        @ApiResponse(responseCode = "200", description = "Group members updated successfully")
        @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors")
        @ApiResponse(responseCode = "401", description = "Authentication required")
        @ApiResponse(responseCode = "403", description = "Access denied")
        @ApiResponse(responseCode = "404", description = "Group not found")
        @PatchMapping("${app.authorization.context-path}/groups/{id}/members")
        @Oauth2Authorize(permission = GROUP_UPDATE)
        public void updateMembers(@PathVariable(value = "id") UUID id,
                        @RequestBody @Valid UpdateGroupMembersRequest request) {
                UpdateGroupMembersCommand command = new UpdateGroupMembersCommand(
                                id,
                                request.getAccountIds(),
                                request.getOwnerAccountIds());
                updateGroupMembersHandler.updateMembers(command);
        }

        @Operation(summary = "Partially update a group's roles", description = "Adds or removes roles from an existing account group.")
        @ApiResponse(responseCode = "200", description = "Group roles updated successfully")
        @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors")
        @ApiResponse(responseCode = "401", description = "Authentication required")
        @ApiResponse(responseCode = "403", description = "Access denied")
        @ApiResponse(responseCode = "404", description = "Group not found")
        @PatchMapping("${app.authorization.context-path}/groups/{id}/roles")
        @Oauth2Authorize(permission = GROUP_UPDATE)
        public void updateRoles(@PathVariable(value = "id") UUID id,
                        @RequestBody @Valid UpdateGroupRolesRequest request) {
                UpdateGroupRolesCommand command = new UpdateGroupRolesCommand(
                                id,
                                request.getRoleIds());
                updateGroupRolesHandler.updateRoles(command);
        }
}