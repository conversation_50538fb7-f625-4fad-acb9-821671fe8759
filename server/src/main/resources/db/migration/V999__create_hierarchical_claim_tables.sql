/*
 * Flyway Migration: V999__create_hierarchical_claim_tables.sql
 * Purpose: Create schema for hierarchical claims.
 * Note: This migration is idempotent.
 */

DECLARE @claimDefinitionDataTypeDefault varchar(max);
SELECT @claimDefinitionDataTypeDefault = cc.name FROM sys.check_constraints cc INNER JOIN sys.columns col ON cc.parent_object_id = col.object_id AND col.column_id = cc.parent_column_id
WHERE cc.parent_object_id = OBJECT_ID('claim_definition') AND col.name = 'data_type';

IF (@claimDefinitionDataTypeDefault IS NOT NULL)
BEGIN
    DECLARE @dropConstraintQuery nvarchar(max);
    SET @dropConstraintQuery = N'ALTER TABLE claim_definition DROP CONSTRAINT ' + QUOTENAME(@claimDefinitionDataTypeDefault);
	EXEC sp_executesql @dropConstraintQuery;
END

IF NOT EXISTS (
    SELECT 1 FROM sys.check_constraints cc INNER JOIN sys.columns col ON cc.parent_object_id = col.object_id AND col.column_id = cc.parent_column_id
	WHERE cc.parent_object_id = OBJECT_ID('claim_definition') AND col.name = 'data_type')
BEGIN
    ALTER TABLE claim_definition WITH CHECK ADD CONSTRAINT [CK__claim_def__data_type] CHECK (
		[data_type] = 'DATETIME' OR
		[data_type] = 'DATE' OR
		[data_type] = 'ARRAY' OR
		[data_type] = 'IMAGE' OR
		[data_type] = 'BOOL' OR
		[data_type] = 'NUMERIC' OR
		[data_type] = 'STRING' OR
		[data_type] = 'CLAIMSET'
	);
END
GO

-- Table to associate ClaimValues with ClaimSets
CREATE TABLE [claim_set_claim_values] (
    [claim_set_id] [uniqueidentifier] NOT NULL,
    [claim_value_id] [uniqueidentifier] NOT NULL,
    [created_at] [datetimeoffset](6) NOT NULL,
    PRIMARY KEY ([claim_set_id], [claim_value_id]),
    FOREIGN KEY ([claim_set_id]) REFERENCES [claim_set]([id]) ON DELETE CASCADE,
    FOREIGN KEY ([claim_value_id]) REFERENCES [claim_value]([id]) ON DELETE CASCADE
);

-- Index for reverse lookup
CREATE INDEX [idx_claim_set_claim_values_claim_value_id]
ON [claim_set_claim_values]([claim_value_id]);

-- Table for references between ClaimValues and ClaimSets (hierarchical navigation)
CREATE TABLE [claim_value_references] (
    [id] [uniqueidentifier] PRIMARY KEY,
    [source_claim_value_id] [uniqueidentifier] NOT NULL,
    [referenced_claim_set_id] [uniqueidentifier],
    [referenced_claim_definition_id] [uniqueidentifier],
    [sequence_order] INTEGER,
    [created_at] [datetimeoffset](6) NOT NULL,
    FOREIGN KEY ([source_claim_value_id]) REFERENCES [claim_value]([id]) ON DELETE CASCADE,
    FOREIGN KEY ([referenced_claim_set_id]) REFERENCES [claim_set]([id]) ON DELETE CASCADE,
    FOREIGN KEY ([referenced_claim_definition_id]) REFERENCES [claim_definition]([id]) ON DELETE CASCADE
);

-- Indexes to optimize hierarchical queries
CREATE INDEX idx_claim_value_references_source ON claim_value_references(source_claim_value_id);
CREATE INDEX idx_claim_value_references_claim_set ON claim_value_references(referenced_claim_set_id);
CREATE INDEX idx_claim_value_references_claim_def ON claim_value_references(referenced_claim_definition_id);