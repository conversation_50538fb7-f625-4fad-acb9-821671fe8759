package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.command.UpdateGroupRolesCommand;
import com.vusecurity.auth.authorization.application.exception.GroupNotFoundException;
import com.vusecurity.auth.authorization.application.exception.RoleNotFoundException;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.RoleRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountGroupRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class UpdateGroupRolesHandler {

    private final AccountGroupRepository accountGroupRepository;
    private final RoleRepository roleRepository;

    @Transactional
    public void updateRoles(UpdateGroupRolesCommand command) {
        command.validate();

        AccountGroupJpaEntity group = accountGroupRepository.findByIdWithRoles(command.groupId())
                .orElseThrow(() -> new GroupNotFoundException("Group not found with ID: " + command.groupId()));

        Set<UUID> desiredRoleIds = (command.roleIds() == null) ? Collections.emptySet() : new HashSet<>(command.roleIds());
        Set<RoleJpaEntity> currentRoles = new HashSet<>(group.getRoles());

        // Roles to remove
        currentRoles.removeIf(role -> !desiredRoleIds.contains(role.getId()));

        // Roles to add
        Set<UUID> currentRoleIds = currentRoles.stream()
                .map(RoleJpaEntity::getId)
                .collect(Collectors.toSet());

        Set<UUID> roleIdsToAdd = new HashSet<>(desiredRoleIds);
        roleIdsToAdd.removeAll(currentRoleIds);

        if (!roleIdsToAdd.isEmpty()) {
            List<RoleJpaEntity> rolesToAdd = roleRepository.findAllById(roleIdsToAdd);
            if (rolesToAdd.size() != roleIdsToAdd.size()) {
                throw new RoleNotFoundException("One or more roles to add were not found.");
            }
            currentRoles.addAll(rolesToAdd);
        }

        group.setRoles(currentRoles);
        accountGroupRepository.save(group);
    }
} 