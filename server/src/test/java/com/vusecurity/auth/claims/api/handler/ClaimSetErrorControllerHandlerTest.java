package com.vusecurity.auth.claims.api.handler;

import com.vusecurity.core.commons.ApiMessage;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.server.ResponseStatusException;

import java.util.NoSuchElementException;

import static org.junit.jupiter.api.Assertions.*;

class ClaimSetErrorControllerHandlerTest {

    private final ClaimSetErrorControllerHandler handler = new ClaimSetErrorControllerHandler();

    @Test
    void handleResponseStatusException_preserves_status_and_maps_code() {
        ResponseStatusException ex = new ResponseStatusException(HttpStatus.NOT_FOUND, "missing");
        ResponseEntity<ApiMessage> resp = handler.handleResponseStatusException(ex);
        assertEquals(HttpStatus.NOT_FOUND, resp.getStatusCode());
        assertEquals(5304, resp.getBody().getCode());
        assertEquals("missing", resp.getBody().getMessage());
    }

    @Test
    void handleNoSuchElementException_maps_404() {
        ResponseEntity<ApiMessage> resp = handler.handleNoSuchElementException(new NoSuchElementException("none"));
        assertEquals(404, resp.getStatusCode().value());
        assertEquals(5304, resp.getBody().getCode());
        assertEquals("none", resp.getBody().getMessage());
    }

    @Test
    void handleIllegalArgumentException_maps_400() {
        ResponseEntity<ApiMessage> resp = handler.handleIllegalArgumentException(new IllegalArgumentException("bad"));
        assertEquals(400, resp.getStatusCode().value());
        assertEquals(5300, resp.getBody().getCode());
        assertEquals("bad", resp.getBody().getMessage());
    }
}

