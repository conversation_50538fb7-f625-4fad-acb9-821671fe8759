package com.vusecurity.auth.identities.application.handler;

import com.vusecurity.auth.contracts.enums.IdentityLifecycleState;
import com.vusecurity.auth.identities.application.command.UpdateIdentityCommand;
import com.vusecurity.auth.identities.application.exception.IdentityNotFoundException;
import com.vusecurity.auth.identities.application.exception.IdentityValidationException;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@RequiredArgsConstructor
public class UpdateIdentityHandler {

    private static final Logger logger = LoggerFactory.getLogger(UpdateIdentityHandler.class);
    
    private final IdentityRepository identityRepository;

    @Transactional
    public void updateIdentity(UpdateIdentityCommand command) {
        logger.trace("Updating identity: {}", command.getIdentityId());

        UUID identityId;
        try {
            identityId = UUID.fromString(command.getIdentityId());
        } catch (IllegalArgumentException e) {
            throw new IdentityValidationException("Invalid identity ID format: " + command.getIdentityId());
        }
        IdentityJpaEntity identity = identityRepository.findByIdentityId(identityId)
                .orElseThrow(() -> new IdentityNotFoundException(command.getIdentityId()));

        // Update fields if provided
        if (command.getName() != null && !command.getName().trim().isEmpty()) {
            identity.setName(command.getName().trim());
        }

        if (command.getLifecycleState() != null && !command.getLifecycleState().trim().isEmpty()) {
            try {
                IdentityLifecycleState lifecycleState = IdentityLifecycleState.valueOf(command.getLifecycleState().toUpperCase());
                identity.setLifecycleState(lifecycleState);
            } catch (IllegalArgumentException e) {
                throw new IdentityValidationException("Invalid lifecycle state: " + command.getLifecycleState());
            }
        }

        if (command.getMetadata() != null) {
            identity.setMetadata(command.getMetadata());
        }

        // Validate before saving
        identity.validateForUpdate();

        identityRepository.save(identity);

        logger.debug("Identity updated successfully: {}", command.getIdentityId());
    }

}
