/*
 * Flyway Migration: V5__add_role_account.sql
 * Purpose: Assign admin role to existing admin account.
 * Idempotent: Uses IF NOT EXISTS checks for SQL Server syntax.
 */

INSERT INTO account_role (role_id, account_id)
SELECT '9FA7A7F6-B8D4-4DB4-A8C7-1642A80E863A', 'A5F5129E-3901-4F65-9C1C-90F176D7D60F'
WHERE NOT EXISTS (
    SELECT 1
    FROM account_role
    WHERE role_id = '9FA7A7F6-B8D4-4DB4-A8C7-1642A80E863A'
      AND account_id = 'A5F5129E-3901-4F65-9C1C-90F176D7D60F'
)
AND EXISTS (
    SELECT 1
    FROM account
    WHERE id = 'A5F5129E-3901-4F65-9C1C-90F176D7D60F'
);

IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE TABLE_NAME = 'scim_to_claim_mapping' AND CONSTRAINT_NAME = 'fk_account_business')
BEGIN
    ALTER TABLE [scim_to_claim_mapping] DROP CONSTRAINT [fk_account_business];
END

IF COL_LENGTH('scim_to_claim_mapping','description') IS NULL
BEGIN
	ALTER TABLE [scim_to_claim_mapping] ADD [description] [varchar](255) NULL;
END


GO

UPDATE [scim_to_claim_mapping] SET [description] = 'Extracts Username' WHERE [filter_mapping] = 'userName' and [description] IS NULL;
UPDATE [scim_to_claim_mapping] SET [description] = 'Extracts Name' WHERE [filter_mapping] = 'name.givenName'and [description] IS NULL;
UPDATE [scim_to_claim_mapping] SET [description] = 'Extracts primary email' WHERE [filter_mapping] = 'emails[primary eq true].value'and [description] IS NULL;
UPDATE [scim_to_claim_mapping] SET [description] = 'Extracts Surname' WHERE [filter_mapping] = 'name.familyName'and [description] IS NULL;
UPDATE [scim_to_claim_mapping] SET [description] = 'Extracts externalId' WHERE [filter_mapping] = 'externalId'and [description] IS NULL;