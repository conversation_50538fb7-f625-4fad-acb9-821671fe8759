package com.vusecurity.auth.identities.application.command;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * Command for creating a new identity.
 * Contains all the data needed to create an identity.
 */
@Data
@Builder
public class CreateIdentityCommand {
    private String name;
    private String identityType;
    private Map<String, Object> metadata;
    private String lifecycleState;
}
