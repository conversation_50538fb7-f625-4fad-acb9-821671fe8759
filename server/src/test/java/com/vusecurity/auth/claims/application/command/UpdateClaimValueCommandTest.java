package com.vusecurity.auth.claims.application.command;

import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class UpdateClaimValueCommandTest {

    @Test
    void shouldValidateSuccessfully_WhenIdIsProvided() {
        UpdateClaimValueCommand command = new UpdateClaimValueCommand(
                UUID.randomUUID(), // id
                UUID.randomUUID(), // claimDefinitionId
                UUID.randomUUID(), // identityId
                null,              // accountId
                "updated-value",
                true,              // isPrimary
                false,             // isComputed
                "USER_INPUT",
                Instant.now(),
                UUID.randomUUID()
        );

        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WhenOnlyIdIsProvided() {
        UpdateClaimValueCommand command = new UpdateClaimValueCommand(
                UUID.randomUUID(), // id
                null,              // claimDefinitionId
                null,              // identityId
                null,              // accountId
                null,              // value
                null,              // isPrimary
                null,              // isComputed
                null,              // source
                null,              // issuedAt
                null               // claimVerificationId
        );

        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldThrowException_WhenIdIsNull() {
        UpdateClaimValueCommand command = new UpdateClaimValueCommand(
                null,              // id is null
                UUID.randomUUID(), // claimDefinitionId
                UUID.randomUUID(), // identityId
                null,              // accountId
                "updated-value",
                true,              // isPrimary
                false,             // isComputed
                "USER_INPUT",
                Instant.now(),
                UUID.randomUUID()
        );

        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("id is required", exception.getMessage());
    }

    @Test
    void should_validate_against_regex_pattern() {
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "<EMAIL>",
                true,
                false,
                "USER_INPUT",
                Instant.now(),
                UUID.randomUUID()
        );

        String emailRegex = "^[\\w-.]+@([\\w-]+\\.)+[\\w-]{2,4}$";
        assertDoesNotThrow(() -> cmd.validateWithPattern(emailRegex));
    }

    @Test
    void should_fail_if_regex_does_not_match() {
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "not-an-email",
                true,
                false,
                "USER_INPUT",
                Instant.now(),
                UUID.randomUUID()
        );

        String emailRegex = "^[\\w-.]+@([\\w-]+\\.)+[\\w-]{2,4}$";
        assertThrows(IllegalArgumentException.class, () -> cmd.validateWithPattern(emailRegex));
    }

    @Test
    void should_validate_against_phone_number_pattern() {
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "******-123-4567",
                true,
                false,
                "USER_INPUT",
                Instant.now(),
                UUID.randomUUID()
        );

        String phoneRegex = "^\\+?[1-9]\\d?-?\\d{3}-\\d{3}-\\d{4}$";
        assertDoesNotThrow(() -> cmd.validateWithPattern(phoneRegex));
    }

    @Test
    void should_validate_against_employee_id_pattern() {
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "EMP-2023-001",
                true,
                false,
                "SYSTEM_GENERATED",
                Instant.now(),
                UUID.randomUUID()
        );

        String empIdRegex = "^EMP-\\d{4}-\\d{3}$";
        assertDoesNotThrow(() -> cmd.validateWithPattern(empIdRegex));
    }

    @Test
    void should_pass_validation_when_pattern_is_null() {
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "any-value",
                true,
                false,
                "USER_INPUT",
                Instant.now(),
                UUID.randomUUID()
        );

        assertDoesNotThrow(() -> cmd.validateWithPattern(null));
    }

    @Test
    void should_pass_validation_when_pattern_is_blank() {
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "any-value",
                true,
                false,
                "USER_INPUT",
                Instant.now(),
                UUID.randomUUID()
        );

        assertDoesNotThrow(() -> cmd.validateWithPattern(""));
        assertDoesNotThrow(() -> cmd.validateWithPattern("   "));
    }

    @Test
    void should_validate_complex_regex_patterns() {
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "<EMAIL>",
                true,
                false,
                "USER_INPUT",
                Instant.now(),
                UUID.randomUUID()
        );

        // Complex email regex
        String complexEmailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        assertDoesNotThrow(() -> cmd.validateWithPattern(complexEmailRegex));
    }

    @Test
    void should_fail_validation_with_descriptive_error_message() {
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "invalid-format",
                true,
                false,
                "USER_INPUT",
                Instant.now(),
                UUID.randomUUID()
        );

        String pattern = "^[0-9]+$";
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> cmd.validateWithPattern(pattern)
        );
        assertEquals("value does not match the required pattern", exception.getMessage());
    }

    @Test
    void should_handle_null_value_in_pattern_validation() {
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                null,              // null value
                true,
                false,
                "USER_INPUT",
                Instant.now(),
                UUID.randomUUID()
        );

        String pattern = "^[0-9]+$";
        // Should not throw because value is null (no validation needed)
        assertDoesNotThrow(() -> cmd.validateWithPattern(pattern));
    }

    @Test
    void shouldValidateSuccessfully_WithAccountId() {
        UpdateClaimValueCommand command = new UpdateClaimValueCommand(
                UUID.randomUUID(), // id
                UUID.randomUUID(), // claimDefinitionId
                null,              // identityId
                UUID.randomUUID(), // accountId
                "updated-value",
                true,              // isPrimary
                false,             // isComputed
                "USER_INPUT",
                Instant.now(),
                UUID.randomUUID()
        );

        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithBothIdentityAndAccountId() {
        UpdateClaimValueCommand command = new UpdateClaimValueCommand(
                UUID.randomUUID(), // id
                UUID.randomUUID(), // claimDefinitionId
                UUID.randomUUID(), // identityId
                UUID.randomUUID(), // accountId
                "updated-value",
                true,              // isPrimary
                false,             // isComputed
                "USER_INPUT",
                Instant.now(),
                UUID.randomUUID()
        );

        // Unlike CreateClaimValueCommand, UpdateClaimValueCommand allows both
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithDifferentBooleanCombinations() {
        boolean[] booleanValues = {true, false};

        for (Boolean isPrimary : new Boolean[]{true, false, null}) {
            for (Boolean isComputed : new Boolean[]{true, false, null}) {
                UpdateClaimValueCommand command = new UpdateClaimValueCommand(
                        UUID.randomUUID(), // id
                        UUID.randomUUID(), // claimDefinitionId
                        UUID.randomUUID(), // identityId
                        null,              // accountId
                        "test-value",
                        isPrimary,
                        isComputed,
                        "TEST_SOURCE",
                        Instant.now(),
                        UUID.randomUUID()
                );

                assertDoesNotThrow(command::validate,
                        String.format("Validation should pass for isPrimary=%s, isComputed=%s",
                                isPrimary, isComputed));
            }
        }
    }

    @Test
    void shouldValidateSuccessfully_WithDifferentValueTypes() {
        String[] testValues = {
                "simple-text",
                "<EMAIL>",
                "123456",
                "true",
                "2023-12-25",
                "Complex value with spaces and symbols!@#$%",
                "Multi\nline\nvalue",
                null // null value should be valid for updates
        };

        for (String value : testValues) {
            UpdateClaimValueCommand command = new UpdateClaimValueCommand(
                    UUID.randomUUID(), // id
                    UUID.randomUUID(), // claimDefinitionId
                    UUID.randomUUID(), // identityId
                    null,              // accountId
                    value,
                    true,              // isPrimary
                    false,             // isComputed
                    "USER_INPUT",
                    Instant.now(),
                    UUID.randomUUID()
            );

            assertDoesNotThrow(command::validate,
                    "Validation should pass for value: " + value);
        }
    }

    @Test
    void shouldValidateSuccessfully_WithDifferentSources() {
        String[] testSources = {
                "USER_INPUT",
                "SYSTEM_GENERATED",
                "EXTERNAL_API",
                "COMPUTED",
                null // source is optional
        };

        for (String source : testSources) {
            UpdateClaimValueCommand command = new UpdateClaimValueCommand(
                    UUID.randomUUID(), // id
                    UUID.randomUUID(), // claimDefinitionId
                    UUID.randomUUID(), // identityId
                    null,              // accountId
                    "test-value",
                    true,              // isPrimary
                    false,             // isComputed
                    source,
                    Instant.now(),
                    UUID.randomUUID()
            );

            assertDoesNotThrow(command::validate,
                    "Validation should pass for source: " + source);
        }
    }
}