package com.vusecurity.auth.authorization.application.command;

import com.vusecurity.auth.authorization.application.exception.InvalidGroupRequestException;

import java.util.List;
import java.util.UUID;

public record UpdateGroupRolesCommand(
        UUID groupId,
        List<UUID> roleIds
) {
    public void validate() {
        if (groupId == null) {
            throw new InvalidGroupRequestException("Group ID is required");
        }
    }
} 