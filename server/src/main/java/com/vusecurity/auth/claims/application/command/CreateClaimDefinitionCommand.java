package com.vusecurity.auth.claims.application.command;

import com.vusecurity.auth.contracts.enums.ClaimType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;

/**
 * Command for creating a new claim definition.
 * If no claimType is passed, USER_DEFINED is assumed.
 */
public record CreateClaimDefinitionCommand (
        String code,
        String name,
        String description,
        ClaimType claimType,
        DataTypeEnum dataType,
        String dataFormat
) {

    public CreateClaimDefinitionCommand (String code,
        String name,
        String description,
        DataTypeEnum dataType,
        String dataFormat) {
            this(code, name, description, ClaimType.USER_DEFINED, dataType, dataFormat);
    }
    
    /**
     * Validates the command parameters.
     * @throws IllegalArgumentException if validation fails
     */
    public void validate() {
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("Code cannot be null or empty");
        }
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Name cannot be null or empty");
        }
        if (dataType == null) {
            throw new IllegalArgumentException("Data dataFormat cannot be null");
        }
        if (code.length() > 255) {
            throw new IllegalArgumentException("Code must not exceed 255 characters");
        }
        if (name.length() > 255) {
            throw new IllegalArgumentException("Name must not exceed 255 characters");
        }
        if (description != null && description.length() > 1024) {
            throw new IllegalArgumentException("Description must not exceed 1024 characters");
        }
        if (dataFormat != null && dataFormat.length() > 1024) {
            throw new IllegalArgumentException("Format must not exceed 1024 characters");
        }
    }
}
