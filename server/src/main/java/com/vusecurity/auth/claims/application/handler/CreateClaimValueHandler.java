package com.vusecurity.auth.claims.application.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vusecurity.auth.claims.application.command.CreateClaimValueCommand;
import com.vusecurity.auth.claims.application.exception.ClaimDefinitionNotFoundException;
import com.vusecurity.auth.claims.application.exception.DuplicateClaimValueException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetDefinitionMappingJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetDefinitionMappingRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.identities.application.dto.AccountBusinessInfo;
import com.vusecurity.auth.identities.application.exception.AccountNotFoundException;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import com.vusecurity.auth.shared.util.DatabaseConstraintUtils;
import com.vusecurity.auth.shared.util.DataFormatUtils;
import com.vusecurity.auth.shared.util.ArrayParsingUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class CreateClaimValueHandler {

    private static final String UX_OWNER_CD_VAL = "ux_claim_value_owner_cd_val";

    private final ClaimValueRepository valueRepo;
    private final ClaimDefinitionRepository defRepo;
    private final ClaimSetDefinitionMappingRepository mappingRepo;
    private final AccountRepository accountRepo;
    private final ObjectMapper objectMapper;

    @Transactional
    public ClaimValueJpaEntity handle(CreateClaimValueCommand cmd) {
        cmd.validate();

        ClaimDefinitionJpaEntity definition = defRepo.findById(cmd.claimDefinitionId())
                .orElseThrow(() -> new ClaimDefinitionNotFoundException(cmd.claimDefinitionId()));

        /* ---------------- VALUE MATCH WITH PATTERN ------------------------ */

        validateWithDataFormat(cmd, definition);

        /* ---------------- VALUE MATCH WITH TYPE ------------------------ */
        DataTypeEnum type = definition.getDataType();

        if (!type.isValid(cmd.value())) {
            throw new IllegalArgumentException("Value does not match data type: " + type.name());
        }

        /* ---------------- VALUE NORMALIZED ------------------------ */
        String normalizedValue = type.normalize(cmd.value());

        // Manejar tipo CLAIMSET - crear referencias a ClaimValues del ClaimSet referenciado
        if (type == DataTypeEnum.CLAIMSET) {
            return handleClaimSetReference(cmd, definition, normalizedValue);
        }

        /* ---------------- HIERARCHICAL CLAIMSET HANDLING ------------------- */
        // Check if this is an ARRAY type with isAListOf pointing to a CLAIMSET type
        if (type == DataTypeEnum.ARRAY && definition.getIsAListOf() != null) {
            ClaimDefinitionJpaEntity referencedDefinition = definition.getIsAListOf();
            if (referencedDefinition.getDataType() == DataTypeEnum.CLAIMSET) {
                return handleHierarchicalClaimSetArray(cmd, definition, referencedDefinition, normalizedValue);
            }
        }

        /* ---------------- IDENTITY PATH ----------------------------------- */
        if (cmd.identityId() != null) {
            //Because we removed the unique constraint ux_claim_value_owner_cd_val now we must check for duplicated values.
            //? What about array definitions and values.
            //For now, give the error, needs more definition.
            if (valueRepo.existsByOwnerTypeAndOwnerIdAndClaimDefinitionId(
                        OwnerType.IDENTITY, cmd.identityId(), definition.getId())) {
                throw new DuplicateClaimValueException(
                    "Identity already has a value for claim " + definition.getCode());
            }
            return save(definition, OwnerType.IDENTITY, cmd.identityId(), cmd, normalizedValue);
        }

        /* ---------------- ACCOUNT PATH ------------------------------------- */
        UUID accountId = cmd.accountId();

        AccountBusinessInfo info = accountRepo.findInfoById(accountId)
                .orElseThrow(() -> new AccountNotFoundException(accountId));

        var mappings = mappingRepo.findApplicableMappings(
                        info.getBusinessId(),
                        info.getAccountType(),
                        definition.getId());
        
        if (mappings.isEmpty()) {
            throw new IllegalStateException(
                    "No applicable mapping for claim definition " + definition.getId());
        }

        // Filter mappings that don't apply to the claim Set where the claim Value is going to be saved.
        mappings = mappings.stream().filter(mapping -> mapping.getClaimSet().getId().equals(cmd.claimSetId())).toList();

        // Business Rule: Uniqueness is enforced if ANY mapping requires it
        boolean uniquenessRequired = mappings.stream().anyMatch(mapping ->
                Boolean.TRUE.equals(mapping.getClaimSet().getIsIdentifier()) ||
                        Boolean.TRUE.equals(mapping.getEnforceUniqueness()));

        /* -------- Rule: at most one value for this owner & CD ----------- */
        if (uniquenessRequired &&
                valueRepo.existsByOwnerTypeAndOwnerIdAndClaimDefinitionIdAndClaimSetClaimValue_ClaimSet_Id(
                        OwnerType.ACCOUNT, accountId, definition.getId(), cmd.claimSetId())) {
            throw new DuplicateClaimValueException(
                    "Account already has a value for claim " + definition.getCode());
        }

        /* -------- Rule: no duplicate across *other* accounts of same AT - */
        if (uniquenessRequired &&
                valueRepo.existsForAccountType(
                        info.getAccountType(),
                        definition.getId(),
                        cmd.convertValueToString().toLowerCase(),   // normalised for case-insensitivity
                        accountId                    // exclude the caller itself
                )) {
            throw new DuplicateClaimValueException(
                    "Value '" + cmd.convertValueToString() + "' is already used by another "
                            + info.getAccountType() + " account");
        }

        return save(definition, OwnerType.ACCOUNT, accountId, cmd, normalizedValue);
    }


    private ClaimValueJpaEntity save(ClaimDefinitionJpaEntity def,
                                     OwnerType ownerType,
                                     UUID ownerId,
                                     CreateClaimValueCommand cmd,
                                     String valueNormalized) {

        ClaimValueJpaEntity cv = new ClaimValueJpaEntity(def, ownerType, ownerId);
        cv.setValue(valueNormalized);
        cv.setPrimary(cmd.isPrimary());
        cv.setComputed(cmd.isComputed());
        cv.setSource(cmd.source());
        cv.setPrimaryIndex(cmd.primaryIndex());

        if (ownerType.equals(OwnerType.ACCOUNT)){
            Optional<ClaimSetDefinitionMappingJpaEntity> csdMapping = def.getClaimSetMappings().stream().filter(
                csm ->
                    csm.getClaimSet().getId().equals(cmd.claimSetId()) &&
                    csm.getClaimDefinition().getId().equals(cmd.claimDefinitionId())
                ).findFirst();
            if (csdMapping.isPresent()){
                ClaimSetClaimValueJpaEntity relation = new ClaimSetClaimValueJpaEntity();
                relation.setClaimSet(csdMapping.get().getClaimSet());
                relation.setClaimValue(cv);
                cv.setClaimSetClaimValue(relation);
            }
        }

        // Use utility to handle constraint violations
        return DatabaseConstraintUtils.executeWithConstraintHandling(
                () -> valueRepo.saveAndFlush(cv), // we are forcefully flushing to catch constraint violations immediately
                UX_OWNER_CD_VAL,
                () -> {
                    String owner = ownerType == OwnerType.ACCOUNT ? "account" : "identity";
                    return new DuplicateClaimValueException(
                            "The %s already has the value '%s' for claim '%s'"
                                    .formatted(owner, cmd.convertValueToString(), def.getCode()));
                }
        );
    }

    private void validateWithDataFormat(CreateClaimValueCommand cmd, ClaimDefinitionJpaEntity definition) {
        String dataFormat = definition.getDataFormat();
        if (dataFormat == null || dataFormat.isBlank()) {
            return; // No validation pattern specified
        }

        // For ARRAY types, check if dataFormat is a UUID reference to another ClaimDefinition
        if (definition.getDataType() == DataTypeEnum.ARRAY && DataFormatUtils.isUuidFormat(dataFormat)) {
            validateArrayWithUuidReference(cmd, dataFormat);
        } else {
            // For non-ARRAY types or when dataFormat is not UUID, use regex validation
            cmd.validateWithPattern(dataFormat);
        }
    }

    private void validateArrayWithUuidReference(CreateClaimValueCommand cmd, String referencedClaimDefinitionId) {
        try {
            UUID referencedId = UUID.fromString(referencedClaimDefinitionId.trim());
            ClaimDefinitionJpaEntity referencedDefinition = defRepo.findById(referencedId)
                    .orElseThrow(() -> new IllegalArgumentException(
                            "Referenced claim definition not found: " + referencedClaimDefinitionId));

            // Parse the array value and validate each element against the referenced definition's pattern
            String[] arrayElements = ArrayParsingUtils.parseArrayValue(cmd.convertValueToString());
            String referencedPattern = referencedDefinition.getDataFormat();

            for (String element : arrayElements) {
                if (referencedPattern != null && !referencedPattern.isBlank() && !element.trim().matches(referencedPattern)) {
                    throw new IllegalArgumentException(
                            "Array element '" + element + "' does not match the required pattern for " +
                            referencedDefinition.getCode());
                }

                // Also validate against the referenced definition's data type
                DataTypeEnum referencedType = referencedDefinition.getDataType();
                if (!referencedType.isValid(element.trim())) {
                    throw new IllegalArgumentException(
                            "Array element '" + element + "' does not match data type: " + referencedType.name());
                }
            }
        } catch (IllegalArgumentException e) {
            throw e;
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid UUID reference in dataFormat: " + referencedClaimDefinitionId, e);
        }
    }

    /**
     * Handles creation of hierarchical ClaimSet arrays where the array contains JSON objects
     * representing ClaimSet data that should be automatically parsed and stored as individual ClaimValues.
     */
    private ClaimValueJpaEntity handleHierarchicalClaimSetArray(CreateClaimValueCommand cmd,
                                                               ClaimDefinitionJpaEntity arrayDefinition,
                                                               ClaimDefinitionJpaEntity claimSetDefinition,
                                                               String normalizedValue) {
        try {
            // Parse the array value to get individual ClaimSet objects
            String[] arrayElements = ArrayParsingUtils.parseArrayValue(normalizedValue);

            // Create the main array ClaimValue first
            ClaimValueJpaEntity arrayClaimValue = createMainArrayClaimValue(cmd, arrayDefinition, normalizedValue);

            // Process each array element as a ClaimSet object
            for (String element : arrayElements) {
                processClaimSetElement(element, cmd);
            }

            return arrayClaimValue;

        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to process hierarchical ClaimSet array: " + e.getMessage(), e);
        }
    }

    /**
     * Creates the main array ClaimValue that holds the array structure.
     */
    private ClaimValueJpaEntity createMainArrayClaimValue(CreateClaimValueCommand cmd,
                                                         ClaimDefinitionJpaEntity definition,
                                                         String normalizedValue) {
        // Determine owner type and ID
        OwnerType ownerType = cmd.accountId() != null ? OwnerType.ACCOUNT : OwnerType.IDENTITY;
        UUID ownerId = cmd.accountId() != null ? cmd.accountId() : cmd.identityId();

        return save(definition, ownerType, ownerId, cmd, normalizedValue);
    }

    /**
     * Processes a single ClaimSet element from the array, parsing its JSON structure
     * and creating individual ClaimValues for each property.
     */
    private void processClaimSetElement(String claimSetJson, CreateClaimValueCommand originalCmd) {
        try {
            JsonNode claimSetNode = objectMapper.readTree(claimSetJson);

            if (!claimSetNode.isObject()) {
                throw new IllegalArgumentException("ClaimSet element must be a JSON object");
            }

            // Extract claimSetId if present, otherwise skip this element
            UUID claimSetId = extractClaimSetId(claimSetNode, originalCmd);
            if (claimSetId == null) {
                return; // Skip elements without valid claimSetId
            }

            // Process each property in the ClaimSet object
            claimSetNode.properties().forEach(entry -> {
                String propertyName = entry.getKey();
                JsonNode propertyValue = entry.getValue();

                // Skip the claimSetId property as it's metadata
                if (!"claimSetId".equals(propertyName)) {
                    createClaimValueForProperty(propertyName, propertyValue, claimSetId, originalCmd);
                }
            });

        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("Invalid JSON in ClaimSet element: " + claimSetJson, e);
        }
    }

    /**
     * Extracts the claimSetId from a ClaimSet JSON object.
     * If no claimSetId is provided, tries to find an existing ClaimSet based on the data.
     */
    private UUID extractClaimSetId(JsonNode claimSetNode, CreateClaimValueCommand originalCmd) {
        // First, try to get explicit claimSetId
        JsonNode claimSetIdNode = claimSetNode.get("claimSetId");
        if (claimSetIdNode != null && !claimSetIdNode.isNull()) {
            try {
                return UUID.fromString(claimSetIdNode.asText());
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("Invalid claimSetId format: " + claimSetIdNode.asText());
            }
        }

        // If no explicit claimSetId, use the one from the original command
        return originalCmd.claimSetId();
    }

    /**
     * Creates a ClaimValue for a specific property within a ClaimSet.
     */
    private void createClaimValueForProperty(String propertyName, JsonNode propertyValue,
                                           UUID claimSetId, CreateClaimValueCommand originalCmd) {
        try {
            // Find the ClaimDefinition for this property name
            Optional<ClaimDefinitionJpaEntity> propertyDefinition = findClaimDefinitionByCode(propertyName);
            if (propertyDefinition.isEmpty()) {
                // Skip properties that don't have corresponding ClaimDefinitions
                return;
            }

            // Create ClaimValue for this property
            CreateClaimValueCommand propertyCmd = new CreateClaimValueCommand(
                claimSetId,
                propertyDefinition.get().getId(),
                originalCmd.identityId(),
                originalCmd.accountId(),
                propertyValue.asText(),
                false, // Not primary by default
                false, // Not computed
                originalCmd.source(),
                null   // primaryIndex
            );

            // Recursively handle the property (this will go through normal validation)
            handle(propertyCmd);

        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to create ClaimValue for property '" + propertyName + "': " + e.getMessage(), e);
        }
    }

    /**
     * Finds a ClaimDefinition by its code.
     */
    private Optional<ClaimDefinitionJpaEntity> findClaimDefinitionByCode(String code) {
        return defRepo.findByCode(code);
    }

    private ClaimValueJpaEntity handleClaimSetReference(CreateClaimValueCommand cmd,
                                                       ClaimDefinitionJpaEntity definition,
                                                       String normalizedValue) {
            return handleClaimSetByJson(cmd, definition, normalizedValue);
    }

    private ClaimValueJpaEntity handleClaimSetByJson(CreateClaimValueCommand cmd,
                                                   ClaimDefinitionJpaEntity definition,
                                                   String jsonValue) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode claimSetNode = mapper.readTree(jsonValue);

            // Crear el ClaimValue principal con el JSON completo
            OwnerType ownerType = cmd.accountId() != null ? OwnerType.ACCOUNT : OwnerType.IDENTITY;
            UUID ownerId = cmd.accountId() != null ? cmd.accountId() : cmd.identityId();

            ClaimValueJpaEntity mainClaimValue = save(definition, ownerType, ownerId, cmd, jsonValue);

            // Obtener el ClaimSet asociado a esta ClaimDefinition para saber qué ClaimDefinitions crear
            UUID claimSetId = cmd.claimSetId();
            List<ClaimDefinitionJpaEntity> claimSetDefinitions =
                    mappingRepo.findByClaimSetId(claimSetId).stream()
                        .map(ClaimSetDefinitionMappingJpaEntity::getClaimDefinition)
                        .toList();

            // Crear ClaimValues individuales para cada propiedad en el JSON
            claimSetNode.properties().forEach(entry -> {
                String propertyName = entry.getKey();
                String propertyValue = entry.getValue().asText();

                // Buscar la ClaimDefinition correspondiente
                Optional<ClaimDefinitionJpaEntity> propertyDefinition = defRepo.findById(UUID.fromString(propertyName));

                if (propertyDefinition.isPresent()) {
                    createClaimValueForProperty(propertyDefinition.get(), propertyValue, cmd, mainClaimValue.getId());
                } else {
                    throw new IllegalArgumentException("Property '" + propertyName + "' not found in ClaimSet definition");
                }
            });

            return mainClaimValue;

        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("Invalid JSON for ClaimSet: " + jsonValue, e);
        }
    }

    private void createClaimValueForProperty(ClaimDefinitionJpaEntity propertyDefinition,
                                           String propertyValue,
                                           CreateClaimValueCommand originalCmd,
                                           UUID parentClaimValueId) {
        // Validar el valor según el tipo de la ClaimDefinition
        DataTypeEnum propertyType = propertyDefinition.getDataType();

        if (!propertyType.isValid(propertyValue)) {
            throw new IllegalArgumentException(
                String.format("Value '%s' is not valid for property '%s' of type %s",
                             propertyValue, propertyDefinition.getCode(), propertyType.name()));
        }

        String normalizedPropertyValue = propertyType.normalize(propertyValue);

        CreateClaimValueCommand propertyCmd = new CreateClaimValueCommand(
            originalCmd.claimSetId(),
            propertyDefinition.getId(),
            originalCmd.identityId(),
            originalCmd.accountId(),
            normalizedPropertyValue,
            false, // Las propiedades no son primary por defecto
            false,
            originalCmd.source(),
            null
        );

        // Crear el ClaimValue para la propiedad
        OwnerType ownerType = originalCmd.accountId() != null ? OwnerType.ACCOUNT : OwnerType.IDENTITY;
        UUID ownerId = originalCmd.accountId() != null ? originalCmd.accountId() : originalCmd.identityId();

        ClaimValueJpaEntity propertyClaimValue = save(propertyDefinition, ownerType, ownerId, propertyCmd, normalizedPropertyValue);

    }

    private ClaimValueJpaEntity handleClaimSetType(CreateClaimValueCommand cmd,
                                                  ClaimDefinitionJpaEntity definition) {
        try {
            // Parsear el JSON del ClaimSet
            ObjectMapper mapper = new ObjectMapper();
            JsonNode claimSetNode = mapper.readTree(cmd.convertValueToString());

            // Crear el ClaimValue principal para el CLAIMSET
            ClaimValueJpaEntity mainClaimValue = createMainClaimSetValue(cmd, definition);

            // Crear ClaimValues individuales para cada propiedad
            claimSetNode.fields().forEachRemaining(entry -> {
                String propertyName = entry.getKey();
                String propertyValue = entry.getValue().asText();

                createClaimValueForClaimSetProperty(propertyName, propertyValue,
                                                  mainClaimValue.getId(), cmd);
            });

            return mainClaimValue;

        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("Invalid JSON for ClaimSet: " + cmd.convertValueToString(), e);
        }
    }

    private ClaimValueJpaEntity createMainClaimSetValue(CreateClaimValueCommand cmd,
                                                       ClaimDefinitionJpaEntity definition) {
        OwnerType ownerType = cmd.accountId() != null ? OwnerType.ACCOUNT : OwnerType.IDENTITY;
        UUID ownerId = cmd.accountId() != null ? cmd.accountId() : cmd.identityId();

        return save(definition, ownerType, ownerId, cmd, cmd.convertValueToString());
    }

    private void createClaimValueForClaimSetProperty(String propertyName, String propertyValue,
                                                   UUID parentClaimValueId, CreateClaimValueCommand originalCmd) {
        // Buscar la ClaimDefinition para esta propiedad
        Optional<ClaimDefinitionJpaEntity> propertyDefinition =
            defRepo.findByCode(propertyName);

        if (propertyDefinition.isEmpty()) {
            throw new IllegalArgumentException("Property '" + propertyName + "' not found in ClaimSet definition");
        }

        // Crear ClaimValue para la propiedad
        CreateClaimValueCommand propertyCmd = new CreateClaimValueCommand(
            originalCmd.claimSetId(),
            propertyDefinition.get().getId(),
            originalCmd.identityId(),
            originalCmd.accountId(),
            propertyValue,
            false, // Las propiedades no son primary por defecto
            false,
            originalCmd.source(),
            null
        );

        // Recursivamente crear el ClaimValue
        handle(propertyCmd);
    }

}
