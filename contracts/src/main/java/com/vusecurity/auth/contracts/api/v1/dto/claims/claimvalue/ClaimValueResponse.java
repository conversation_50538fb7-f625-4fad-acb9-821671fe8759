package com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import com.vusecurity.auth.contracts.enums.OwnerType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Objects;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "Claim value information response")
public class ClaimValueResponse extends BaseResponse {
    @Schema(description = "Unique identifier of the claim value",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    @JsonInclude(Include.NON_NULL)
    @Schema(description = "Unique identifier of the claim set this value belongs to",
            example = "********-51a2-43d1-9c4f-abc9987fcdeb")
    private UUID claimSetId;

    @Schema(description = "Unique identifier of the claim definition this value belongs to",
            example = "987fcdeb-51a2-43d1-9c4f-********9abc")
    private UUID claimDefinitionId;

    @Schema(description = "Type of owner for this claim value",
            example = "IDENTITY")
    private OwnerType ownerType;

    @Schema(description = "Unique identifier of the owner (identity or account) this claim value belongs to",
            example = "456e7890-a12b-34c5-d678-901234567def")
    private UUID ownerId;

    @Schema(description = "The actual claim value. Can be a string for simple values or an array for ARRAY type claims.",
            example = "<EMAIL>",
            oneOf = {String.class, Object[].class})
    private Object value;

    @Schema(description = "Whether this is the primary value for this claim type",
            example = "true")
    private boolean isPrimary;

    @Schema(description = "Whether this value is computed automatically",
            example = "false")
    private boolean isComputed;

    @Schema(description = "Source of the claim value",
            example = "USER_INPUT")
    private String source;

    @Schema(description = "Index indicating the primary order of the claim value",
            example = "1")
    private Integer primaryIndex;

    @Schema(description = "Timestamp when the claim was issued",
            example = "2024-01-15T10:30:00Z")
    private String issuedAt;

    @Schema(description = "Unique identifier of the claim verification",
            example = "aaa1bbbb-c22d-33e4-f555-************")
    private UUID claimVerificationId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        ClaimValueResponse that = (ClaimValueResponse) o;
        return isPrimary == that.isPrimary &&
               isComputed == that.isComputed &&
               Objects.equals(id, that.id) &&
               Objects.equals(claimSetId, that.claimSetId) &&
               Objects.equals(claimDefinitionId, that.claimDefinitionId) &&
               ownerType == that.ownerType &&
               Objects.equals(ownerId, that.ownerId) &&
               Objects.equals(value, that.value) &&
               Objects.equals(source, that.source) &&
               Objects.equals(primaryIndex, that.primaryIndex) &&
               Objects.equals(issuedAt, that.issuedAt) &&
               Objects.equals(claimVerificationId, that.claimVerificationId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), id, claimSetId, claimDefinitionId, ownerType, ownerId, value, isPrimary, isComputed, source, primaryIndex, issuedAt, claimVerificationId);
    }
}
