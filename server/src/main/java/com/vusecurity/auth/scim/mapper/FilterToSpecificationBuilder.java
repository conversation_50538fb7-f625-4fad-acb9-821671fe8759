package com.vusecurity.auth.scim.mapper;

import org.apache.directory.scim.spec.filter.*;
import org.springframework.data.jpa.domain.Specification;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;

import jakarta.persistence.criteria.*;
import java.util.*;

public class FilterToSpecificationBuilder<T> {

    private final Map<String, List<String>> scimToJpaFieldMap;
    private final Map<String, ColumnConstraint> scimToConstraintMap;
    private final Map<String, FieldSpecificConstraint> scimToFieldSpecificConstraints;

    /**
     * Represents an additional constraint for a SCIM field mapping
     */
    public static class ColumnConstraint {
        private final String columnName;
        private final Object requiredValue;

        public ColumnConstraint(String columnName, Object requiredValue) {
            this.columnName = columnName;
            this.requiredValue = requiredValue;
        }

        public String getColumnName() { return columnName; }
        public Object getRequiredValue() { return requiredValue; }
    }

    /**
     * Represents field-specific constraints where each JPA field can have its own constraint
     */
    public static class FieldSpecificConstraint {
        private final Map<String, ColumnConstraint> fieldToConstraintMap;

        public FieldSpecificConstraint(Map<String, ColumnConstraint> fieldToConstraintMap) {
            this.fieldToConstraintMap = fieldToConstraintMap;
        }

        public ColumnConstraint getConstraintForField(String fieldName) {
            return fieldToConstraintMap.get(fieldName);
        }

        public Set<String> getConstrainedFields() {
            return fieldToConstraintMap.keySet();
        }

        public Map<String, ColumnConstraint> getFieldToConstraintMap(){
            return this.fieldToConstraintMap;
        }
    }

    public FilterToSpecificationBuilder(Map<String, List<String>> scimToJpaFieldMap, 
                                              Map<String, ColumnConstraint> scimToConstraintMap,
                                              Map<String, FieldSpecificConstraint> scimToFieldSpecificConstraints) {
        this.scimToJpaFieldMap = scimToJpaFieldMap;
        this.scimToConstraintMap = scimToConstraintMap != null ? scimToConstraintMap : new HashMap<>();
        this.scimToFieldSpecificConstraints = scimToFieldSpecificConstraints != null ? scimToFieldSpecificConstraints : new HashMap<>();
    }

    public FilterToSpecificationBuilder(Map<String, List<String>> scimToJpaFieldMap, 
                                              Map<String, ColumnConstraint> scimToConstraintMap) {
        this(scimToJpaFieldMap, scimToConstraintMap, null);
    }

    public FilterToSpecificationBuilder(Map<String, List<String>> scimToJpaFieldMap) {
        this(scimToJpaFieldMap, null, null);
    }

    public static <T> FilterToSpecificationBuilder<T> withPrefixMappings(Map<String, List<String>> multiFieldMappings) {
        return new FilterToSpecificationBuilder<>(multiFieldMappings);
    }

    public Specification<T> build(Filter filter) {
        return toSpecification(filter.getExpression());
    }

    private Specification<T> toSpecification(FilterExpression filterExpression) {
        return switch (filterExpression) {
            case LogicalExpression logical -> handleLogical(logical);
            case AttributeComparisonExpression comparison -> handleComparison(comparison, null);
            case ValuePathExpression valuePath -> handleValuePath(valuePath);
            default -> throw new UnsupportedOperationException("Unsupported filter type: " + filterExpression.getClass().getSimpleName());
        };
    }

    private Specification<T> handleLogical(LogicalExpression logical) {
        Specification<T> leftSpec = toSpecification(logical.getLeft());
        Specification<T> rightSpec = toSpecification(logical.getRight());

        return switch (logical.getOperator()) {
            case AND -> leftSpec.and(rightSpec);
            case OR -> leftSpec.or(rightSpec);
        };
    }

    private Specification<T> handleValuePath(ValuePathExpression valuePath) {
        String base = valuePath.getAttributePath().toString();
        FilterExpression subFilter = valuePath.getAttributeExpression();

        if (!(subFilter instanceof AttributeComparisonExpression comparison)) {
            throw new UnsupportedOperationException("Only AttributeComparisonExpression is supported inside ValuePath.");
        }

        return (root, query, cb) -> {
            Join<Object, Object> join = root.join(base);
            return handleComparison(comparison, join).toPredicate(root, query, cb);
        };
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    private Specification<T> handleComparison(AttributeComparisonExpression comp, From<?, ?> overrideRoot) {
        return (root, query, cb) -> {
            From<?, ?> base = (overrideRoot != null) ? overrideRoot : root;

            String scimPath = comp.getAttributePath().toString();
            List<String> jpaFields = getJpaFields(scimPath);
            Object value = comp.getCompareValue();

            //Hack for now
            if (scimPath.equalsIgnoreCase("active")){
                value = Boolean.TRUE.equals(value) ? "ACTIVE" : "DEACTIVATED";
            }

            List<Predicate> fieldPredicates = new ArrayList<>();
            
            for (String jpaField : jpaFields) {
                try {
                    // This is where we handle the prefix logic
                    PathInfo pathInfo = resolvePathWithPrefix(base, jpaField, query);
                    Path<?> path = pathInfo.path;
                    
                    Predicate mainPredicate = createPredicate(cb, path, comp.getOperation(), value);
                    
                    // Handle field-specific constraints
                    FieldSpecificConstraint fieldSpecificConstraint = scimToFieldSpecificConstraints.get(scimPath);
                    if (fieldSpecificConstraint != null) {
                        ColumnConstraint constraint = fieldSpecificConstraint.getConstraintForField(jpaField);
                        if (constraint != null) {
                            try {
                                PathInfo constraintPathInfo = resolvePathWithPrefix(pathInfo.root, constraint.getColumnName(), query);
                                Predicate constraintPredicate = cb.equal(constraintPathInfo.path, constraint.getRequiredValue());
                                fieldPredicates.add(cb.and(constraintPredicate, mainPredicate));
                            } catch (Exception e) {
                                throw new RuntimeException("Could not resolve field-specific constraint path '" + 
                                    constraint.getColumnName() + "' for field '" + jpaField + "': " + e.getMessage());
                            }
                        } else {
                            fieldPredicates.add(mainPredicate);
                        }
                    } else {
                        fieldPredicates.add(mainPredicate);
                    }
                } catch (Exception e) {
                    System.err.println("Warning: Could not resolve path '" + jpaField + "': " + e.getMessage());
                }
            }

            if (fieldPredicates.isEmpty()) {
                throw new IllegalArgumentException("No valid JPA fields found for SCIM path: " + scimPath);
            }

            Predicate mainCondition = fieldPredicates.size() == 1 ? 
                fieldPredicates.get(0) : 
                cb.or(fieldPredicates.toArray(new Predicate[0]));

            // Handle global constraints
            ColumnConstraint globalConstraint = scimToConstraintMap.get(scimPath);
            if (globalConstraint != null && scimToFieldSpecificConstraints.get(scimPath) == null) {
                try {
                    PathInfo constraintPathInfo = resolvePathWithPrefix(base, globalConstraint.getColumnName(), query);
                    Predicate constraintPredicate = cb.equal(constraintPathInfo.path, globalConstraint.getRequiredValue());
                    return cb.and(constraintPredicate, mainCondition);
                } catch (Exception e) {
                    System.err.println("Warning: Could not resolve global constraint path '" + 
                        globalConstraint.getColumnName() + "': " + e.getMessage());
                    return mainCondition;
                }
            }

            return mainCondition;
        };
    }

    /**
     * Helper class to hold path resolution results
     */
    private static class PathInfo {
        final Path<?> path;
        final From<?, ?> root;
        
        PathInfo(Path<?> path, From<?, ?> root) {
            this.path = path;
            this.root = root;
        }
    }

    /**
     * Resolve path with prefix support - this is the key method that handles entity prefixes
     */
    private PathInfo resolvePathWithPrefix(From<?, ?> defaultRoot, String jpaPath, CriteriaQuery<?> query) {
        // Check for entity prefix
        if (jpaPath.contains(".") && ( jpaPath.startsWith("account.") || jpaPath.startsWith("claim."))) {
            String[] parts = jpaPath.split("\\.", 2); // Split into prefix and actual path
            String entityPrefix = parts[0];
            String actualPath = parts[1];
            
            // Find the appropriate root based on prefix
            From<?, ?> targetRoot = findRootByPrefix(entityPrefix, query, defaultRoot);
            Path<?> path = resolvePath(targetRoot, actualPath);
            return new PathInfo(path, targetRoot);
        } else {
            // No prefix, use default root
            Path<?> path = resolvePath(defaultRoot, jpaPath);
            return new PathInfo(path, defaultRoot);
        }
    }

    /**
     * Find the appropriate root based on entity prefix
     */
    private From<?, ?> findRootByPrefix(String entityPrefix, CriteriaQuery<?> query, From<?, ?> defaultRoot) {
        Set<Root<?>> roots = query.getRoots();
        
        switch (entityPrefix.toLowerCase()) {      
            case "account":
                // Find EntityA root
                for (Root<?> root : roots) {
                    // You might need to adjust this based on your actual entity class names
                    if (root.getJavaType().getSimpleName().equals(AccountJpaEntity.class.getSimpleName())) {
                        return root;
                    }
                }
                return defaultRoot; // Fallback to default root
            case "claim":
                // Find claim root
                for (Root<?> root : roots) {
                    // You might need to adjust this based on your actual entity class names
                    if (root.getJavaType().getSimpleName().equals(ClaimValueJpaEntity.class.getSimpleName())) {
                        return root;
                    }
                }
                throw new IllegalArgumentException("claim root not found in query. Make sure claim is joined in your business specification.");
                
            default:
                return defaultRoot;
        }
    }

    private List<String> getJpaFields(String scimPath) {
        List<String> fields = scimToJpaFieldMap.get(scimPath);
        if (fields == null || fields.isEmpty()) {
            return Arrays.asList(scimPath);
        }
        return fields;
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    private Predicate createPredicate(CriteriaBuilder cb, Path<?> path, CompareOperator operation, Object value) {
        return switch (operation) {
            case CompareOperator.EQ -> cb.equal(path, value);
            case CompareOperator.NE -> cb.notEqual(path, value);
            case CompareOperator.CO -> cb.like(cb.lower(path.as(String.class)), "%" + value.toString().toLowerCase() + "%");
            case CompareOperator.SW -> cb.like(cb.lower(path.as(String.class)), value.toString().toLowerCase() + "%");
            case CompareOperator.EW -> cb.like(cb.lower(path.as(String.class)), "%" + value.toString().toLowerCase());
            case CompareOperator.GT -> cb.greaterThan(path.as(Comparable.class), (Comparable) value);
            case CompareOperator.GE -> cb.greaterThanOrEqualTo(path.as(Comparable.class), (Comparable) value);
            case CompareOperator.LT -> cb.lessThan(path.as(Comparable.class), (Comparable) value);
            case CompareOperator.LE -> cb.lessThanOrEqualTo(path.as(Comparable.class), (Comparable) value);
            case CompareOperator.PR -> cb.isNotNull(path);
        };
    }

    private Path<?> resolvePath(From<?, ?> root, String jpaPath) {
        String[] parts = jpaPath.split("\\.");
        Path<?> path = root.get(parts[0]);

        for (int i = 1; i < parts.length; i++) {
            path = path.get(parts[i]);
        }

        return path;
    }
}