package com.vusecurity.auth.claims.application.command;

import java.util.List;
import java.util.UUID;

public record RemoveClaimSetsFromClaimDefinitionCommand(
        UUID claimDefinitionId,
        List<UUID> claimSetIds) {

    public void validate() {
        if (claimDefinitionId == null) {
            throw new IllegalArgumentException("claimDefinitionIds is required");
        }
        if (claimSetIds == null) {
            throw new IllegalArgumentException("claimSetIds is required");
        }
        if (claimSetIds.isEmpty()) {
            throw new IllegalArgumentException("At least one claim set ID must be provided");
        }
        for (UUID id : claimSetIds) {
            if (id == null) {
                throw new IllegalArgumentException("claimSetIds cannot contain null values");
            }
        }
    }
}