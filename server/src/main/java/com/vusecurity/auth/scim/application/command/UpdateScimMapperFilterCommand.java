package com.vusecurity.auth.scim.application.command;

import com.vusecurity.auth.contracts.api.v1.dto.scimConfig.ScimFilterMapperRequest;
import com.vusecurity.auth.scim.application.exception.InvalidScimMapperRequest;

import java.util.*;

public record UpdateScimMapperFilterCommand(
        UUID filterId,
        ScimFilterMapperRequest definition
) {
    
    public void validate() {

        if (filterId == null) {
            throw new InvalidScimMapperRequest("filterId is required.");
        }

        if (definition == null) {
            throw new InvalidScimMapperRequest("filter definition object cannot be null.");
        }

        if (definition.getClaimDefinitionId() == null) {
            throw new InvalidScimMapperRequest("claimDefinitionCode is required.");
        }

        if (definition.getFilter() == null || definition.getFilter().trim().isEmpty()) {
            throw new InvalidScimMapperRequest("filter is required.");
        }

        if (definition.getClaimSetIdOrigin() == null) {
            throw new InvalidScimMapperRequest("claimSetIdOrigin is required.");
        }
    }
} 