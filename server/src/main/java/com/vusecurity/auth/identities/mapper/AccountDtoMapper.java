package com.vusecurity.auth.identities.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vusecurity.auth.contracts.api.v1.dto.identities.AccountResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import org.springframework.stereotype.Component;

/**
 * Manual mapper for converting between Account DTOs and AccountJpaEntity.
 * Maps DTOs directly to entities following the "Variant A - Collapse Domain & Entity" pattern.
 */
@Component
public class AccountDtoMapper {

    /**
     * Convert AccountJpaEntity to AccountResponse DTO.
     * @param entity the account entity
     * @return the response DTO
     */
    public AccountResponse toResponse(AccountJpaEntity entity) {
        if (entity == null) {
            return null;
        }

        AccountResponse response = new AccountResponse();
        response.setId(entity.getId());
        response.setBusinessId(entity.getBusinessId());
        response.setBusinessName(entity.getBusiness().getName());
        response.setIdentityId(entity.getIdentityId());
        response.setIdentityProviderId(entity.getIdentityProviderId());
        response.setAccountType(entity.getAccountType());
        response.setLifecycleState(entity.getLifecycleState());
        response.setMetadata(entity.getMetadata());
        response.setMergePrimary(entity.getMergePrimary());
        response.setMergeSecondary(entity.getMergeSecondary());

        // Set audit information
        response.setAudit(new BaseResponse.AuditInfo()
                .setCreatedAt(entity.getCreatedAt())
                .setUpdatedAt(entity.getUpdatedAt())
                .setCreatedBy(entity.getCreatedBy())
                .setUpdatedBy(entity.getUpdatedBy()));

        return response;
    }
}
