package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.DeleteClaimSetCommand;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class DeleteClaimSetHandler {

    private static final Logger logger = LoggerFactory.getLogger(DeleteClaimSetHandler.class);
    
    private final ClaimSetRepository claimSetRepository;

    @Transactional
    public void deleteClaimSet(DeleteClaimSetCommand cmd) {
        logger.debug("Deleting claim set with command: {}", cmd);
        
        cmd.validate();

        if (!claimSetRepository.existsById(cmd.id())) {
            throw new IllegalArgumentException("Claim set not found with ID: " + cmd.id());
        }

        claimSetRepository.deleteById(cmd.id());
        logger.debug("Claim set deleted successfully with ID: {}", cmd.id());
    }
}
