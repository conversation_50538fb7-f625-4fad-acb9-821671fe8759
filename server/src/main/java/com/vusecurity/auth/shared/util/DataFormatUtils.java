package com.vusecurity.auth.shared.util;

import java.util.UUID;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

public class DataFormatUtils {
    
    private static final Pattern UUID_PATTERN = Pattern.compile(
        "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"
    );
    
    private DataFormatUtils() {
        // Utility class
    }
    
    /**
     * Determines if the given string is a valid UUID format
     * @param value the string to check
     * @return true if the string matches UUID format, false otherwise
     */
    public static boolean isUuidFormat(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        return UUID_PATTERN.matcher(value.trim()).matches();
    }
    
    /**
     * Determines if the given string is a valid regex pattern
     * @param value the string to check
     * @return true if the string is a valid regex pattern, false otherwise
     */
    public static boolean isValidRegexPattern(String value) {
        if (value == null || value.trim().isEmpty()) {
            return true; // null/empty patterns are considered valid (no validation)
        }
        try {
            Pattern.compile(value);
            return true;
        } catch (PatternSyntaxException e) {
            return false;
        }
    }
    
    /**
     * Attempts to parse the given string as a UUID
     * @param value the string to parse
     * @return the parsed UUID, or null if parsing fails
     */
    public static UUID parseUuidSafely(String value) {
        if (!isUuidFormat(value)) {
            return null;
        }
        try {
            return UUID.fromString(value.trim());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}