package com.vusecurity.auth.shared.config;

import com.vusecurity.auth.identities.application.query.GetAccountClaimsQuery;
import com.vusecurity.auth.identities.application.query.GetAccountQuery;
import com.vusecurity.auth.shared.infrastructure.security.oidc.AccountDetailService;
import com.vusecurity.auth.shared.infrastructure.security.oidc.VuOidcUserInfoMapper;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.core.oidc.OidcUserInfo;
import org.springframework.security.oauth2.server.authorization.oidc.authentication.OidcUserInfoAuthenticationContext;
import org.springframework.web.client.RestTemplate;

import java.util.function.Function;

/**
 * Configuration for OIDC UserInfo endpoint customization.
 * This configuration registers the custom UserInfo mapper that integrates with
 * the account-based authentication system.
 */
@Configuration
public class OidcUserInfoSecurityConfig {

    /**
     * Provides the custom UserInfo mapper as a bean.
     * This mapper will be automatically used by Spring Authorization Server
     * when the OIDC UserInfo endpoint is configured.
     */
    @Bean
    public Function<OidcUserInfoAuthenticationContext, OidcUserInfo> userInfoMapper(
            VuOidcUserInfoMapper vuOidcUserInfoMapper) {
        return vuOidcUserInfoMapper;
    }

    /**
     * Primary UserDetailsService that uses the account-based authentication system.
     * This service loads user details from AccountJpaEntity and includes claim
     * values.
     */
    @Bean
    @Primary
    public UserDetailsService accountUserDetailsService(GetAccountQuery getAccountQuery,
            GetAccountClaimsQuery getAccountClaimsQuery) {
        return new AccountDetailService(getAccountQuery, getAccountClaimsQuery);
    }

    /**
     * RestTemplate bean for HTTP client operations.
     * Used by FactorServerClient to communicate with the external factor server.
     */
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
