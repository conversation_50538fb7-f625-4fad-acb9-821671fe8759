package com.vusecurity.auth.claims.api.handler;

import com.vusecurity.core.commons.ApiMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.server.ResponseStatusException;

import java.util.NoSuchElementException;

/**
 * Exception handler for ClaimSetController.
 * Handles specific exceptions thrown by claim set operations and converts them to appropriate HTTP responses.
 */
@ControllerAdvice(assignableTypes = {com.vusecurity.auth.claims.api.controller.ClaimSetController.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ClaimSetErrorControllerHandler {

    private static final Logger logger = LoggerFactory.getLogger(ClaimSetErrorControllerHandler.class);

    /**
     * Handle ResponseStatusException thrown by claim set controller.
     * This ensures that the HTTP status code from the ResponseStatusException is preserved.
     */
    @ExceptionHandler(ResponseStatusException.class)
    public ResponseEntity<ApiMessage> handleResponseStatusException(ResponseStatusException ex) {
        logger.debug("Handling ResponseStatusException: {} - {}", ex.getStatusCode(), ex.getReason());
        
        // Extract the status code and reason from the ResponseStatusException
        int statusCode = ex.getStatusCode().value();
        String message = ex.getReason() != null ? ex.getReason() : ex.getMessage();
        
        // Create appropriate error code based on status
        int errorCode = switch (statusCode) {
            case 400 -> 5300; // Bad Request
            case 404 -> 5304; // Not Found
            case 500 -> 5350; // Internal Server Error
            default -> 5300; // Generic error
        };
        
        ApiMessage error = new ApiMessage(errorCode, message);
        return ResponseEntity.status(ex.getStatusCode()).body(error);
    }

    /**
     * Handle NoSuchElementException thrown by claim set services.
     * This is a fallback in case the service throws NoSuchElementException directly.
     */
    @ExceptionHandler(NoSuchElementException.class)
    public ResponseEntity<ApiMessage> handleNoSuchElementException(NoSuchElementException ex) {
        logger.debug("Handling NoSuchElementException: {}", ex.getMessage());
        
        ApiMessage error = new ApiMessage(5304, ex.getMessage());
        return ResponseEntity.status(404).body(error);
    }

    /**
     * Handle IllegalArgumentException thrown by claim set services.
     * This is a fallback in case the service throws IllegalArgumentException directly.
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiMessage> handleIllegalArgumentException(IllegalArgumentException ex) {
        logger.debug("Handling IllegalArgumentException: {}", ex.getMessage());
        
        ApiMessage error = new ApiMessage(5300, ex.getMessage());
        return ResponseEntity.status(400).body(error);
    }
}
