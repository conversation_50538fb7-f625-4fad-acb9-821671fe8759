package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.CreateClaimValueCommand;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.*;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.service.ClaimSetClaimValueService;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CreateClaimValueHandlerHierarchicalTest {

    @Mock
    private ClaimValueRepository claimValueRepo;
    
    @Mock
    private ClaimDefinitionRepository claimDefinitionRepo;
    
    @Mock
    private ClaimSetRepository claimSetRepo;
    
    @Mock
    private ClaimSetClaimValueService claimSetClaimValueService;
    
    @Mock
    private ObjectMapper objectMapper;
    
    @InjectMocks
    private CreateClaimValueHandler handler;
    
    private UUID claimSetId;
    private UUID claimDefinitionId;
    private UUID accountId;
    private ClaimSetJpaEntity testClaimSet;
    private ClaimDefinitionJpaEntity claimSetDefinition;
    private ClaimValueJpaEntity savedClaimValue;
    
    @BeforeEach
    void setUp() {
        claimSetId = UUID.randomUUID();
        claimDefinitionId = UUID.randomUUID();
        accountId = UUID.randomUUID();
        
        testClaimSet = new ClaimSetJpaEntity();
        testClaimSet.setId(claimSetId);
        
        claimSetDefinition = new ClaimDefinitionJpaEntity();
        claimSetDefinition.setId(claimDefinitionId);
        claimSetDefinition.setCode("direccion_principal");
        claimSetDefinition.setDataType(DataTypeEnum.CLAIMSET);
        
        savedClaimValue = new ClaimValueJpaEntity();
        savedClaimValue.setId(UUID.randomUUID());
        savedClaimValue.setClaimDefinition(claimSetDefinition);
    }
    
    @Test
    void shouldCreateClaimValueForClaimSetType_Successfully() {
        // Given
        UUID referencedClaimSetId = UUID.randomUUID();
        ClaimSetJpaEntity referencedClaimSet = new ClaimSetJpaEntity();
        referencedClaimSet.setId(referencedClaimSetId);
        
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
            claimSetId, claimDefinitionId, null, accountId,
            referencedClaimSetId.toString(), true, false, "USER_INPUT", null);
        
        when(claimDefinitionRepo.findById(claimDefinitionId)).thenReturn(Optional.of(claimSetDefinition));
        when(claimSetRepo.findById(referencedClaimSetId)).thenReturn(Optional.of(referencedClaimSet));
        when(claimSetRepo.getReferenceById(claimSetId)).thenReturn(testClaimSet);
        when(claimSetRepo.getReferenceById(referencedClaimSetId)).thenReturn(referencedClaimSet);
        when(claimValueRepo.save(any(ClaimValueJpaEntity.class))).thenReturn(savedClaimValue);
        
        ClaimSetClaimValuesJpaEntity association = new ClaimSetClaimValuesJpaEntity(testClaimSet, savedClaimValue);
        when(claimSetClaimValueService.associateClaimValueToClaimSet(testClaimSet, savedClaimValue))
            .thenReturn(association);
        
        ClaimValueReferencesJpaEntity reference = new ClaimValueReferencesJpaEntity(
            savedClaimValue, referencedClaimSet, claimSetDefinition, null);
        when(claimSetClaimValueService.createClaimSetReference(savedClaimValue, referencedClaimSet, claimSetDefinition, null))
            .thenReturn(reference);
        
        // When
        ClaimValueJpaEntity result = handler.handle(cmd);
        
        // Then
        assertNotNull(result);
        assertEquals(referencedClaimSetId.toString(), result.getValue());
        verify(claimSetRepo).findById(referencedClaimSetId);
        verify(claimSetClaimValueService).associateClaimValueToClaimSet(testClaimSet, savedClaimValue);
        verify(claimSetClaimValueService).createClaimSetReference(savedClaimValue, referencedClaimSet, claimSetDefinition, null);
    }
    
    @Test
    void shouldThrowException_WhenReferencedClaimSetNotFound() {
        // Given
        UUID nonExistentClaimSetId = UUID.randomUUID();
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
            claimSetId, claimDefinitionId, null, accountId,
            nonExistentClaimSetId.toString(), true, false, "USER_INPUT", null);
        
        when(claimDefinitionRepo.findById(claimDefinitionId)).thenReturn(Optional.of(claimSetDefinition));
        when(claimSetRepo.findById(nonExistentClaimSetId)).thenReturn(Optional.empty());
        
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> handler.handle(cmd));
        verify(claimSetRepo).findById(nonExistentClaimSetId);
        verify(claimValueRepo, never()).save(any());
        verify(claimSetClaimValueService, never()).associateClaimValueToClaimSet(any(), any());
    }
    
    @Test
    void shouldThrowException_WhenClaimSetValueIsInvalidUUID() {
        // Given
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
            claimSetId, claimDefinitionId, null, accountId,
            "invalid-uuid", true, false, "USER_INPUT", null);
        
        when(claimDefinitionRepo.findById(claimDefinitionId)).thenReturn(Optional.of(claimSetDefinition));
        
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> handler.handle(cmd));
        verify(claimValueRepo, never()).save(any());
    }
    
    @Test
    void shouldValidateClaimSetTemplate_WhenIsAListOfIsDefined() {
        // Given
        ClaimDefinitionJpaEntity templateDefinition = new ClaimDefinitionJpaEntity();
        templateDefinition.setId(UUID.randomUUID());
        templateDefinition.setDataType(DataTypeEnum.CLAIMSET);
        
        claimSetDefinition.setIsAListOf(templateDefinition);
        
        UUID referencedClaimSetId = UUID.randomUUID();
        ClaimSetJpaEntity referencedClaimSet = new ClaimSetJpaEntity();
        referencedClaimSet.setId(referencedClaimSetId);
        
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
            claimSetId, claimDefinitionId, null, accountId,
            referencedClaimSetId.toString(), true, false, "USER_INPUT", null);
        
        when(claimDefinitionRepo.findById(claimDefinitionId)).thenReturn(Optional.of(claimSetDefinition));
        when(claimSetRepo.findById(referencedClaimSetId)).thenReturn(Optional.of(referencedClaimSet));
        when(claimSetRepo.getReferenceById(claimSetId)).thenReturn(testClaimSet);
        when(claimSetRepo.getReferenceById(referencedClaimSetId)).thenReturn(referencedClaimSet);
        when(claimValueRepo.save(any(ClaimValueJpaEntity.class))).thenReturn(savedClaimValue);
        
        // When
        ClaimValueJpaEntity result = handler.handle(cmd);
        
        // Then
        assertNotNull(result);
        verify(claimSetRepo).findById(referencedClaimSetId);
        // Aquí se validaría el template si implementas la lógica de validación
    }
}