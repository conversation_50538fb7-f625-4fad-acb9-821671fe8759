package com.vusecurity.auth.client.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
public class AuthServerClientException extends RuntimeException {
    
    private final HttpStatus httpStatus;
    private final String errorCode;
    private final String errorDetails;
    
    public AuthServerClientException(String message) {
        super(message);
        this.httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        this.errorCode = null;
        this.errorDetails = null;
    }
    
    public AuthServerClientException(String message, Throwable cause) {
        super(message, cause);
        this.httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        this.errorCode = null;
        this.errorDetails = null;
    }
    
    public AuthServerClientException(String message, HttpStatus httpStatus) {
        super(message);
        this.httpStatus = httpStatus;
        this.errorCode = null;
        this.errorDetails = null;
    }
    
    public AuthServerClientException(String message, HttpStatus httpStatus, String errorCode, String errorDetails) {
        super(message);
        this.httpStatus = httpStatus;
        this.errorCode = errorCode;
        this.errorDetails = errorDetails;
    }
    
    public AuthServerClientException(String message, HttpStatus httpStatus, Throwable cause) {
        super(message, cause);
        this.httpStatus = httpStatus;
        this.errorCode = null;
        this.errorDetails = null;
    }
}