package com.vusecurity.auth.identities.api.controller;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetDefinitionMappingJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetDefinitionMappingRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.contracts.enums.IdentityType;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityProviderRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityRepository;
import com.vusecurity.auth.shared.TestDataBuilder;
import com.vusecurity.auth.shared.test.BaseIntegrationTest;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.domain.enums.BusinessTypeEnum;
import com.vusecurity.business.domain.repositories.BusinessRepository;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.context.WebApplicationContext;
import org.testcontainers.junit.jupiter.Testcontainers;

import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.setup.MockMvcBuilders.webAppContextSetup;

@AutoConfigureWebMvc
@Testcontainers
class IdentityControllerIT extends BaseIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private BusinessRepository businessRepository;

    @Autowired
    private IdentityRepository identityRepository;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private IdentityProviderRepository identityProviderRepository;

    @Autowired
    private ClaimDefinitionRepository claimDefinitionRepo;

    @Autowired
    private ClaimSetRepository claimSetRepo;

    @Autowired
    private ClaimSetDefinitionMappingRepository mappingRepo;

    @Autowired
    private ClaimValueRepository claimValueRepo;

    private MockMvc mockMvc;

    private Business business;
    private IdentityJpaEntity identity;
    private AccountJpaEntity account;
    private IdentityProviderJpaEntity identityProvider;
    private ClaimDefinitionJpaEntity stringClaimDef;
    private ClaimValueJpaEntity stringClaimValue;
    private ClaimValueJpaEntity stringIdentityClaimValue;
    private ClaimSetJpaEntity testClaimSet;

    @BeforeEach
    void setUp() {
        claimValueRepo.deleteAll();
        mappingRepo.deleteAll();
        claimSetRepo.deleteAll();
        claimDefinitionRepo.deleteAll();
        accountRepository.deleteAll();
        identityRepository.deleteAll();
        businessRepository.deleteAll();

        mockMvc = webAppContextSetup(webApplicationContext).build();

        business = new Business().setName("Test Business").setBusinessType(BusinessTypeEnum.BUSINESS_UNIT);
        business.setCreatedBy("test");
        business = businessRepository.save(business);

        identityProvider = identityProviderRepository.save(new IdentityProviderJpaEntity("Default"));
        identity = identityRepository.save(new IdentityJpaEntity(IdentityType.PERSON, "<EMAIL>"));
        account = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        accountRepository.save(account);

        // Create string claim definition
        stringClaimDef = TestDataBuilder.validClaimDefinition()
                .code("email")
                .name("Email Address")
                .description("Email address")
                .dataType(DataTypeEnum.STRING)
                .dataFormat("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
                .build();
        stringClaimDef = claimDefinitionRepo.save(stringClaimDef);

        // Create test claim set
        testClaimSet = TestDataBuilder.validClaimSet()
                .business(business)
                .accountType(AccountType.WORKFORCE)
                .name("Test ClaimSet")
                .build();
        testClaimSet = claimSetRepo.save(testClaimSet);

        // Create claim set mappings
        ClaimSetDefinitionMappingJpaEntity stringMapping = new ClaimSetDefinitionMappingJpaEntity();
        stringMapping.setClaimSet(testClaimSet);
        stringMapping.setClaimDefinition(stringClaimDef);
        stringMapping.setEnforceUniqueness(false);
        stringMapping.setClaimDefinitionOrder(1);
        mappingRepo.save(stringMapping);

        // Create identity test claim values
        stringIdentityClaimValue = TestDataBuilder.validClaimValue()
                .claimDefinition(stringClaimDef)
                .ownerId(identity.getId())
                .ownerType(OwnerType.IDENTITY)
                .value("<EMAIL>")
                .isPrimary(true)
                .source("USER_INPUT")
                .build();
        stringIdentityClaimValue = claimValueRepo.save(stringIdentityClaimValue);

        // Create account test claim values
        stringClaimValue = TestDataBuilder.validClaimValue()
                .claimDefinition(stringClaimDef)
                .ownerId(account.getId())
                .ownerType(OwnerType.ACCOUNT)
                .value("<EMAIL>")
                .isPrimary(true)
                .source("USER_INPUT")
                .build();
        stringClaimValue.setClaimSetClaimValue(new ClaimSetClaimValueJpaEntity(testClaimSet, stringClaimValue));
        stringClaimValue = claimValueRepo.save(stringClaimValue);

    }

    @Test
    void shouldReturnIdentitiesWithBusinessInclude() throws Exception {
        mockMvc.perform(get("/api/v1/identities")
                        .param("include", "business")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.content[0].businesses", hasSize(1)))
                .andExpect(jsonPath("$.content[0].businesses[0].id").value(business.getId().toString()))
                .andExpect(jsonPath("$.content[0].businesses[0].name").value(business.getName()));
    }

    @Test
    void shouldReturnIdentitiesWithoutBusinessInclude() throws Exception {
        mockMvc.perform(get("/api/v1/identities")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.content[0].businesses").doesNotExist());
    }

    @Test
    void shouldReturnAllBusinessesForIdentityWithMultipleAccounts() throws Exception {
        // Create a second business and account for the same identity
        Business business2 = new Business().setName("Test Business 2").setBusinessType(BusinessTypeEnum.BUSINESS_UNIT);
        business2.setCreatedBy("test");
        business2 = businessRepository.save(business2);

        AccountJpaEntity account2 = new AccountJpaEntity(business2, identity, identityProvider, AccountType.WORKFORCE);
        accountRepository.save(account2);

        mockMvc.perform(get("/api/v1/identities")
                        .param("include", "business")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.content[0].businesses", notNullValue()))
                .andExpect(jsonPath("$.content[0].businesses", hasSize(2)))
                .andExpect(jsonPath("$.content[0].businesses[*].id").value(Matchers.hasItems(
                        business.getId().toString(),
                        business2.getId().toString()
                )))
                .andExpect(jsonPath("$.content[0].businesses[*].name").value(Matchers.hasItems(
                        business.getName(),
                        business2.getName()
                )));
    }

    @Test
    void shouldReturnAllClaimsForIdentity() throws Exception {
        
        mockMvc.perform(get("/api/v1/identities/" + identity.getId() + "/claims")
                    .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(identity.getId().toString()))
                .andExpect(jsonPath("$.name").value(identity.getName()))
                .andExpect(jsonPath("$.identityType").value(identity.getIdentityType().toString()))
                .andExpect(jsonPath("$.accounts", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.accounts[0].id").value(account.getId().toString()))
                .andExpect(jsonPath("$.accounts[0].accountType").value(AccountType.WORKFORCE.toString()))
                .andExpect(jsonPath("$.claimValues", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.claimValues[0].id").value(stringIdentityClaimValue.getId().toString()))
                .andExpect(jsonPath("$.claimValues[0].claimDefinitionId").value(stringIdentityClaimValue.getClaimDefinition().getId().toString()))
                .andExpect(jsonPath("$.claimValues[0].value").value(stringIdentityClaimValue.getValue()));
    }

    @Test
    void shouldReturnAllAccountClaimsGroupedByBusinessForIdentity() throws Exception {
        
        mockMvc.perform(get("/api/v1/identities/" + identity.getId() + "/claims/grouped-by-business")
                    .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(identity.getId().toString()))
                .andExpect(jsonPath("$.name").value(identity.getName()))
                .andExpect(jsonPath("$.identityType").value(identity.getIdentityType().toString()))
                .andExpect(jsonPath("$.claimValues", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.claimValues[0].id").value(stringIdentityClaimValue.getId().toString()))
                .andExpect(jsonPath("$.claimValues[0].claimDefinitionId").value(stringIdentityClaimValue.getClaimDefinition().getId().toString()))
                .andExpect(jsonPath("$.claimValues[0].value").value(stringIdentityClaimValue.getValue()))
                .andExpect(jsonPath("$.businessList", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.businessList[0].id").value(business.getId().toString()))
                .andExpect(jsonPath("$.businessList[0].name").value(business.getName()))
                .andExpect(jsonPath("$.businessList[0].claimSets", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.businessList[0].claimSets[0].id").value(testClaimSet.getId().toString()))
                .andExpect(jsonPath("$.businessList[0].claimSets[0].claimDefinitions", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.businessList[0].claimSets[0].claimDefinitions[0].id").value(stringClaimDef.getId().toString()))
                .andExpect(jsonPath("$.businessList[0].accounts", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.businessList[0].accounts[0].id").value(account.getId().toString()))
                .andExpect(jsonPath("$.businessList[0].accounts[0].accountType").value(AccountType.WORKFORCE.toString()))
                .andExpect(jsonPath("$.businessList[0].accounts[0].claimValues", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.businessList[0].accounts[0].claimValues[0].id").value(stringClaimValue.getId().toString()))
                .andExpect(jsonPath("$.businessList[0].accounts[0].claimValues[0].claimDefinitionId").value(stringClaimValue.getClaimDefinition().getId().toString()))
                .andExpect(jsonPath("$.businessList[0].accounts[0].claimValues[0].value").value(stringClaimValue.getValue()));
    }
} 