package com.vusecurity.auth.shared.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ArrayParsingUtils.
 * Tests comprehensive coverage of all parsing scenarios and edge cases.
 */
class ArrayParsingUtilsTest {

    @Test
    void should_parse_json_array_with_quoted_strings() {
        // Given
        String input = "[\"value1\", \"value2\", \"value3\"]";
        
        // When
        String[] result = ArrayParsingUtils.parseArrayValue(input);
        
        // Then
        assertNotNull(result);
        assertEquals(3, result.length);
        assertEquals("value1", result[0]);
        assertEquals("value2", result[1]);
        assertEquals("value3", result[2]);
    }

    @Test
    void should_parse_empty_json_array() {
        // Given
        String input = "[]";
        
        // When
        String[] result = ArrayParsingUtils.parseArrayValue(input);
        
        // Then
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    void should_parse_comma_separated_values() {
        // Given
        String input = "value1,value2,value3";
        
        // When
        String[] result = ArrayParsingUtils.parseArrayValue(input);
        
        // Then
        assertNotNull(result);
        assertEquals(3, result.length);
        assertEquals("value1", result[0]);
        assertEquals("value2", result[1]);
        assertEquals("value3", result[2]);
    }

    @Test
    void should_handle_single_value() {
        // Given
        String input = "single-value";
        
        // When
        String[] result = ArrayParsingUtils.parseArrayValue(input);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.length);
        assertEquals("single-value", result[0]);
    }

    @Test
    void should_handle_null_input() {
        // Given
        String input = null;
        
        // When
        String[] result = ArrayParsingUtils.parseArrayValue(input);
        
        // Then
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    void should_handle_whitespace_trimming() {
        // Given
        String input = "[  \"value1\"  ,  \"value2\"  ]";
        
        // When
        String[] result = ArrayParsingUtils.parseArrayValue(input);
        
        // Then
        assertNotNull(result);
        assertEquals(2, result.length);
        assertEquals("value1", result[0]);
        assertEquals("value2", result[1]);
    }

    @Test
    void should_remove_quotes_from_start_and_end() {
        // Given - test the regex fix for proper precedence
        String input = "[\"start_quote\", \"end_quote\", \"both_quotes\"]";
        
        // When
        String[] result = ArrayParsingUtils.parseArrayValue(input);
        
        // Then
        assertNotNull(result);
        assertEquals(3, result.length);
        assertEquals("start_quote", result[0]);
        assertEquals("end_quote", result[1]);
        assertEquals("both_quotes", result[2]);
        
        // Verify no quotes remain at start or end
        for (String element : result) {
            assertFalse(element.startsWith("\""));
            assertFalse(element.endsWith("\""));
        }
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "[\"<EMAIL>\", \"<EMAIL>\"]",
        "[\"<EMAIL>\", \"<EMAIL>\"]"
    })
    void should_handle_email_arrays(String input) {
        // When
        String[] result = ArrayParsingUtils.parseArrayValue(input);
        
        // Then
        assertNotNull(result);
        assertEquals(2, result.length);
        assertTrue(result[0].contains("@"));
        assertTrue(result[1].contains("@"));
    }

    @Test
    void should_handle_empty_string_elements() {
        // Given
        String input = "[\"\", \"value\", \"\"]";
        
        // When
        String[] result = ArrayParsingUtils.parseArrayValue(input);
        
        // Then
        assertNotNull(result);
        assertEquals(3, result.length);
        assertEquals("", result[0]);
        assertEquals("value", result[1]);
        assertEquals("", result[2]);
    }
}
