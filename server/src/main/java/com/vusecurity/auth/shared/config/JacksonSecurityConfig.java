package com.vusecurity.auth.shared.config;

import java.util.Collection;
import java.util.UUID;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.jackson2.SecurityJackson2Modules;
import org.springframework.security.oauth2.server.authorization.jackson2.OAuth2AuthorizationServerJackson2Module;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.StreamReadFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.vusecurity.auth.shared.infrastructure.security.mfa.MultiFactorAuthenticationToken;

/**
 * Jackson configuration for Spring Security serialization.
 * This configuration enables proper serialization/deserialization of Spring
 * Security objects,
 * including our custom MultiFactorAuthenticationToken.
 */
@Configuration
public class JacksonSecurityConfig {

    /**
     * Configure ObjectMapper with Spring Security modules and custom mixins.
     * This allows proper serialization of authentication tokens in sessions.
     */
    @Bean
    public ObjectMapper securityObjectMapper() {
        JsonFactory factory = JsonFactory.builder()
                .enable(StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION)
                .build();

        ObjectMapper mapper = new ObjectMapper(factory);

        // Register standard modules
        mapper.registerModule(new JavaTimeModule());
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        // Register Spring Security Jackson modules
        mapper.registerModules(SecurityJackson2Modules.getModules(getClass().getClassLoader()));

        mapper.registerModule(new OAuth2AuthorizationServerJackson2Module());

        mapper.activateDefaultTyping(
                mapper.getPolymorphicTypeValidator(),
                ObjectMapper.DefaultTyping.NON_FINAL,
                JsonTypeInfo.As.PROPERTY);

        // Add mixin for MultiFactorAuthenticationToken
        mapper.addMixIn(MultiFactorAuthenticationToken.class, MultiFactorAuthenticationTokenMixin.class);

        return mapper;
    }

    /**
     * Mixin class for MultiFactorAuthenticationToken serialization.
     * This provides Jackson annotations for proper serialization/deserialization
     * without modifying the original class.
     *
     * The mixin handles both unauthenticated and authenticated token states.
     */
    @JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, include = JsonTypeInfo.As.PROPERTY, property = "@class")
    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.PUBLIC_ONLY, isGetterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, creatorVisibility = JsonAutoDetect.Visibility.PUBLIC_ONLY)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public abstract static class MultiFactorAuthenticationTokenMixin {

        // Fields for serialization
        @JsonProperty("accountId")
        abstract UUID getAccountId();

        @JsonProperty("totp")
        abstract String getTotp();

        @JsonProperty("password")
        abstract String getPassword();

        @JsonProperty("authorities")
        abstract Collection<? extends GrantedAuthority> getAuthorities();

        @JsonProperty("authenticated")
        abstract boolean isAuthenticated();

        /**
         * Primary constructor for Jackson deserialization.
         * This constructor can handle both authenticated and unauthenticated tokens
         * based on the presence of different properties.
         */
        @JsonCreator
        protected MultiFactorAuthenticationTokenMixin(
                @JsonProperty("accountId") UUID accountId,
                @JsonProperty("totp") String totp,
                @JsonProperty("password") String password,
                @JsonProperty("authorities") Collection<? extends GrantedAuthority> authorities,
                @JsonProperty("authenticated") boolean authenticated) {
            // This is a mixin - implementation is in the actual class
        }
    }
}
