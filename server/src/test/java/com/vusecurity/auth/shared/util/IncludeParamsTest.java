package com.vusecurity.auth.shared.util;

import org.junit.jupiter.api.Test;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

class IncludeParamsTest {

    @Test
    void parses_and_filters_includes_case_insensitively() {
        IncludeParams params = new IncludeParams("Claims, Business,unknown ", Set.of("claims", "business"));
        assertTrue(params.contains("claims"));
        assertTrue(params.contains("BUSINESS"));
        assertFalse(params.contains("unknown"));
        assertEquals(Set.of("claims", "business"), params.getRequested());
        assertEquals(Set.of("claims", "business"), params.getAllowed());
    }

    @Test
    void handles_null_input_as_empty() {
        IncludeParams params = new IncludeParams(null, Set.of("a"));
        assertFalse(params.contains("a"));
        assertTrue(params.getRequested().isEmpty());
        assertEquals(Set.of("a"), params.getAllowed());
    }
}

