package com.vusecurity.auth.contracts.api.v1.dto.identities;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;
import java.util.UUID;

@Schema(description = "Claim whose value is an array of strings")
public record ArrayClaimValueRequest(

        @Schema(description = "Unique identifier of the claim set",
            example = "123e4567-e89b-12d3-a456-************")
        UUID claimSetId,

        @NotNull(message = "Claim definition ID is required")
        @Schema(description = "Unique identifier of the claim definition",
                example = "123e4567-e89b-12d3-a456-************")
        UUID claimDefinitionId,

        @NotNull @Size(min = 1)
        List<String> values,

        @NotNull
        Integer primaryIndex,

        String source

) implements AccountClaimValueRequest {

    public ArrayClaimValueRequest(
        UUID claimSetId,
        UUID claimDefinitionId,
        List<String> values,
        Integer primaryIndex
    ){
        this(claimSetId, claimDefinitionId, values, primaryIndex, "USER_INPUT");
    }
}
