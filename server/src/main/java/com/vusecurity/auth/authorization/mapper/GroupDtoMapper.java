package com.vusecurity.auth.authorization.mapper;

import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.GroupMembershipJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.GroupMembershipRepository;
import com.vusecurity.auth.contracts.api.v1.dto.authorization.AccountSummary;
import com.vusecurity.auth.contracts.api.v1.dto.authorization.GroupResponse;
import com.vusecurity.auth.contracts.api.v1.dto.authorization.RoleSummary;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class GroupDtoMapper {

    private final GroupMembershipRepository groupMembershipRepository;

    public GroupResponse toResponse(AccountGroupJpaEntity entity) {
        return toResponse(entity, true);
    }

    public GroupResponse toResponse(AccountGroupJpaEntity entity, boolean includeDetails) {
        GroupResponse response = new GroupResponse();
        response.setId(entity.getId());
        response.setName(entity.getName());
        response.setDescription(entity.getDescription());
        response.setMetadata(entity.getMetadata());

        // Always include account count for performance and user experience
        long accountCount = groupMembershipRepository.countActiveByGroupId(entity.getId());
        response.setAccountCount(accountCount);

        if (includeDetails) {
            // Map roles
            response.setRoles(entity.getRoles().stream()
                    .map(this::toRoleSummary)
                    .toList());

            // Map accounts from memberships
            List<GroupMembershipJpaEntity> memberships = groupMembershipRepository.findActiveByGroupId(entity.getId());
            response.setAccounts(memberships.stream()
                    .map(this::toAccountSummary)
                    .toList());
        } else {
            response.setRoles(new ArrayList<>());
            response.setAccounts(new ArrayList<>());
        }

        // Set audit information
        response.setAudit(new BaseResponse.AuditInfo()
                .setCreatedAt(entity.getCreatedAt())
                .setUpdatedAt(entity.getUpdatedAt())
                .setCreatedBy(entity.getCreatedBy())
                .setUpdatedBy(entity.getUpdatedBy()));

        return response;
    }

    private RoleSummary toRoleSummary(RoleJpaEntity role) {
        return new RoleSummary()
                .setId(role.getId())
                .setName(role.getName())
                .setDescription(role.getDescription());
    }

    private AccountSummary toAccountSummary(GroupMembershipJpaEntity groupMembership) {
        AccountJpaEntity account = groupMembership.getAccount();
        AccountSummary summary = new AccountSummary()
                .setId(account.getId())
                .setAccountType(account.getAccountType())
                .setGroupRole(groupMembership.getMembershipRole().toString());

        // Set username and display name from identity if available
        if (account.getIdentity() != null) {
            summary.setUsername(account.getIdentity().getName());
            summary.setDisplayName(account.getIdentity().getName());
        }

        return summary;
    }

    public List<GroupResponse> toResponseList(List<AccountGroupJpaEntity> entities) {
        return entities.stream()
                .map(entity -> toResponse(entity, false)) // Don't include details for list views
                .toList();
    }

    public List<GroupResponse> toResponseListWithDetails(List<AccountGroupJpaEntity> entities) {
        return entities.stream()
                .map(entity -> toResponse(entity, true))
                .toList();
    }
} 