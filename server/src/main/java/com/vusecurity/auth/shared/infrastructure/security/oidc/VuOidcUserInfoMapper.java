package com.vusecurity.auth.shared.infrastructure.security.oidc;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.core.oidc.OidcUserInfo;
import org.springframework.security.oauth2.server.authorization.oidc.authentication.OidcUserInfoAuthenticationContext;
import org.springframework.security.oauth2.server.authorization.oidc.authentication.OidcUserInfoAuthenticationToken;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;

import com.vusecurity.auth.identities.application.query.GetAccountClaimsQuery;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.shared.config.OidcUserInfoConfig;

/**
 * Custom UserInfo mapper for OIDC UserInfo endpoint that integrates with the
 * account-based system.
 * This mapper builds OIDC UserInfo responses using Account, Identity, and
 * Claims information
 * following the project's established architecture patterns.
 */
@Component
public class VuOidcUserInfoMapper implements Function<OidcUserInfoAuthenticationContext, OidcUserInfo> {

    private static final Logger logger = LoggerFactory.getLogger(VuOidcUserInfoMapper.class);

    private final AccountDetailService accountDetailService;
    private final GetAccountClaimsQuery getAccountClaimsQuery;
    private final OidcUserInfoConfig config;

    public VuOidcUserInfoMapper(UserDetailsService accountUserDetailsService,
            GetAccountClaimsQuery getAccountClaimsQuery,
            OidcUserInfoConfig config) {
        this.accountDetailService = (AccountDetailService) accountUserDetailsService;
        this.getAccountClaimsQuery = getAccountClaimsQuery;
        this.config = config != null ? config : OidcUserInfoConfig.createDefaultConfig();
    }

    @Override
    public OidcUserInfo apply(OidcUserInfoAuthenticationContext context) {
        logger.debug("Processing OIDC UserInfo request");

        try {
            OidcUserInfoAuthenticationToken authentication = context.getAuthentication();
            JwtAuthenticationToken principal = (JwtAuthenticationToken) authentication.getPrincipal();

            // Get the subject from the JWT token
            String subject = principal.getToken().getSubject();
            logger.debug("Building UserInfo for subject: {}", subject);

            // Load account details using the subject
            AccountUserDetails userDetails = (AccountUserDetails) accountDetailService.loadUserByUsername(subject);
            AccountJpaEntity account = userDetails.getAccount();

            // Build OIDC UserInfo claims
            Map<String, Object> userInfoClaims = buildUserInfoClaims(account, userDetails);

            // Create and return OidcUserInfo object
            OidcUserInfo userInfo = new OidcUserInfo(userInfoClaims);
            logger.debug("Built UserInfo with {} claims for subject: {}", userInfoClaims.size(), subject);

            return userInfo;

        } catch (Exception e) {
            logger.error("Failed to build UserInfo response", e);
            // Return minimal UserInfo with just the subject
            Map<String, Object> minimalClaims = new HashMap<>();

            try {
                OidcUserInfoAuthenticationToken authentication = context.getAuthentication();
                JwtAuthenticationToken principal = (JwtAuthenticationToken) authentication.getPrincipal();
                minimalClaims.put("sub", principal.getToken().getSubject());
            } catch (Exception ex) {
                logger.error("Failed to extract subject from token", ex);
                minimalClaims.put("sub", "unknown");
            }

            return new OidcUserInfo(minimalClaims);
        }
    }

    /**
     * Builds OIDC UserInfo claims from Account, Identity, and Claims information.
     * Maps internal claims to standard OIDC claims where possible.
     */
    private Map<String, Object> buildUserInfoClaims(AccountJpaEntity account, AccountUserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();
        Map<String, Object> accountClaims = userDetails.getAllClaimValues();

        // Standard OIDC claims

        // Subject (required) - use account ID
        claims.put("sub", account.getId().toString());

        // Name claims
        String displayName = userDetails.getDisplayName();
        if (displayName != null && !displayName.trim().isEmpty()) {
            claims.put("name", displayName);
        }

        claims.put("authorities", userDetails.getAuthorities().stream().map(GrantedAuthority::getAuthority)
                .toList());

        // Map standard profile claims from account claims
        mapStandardClaim(accountClaims, claims, "given_name", "first_name");
        mapStandardClaim(accountClaims, claims, "family_name", "last_name");
        mapStandardClaim(accountClaims, claims, "middle_name", "middle_name");
        mapStandardClaim(accountClaims, claims, "nickname", "nickname");
        mapStandardClaim(accountClaims, claims, "preferred_username", "username");

        // Profile URL and picture
        mapStandardClaim(accountClaims, claims, "profile", "profile_url");
        mapStandardClaim(accountClaims, claims, "picture", "picture_url");
        mapStandardClaim(accountClaims, claims, "website", "website");

        // Email claims
        mapStandardClaim(accountClaims, claims, "email", "email_address");
        mapEmailVerified(accountClaims, claims);

        // Phone claims
        mapStandardClaim(accountClaims, claims, "phone_number", "phone_number");
        mapPhoneVerified(accountClaims, claims);

        // Address claim (complex object)
        if (config.isIncludeAddress()) {
            mapAddressClaim(accountClaims, claims);
        }

        // Gender and birthdate
        mapStandardClaim(accountClaims, claims, "gender", "gender");
        mapStandardClaim(accountClaims, claims, "birthdate", "birth_date");

        // Timezone and locale
        mapStandardClaim(accountClaims, claims, "zoneinfo", "timezone");
        mapStandardClaim(accountClaims, claims, "locale", "locale");

        // Updated timestamp
        if (account.getUpdatedAt() != null) {
            claims.put("updated_at", account.getUpdatedAt().getEpochSecond());
        } else if (account.getCreatedAt() != null) {
            claims.put("updated_at", account.getCreatedAt().getEpochSecond());
        }

        // Custom claims - add business and identity context
        if (config.isIncludeCustomClaims()) {
            addCustomClaims(account, claims);
        }

        logger.debug("Built UserInfo with {} claims for account: {}", claims.size(), account.getId());
        return claims;
    }

    /**
     * Maps a standard OIDC claim from account claims if present.
     */
    private void mapStandardClaim(Map<String, Object> accountClaims, Map<String, Object> userInfoClaims,
            String oidcClaimName, String accountClaimCode) {
        // Check if claim should be excluded
        if (config.isClaimExcluded(accountClaimCode)) {
            return;
        }

        // Get mapped claim name
        String mappedClaimName = config.getOidcClaimName(accountClaimCode);

        Object claimInfo = accountClaims.get(accountClaimCode);
        if (claimInfo instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> claimMap = (Map<String, Object>) claimInfo;
            Object claimValue = claimMap.get("value");
            if (claimValue != null && !claimValue.toString().trim().isEmpty()) {
                userInfoClaims.put(mappedClaimName, claimValue);
            }
        } else {
            // Check for default value
            Object defaultValue = config.getDefaultClaimValue(oidcClaimName);
            if (defaultValue != null) {
                userInfoClaims.put(oidcClaimName, defaultValue);
            }
        }
    }

    /**
     * Maps email_verified claim based on email verification status.
     */
    private void mapEmailVerified(Map<String, Object> accountClaims, Map<String, Object> userInfoClaims) {
        Object email = accountClaims.get("email");

        if (email instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> claimInfo = (Map<String, Object>) email;
            Object claimValue = claimInfo.get("value");
            Object isVerified = claimInfo.get("isVerified");
            if (claimValue != null &&  Boolean.TRUE.equals(isVerified) ) {
                userInfoClaims.put("email_verified", claimValue);
            }
        } 
    }

    /**
     * Maps phone_number_verified claim based on phone verification status.
     */
    private void mapPhoneVerified(Map<String, Object> accountClaims, Map<String, Object> userInfoClaims) {
        Object phone = accountClaims.get("phone");

        if (phone instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> claimInfo = (Map<String, Object>) phone;
            Object claimValue = claimInfo.get("value");
            Object isVerified = claimInfo.get("isVerified");
            if (claimValue != null &&  Boolean.TRUE.equals(isVerified) ) {
                userInfoClaims.put("phone_verified", claimValue);
            }
        } 
    }

    /**
     * Maps address claim as a structured object according to OIDC specification.
     */
    private void mapAddressClaim(Map<String, Object> accountClaims, Map<String, Object> userInfoClaims) {
        Map<String, Object> address = new HashMap<>();

        mapStandardClaim(accountClaims, address, "formatted", "address_formatted");
        mapStandardClaim(accountClaims, address, "street_address", "address_street");
        mapStandardClaim(accountClaims, address, "locality", "address_city");
        mapStandardClaim(accountClaims, address, "region", "address_state");
        mapStandardClaim(accountClaims, address, "postal_code", "address_postal_code");
        mapStandardClaim(accountClaims, address, "country", "address_country");

        if (!address.isEmpty()) {
            userInfoClaims.put("address", address);
        }
    }

    /**
     * Adds custom claims specific to the VU Security platform.
     */
    private void addCustomClaims(AccountJpaEntity account, Map<String, Object> claims) {
        // Business context
        if (account.getBusinessId() != null) {
            claims.put("business_id", account.getBusinessId().toString());
        }

        // Identity context
        if (account.getIdentityId() != null) {
            claims.put("identity_id", account.getIdentityId().toString());
        }

        // Account type and lifecycle state
        if (account.getAccountType() != null) {
            claims.put("account_type", account.getAccountType().name());
        }

        if (account.getLifecycleState() != null) {
            claims.put("account_state", account.getLifecycleState().name());
        }

        // Identity provider information
        if (account.getIdentityProviderId() != null) {
            claims.put("identity_provider_id", account.getIdentityProviderId().toString());
        }

        // Account metadata (if any relevant for UserInfo)
        if (config.isIncludeMetadata() && account.getMetadata() != null && !account.getMetadata().isEmpty()) {
            Map<String, Object> publicMetadata = new HashMap<>();
            account.getMetadata().forEach((key, value) -> {
                if (config.isMetadataKeyIncluded(key)) {
                    publicMetadata.put(key, value);
                }
            });

            if (!publicMetadata.isEmpty()) {
                claims.put("metadata", publicMetadata);
            }
        }
    }
}
