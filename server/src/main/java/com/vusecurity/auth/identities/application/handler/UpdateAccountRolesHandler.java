package com.vusecurity.auth.identities.application.handler;

import com.vusecurity.auth.authorization.application.exception.RoleNotFoundException;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.RoleRepository;
import com.vusecurity.auth.identities.application.command.UpdateAccountRolesCommand;
import com.vusecurity.auth.identities.application.exception.AccountNotFoundException;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class UpdateAccountRolesHandler {

    private final AccountRepository accountRepository;
    private final RoleRepository roleRepository;

    @Transactional
    public void updateRoles(UpdateAccountRolesCommand command) {
        command.validate();

        AccountJpaEntity account = accountRepository.findByIdWithRoles(command.accountId())
                .orElseThrow(() -> new AccountNotFoundException("Account not found with ID: " + command.accountId()));

        Set<UUID> desiredRoleIds = (command.roleIds() == null) ? Collections.emptySet() : new HashSet<>(command.roleIds());
        Set<RoleJpaEntity> currentRoles = new HashSet<>(account.getRoles());

        // Roles to remove - update both sides of the relationship
        Set<RoleJpaEntity> rolesToRemove = currentRoles.stream()
                .filter(role -> !desiredRoleIds.contains(role.getId()))
                .collect(Collectors.toSet());
        
        // Load roles with accounts to be able to modify the owning side
        if (!rolesToRemove.isEmpty()) {
            Set<UUID> roleIdsToRemove = rolesToRemove.stream()
                    .map(RoleJpaEntity::getId)
                    .collect(Collectors.toSet());
            List<RoleJpaEntity> rolesWithAccounts = roleRepository.findAllByIdWithAccounts(roleIdsToRemove);
            
            for (RoleJpaEntity role : rolesWithAccounts) {
                role.removeAccount(account); // Remove from role side (owner)
                currentRoles.removeIf(r -> r.getId().equals(role.getId())); // Remove from account side
            }
            
            // Update rolesToRemove with the loaded entities
            rolesToRemove = new HashSet<>(rolesWithAccounts);
        }

        // Roles to add
        Set<UUID> currentRoleIds = currentRoles.stream()
                .map(RoleJpaEntity::getId)
                .collect(Collectors.toSet());

        Set<UUID> roleIdsToAdd = new HashSet<>(desiredRoleIds);
        roleIdsToAdd.removeAll(currentRoleIds);

        if (!roleIdsToAdd.isEmpty()) {
            List<RoleJpaEntity> rolesToAdd = roleRepository.findAllByIdWithAccounts(roleIdsToAdd);
            if (rolesToAdd.size() != roleIdsToAdd.size()) {
                throw new RoleNotFoundException("One or more roles to add were not found.");
            }
            
            // Add roles - update both sides of the relationship
            for (RoleJpaEntity role : rolesToAdd) {
                role.addAccount(account); // Add to role side (owner)
                currentRoles.add(role); // Add to account side
            }
            
            // Save the roles that were modified (owning side)
            roleRepository.saveAll(rolesToAdd);
        }

        // Save roles that had accounts removed (owning side)
        if (!rolesToRemove.isEmpty()) {
            roleRepository.saveAll(rolesToRemove);
        }

        // Update and save the account
        account.setRoles(currentRoles);
        accountRepository.save(account);
    }
}