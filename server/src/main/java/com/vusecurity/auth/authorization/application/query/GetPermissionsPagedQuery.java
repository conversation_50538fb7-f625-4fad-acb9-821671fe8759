package com.vusecurity.auth.authorization.application.query;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class GetPermissionsPagedQuery {
    private int page;
    private int pageSize;
    private String filter;

    public void validate() {
        if (page < 1) {
            throw new IllegalArgumentException("page must be greater than 0");
        }
        if (pageSize < 1 || pageSize > 100) {
            throw new IllegalArgumentException("pageSize must be between 1 and 100");
        }
    }
}
