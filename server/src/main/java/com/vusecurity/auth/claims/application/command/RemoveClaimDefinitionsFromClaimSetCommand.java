package com.vusecurity.auth.claims.application.command;

import java.util.List;
import java.util.UUID;

/**
 * Command for removing claim definitions from a claim set.
 */
public record RemoveClaimDefinitionsFromClaimSetCommand(
        UUID claimSetId,
        List<UUID> claimDefinitionIds) {

    public void validate() {
        if (claimSetId == null) {
            throw new IllegalArgumentException("claimSetId is required");
        }
        if (claimDefinitionIds == null) {
            throw new IllegalArgumentException("claimDefinitionIds is required");
        }
        if (claimDefinitionIds.isEmpty()) {
            throw new IllegalArgumentException("At least one claim definition ID must be provided");
        }
        for (UUID id : claimDefinitionIds) {
            if (id == null) {
                throw new IllegalArgumentException("claimDefinitionIds cannot contain null values");
            }
        }
    }
}
