package com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.UUID;

@Data
@Accessors(chain = true)
@Schema(description = "Response for claim definition to claim sets mapping operations")
public class ClaimDefinitionClaimSetsResponse {
    @Schema(description = "Unique identifier of the claim definition",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID claimDefinitionId;

    @Schema(description = "List of claim set associated with the claim definition")
    private List<ClaimSetToClaimDefinition> claimSets;
}
