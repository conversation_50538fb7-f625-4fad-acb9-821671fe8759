package com.vusecurity.auth.scim.api.controller;

import com.vusecurity.auth.scim.api.handler.ScimTokensErrorControllerHandler;
import com.vusecurity.auth.shared.infrastructure.migration.initial.DataSeedConstants;
import com.vusecurity.auth.shared.test.BaseIntegrationTest;
import com.vusecurity.core.commons.Auditable.Status;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import org.testcontainers.junit.jupiter.Testcontainers;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.JsonPath;

import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@AutoConfigureWebMvc
@Import(ScimTokensErrorControllerHandler.class)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@Testcontainers
class ScimTokensControllerIT extends BaseIntegrationTest {

    @Autowired
    WebApplicationContext webApplicationContext;



    @Autowired
    ObjectMapper objectMapper;
    
    @LocalServerPort
    int port;

    @Value("${app.scim.context-path}/scim/tokens")
    String basePath;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        // Set up MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    void shouldCreateToken() throws Exception {
        String tokenJson = """
            {
                "name": "Integration Test SCIM",
                "description": "SCIM authentication",
                "businessId": "%s",
                "identityProviderId":"%s"
            }
            """.formatted(DataSeedConstants.SYSTEM_BUSINESS_ID.toString(), DataSeedConstants.SCIM_PROVIDER_ID.toString());
        
        mockMvc.perform(MockMvcRequestBuilders.post(basePath)
            .contentType(MediaType.APPLICATION_JSON)
            .content(tokenJson))
            .andExpect(status().isCreated())
            .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.id").exists())
            .andExpect(jsonPath("$.token").exists())
            .andExpect(jsonPath("$.businessId").value(DataSeedConstants.SYSTEM_BUSINESS_ID.toString()))
            .andExpect(jsonPath("$.identityProviderId").value(DataSeedConstants.SCIM_PROVIDER_ID.toString()))
            .andExpect(jsonPath("$.status").value(Status.ACTIVE.toString()));
    }

    @Test
    void shouldGetValidToken() throws Exception {
        String tokenJson = """
            {
                "name": "Integration Test SCIM",
                "description": "SCIM authentication",
                "businessId": "%s",
                "identityProviderId":"%s"
            }
            """.formatted(DataSeedConstants.SYSTEM_BUSINESS_ID.toString(), DataSeedConstants.SCIM_PROVIDER_ID.toString());
        
        MvcResult createResult = mockMvc.perform(MockMvcRequestBuilders.post(basePath)
            .contentType(MediaType.APPLICATION_JSON)
            .content(tokenJson))
            .andExpect(status().isCreated())
            .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.id").exists())
            .andExpect(jsonPath("$.token").exists())
            .andExpect(jsonPath("$.businessId").value(DataSeedConstants.SYSTEM_BUSINESS_ID.toString()))
            .andExpect(jsonPath("$.identityProviderId").value(DataSeedConstants.SCIM_PROVIDER_ID.toString()))
            .andExpect(jsonPath("$.status").value(Status.ACTIVE.toString()))
            .andReturn();
        
        String id = JsonPath.read(createResult.getResponse().getContentAsString(), "$.id");

        mockMvc.perform(MockMvcRequestBuilders.get(basePath.concat("/").concat(id))
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.id").exists())
            .andExpect(jsonPath("$.businessId").value(DataSeedConstants.SYSTEM_BUSINESS_ID.toString()))
            .andExpect(jsonPath("$.identityProviderId").value(DataSeedConstants.SCIM_PROVIDER_ID.toString()))
            .andExpect(jsonPath("$.status").value(Status.ACTIVE.toString()));
    }

    @Test
    void shouldDeactivateAndActivateToken() throws Exception {
        String tokenJson = """
            {
                "name": "Integration Test SCIM",
                "description": "SCIM authentication",
                "businessId": "%s",
                "identityProviderId":"%s"
            }
            """.formatted(DataSeedConstants.SYSTEM_BUSINESS_ID.toString(), DataSeedConstants.SCIM_PROVIDER_ID.toString());
        
        MvcResult createResult = mockMvc.perform(MockMvcRequestBuilders.post(basePath)
            .contentType(MediaType.APPLICATION_JSON)
            .content(tokenJson))
            .andExpect(status().isCreated())
            .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.id").exists())
            .andExpect(jsonPath("$.token").exists())
            .andExpect(jsonPath("$.businessId").value(DataSeedConstants.SYSTEM_BUSINESS_ID.toString()))
            .andExpect(jsonPath("$.identityProviderId").value(DataSeedConstants.SCIM_PROVIDER_ID.toString()))
            .andExpect(jsonPath("$.status").value(Status.ACTIVE.toString()))
            .andReturn();
        
        String id = JsonPath.read(createResult.getResponse().getContentAsString(), "$.id");

        //Deactivate
        mockMvc.perform(MockMvcRequestBuilders.put(basePath.concat("/").concat(id).concat("/deactivate"))
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.id").exists())
            .andExpect(jsonPath("$.businessId").value(DataSeedConstants.SYSTEM_BUSINESS_ID.toString()))
            .andExpect(jsonPath("$.identityProviderId").value(DataSeedConstants.SCIM_PROVIDER_ID.toString()))
            .andExpect(jsonPath("$.status").value(Status.INACTIVE.toString()));

        //Activate
        mockMvc.perform(MockMvcRequestBuilders.put(basePath.concat("/").concat(id).concat("/activate"))
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.id").exists())
            .andExpect(jsonPath("$.businessId").value(DataSeedConstants.SYSTEM_BUSINESS_ID.toString()))
            .andExpect(jsonPath("$.identityProviderId").value(DataSeedConstants.SCIM_PROVIDER_ID.toString()))
            .andExpect(jsonPath("$.status").value(Status.ACTIVE.toString()));
    }

    @Test
    void shouldGetAllTokensPaginated() throws Exception {
        for (int i = 1; i <= 15; i++) {
        String tokenJson = """
            {
                "name": "Integration Test SCIM %d",
                "description": "SCIM authentication",
                "businessId": "%s",
                "identityProviderId":"%s"
            }
            """.formatted(i, DataSeedConstants.SYSTEM_BUSINESS_ID.toString(), DataSeedConstants.SCIM_PROVIDER_ID.toString());
        
        mockMvc.perform(MockMvcRequestBuilders.post(basePath)
            .contentType(MediaType.APPLICATION_JSON)
            .content(tokenJson));
        }

        mockMvc.perform(get(basePath)
                    .param("page", "1")
                    .param("size", "5")
                    .param("sortBy", "status")
                    .param("sortDirection", "DESC")
                    .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.currentPage").value(1))
            .andExpect(jsonPath("$.pageSize").value(5))
            .andExpect(jsonPath("$.totalElements").value(15));
    }
}