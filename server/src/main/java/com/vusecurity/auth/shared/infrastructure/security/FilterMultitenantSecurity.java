package com.vusecurity.auth.shared.infrastructure.security;

import java.io.IOException;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.web.filter.GenericFilterBean;

import com.vusecurity.multitenant.MultitenantConfiguration;
import com.vusecurity.multitenant.TenantContext;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

public class FilterMultitenantSecurity extends GenericFilterBean {

    private static final Logger logger = LoggerFactory.getLogger(FilterMultitenantSecurity.class);

    private final MultitenantConfiguration multitenantConfiguration;

    public FilterMultitenantSecurity(MultitenantConfiguration multitenantConfiguration) {
        this.multitenantConfiguration = multitenantConfiguration;
    }

    @Override
    public void destroy() {
        super.destroy();
        TenantContext.clear();
        EngagementContext.clear();
        MDC.clear();
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        // Extract tenant ID from the configured header
        String tenantId = request.getHeader(multitenantConfiguration.getIdHeader());

        if (request.getHeader("X-Business-Id") != null) {
            // Extract Engagement ID from the request header
            String businessId = request.getHeader("X-Business-Id");
            EngagementContext.setBusinessId(UUID.fromString(businessId));
            logger.trace("Business ID: {} extracted from header", businessId);
        }

        if (request.getHeader("X-Channel-Id") != null) {
            // Extract Channel ID from the request header
            String channelId = request.getHeader("X-Channel-Id");
            EngagementContext.setChannelId(UUID.fromString(channelId));
            logger.trace("Channel ID: {} extracted from header", channelId);
        }

        if (tenantId != null && !tenantId.isEmpty()) {
            MDC.put("tenant", tenantId);
            TenantContext.setCurrentTenant(tenantId);
            logger.trace("TenantId: {} extracted from header: {}", tenantId, multitenantConfiguration.getIdHeader());
        } else {
            // Fallback to public tenant when header is missing
            MDC.put("tenant", "public");
            TenantContext.setCurrentTenant("public");
            logger.trace("No tenant header found, using public tenant. Header expected: {}",
                    multitenantConfiguration.getIdHeader());
        }

        filterChain.doFilter(request, response);
    }
}
