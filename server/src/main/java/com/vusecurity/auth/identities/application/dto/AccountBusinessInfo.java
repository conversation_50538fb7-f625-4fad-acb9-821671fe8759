package com.vusecurity.auth.identities.application.dto;

import com.vusecurity.auth.contracts.enums.AccountType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AccountBusinessInfo {
    private UUID businessId;
    private AccountType accountType;
}
