package com.vusecurity.auth.authorization.application.command;

import com.vusecurity.auth.authorization.application.exception.InvalidGroupRequestException;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for DeleteGroupCommand validation logic.
 */
class DeleteGroupCommandTest {

    @Test
    void shouldValidateSuccessfully_WhenGroupIdIsProvided() {
        // Given
        DeleteGroupCommand command = new DeleteGroupCommand(UUID.randomUUID());

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldThrowException_WhenGroupIdIsNull() {
        // Given
        DeleteGroupCommand command = new DeleteGroupCommand(null);

        // When & Then
        var exception = assertThrows(InvalidGroupRequestException.class, command::validate);
        assertEquals("groupId is required", exception.getMessage());
    }
} 