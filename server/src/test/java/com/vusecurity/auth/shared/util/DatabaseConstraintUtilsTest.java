package com.vusecurity.auth.shared.util;

import org.hibernate.exception.ConstraintViolationException;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.dao.DataIntegrityViolationException;

import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.junit.jupiter.api.Assertions.*;

class DatabaseConstraintUtilsTest {

    private DataIntegrityViolationException makeDataIntegrityViolation(String constraintName) {
        SQLException sqlEx = new SQLException("Integrity violation");
        ConstraintViolationException cve = new ConstraintViolationException("dup", sqlEx, constraintName);
        return new DataIntegrityViolationException("Data integrity", cve);
    }

    @Test
    void findThrowableOfType_should_traverse_cause_chain() {
        Throwable deep = new IllegalArgumentException("deep");
        Throwable mid = new RuntimeException("mid", deep);
        Throwable top = new Exception("top", mid);

        IllegalArgumentException found = DatabaseConstraintUtils.findThrowableOfType(top, IllegalArgumentException.class);
        assertNotNull(found);
        assertEquals("deep", found.getMessage());
    }

    @Test
    void isConstraintViolation_matches_name_case_insensitive() {
        DataIntegrityViolationException ex = makeDataIntegrityViolation("UNQ_USERS_EMAIL");
        assertTrue(DatabaseConstraintUtils.isConstraintViolation(ex, "unq_users_email"));
        assertFalse(DatabaseConstraintUtils.isConstraintViolation(ex, "other_constraint"));
    }

    @Test
    void handleConstraintViolation_supplier_throws_business_exception_on_match() {
        DataIntegrityViolationException ex = makeDataIntegrityViolation("UNQ_CODE");

        RuntimeException business = assertThrows(RuntimeException.class, () ->
                DatabaseConstraintUtils.handleConstraintViolation(ex, "UNQ_CODE", () -> new RuntimeException("business"))
        );
        assertEquals("business", business.getMessage());
    }

    @Test
    void handleConstraintViolation_supplier_rethrows_original_on_mismatch() {
        DataIntegrityViolationException ex = makeDataIntegrityViolation("UNQ_CODE");

        DataIntegrityViolationException rethrown = assertThrows(DataIntegrityViolationException.class, () ->
                DatabaseConstraintUtils.handleConstraintViolation(ex, "OTHER", () -> new RuntimeException("business"))
        );
        assertSame(ex, rethrown);
    }

    @Test
    void handleConstraintViolation_factory_builds_exception_with_constraint_details() {
        DataIntegrityViolationException ex = makeDataIntegrityViolation("UNQ_CODE");

        class MyBusinessEx extends RuntimeException { MyBusinessEx(String m) { super(m); } }

        RuntimeException business = assertThrows(MyBusinessEx.class, () ->
                DatabaseConstraintUtils.handleConstraintViolation(ex, "UNQ_CODE",
                        cve -> new MyBusinessEx("violated: " + cve.getConstraintName()))
        );
        assertEquals("violated: UNQ_CODE", business.getMessage());
    }

    @Test
    void executeWithConstraintHandling_supplier_variant_returns_value_when_no_violation() {
        String value = DatabaseConstraintUtils.executeWithConstraintHandling(
                () -> "ok",
                "UNQ",
                () -> new RuntimeException("business")
        );
        assertEquals("ok", value);
    }

    @Test
    void executeWithConstraintHandling_supplier_variant_translates_violation() {
        class Biz extends RuntimeException { Biz(String m) { super(m); } }

        DataIntegrityViolationException ex = makeDataIntegrityViolation("UNQ");

        RuntimeException business = assertThrows(Biz.class, () ->
                DatabaseConstraintUtils.executeWithConstraintHandling(
                        () -> { throw ex; },
                        "UNQ",
                        () -> new Biz("dup")
                )
        );
        assertEquals("dup", business.getMessage());
    }

    @Test
    void executeWithConstraintHandling_factory_variant_translates_violation_with_details() {
        class Biz extends RuntimeException { Biz(String m) { super(m); } }

        DataIntegrityViolationException ex = makeDataIntegrityViolation("UNQ");

        RuntimeException business = assertThrows(Biz.class, () ->
                DatabaseConstraintUtils.executeWithConstraintHandling(
                        () -> { throw ex; },
                        "UNQ",
                        cve -> new Biz("dup-" + cve.getConstraintName())
                )
        );
        assertEquals("dup-UNQ", business.getMessage());
    }
}

