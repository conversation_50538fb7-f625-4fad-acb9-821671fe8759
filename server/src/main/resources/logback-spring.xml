<configuration>

    <springProperty name="springAppName" source="spring.application.name"/>

    <!-- Appender para salida asincrónica en STDOUT -->
    <appender name="ASYNC_STDOUT" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="STDOUT_JSON" />
    </appender>

<!--    <conversionRule conversionWord="jsonconverter" converterClass="org.vusecurity.logs.json.JSONConverter"/>-->

    <appender name="STDOUT_JSON" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <timeZone>UTC</timeZone>
                </timestamp>
                <pattern>
                    <pattern>
                        {"client": "%X{tenant}",
                        "level": "%level",
                        "message": "%message",
                        "traceId": "%X{traceId}",
                        "spanId": "%X{spanId}",
                        "parentSpanId": "%X{parentSpanId}",
                        "groupId":"${springAppName:-}",
                        "operationId":"%X{operationId:-}",
                        "userId": "%X{userId}",
                        "http_status":"%X{http_status:-}",
                        "srvRef":"%X{srvRef:-}",
                        "timeResponse":"%X{timeResponse:-}",
                        "pid":"${PID:-}",
                        "thread":"%thread",
                        "class":"%logger{40}"}
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <!-- Root logger -->
    <root level="INFO">
        <appender-ref ref="ASYNC_STDOUT" />
    </root>

</configuration>
