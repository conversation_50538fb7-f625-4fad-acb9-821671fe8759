package com.vusecurity.auth.scim.api.controller;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetDefinitionMappingJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.scim.api.handler.ScimMappersErrorControllerHandler;
import com.vusecurity.auth.shared.infrastructure.migration.initial.DataSeedConstants;
import com.vusecurity.auth.shared.test.BaseIntegrationTest;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.domain.enums.BusinessTypeEnum;
import com.vusecurity.business.domain.repositories.BusinessRepository;
import com.vusecurity.core.commons.Auditable.Status;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.Set;
import java.util.UUID;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.JsonPath;

import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@AutoConfigureWebMvc
@Import(ScimMappersErrorControllerHandler.class)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@Testcontainers
class ScimMappersControllerIT extends BaseIntegrationTest {

    @Autowired
    WebApplicationContext webApplicationContext;



    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    BusinessRepository businessRepository;

    @Autowired
    ClaimSetRepository claimSetRepository;

    @Autowired
    ClaimDefinitionRepository claimDefinitionRepository;
    
    @LocalServerPort
    int port;

    @Value("${app.scim.context-path}/scim/mappers")
    String basePath;

    private UUID businessId;
    private UUID claimSetId;
    private Business business;
    private MockMvc mockMvc;

    ClaimSetJpaEntity claimSet;
    ClaimDefinitionJpaEntity claimDefName;
    ClaimDefinitionJpaEntity claimDefExternalId;

    @BeforeEach
    void setUp() {
        // Set up MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        businessId = UUID.randomUUID();
        claimSetId = UUID.randomUUID();

        //Set up a new business
        business = new Business()
            .setId(businessId)
            .setBusinessType(BusinessTypeEnum.BUSINESS_UNIT)
            .setName("Test business")
            .setDescription("A Test business");
        business
            .setCreatedBy("TEST")
            .setStatus(Status.ACTIVE);
        
        businessRepository.save(business);

        //set up paths for a mapper.
        claimSet = new ClaimSetJpaEntity(claimSetId, business, AccountType.FEDERATED, false, "Scim test claimSet", "Claim set for testing Scim mappers");
        claimDefName = claimDefinitionRepository.findById(DataSeedConstants.CLAIM_DEF_SCIM_USER_NAME_ID).get();
        claimDefExternalId = claimDefinitionRepository.findById(DataSeedConstants.CLAIM_DEF_SCIM_EXTERNAL_ID).get();
        ClaimDefinitionJpaEntity claimDefFirstName = claimDefinitionRepository.findById(DataSeedConstants.CLAIM_DEF_FIRST_NAME_ID).get();

        claimSet.setClaimDefinitionMappings(Set.of(
            new ClaimSetDefinitionMappingJpaEntity(claimSet, claimDefName),
            new ClaimSetDefinitionMappingJpaEntity(claimSet, claimDefExternalId),
            new ClaimSetDefinitionMappingJpaEntity(claimSet, claimDefFirstName)
        ));

        claimSet
            .setCreatedBy("TEST")
            .setStatus(Status.ACTIVE);

        claimSetRepository.save(claimSet);
    }

    @Test
    void shouldCreateMapper() throws Exception {
        String mapperJson = """
            {
                "businessId": "%s",
                "filters": [
                    {
                    "claimSetIdOrigin": "%s",
                    "claimDefinitionId": "%s",
                    "filter": "userName"
                    },
                    {
                    "claimSetIdOrigin": "%s",
                    "claimDefinitionId": "%s",
                    "filter": "externalId"
                    }
                ]
            }
            """.formatted(
                businessId,
                claimSetId,
                DataSeedConstants.CLAIM_DEF_SCIM_USER_NAME_ID,
                claimSetId,
                DataSeedConstants.CLAIM_DEF_SCIM_EXTERNAL_ID);
        
        mockMvc.perform(MockMvcRequestBuilders.post(basePath)
            .contentType(MediaType.APPLICATION_JSON)
            .content(mapperJson))
            .andExpect(status().isCreated())
            .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.businessId")
                .value(businessId.toString()))
            .andExpect(jsonPath("$.filters").exists())
            .andExpect(jsonPath("$.filters[0].id").exists())
            .andExpect(jsonPath("$.filters[?(@.filter == 'userName')]").exists())
            .andExpect(jsonPath("$.filters[0].claimSetIdOrigin")
                .value(claimSetId.toString()))
            .andExpect(jsonPath("$.filters[?(@.claimDefinitionId == '"+DataSeedConstants.CLAIM_DEF_SCIM_USER_NAME_ID.toString()+"')]").exists())
            .andExpect(jsonPath("$.filters[1].id").exists())
            .andExpect(jsonPath("$.filters[?(@.filter == 'externalId')]").exists())
            .andExpect(jsonPath("$.filters[1].claimSetIdOrigin")
                .value(claimSetId.toString()))
            .andExpect(jsonPath("$.filters[?(@.claimDefinitionId == '"+DataSeedConstants.CLAIM_DEF_SCIM_EXTERNAL_ID.toString()+"')]").exists());
    }

    @Test
    void shouldGetValidMapper() throws Exception {
        String mapperJson = """
            {
                "businessId": "%s",
                "filters": [
                    {
                    "claimSetIdOrigin": "%s",
                    "claimDefinitionId": "%s",
                    "filter": "userName"
                    },
                    {
                    "claimSetIdOrigin": "%s",
                    "claimDefinitionId": "%s",
                    "filter": "externalId"
                    }
                ]
            }
            """.formatted(
                businessId,
                claimSetId,
                DataSeedConstants.CLAIM_DEF_SCIM_USER_NAME_ID,
                claimSetId,
                DataSeedConstants.CLAIM_DEF_SCIM_EXTERNAL_ID);
        
        mockMvc.perform(MockMvcRequestBuilders.post(basePath)
            .contentType(MediaType.APPLICATION_JSON)
            .content(mapperJson));

        mockMvc.perform(MockMvcRequestBuilders.get(basePath.concat("/business/").concat(businessId.toString()))
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.businessId")
                .value(businessId.toString()))
            .andExpect(jsonPath("$.filters").exists())
            .andExpect(jsonPath("$.filters[0].id").exists())
            .andExpect(jsonPath("$.filters[?(@.filter == 'userName')]").exists())
            .andExpect(jsonPath("$.filters[0].claimSetIdOrigin")
                .value(claimSetId.toString()))
            .andExpect(jsonPath("$.filters[?(@.claimDefinitionId == '"+DataSeedConstants.CLAIM_DEF_SCIM_USER_NAME_ID.toString()+"')]").exists())
            .andExpect(jsonPath("$.filters[1].id").exists())
            .andExpect(jsonPath("$.filters[?(@.filter == 'externalId')]").exists())
            .andExpect(jsonPath("$.filters[1].claimSetIdOrigin")
                .value(claimSetId.toString()))
            .andExpect(jsonPath("$.filters[?(@.claimDefinitionId == '"+DataSeedConstants.CLAIM_DEF_SCIM_EXTERNAL_ID.toString()+"')]").exists());
    }

    @Test
    void shouldGetValidFilter() throws Exception {
        String mapperJson = """
            {
                "businessId": "%s",
                "filters": [
                    {
                    "claimSetIdOrigin": "%s",
                    "claimDefinitionId": "%s",
                    "filter": "userName"
                    },
                    {
                    "claimSetIdOrigin": "%s",
                    "claimDefinitionId": "%s",
                    "filter": "externalId"
                    }
                ]
            }
            """.formatted(
                businessId,
                claimSetId,
                DataSeedConstants.CLAIM_DEF_SCIM_USER_NAME_ID,
                claimSetId,
                DataSeedConstants.CLAIM_DEF_SCIM_EXTERNAL_ID);
        
        MvcResult createResult = mockMvc.perform(MockMvcRequestBuilders.post(basePath)
            .contentType(MediaType.APPLICATION_JSON)
            .content(mapperJson)).andReturn();

        String id = JsonPath.read(createResult.getResponse().getContentAsString(), "$.filters[0].id");

        //Search
        mockMvc.perform(MockMvcRequestBuilders.get(basePath.concat("/filter/").concat(id))
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.businessId")
                .value(businessId.toString()))
            .andExpect(jsonPath("$.filters").exists())
            .andExpect(jsonPath("$.filters[0].id").exists())
            .andExpect(jsonPath("$.filters[0].filter").value("userName"))
            .andExpect(jsonPath("$.filters[0].claimSetIdOrigin")
                .value(claimSetId.toString()))
            .andExpect(jsonPath("$.filters[0].claimDefinitionId")
                .value(DataSeedConstants.CLAIM_DEF_SCIM_USER_NAME_ID.toString()));
    }

    @Test
    void shouldDeleteMapper() throws Exception {
        String mapperJson = """
            {
                "businessId": "%s",
                "filters": [
                    {
                    "claimSetIdOrigin": "%s",
                    "claimDefinitionId": "%s",
                    "filter": "userName"
                    },
                    {
                    "claimSetIdOrigin": "%s",
                    "claimDefinitionId": "%s",
                    "filter": "externalId"
                    }
                ]
            }
            """.formatted(
                businessId,
                claimSetId,
                DataSeedConstants.CLAIM_DEF_SCIM_USER_NAME_ID,
                claimSetId,
                DataSeedConstants.CLAIM_DEF_SCIM_EXTERNAL_ID);
        
        mockMvc.perform(MockMvcRequestBuilders.post(basePath)
            .contentType(MediaType.APPLICATION_JSON)
            .content(mapperJson));

        //Delete
        mockMvc.perform(MockMvcRequestBuilders.delete(basePath.concat("/business/").concat(businessId.toString()))
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        //Search
        mockMvc.perform(MockMvcRequestBuilders.get(basePath.concat("/business/").concat(businessId.toString()))
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().is5xxServerError());
            //For now tests throw 500 instead of 404 because they aren't loading the errorHandler for some unknown reason.
            //.andExpect(status().isNotFound());
    }

    @Test
    void shouldDeleteFilter() throws Exception {
        String mapperJson = """
            {
                "businessId": "%s",
                "filters": [
                    {
                    "claimSetIdOrigin": "%s",
                    "claimDefinitionId": "%s",
                    "filter": "userName"
                    },
                    {
                    "claimSetIdOrigin": "%s",
                    "claimDefinitionId": "%s",
                    "filter": "externalId"
                    },
                    {
                    "claimSetIdOrigin": "%s",
                    "claimDefinitionId": "%s",
                    "filter": "name.givenName"
                    }
                ]
            }
            """.formatted(
                businessId,
                claimSetId,
                DataSeedConstants.CLAIM_DEF_SCIM_USER_NAME_ID,
                claimSetId,
                DataSeedConstants.CLAIM_DEF_SCIM_EXTERNAL_ID,
                claimSetId,
                DataSeedConstants.CLAIM_DEF_FIRST_NAME_ID);
        
        MvcResult createResult = mockMvc.perform(MockMvcRequestBuilders.post(basePath)
            .contentType(MediaType.APPLICATION_JSON)
            .content(mapperJson)).andReturn();

        String id = JsonPath.read(createResult.getResponse().getContentAsString(), "$.filters[2].id");
        

        //Delete
        mockMvc.perform(MockMvcRequestBuilders.delete(basePath.concat("/filter/").concat(id))
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        //Search
        mockMvc.perform(MockMvcRequestBuilders.get(basePath.concat("/filter/").concat(id))
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().is5xxServerError());
            //For now tests throw 500 instead of 404 because they aren't loading the errorHandler for some unknown reason.
            //.andExpect(status().isNotFound());
    }
}