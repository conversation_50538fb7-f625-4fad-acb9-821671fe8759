# Example configuration for Auth Server Client SDK
auth:
  server:
    client:
      # Enable/disable the auto-configuration (default: true)
      enabled: true
      
      # Base URL of the Auth Server
      base-url: http://localhost:8082
      
      # API Key for authentication
      api-key: your-api-key-here
      
      # Context path for API endpoints (default: /api/v1)
      context-path: /api/v1
      
      # Connection timeout in milliseconds (default: 5000)
      connect-timeout: 5000
      
      # Read timeout in milliseconds (default: 10000)
      read-timeout: 10000
      
      # Maximum total connections (default: 50)
      max-connections: 50
      
      # Maximum connections per route (default: 10)
      max-connections-per-route: 10