package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.query.GetClaimDefinitionQuery;
import com.vusecurity.auth.claims.application.query.GetClaimDefinitionsPagedQuery;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class GetClaimDefinitionHandler implements GetClaimDefinitionQuery {

    private static final Logger logger = LoggerFactory.getLogger(GetClaimDefinitionHandler.class);
    private static final String DATA_TYPE_FIELD = "dataType";

    private final ClaimDefinitionRepository claimDefinitionRepository;

    @Override
    public ClaimDefinitionJpaEntity getClaimDefinitionById(UUID claimDefinitionId) {
        logger.debug("Getting claim definition by ID with claim sets: {}", claimDefinitionId);

        return claimDefinitionRepository.findByIdWithClaimSets(claimDefinitionId)
                .orElseThrow(() -> new IllegalArgumentException("Claim definition not found with ID: " + claimDefinitionId));
    }

    @Override
    public ClaimDefinitionJpaEntity getClaimDefinitionByCode(String code) {
        logger.debug("Getting claim definition by code: {}", code);
        
        return claimDefinitionRepository.findByCode(code)
                .orElseThrow(() -> new IllegalArgumentException("Claim definition not found with code: " + code));
    }

    @Override
    public Page<ClaimDefinitionJpaEntity> getAllClaimDefinitions(GetClaimDefinitionsPagedQuery query) {
        logger.debug("Getting all claim definitions with query: {}", query);

        // Convert 1-based page to 0-based for Spring Data
        int pageNumber = Math.max(0, query.getPage() - 1);
        Pageable pageable = PageRequest.of(pageNumber, query.getPageSize());

        // Build specification with complex filtering
        Specification<ClaimDefinitionJpaEntity> spec = buildSpecification(query);

        Page<ClaimDefinitionJpaEntity> claimDefinitions = claimDefinitionRepository.findAll(spec, pageable);

        // Load claim set mappings for each claim definition to avoid N+1 queries
        claimDefinitions.getContent().forEach(cd ->
            // This will trigger lazy loading of claim set mappings
            cd.getClaimSetMappings().size()
        );

        logger.debug("Found {} claim definitions", claimDefinitions.getTotalElements());
        return claimDefinitions;
    }

    @Override
    public List<ClaimDefinitionJpaEntity> getClaimDefinitionsByName(String name) {
        logger.debug("Getting claim definitions by name: {}", name);
        
        if (name == null || name.trim().isEmpty()) {
            return claimDefinitionRepository.findAll();
        }
        
        return claimDefinitionRepository.findByNameContaining(name);
    }

    @Override
    public List<ClaimDefinitionJpaEntity> getAllClaimDefinitionsByIds(List<UUID> listOfIds){
        return claimDefinitionRepository.findAllById(listOfIds);
    }



    /**
     * Builds a complex specification based on the query filters.
     * This replaces the original ClaimDefinitionSpecifications.withFilters method.
     */
    private Specification<ClaimDefinitionJpaEntity> buildSpecification(GetClaimDefinitionsPagedQuery query) {
        return (root, criteriaQuery, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (query.getClaimDefinitionId() != null) {
                predicates.add(cb.equal(root.get("id"), query.getClaimDefinitionId()));
            }

            if (query.getName() != null && !query.getName().trim().isEmpty()) {
                predicates.add(cb.like(cb.lower(root.get("name")), "%" + query.getName().toLowerCase() + "%"));
            }

            if (query.getCode() != null && !query.getCode().trim().isEmpty()) {
                predicates.add(cb.like(cb.lower(root.get("code")), "%" + query.getCode().toLowerCase() + "%"));
            }

            if (query.getDescription() != null && !query.getDescription().trim().isEmpty()) {
                predicates.add(cb.like(cb.lower(root.get("description")), "%" + query.getDescription().toLowerCase() + "%"));
            }

            if (query.getDataType() != null && !query.getDataType().trim().isEmpty()) {
                try {
                    DataTypeEnum dataTypeEnum = DataTypeEnum.valueOf(query.getDataType().toUpperCase());
                    predicates.add(cb.equal(root.get(DATA_TYPE_FIELD), dataTypeEnum));
                } catch (IllegalArgumentException e) {
                    // Invalid dataType value provided - ignore the filter
                    logger.warn("Invalid dataType value provided: {}", query.getDataType());
                }
            }

            // IsAList filter (based on dataType)
            if (query.getIsAList() != null) {
                if (query.getIsAList()) {
                    predicates.add(cb.equal(root.get(DATA_TYPE_FIELD), DataTypeEnum.ARRAY));
                } else {
                    predicates.add(cb.notEqual(root.get(DATA_TYPE_FIELD), DataTypeEnum.ARRAY));
                }
            }

            // General filter - search across multiple fields
            if (query.getFilter() != null && !query.getFilter().trim().isEmpty()) {
                String filterValue = "%" + query.getFilter().toLowerCase() + "%";
                Predicate namePredicate = cb.like(cb.lower(root.get("name")), filterValue);
                Predicate codePredicate = cb.like(cb.lower(root.get("code")), filterValue);
                Predicate descriptionPredicate = cb.like(cb.lower(root.get("description")), filterValue);

                predicates.add(cb.or(namePredicate, codePredicate, descriptionPredicate));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }


}
