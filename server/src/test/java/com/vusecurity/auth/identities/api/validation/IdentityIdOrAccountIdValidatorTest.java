package com.vusecurity.auth.identities.api.validation;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class IdentityIdOrAccountIdValidatorTest {

    static class SampleDto {
        public String identityId;
        public String accountId;
    }

    @Test
    void isValid_returns_true_when_identity_present() {
        IdentityIdOrAccountIdValidator v = new IdentityIdOrAccountIdValidator();
        v.initialize(new IdentityIdOrAccountIdRequired() {
            public String identityId() { return "identityId"; }
            public String accountId() { return "accountId"; }
            public String message() { return ""; }
            public Class<?>[] groups() { return new Class[0]; }
            public Class<? extends java.lang.annotation.Annotation> annotationType() { return IdentityIdOrAccountIdRequired.class; }
            public Class<? extends jakarta.validation.Payload>[] payload() { return new Class[0]; }
        });
        SampleDto dto = new SampleDto();
        dto.identityId = "abc";
        assertTrue(v.isValid(dto, null));
    }

    @Test
    void isValid_returns_true_when_account_present() {
        IdentityIdOrAccountIdValidator v = new IdentityIdOrAccountIdValidator();
        v.initialize(new IdentityIdOrAccountIdRequired() {
            public String identityId() { return "identityId"; }
            public String accountId() { return "accountId"; }
            public String message() { return ""; }
            public Class<?>[] groups() { return new Class[0]; }
            public Class<? extends java.lang.annotation.Annotation> annotationType() { return IdentityIdOrAccountIdRequired.class; }
            public Class<? extends jakarta.validation.Payload>[] payload() { return new Class[0]; }
        });
        SampleDto dto = new SampleDto();
        dto.accountId = "def";
        assertTrue(v.isValid(dto, null));
    }

    @Test
    void isValid_returns_false_when_both_missing() {
        IdentityIdOrAccountIdValidator v = new IdentityIdOrAccountIdValidator();
        v.initialize(new IdentityIdOrAccountIdRequired() {
            public String identityId() { return "identityId"; }
            public String accountId() { return "accountId"; }
            public String message() { return ""; }
            public Class<?>[] groups() { return new Class[0]; }
            public Class<? extends java.lang.annotation.Annotation> annotationType() { return IdentityIdOrAccountIdRequired.class; }
            public Class<? extends jakarta.validation.Payload>[] payload() { return new Class[0]; }
        });
        assertFalse(v.isValid(new SampleDto(), null));
    }

    @Test
    void isValid_returns_false_on_reflection_error() {
        IdentityIdOrAccountIdValidator v = new IdentityIdOrAccountIdValidator();
        v.initialize(new IdentityIdOrAccountIdRequired() {
            public String identityId() { return "missingField"; }
            public String accountId() { return "accountId"; }
            public String message() { return ""; }
            public Class<?>[] groups() { return new Class[0]; }
            public Class<? extends java.lang.annotation.Annotation> annotationType() { return IdentityIdOrAccountIdRequired.class; }
            public Class<? extends jakarta.validation.Payload>[] payload() { return new Class[0]; }
        });
        assertFalse(v.isValid(new SampleDto(), null));
    }
}

