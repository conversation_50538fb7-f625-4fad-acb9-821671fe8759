package com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import jakarta.persistence.*;

import java.util.Objects;
import java.util.UUID;

@Entity
@Table(name = "claim_set_definition_mapping",
        indexes = {
                @Index(name = "uk_claim_set_def_mapping_id", columnList = "id", unique = true),
                @Index(name = "uk_claim_set_def_mapping_composite",
                       columnList = "claim_set_id,claim_definition_id", unique = true)
        })
@SuppressWarnings("LombokGetterMayBeUsed, LombokSetterMayBeUsed")
public class ClaimSetDefinitionMappingJpaEntity extends AbstractEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "claim_set_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(name = "fk_claim_set_def_mapping_claim_set"),
            nullable = false
    )
    private ClaimSetJpaEntity claimSet;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "claim_definition_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(name = "fk_claim_set_def_mapping_claim_def"),
            nullable = false
    )
    private ClaimDefinitionJpaEntity claimDefinition;

    @Column(name = "claim_definition_order")
    private Integer claimDefinitionOrder;

    @Column(name = "enforce_uniqueness", nullable = false)
    private Boolean enforceUniqueness = false;

    // Constructors
    public ClaimSetDefinitionMappingJpaEntity() {
    }

    // Constructor with ID (for migration/test scenarios)
    public ClaimSetDefinitionMappingJpaEntity(UUID id, ClaimSetJpaEntity claimSet,
                                              ClaimDefinitionJpaEntity claimDefinition,
                                              Integer claimDefinitionOrder, Boolean enforceUniqueness) {
        this.setId(id);
        this.claimSet = claimSet;
        this.claimDefinition = claimDefinition;
        this.claimDefinitionOrder = claimDefinitionOrder;
        this.enforceUniqueness = Boolean.TRUE.equals(enforceUniqueness);
    }

    // Constructor without ID (for production use - JPA will generate ID)
    public ClaimSetDefinitionMappingJpaEntity(ClaimSetJpaEntity claimSet, ClaimDefinitionJpaEntity claimDefinition) {
        if (claimSet == null) {
            throw new IllegalArgumentException("claimSet cannot be null");
        }
        if (claimDefinition == null) {
            throw new IllegalArgumentException("claimDefinition cannot be null");
        }

        this.claimSet = claimSet;
        this.claimDefinition = claimDefinition;
        this.enforceUniqueness = false;
    }

    // Getters
    public ClaimSetJpaEntity getClaimSet() {
        return claimSet;
    }

    public ClaimDefinitionJpaEntity getClaimDefinition() {
        return claimDefinition;
    }

    public Integer getClaimDefinitionOrder() {
        return claimDefinitionOrder;
    }

    public Boolean getEnforceUniqueness() {
        return enforceUniqueness;
    }

    // Setters
    public void setClaimSet(ClaimSetJpaEntity claimSet) {
        this.claimSet = claimSet;
    }

    public void setClaimDefinition(ClaimDefinitionJpaEntity claimDefinition) {
        this.claimDefinition = claimDefinition;
    }

    public void setClaimDefinitionOrder(Integer claimDefinitionOrder) {
        this.claimDefinitionOrder = claimDefinitionOrder;
    }

    public void setEnforceUniqueness(Boolean enforceUniqueness) {
        this.enforceUniqueness = Boolean.TRUE.equals(enforceUniqueness);
    }

    // Helper methods
    public UUID getClaimSetId() {
        return claimSet != null ? claimSet.getId() : null;
    }

    public UUID getClaimDefinitionId() {
        return claimDefinition != null ? claimDefinition.getId() : null;
    }

    // equals() and hashCode() using composite business key
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ClaimSetDefinitionMappingJpaEntity that = (ClaimSetDefinitionMappingJpaEntity) o;
        return Objects.equals(getClaimSetId(), that.getClaimSetId()) &&
               Objects.equals(getClaimDefinitionId(), that.getClaimDefinitionId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getClaimSetId(), getClaimDefinitionId());
    }

    @Override
    public String toString() {
        return "ClaimSetDefinitionMappingJpaEntity{" +
                "id=" + getId() +
                ", claimSetId=" + getClaimSetId() +
                ", claimDefinitionId=" + getClaimDefinitionId() +
                ", claimDefinitionOrder=" + claimDefinitionOrder +
                ", enforceUniqueness=" + enforceUniqueness +
                '}';
    }
}
