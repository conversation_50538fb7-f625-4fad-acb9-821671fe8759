package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.UpdateClaimSetCommand;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.domain.repositories.BusinessRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class UpdateClaimSetHandler {

    private static final Logger logger = LoggerFactory.getLogger(UpdateClaimSetHandler.class);

    private final ClaimSetRepository claimSetRepository;
    private final BusinessRepository businessRepository;

    @Transactional
    public void handle(UpdateClaimSetCommand cmd) {
        validateCommand(cmd);
        updateClaimSet(cmd);
    }

    private void validateCommand(UpdateClaimSetCommand cmd) {
        cmd.validate();

        if (logger.isDebugEnabled()) {
            logger.debug("Validated UpdateClaimSetCommand {}", cmd);
        }
    }

    private void updateClaimSet(UpdateClaimSetCommand cmd) {
        if (logger.isDebugEnabled()) {
            logger.debug("Updating ClaimSet with command {}", cmd);
        }

        ClaimSetJpaEntity claimSet = claimSetRepository.findById(cmd.id())
                .orElseThrow(() -> new IllegalArgumentException("Claim set not found with ID: " + cmd.id()));

        // Determine the final values after the update
        var finalBusinessId = cmd.businessId() != null ? cmd.businessId() : claimSet.getBusinessId();
        var finalAccountType = cmd.accountType() != null ? cmd.accountType() : claimSet.getAccountType();
        var finalIsIdentifier = cmd.isIdentifier() != null ? cmd.isIdentifier() : claimSet.getIsIdentifier();

        // Validate business rule: only one identifier claim set per business/account type combination
        if (Boolean.TRUE.equals(finalIsIdentifier)) {
            var existingIdentifierClaimSet = claimSetRepository.findByBusinessIdAndAccountTypeAndIsIdentifier(
                    finalBusinessId, finalAccountType, true);
            if (existingIdentifierClaimSet.isPresent() && !existingIdentifierClaimSet.get().getId().equals(cmd.id())) {
                throw new IllegalArgumentException(
                        String.format("An identifier claim set already exists for business %s and account type %s",
                                finalBusinessId, finalAccountType));
            }
        }

        if (cmd.businessId() != null) {
            Business businessProxy = businessRepository.getReferenceById(cmd.businessId());
            claimSet.setBusiness(businessProxy);
        }

        if (cmd.accountType() != null) {
            claimSet.setAccountType(cmd.accountType());
        }

        if (cmd.isIdentifier() != null) {
            claimSet.setIsIdentifier(cmd.isIdentifier());
        }

        if (cmd.name() != null && !cmd.name().isBlank()) {
            claimSet.changeName(cmd.name());
        }

        if (cmd.description() != null) {
            claimSet.changeDescription(cmd.description());
        }

        if (cmd.lookupStrategy() != null) {
            claimSet.setLookupStrategy(cmd.lookupStrategy());
        }
    }
}