package com.vusecurity.auth.claims.mapper;

import com.vusecurity.auth.claims.application.command.AddClaimDefinitionsToClaimSetCommand;
import com.vusecurity.auth.claims.application.command.PatchClaimDefinitionsOnClaimSetCommand;
import com.vusecurity.auth.claims.application.command.ReplaceClaimDefinitionsInClaimSetCommand;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.service.ClaimSetDefinitionMappingService;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition.ClaimDefinitionSummaryResponse;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.ClaimDefinitionInClaimSetResponse;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetDefinitionMappingJpaEntity;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.AddClaimDefinitionsToClaimSetRequest;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.ClaimSetClaimDefinitionsResponse;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.ClaimSetResponse;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.PatchClaimDefinitionsOnClaimSetRequest;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.ReplaceClaimDefinitionsOnClaimSetRequest;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class ClaimSetDtoMapper {

    private final ClaimSetDefinitionMappingService mappingService;

    public ClaimSetResponse toResponse(ClaimSetJpaEntity entity) {
        if (entity == null) {
            return null;
        }

        ClaimSetResponse response = new ClaimSetResponse();
        response.setId(entity.getId());
        response.setBusinessId(entity.getBusinessId());
        response.setBusinessName(entity.getBusiness().getName());
        response.setAccountType(entity.getAccountType());
        response.setIsIdentifier(entity.getIsIdentifier());
        response.setName(entity.getName());
        response.setDescription(entity.getDescription());
        response.setLookupStrategy(entity.getLookupStrategy());

        // Set audit information
        response.setAudit(new BaseResponse.AuditInfo()
                .setCreatedAt(entity.getCreatedAt())
                .setUpdatedAt(entity.getUpdatedAt())
                .setCreatedBy(entity.getCreatedBy())
                .setUpdatedBy(entity.getUpdatedBy()));
        
        // Get claim definition mappings with order information
        try {
            List<ClaimSetDefinitionMappingJpaEntity> mappings = mappingService.getClaimDefinitionMappingsForClaimSet(entity);
            response.setClaimDefinitions(
                mappings.stream()
                    .map(this::toClaimDefinitionInClaimSetResponse)
                    .toList()
            );
        } catch (Exception e) {
            // If there's an error loading claim definitions, set empty list
            response.setClaimDefinitions(java.util.List.of());
        }

        return response;
    }



    /**
     * Converts a ClaimSetDefinitionMappingJpaEntity to ClaimDefinitionInClaimSetResponse.
     * @param mapping the mapping entity containing claim definition and order information
     * @return the claim definition response with order information
     */
    private ClaimDefinitionInClaimSetResponse toClaimDefinitionInClaimSetResponse(ClaimSetDefinitionMappingJpaEntity mapping) {
        if (mapping == null || mapping.getClaimDefinition() == null) {
            return null;
        }

        ClaimDefinitionJpaEntity entity = mapping.getClaimDefinition();
        ClaimDefinitionInClaimSetResponse response = new ClaimDefinitionInClaimSetResponse();
        response.setId(entity.getId());
        response.setCode(entity.getCode());
        response.setName(entity.getName());
        response.setDescription(entity.getDescription());
        response.setDataType(entity.getDataType());
        response.setDataFormat(entity.getDataFormat());
        response.setClaimDefinitionOrder(mapping.getClaimDefinitionOrder());
        response.setEnforceUniqueness(mapping.getEnforceUniqueness());
        return response;
    }

    public static AddClaimDefinitionsToClaimSetCommand toClaimDefinitionsToClaimSetCommand(UUID claimSetId, AddClaimDefinitionsToClaimSetRequest request) {
        List<AddClaimDefinitionsToClaimSetCommand.ClaimDefinitionsToClaimSetCommand> commandList = request.getClaimDefinitions()
                .stream()
                .map(def -> new AddClaimDefinitionsToClaimSetCommand.ClaimDefinitionsToClaimSetCommand(
                        def.getClaimDefinitionId(),
                        def.getClaimDefinitionOrder(),
                        def.getEnforceUniqueness()))
                .toList();

        return new AddClaimDefinitionsToClaimSetCommand(claimSetId, commandList);
    }

    public static ClaimSetClaimDefinitionsResponse toClaimDefinitionsToClaimSetCommandResponse(UUID claimSetId, List<AddClaimDefinitionsToClaimSetRequest.ClaimDefinitionsToClaimSetRequest> definitions) {
        List<ClaimSetClaimDefinitionsResponse.ClaimDefinitionsToClaimSetResponse> responseList = definitions.stream()
                .map(def -> {
                    ClaimSetClaimDefinitionsResponse.ClaimDefinitionsToClaimSetResponse response =
                            new ClaimSetClaimDefinitionsResponse.ClaimDefinitionsToClaimSetResponse()
                                    .setClaimDefinitionId(def.getClaimDefinitionId())
                                    .setClaimDefinitionOrder(def.getClaimDefinitionOrder());
                    Optional.ofNullable(def.getEnforceUniqueness())
                            .ifPresent(response::setEnforceUniqueness);

                    return response;
                })
                .toList();

        return new ClaimSetClaimDefinitionsResponse()
                .setClaimSetId(claimSetId)
                .setClaimDefinitions(responseList);
    }

    public static ReplaceClaimDefinitionsInClaimSetCommand toReplaceClaimDefinitionsToClaimSetCommand(UUID claimSetId, ReplaceClaimDefinitionsOnClaimSetRequest request) {
        List<ReplaceClaimDefinitionsInClaimSetCommand.ClaimDefinitionsToClaimSetCommand> commandList = request.getClaimDefinitions()
                .stream()
                .map(def -> new ReplaceClaimDefinitionsInClaimSetCommand.ClaimDefinitionsToClaimSetCommand(
                        def.getClaimDefinitionId(),
                        def.getClaimDefinitionOrder(),
                        def.getEnforceUniqueness()))
                .toList();

        return new ReplaceClaimDefinitionsInClaimSetCommand(claimSetId, commandList);
    }

    public static ClaimSetClaimDefinitionsResponse toReplaceClaimDefinitionsToClaimSetCommandResponse(UUID claimSetId, List<ReplaceClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest> definitions) {
        List<ClaimSetClaimDefinitionsResponse.ClaimDefinitionsToClaimSetResponse> responseList = definitions.stream()
                .map(def -> {
                    ClaimSetClaimDefinitionsResponse.ClaimDefinitionsToClaimSetResponse response =
                            new ClaimSetClaimDefinitionsResponse.ClaimDefinitionsToClaimSetResponse()
                                    .setClaimDefinitionId(def.getClaimDefinitionId())
                                    .setClaimDefinitionOrder(def.getClaimDefinitionOrder());
                    Optional.ofNullable(def.getEnforceUniqueness())
                            .ifPresent(response::setEnforceUniqueness);

                    return response;
                })
                .toList();

        return new ClaimSetClaimDefinitionsResponse()
                .setClaimSetId(claimSetId)
                .setClaimDefinitions(responseList);
    }

    public static PatchClaimDefinitionsOnClaimSetCommand toPatchClaimDefinitionsToClaimSetCommand(UUID claimSetId, PatchClaimDefinitionsOnClaimSetRequest request) {
        List<PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand> commandList = request.getClaimDefinitions() != null ?
                request.getClaimDefinitions().stream()
                        .map(def -> new PatchClaimDefinitionsOnClaimSetCommand.ClaimDefinitionAssignmentCommand(
                                def.getClaimDefinitionId(),
                                def.getClaimDefinitionOrder(),
                                def.isEnforceUniqueness()))
                        .toList() :
                List.of();

        return new PatchClaimDefinitionsOnClaimSetCommand(claimSetId, commandList);
    }

    public static ClaimSetClaimDefinitionsResponse toPatchClaimDefinitionsToClaimSetCommandResponse(UUID claimSetId, List<PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest> definitions) {
        List<ClaimSetClaimDefinitionsResponse.ClaimDefinitionsToClaimSetResponse> responseList = definitions != null ?
                definitions.stream()
                        .map(def -> new ClaimSetClaimDefinitionsResponse.ClaimDefinitionsToClaimSetResponse()
                                .setClaimDefinitionId(def.getClaimDefinitionId())
                                .setClaimDefinitionOrder(def.getClaimDefinitionOrder())
                                .setEnforceUniqueness(def.isEnforceUniqueness()))
                        .toList() :
                List.of();

        return new ClaimSetClaimDefinitionsResponse()
                .setClaimSetId(claimSetId)
                .setClaimDefinitions(responseList);
    }
}
