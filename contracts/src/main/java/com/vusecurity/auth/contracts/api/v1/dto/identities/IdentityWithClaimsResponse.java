package com.vusecurity.auth.contracts.api.v1.dto.identities;

import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.ClaimValueResponse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "Identity information withs claims response")
public class IdentityWithClaimsResponse extends IdentityResponse {
    
    @Schema(description = "List of Claims values associated with this identity")
    private List<ClaimValueResponse> claimValues;

    public IdentityWithClaimsResponse(IdentityResponse identityResponse, List<ClaimValueResponse> claimValues){
        this.setId(identityResponse.getId());
        this.setName(identityResponse.getName());
        this.setIdentityType(identityResponse.getIdentityType());
        this.setMetadata(identityResponse.getMetadata());
        this.setLifecycleState(identityResponse.getLifecycleState());
        this.setAccounts(identityResponse.getAccounts());
        this.claimValues = claimValues;
    }
}
