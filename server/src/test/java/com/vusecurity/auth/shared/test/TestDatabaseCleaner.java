package com.vusecurity.auth.shared.test;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * Utility class for cleaning up test database between tests.
 * This class provides methods to truncate tables in the correct order
 * to avoid foreign key constraint violations.
 */
@Component
public class TestDatabaseCleaner {

    private static final Logger logger = LoggerFactory.getLogger(TestDatabaseCleaner.class);

    @Autowired
    private DataSource dataSource;

    /**
     * Truncates all tables in the correct order to avoid foreign key violations.
     * This method discovers all tables dynamically and truncates them in dependency order.
     */
    public void cleanDatabase() {
        logger.debug("Starting database cleanup");

        try (Connection connection = dataSource.getConnection()) {
            // Disable foreign key checks temporarily
            disableForeignKeyChecks(connection);

            // Get all table names
            List<String> tableNames = getAllTableNames(connection);

            // Truncate all tables
            for (String tableName : tableNames) {
                truncateTable(connection, tableName);
            }

            // Re-enable foreign key checks
            enableForeignKeyChecks(connection);

            logger.debug("Database cleanup completed successfully");

        } catch (SQLException e) {
            logger.error("Error during database cleanup", e);
            throw new RuntimeException("Failed to clean database", e);
        }
    }

    /**
     * Truncates specific tables in the order provided.
     * Use this method when you want to clean only specific tables.
     */
    public void cleanTables(String... tableNames) {
        logger.debug("Cleaning specific tables: {}", String.join(", ", tableNames));

        try (Connection connection = dataSource.getConnection()) {
            disableForeignKeyChecks(connection);

            for (String tableName : tableNames) {
                truncateTable(connection, tableName);
            }

            enableForeignKeyChecks(connection);

            logger.debug("Specific table cleanup completed");

        } catch (SQLException e) {
            logger.error("Error during specific table cleanup", e);
            throw new RuntimeException("Failed to clean specific tables", e);
        }
    }

    /**
     * Performs a smart cleanup that truncates tables in dependency order.
     * This method is more efficient than cleanDatabase() as it preserves
     * the foreign key relationships and cleans in the correct order.
     */
    public void smartCleanDatabase() {
        logger.debug("Starting smart database cleanup");

        try (Connection connection = dataSource.getConnection()) {
            // Clean tables in dependency order (child tables first)
            String[] orderedTables = {
                // OAuth2 tables
                "oauth2_authorization_consent",
                "oauth2_authorization",
                "oauth2_authorized_client",

                // Claims tables (child tables first)
                "claim_set_claim_value",
                "claim_value",
                "claim_verification",
                "claim_set_definition_mapping",
                "claim_set",
                // KEEP seed claim definitions
                // "claim_definition",

                // Authorization join tables (must be cleaned before parent tables)
                "role_permission",           // Join table between role and permission
                "account_role",              // Join table between account and role
                "account_group_role",        // Join table between account_group and role
                "group_membership",          // Join table between account and account_group

                // Identity and authorization tables
                "account_group",
                "account",
                "identity_provider",
                "permission",
                "role",
                "oauth2_registered_client",

                // Business table (parent table)
                "business"
            };

            for (String tableName : orderedTables) {
                if (!tableExists(connection, tableName)) {
                    continue;
                }

                // Preserve seed rows for certain tables
                if ("business".equalsIgnoreCase(tableName)) {
                    deleteFromBusinessPreservingSeeds(connection);
                } else if ("identity_provider".equalsIgnoreCase(tableName)) {
                    deleteFromIdentityProviderPreservingSeeds(connection);
                } else {
                    deleteFromTable(connection, tableName);
                }
            }

            logger.debug("Smart database cleanup completed");

        } catch (SQLException e) {
            logger.error("Error during smart database cleanup", e);
            throw new RuntimeException("Failed to perform smart database cleanup", e);
        }
    }

    /**
     * Disables foreign key checks in SQL Server
     */
    private void disableForeignKeyChecks(Connection connection) throws SQLException {
        try (PreparedStatement statement = connection.prepareStatement("EXEC sp_MSforeachtable 'ALTER TABLE ? NOCHECK CONSTRAINT ALL'")) {
            statement.execute();
        }
    }

    /**
     * Enables foreign key checks in SQL Server
     */
    private void enableForeignKeyChecks(Connection connection) throws SQLException {
        try (PreparedStatement statement = connection.prepareStatement("EXEC sp_MSforeachtable 'ALTER TABLE ? CHECK CONSTRAINT ALL'")) {
            statement.execute();
        }
    }

    /**
     * Gets all table names from the database
     */
    private List<String> getAllTableNames(Connection connection) throws SQLException {
        List<String> tableNames = new ArrayList<>();

        String query = """
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_TYPE = 'BASE TABLE'
            AND TABLE_SCHEMA = 'dbo'
            AND TABLE_NAME NOT LIKE 'sys%'
            """;

        try (PreparedStatement statement = connection.prepareStatement(query);
             ResultSet resultSet = statement.executeQuery()) {

            while (resultSet.next()) {
                tableNames.add(resultSet.getString("TABLE_NAME"));
            }
        }

        return tableNames;
    }

    /**
     * Truncates a specific table
     */
    private void truncateTable(Connection connection, String tableName) throws SQLException {
        String sql = "TRUNCATE TABLE " + tableName;
        try (PreparedStatement statement = connection.prepareStatement(sql)) {
            statement.execute();
            logger.debug("Truncated table: {}", tableName);
        } catch (SQLException e) {
            // If truncate fails, try delete (some tables might have foreign key constraints)
            deleteFromTable(connection, tableName);
        }
    }

    /**
     * Delete from business preserving seed (system) business row
     */
    private void deleteFromBusinessPreservingSeeds(Connection connection) throws SQLException {
        String sql = "DELETE FROM business WHERE id <> ?";
        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            ps.setString(1, "d37ee3d4-22c6-41f7-819d-1464b3b9c454"); // DataSeedConstants.SYSTEM_BUSINESS_ID
            ps.executeUpdate();
            logger.debug("Deleted non-seed rows from business");
        }
    }

    /**
     * Delete from identity_provider preserving seed providers (LOCAL and SCIM)
     */
    private void deleteFromIdentityProviderPreservingSeeds(Connection connection) throws SQLException {
        String sql = "DELETE FROM identity_provider WHERE id NOT IN (?, ?)";
        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            ps.setString(1, "b3c1c3b0-303c-4a7a-8c43-7bdeb96ef558"); // LOCAL
            ps.setString(2, "55896ef7-303c-4a7a-8c43-bdc3b0b3c1eb"); // SCIM
            ps.executeUpdate();
            logger.debug("Deleted non-seed rows from identity_provider");
        }
    }
    private void deleteFromTable(Connection connection, String tableName) throws SQLException {
        String sql = "DELETE FROM " + tableName;
        try (PreparedStatement statement = connection.prepareStatement(sql)) {
            int deletedRows = statement.executeUpdate();
            logger.debug("Deleted {} rows from table: {}", deletedRows, tableName);
        }
    }

    /**
     * Checks if a table exists in the database
     */
    private boolean tableExists(Connection connection, String tableName) throws SQLException {
        String query = """
            SELECT COUNT(*)
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_NAME = ?
            AND TABLE_SCHEMA = 'dbo'
            """;

        try (PreparedStatement statement = connection.prepareStatement(query)) {
            statement.setString(1, tableName);
            try (ResultSet resultSet = statement.executeQuery()) {
                resultSet.next();
                return resultSet.getInt(1) > 0;
            }
        }
    }
}