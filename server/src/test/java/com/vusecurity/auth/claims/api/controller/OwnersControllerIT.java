package com.vusecurity.auth.claims.api.controller;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetDefinitionMappingJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetDefinitionMappingRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.contracts.enums.IdentityType;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityProviderRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityRepository;
import com.vusecurity.auth.shared.TestDataBuilder;
import com.vusecurity.auth.shared.test.BaseIntegrationTest;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.domain.enums.BusinessTypeEnum;
import com.vusecurity.business.domain.repositories.BusinessRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.context.WebApplicationContext;
import org.testcontainers.junit.jupiter.Testcontainers;

import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.setup.MockMvcBuilders.webAppContextSetup;

@AutoConfigureWebMvc
@Testcontainers
class OwnersControllerIT extends BaseIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private BusinessRepository businessRepository;

    @Autowired
    private IdentityRepository identityRepository;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private IdentityProviderRepository identityProviderRepository;

    @Autowired
    private ClaimDefinitionRepository claimDefinitionRepo;

    @Autowired
    private ClaimSetRepository claimSetRepo;

    @Autowired
    private ClaimSetDefinitionMappingRepository mappingRepo;

    @Autowired
    private ClaimValueRepository claimValueRepo;

    private MockMvc mockMvc;

    private Business business;
    private IdentityJpaEntity identity;
    private AccountJpaEntity account;
    private IdentityProviderJpaEntity identityProvider;
    private ClaimDefinitionJpaEntity stringClaimDef;
    private ClaimValueJpaEntity stringClaimValue;
    private ClaimValueJpaEntity stringIdentityClaimValue;
    private ClaimSetJpaEntity testClaimSet;

    @BeforeEach
    void setUp() {
        claimValueRepo.deleteAll();
        mappingRepo.deleteAll();
        claimSetRepo.deleteAll();
        claimDefinitionRepo.deleteAll();
        accountRepository.deleteAll();
        identityRepository.deleteAll();
        businessRepository.deleteAll();

        mockMvc = webAppContextSetup(webApplicationContext).build();

        business = new Business().setName("Test Business").setBusinessType(BusinessTypeEnum.BUSINESS_UNIT);
        business.setCreatedBy("test");
        business = businessRepository.save(business);

        identityProvider = identityProviderRepository.save(new IdentityProviderJpaEntity("Default"));
        identity = identityRepository.save(new IdentityJpaEntity(IdentityType.PERSON, "<EMAIL>"));
        account = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        accountRepository.save(account);

        // Create string claim definition
        stringClaimDef = TestDataBuilder.validClaimDefinition()
                .code("email")
                .name("Email Address")
                .description("Email address")
                .dataType(DataTypeEnum.STRING)
                .dataFormat("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
                .build();
        stringClaimDef = claimDefinitionRepo.save(stringClaimDef);

        // Create test claim set
        testClaimSet = TestDataBuilder.validClaimSet()
                .business(business)
                .accountType(AccountType.WORKFORCE)
                .name("Test ClaimSet")
                .build();
        testClaimSet = claimSetRepo.save(testClaimSet);

        // Create claim set mappings
        ClaimSetDefinitionMappingJpaEntity stringMapping = new ClaimSetDefinitionMappingJpaEntity();
        stringMapping.setClaimSet(testClaimSet);
        stringMapping.setClaimDefinition(stringClaimDef);
        stringMapping.setEnforceUniqueness(false);
        stringMapping.setClaimDefinitionOrder(1);
        mappingRepo.save(stringMapping);

        // Create identity test claim values
        stringIdentityClaimValue = TestDataBuilder.validClaimValue()
                .claimDefinition(stringClaimDef)
                .ownerId(identity.getId())
                .ownerType(OwnerType.IDENTITY)
                .value("<EMAIL>")
                .isPrimary(true)
                .source("USER_INPUT")
                .build();
        stringIdentityClaimValue = claimValueRepo.save(stringIdentityClaimValue);

        // Create account test claim values
        stringClaimValue = TestDataBuilder.validClaimValue()
                .claimDefinition(stringClaimDef)
                .ownerId(account.getId())
                .ownerType(OwnerType.ACCOUNT)
                .value("<EMAIL>")
                .isPrimary(true)
                .source("USER_INPUT")
                .build();
        stringClaimValue.setClaimSetClaimValue(new ClaimSetClaimValueJpaEntity(testClaimSet, stringClaimValue));
        stringClaimValue = claimValueRepo.save(stringClaimValue);

    }

    @Test
    void shouldReturnAllClaimsForIdentity() throws Exception {
        
        mockMvc.perform(get("/api/v1/owners/identity/" + identity.getId() + "/claims")
                    .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ownerId").value(identity.getId().toString()))
                .andExpect(jsonPath("$.ownerType").value(OwnerType.IDENTITY.toString()))
                .andExpect(jsonPath("$.claimValues", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.claimValues[0].id").value(stringIdentityClaimValue.getId().toString()))
                .andExpect(jsonPath("$.claimValues[0].claimDefinitionId").value(stringIdentityClaimValue.getClaimDefinition().getId().toString()))
                .andExpect(jsonPath("$.claimValues[0].value").value(stringIdentityClaimValue.getValue()));
    }

    @Test
    void shouldReturnAllClaimsForAccount() throws Exception {
        
        mockMvc.perform(get("/api/v1/owners/account/" + account.getId() + "/claims")
                    .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ownerId").value(account.getId().toString()))
                .andExpect(jsonPath("$.ownerType").value(OwnerType.ACCOUNT.toString()))
                .andExpect(jsonPath("$.claimValues", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$.claimValues[0].id").value(stringClaimValue.getId().toString()))
                .andExpect(jsonPath("$.claimValues[0].claimDefinitionId").value(stringClaimValue.getClaimDefinition().getId().toString()))
                .andExpect(jsonPath("$.claimValues[0].claimSetId").value(stringClaimValue.getClaimSetClaimValue().getClaimSetId().toString()))
                .andExpect(jsonPath("$.claimValues[0].value").value(stringClaimValue.getValue()));
    }
} 