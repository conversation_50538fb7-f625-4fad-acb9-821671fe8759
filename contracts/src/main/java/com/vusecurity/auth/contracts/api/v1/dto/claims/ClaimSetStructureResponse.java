package com.vusecurity.auth.contracts.api.v1.dto.claims;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;
import java.util.UUID;

@Schema(description = "Hierarchical structure of a ClaimSet with nested values")
public record ClaimSetStructureResponse(
    @Schema(description = "ClaimSet identifier")
    UUID claimSetId,
    
    @Schema(description = "ClaimSet name")
    String name,
    
    @Schema(description = "Nested structure of claim values")
    Map<String, Object> structure
) {}