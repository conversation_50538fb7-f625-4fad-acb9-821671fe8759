package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.DeleteClaimDefinitionCommand;
import com.vusecurity.auth.claims.application.exception.DeleteClaimTypeSystemException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.contracts.enums.ClaimType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DeleteClaimDefinitionHandlerTest {

    @Mock
    private ClaimDefinitionRepository claimDefinitionRepository;

    @InjectMocks
    private DeleteClaimDefinitionHandler handler;

    @Test
    void should_not_fail_when_deleting_definition_of_claimType_user() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        DeleteClaimDefinitionCommand cmd = new DeleteClaimDefinitionCommand(claimDefinitionId);

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.NUMERIC, null, ClaimType.USER_DEFINED);
        
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        assertDoesNotThrow(() -> handler.deleteClaimDefinition(cmd));
    }

    @Test
    void should_fail_when_deleting_definition_of_claimType_system() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();
        DeleteClaimDefinitionCommand cmd = new DeleteClaimDefinitionCommand(claimDefinitionId);

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.NUMERIC, null, ClaimType.SYSTEM_DEFINED);
        
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        assertThrows(DeleteClaimTypeSystemException.class, () -> handler.deleteClaimDefinition(cmd));
    }

    private ClaimDefinitionJpaEntity createClaimDefinition(UUID id, DataTypeEnum dataType, String dataFormat, ClaimType claimType) {
        ClaimDefinitionJpaEntity definition = new ClaimDefinitionJpaEntity();
        definition.setId(id);
        definition.setCode("test-code");
        definition.setName("Test Claim");
        definition.setDataType(dataType);
        definition.setDataFormat(dataFormat);
        definition.setClaimType(claimType);
        return definition;
    }
}