package com.vusecurity.auth.claims.application.query;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.UUID;

/**
 * Query interface for retrieving claim definitions.
 */
public interface GetClaimDefinitionQuery {
    
    /**
     * Get a claim definition by its ID.
     * @param claimDefinitionId the claim definition ID
     * @return the claim definition
     */
    ClaimDefinitionJpaEntity getClaimDefinitionById(UUID claimDefinitionId);
    
    /**
     * Get a claim definition by its code.
     * @param code the claim definition code
     * @return the claim definition
     */
    ClaimDefinitionJpaEntity getClaimDefinitionByCode(String code);
    
    /**
     * Get all claim definitions with pagination and complex filtering.
     * @param query the paged query parameters with filters
     * @return page of claim definitions
     */
    Page<ClaimDefinitionJpaEntity> getAllClaimDefinitions(GetClaimDefinitionsPagedQuery query);
    
    /**
     * Get claim definitions by name pattern.
     * @param name the name pattern
     * @return list of matching claim definitions
     */
    List<ClaimDefinitionJpaEntity> getClaimDefinitionsByName(String name);

    /**
     * Get claim definitions by a list of Ids.
     * @param listOfIds a list of UUIDs
     * @return list of matching claim definitions
     */
    List<ClaimDefinitionJpaEntity> getAllClaimDefinitionsByIds(List<UUID> listOfIds);
    

}
