/*
 * Flyway Migration: V1__baseline.sql
 * Purpose: Create the database schema.
 * Note: Database initial script.
 */

/********************************************************
SCHEMA
********************************************************/

CREATE TABLE [account](
	[id] [uniqueidentifier] NOT NULL,
	[business_id] [uniqueidentifier] NOT NULL,
	[identity_id] [uniqueidentifier] NOT NULL,
	[identity_provider_id] [uniqueidentifier] NOT NULL,
	[merge_primary] [uniqueidentifier] NULL,
	[merge_secondary] [uniqueidentifier] NULL,
	[account_type] [varchar](255) NOT NULL,
	[lifecycle_state] [varchar](255) NOT NULL,
	[status] [varchar](255) NOT NULL,
	[metadata] [varchar](255) NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
 CONSTRAINT [uk_account_id] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [uk_account_type_business_identity_provider_identity] UNIQUE NONCLUSTERED
(
	[account_type] ASC,
	[business_id] ASC,
	[identity_provider_id] ASC,
	[identity_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [account_group](
	[id] [uniqueidentifier] NOT NULL,
	[name] [varchar](255) NOT NULL,
	[description] [varchar](1024) NULL,
	[status] [varchar](255) NOT NULL,
	[metadata] [varchar](255) NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
 CONSTRAINT [uk_account_group_id] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [uk_account_group_name] UNIQUE NONCLUSTERED
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [account_group_role](
	[account_group_id] [uniqueidentifier] NOT NULL,
	[role_id] [uniqueidentifier] NOT NULL,
PRIMARY KEY CLUSTERED
(
	[account_group_id] ASC,
	[role_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [account_role](
	[account_id] [uniqueidentifier] NOT NULL,
	[role_id] [uniqueidentifier] NOT NULL,
PRIMARY KEY CLUSTERED
(
	[account_id] ASC,
	[role_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [business](
	[id] [uniqueidentifier] NOT NULL,
	[name] [varchar](255) NOT NULL,
	[business_type] [varchar](255) NOT NULL,
	[description] [varchar](255) NULL,
	[status] [varchar](255) NOT NULL,
	[logo] [varchar](max) NULL,
	[accent_color] [varchar](7) NULL,
	[metadata] [varchar](max) NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [UK5iyafjam5laj1n93tm5e4q7hm] UNIQUE NONCLUSTERED
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

CREATE TABLE [channel](
	[id] [uniqueidentifier] NOT NULL,
	[name] [varchar](255) NOT NULL,
	[channel_type] [varchar](255) NOT NULL,
	[description] [varchar](255) NULL,
	[status] [varchar](255) NOT NULL,
	[metadata] [varchar](255) NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
 CONSTRAINT [uk_channel_id] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [UK1r44jjdpx9o6wabic55qp3mgm] UNIQUE NONCLUSTERED
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [claim_definition](
	[id] [uniqueidentifier] NOT NULL,
	[code] [varchar](255) NOT NULL,
	[name] [varchar](255) NOT NULL,
	[description] [varchar](1024) NULL,
	[claim_type] [varchar](255) NOT NULL,
	[data_type] [varchar](255) NULL,
	[data_format] [varchar](1024) NULL,
	[is_a_list_of] [uniqueidentifier] NULL,
	[status] [varchar](255) NOT NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
 CONSTRAINT [uk_claim_def_id] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [uk_claim_def_code] UNIQUE NONCLUSTERED
(
	[code] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [claim_set](
	[id] [uniqueidentifier] NOT NULL,
	[business_id] [uniqueidentifier] NULL,
	[name] [varchar](255) NOT NULL,
	[description] [varchar](1024) NULL,
	[account_type] [varchar](255) NULL,
	[lookup_strategy] [varchar](255) NOT NULL,
	[is_identifier] [bit] NOT NULL,
	[status] [varchar](255) NOT NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
 CONSTRAINT [uk_claim_set_id] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [claim_set_claim_value](
	[id] [uniqueidentifier] NOT NULL,
	[claim_set_id] [uniqueidentifier] NOT NULL,
	[claim_value_id] [uniqueidentifier] NOT NULL,
	[status] [varchar](255) NOT NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [UKh0j87kkkhtu815tkdce3tmv6b] UNIQUE NONCLUSTERED
(
	[claim_value_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [claim_set_definition_mapping](
	[id] [uniqueidentifier] NOT NULL,
	[claim_set_id] [uniqueidentifier] NOT NULL,
	[claim_definition_id] [uniqueidentifier] NOT NULL,
	[claim_definition_order] [int] NULL,
	[enforce_uniqueness] [bit] NOT NULL,
	[status] [varchar](255) NOT NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
 CONSTRAINT [uk_claim_set_def_mapping_id] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [uk_claim_set_def_mapping_composite] UNIQUE NONCLUSTERED
(
	[claim_set_id] ASC,
	[claim_definition_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [claim_value](
	[id] [uniqueidentifier] NOT NULL,
	[claim_definition_id] [uniqueidentifier] NULL,
	[claim_verification_id] [uniqueidentifier] NULL,
	[owner_id] [uniqueidentifier] NOT NULL,
	[owner_type] [varchar](255) NOT NULL,
	[value] [varchar](1024) NULL,
	[source] [varchar](255) NULL,
	[is_computed] [bit] NOT NULL,
	[is_primary] [bit] NOT NULL,
	[primary_index] [int] NULL,
	[status] [varchar](255) NOT NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [claim_verification](
	[id] [uniqueidentifier] NOT NULL,
	[claim_value_id] [uniqueidentifier] NULL,
	[verification_type] [varchar](255) NOT NULL,
	[result] [varchar](255) NOT NULL,
	[status] [varchar](255) NOT NULL,
	[verified_at] [datetimeoffset](6) NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
 CONSTRAINT [uk_claim_verif_id] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [group_membership](
	[id] [uniqueidentifier] NOT NULL,
	[account_id] [uniqueidentifier] NOT NULL,
	[group_id] [uniqueidentifier] NOT NULL,
	[membership_role] [varchar](255) NULL,
	[is_active] [bit] NOT NULL,
	[status] [varchar](255) NOT NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
 CONSTRAINT [uk_group_membership_id] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [identities](
	[id] [uniqueidentifier] NOT NULL,
	[name] [varchar](255) NULL,
	[identity_type] [varchar](255) NOT NULL,
	[lifecycle_state] [varchar](255) NOT NULL,
	[merged_to] [uniqueidentifier] NULL,
	[status] [varchar](255) NOT NULL,
	[metadata] [varchar](255) NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
 CONSTRAINT [uk_identity_id] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [identity_provider](
	[id] [uniqueidentifier] NOT NULL,
	[name] [varchar](255) NOT NULL,
	[description] [varchar](1024) NULL,
	[status] [varchar](255) NOT NULL,
	[metadata] [varchar](255) NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
 CONSTRAINT [uk_identity_provider_id] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [uk_identity_provider_name] UNIQUE NONCLUSTERED
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [oauth2_authorization](
	[access_token_type] [varchar](100) NULL,
	[authorization_grant_type] [varchar](100) NOT NULL,
	[id] [varchar](100) NOT NULL,
	[registered_client_id] [varchar](100) NOT NULL,
	[principal_name] [varchar](200) NOT NULL,
	[state] [varchar](500) NULL,
	[access_token_scopes] [varchar](1000) NULL,
	[authorized_scopes] [varchar](1000) NULL,
	[access_token_expires_at] [varchar](255) NULL,
	[access_token_issued_at] [varchar](255) NULL,
	[authorization_code_expires_at] [varchar](255) NULL,
	[authorization_code_issued_at] [varchar](255) NULL,
	[device_code_expires_at] [varchar](255) NULL,
	[device_code_issued_at] [varchar](255) NULL,
	[oidc_id_token_expires_at] [varchar](255) NULL,
	[oidc_id_token_issued_at] [varchar](255) NULL,
	[refresh_token_expires_at] [varchar](255) NULL,
	[refresh_token_issued_at] [varchar](255) NULL,
	[user_code_expires_at] [varchar](255) NULL,
	[user_code_issued_at] [varchar](255) NULL,
	[access_token_metadata] [varchar](max) NULL,
	[access_token_value] [varchar](max) NULL,
	[attributes] [varchar](max) NULL,
	[authorization_code_metadata] [varchar](max) NULL,
	[authorization_code_value] [varchar](max) NULL,
	[device_code_metadata] [varchar](max) NULL,
	[device_code_value] [varchar](max) NULL,
	[oidc_id_token_metadata] [varchar](max) NULL,
	[oidc_id_token_value] [varchar](max) NULL,
	[refresh_token_metadata] [varchar](max) NULL,
	[refresh_token_value] [varchar](max) NULL,
	[user_code_metadata] [varchar](max) NULL,
	[user_code_value] [varchar](max) NULL,
PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

CREATE TABLE [oauth2_authorization_consent](
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[registered_client_id] [varchar](100) NOT NULL,
	[principal_name] [varchar](200) NOT NULL,
	[authorities] [varchar](1000) NOT NULL,
	[created_by] [varchar](255) NOT NULL,
	[status] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
PRIMARY KEY CLUSTERED
(
	[registered_client_id] ASC,
	[principal_name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [oauth2_authorized_client](
	[access_token_type] [varchar](100) NOT NULL,
	[client_registration_id] [varchar](100) NOT NULL,
	[principal_name] [varchar](200) NOT NULL,
	[access_token_scopes] [varchar](1000) NULL,
	[access_token_expires_at] [varchar](255) NOT NULL,
	[access_token_issued_at] [varchar](255) NOT NULL,
	[created_at] [varchar](255) NOT NULL,
	[refresh_token_issued_at] [varchar](255) NULL,
	[access_token_value] [varchar](max) NOT NULL,
	[refresh_token_value] [varchar](max) NULL,
PRIMARY KEY CLUSTERED
(
	[client_registration_id] ASC,
	[principal_name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

CREATE TABLE [oauth2_registered_client](
	[id] [varchar](100) NOT NULL,
	[client_id] [varchar](100) NOT NULL,
	[client_name] [varchar](200) NOT NULL,
	[client_secret] [varchar](200) NULL,
	[scopes] [varchar](1000) NOT NULL,
	[authorization_grant_types] [varchar](1000) NOT NULL,
	[client_authentication_methods] [varchar](1000) NOT NULL,
	[redirect_uris] [varchar](1000) NULL,
	[post_logout_redirect_uris] [varchar](1000) NULL,
	[access_token_time_to_live] [int] NULL,
	[authorization_code_time_to_live] [int] NULL,
	[device_code_time_to_live] [int] NULL,
	[refresh_token_time_to_live] [int] NULL,
	[require_authorization_consent] [bit] NULL,
	[require_proof_key] [bit] NULL,
	[reuse_refresh_tokens] [bit] NULL,
	[x509certificate_bound_access_tokens] [bit] NULL,
	[access_token_format] [varchar](255) NULL,
	[id_token_signature_algorithm] [varchar](255) NULL,
	[token_endpoint_authentication_signing_algorithm] [varchar](255) NULL,
	[jwk_set_url] [varchar](255) NULL,
	[x509certificate_subjectdn] [varchar](255) NULL,
	[status] [varchar](255) NOT NULL,
	[client_id_issued_at] [datetimeoffset](6) NOT NULL,
	[client_secret_expires_at] [datetimeoffset](6) NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [UKsdq6sc2ye4wfgtsgutyn67es5] UNIQUE NONCLUSTERED
(
	[client_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [permission](
	[id] [uniqueidentifier] NOT NULL,
	[name] [varchar](255) NULL,
	[description] [varchar](255) NULL,
	[status] [varchar](255) NOT NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [role](
	[id] [uniqueidentifier] NOT NULL,
	[business_id] [uniqueidentifier] NULL,
	[name] [varchar](255) NOT NULL,
	[description] [varchar](1000) NULL,
	[status] [varchar](255) NOT NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
 CONSTRAINT [uk_role_id] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [uk_role_name] UNIQUE NONCLUSTERED
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [role_permission](
	[permission_name] [uniqueidentifier] NOT NULL,
	[role_id] [uniqueidentifier] NOT NULL,
PRIMARY KEY CLUSTERED
(
	[permission_name] ASC,
	[role_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [scim_to_claim_mapping](
	[id] [uniqueidentifier] NOT NULL,
	[business_id] [uniqueidentifier] NOT NULL,
	[claim_set_origin] [uniqueidentifier] NULL,
	[claim_set_referenced] [uniqueidentifier] NULL,
	[claim_definition_referenced] [uniqueidentifier] NULL,
	[sequence_order] [int] NOT NULL,
	[filter_mapping] [varchar](255) NULL,
	[status] [varchar](255) NOT NULL,
	[created_at] [datetimeoffset](6) NOT NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[created_by] [varchar](255) NOT NULL,
	[updated_by] [varchar](255) NULL,
 CONSTRAINT [uk_scim_to_claim_mapping_id] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [version_server](
	[applied_at] [datetimeoffset](6) NULL,
	[updated_at] [datetimeoffset](6) NULL,
	[build_version] [varchar](255) NULL,
	[name] [varchar](255) NOT NULL,
	[version] [varchar](255) NOT NULL,
PRIMARY KEY CLUSTERED
(
	[name] ASC,
	[version] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [account] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [account_group] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [business] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [channel] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [claim_definition] ADD  DEFAULT ('USER_DEFINED') FOR [claim_type]
GO
ALTER TABLE [claim_definition] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [claim_set] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [claim_set_claim_value] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [claim_set_definition_mapping] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [claim_value] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [claim_verification] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [group_membership] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [identities] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [identity_provider] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [oauth2_authorization_consent] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [oauth2_registered_client] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [permission] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [role] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [scim_to_claim_mapping] ADD  DEFAULT ('ACTIVE') FOR [status]
GO
ALTER TABLE [account]  WITH CHECK ADD  CONSTRAINT [fk_account_business] FOREIGN KEY([business_id])
REFERENCES [business] ([id])
GO
ALTER TABLE [account] CHECK CONSTRAINT [fk_account_business]
GO
ALTER TABLE [account]  WITH CHECK ADD  CONSTRAINT [fk_account_identity_provider] FOREIGN KEY([identity_provider_id])
REFERENCES [identity_provider] ([id])
GO
ALTER TABLE [account] CHECK CONSTRAINT [fk_account_identity_provider]
GO
ALTER TABLE [account]  WITH CHECK ADD  CONSTRAINT [FK8lbmdi5hpc5okaeqm6cf97au4] FOREIGN KEY([identity_id])
REFERENCES [identities] ([id])
GO
ALTER TABLE [account] CHECK CONSTRAINT [FK8lbmdi5hpc5okaeqm6cf97au4]
GO
ALTER TABLE [account_group_role]  WITH CHECK ADD  CONSTRAINT [fk_account_group_role_group] FOREIGN KEY([account_group_id])
REFERENCES [account_group] ([id])
GO
ALTER TABLE [account_group_role] CHECK CONSTRAINT [fk_account_group_role_group]
GO
ALTER TABLE [account_group_role]  WITH CHECK ADD  CONSTRAINT [fk_account_group_role_role] FOREIGN KEY([role_id])
REFERENCES [role] ([id])
GO
ALTER TABLE [account_group_role] CHECK CONSTRAINT [fk_account_group_role_role]
GO
ALTER TABLE [account_role]  WITH CHECK ADD  CONSTRAINT [fk_account_role_account] FOREIGN KEY([account_id])
REFERENCES [account] ([id])
GO
ALTER TABLE [account_role] CHECK CONSTRAINT [fk_account_role_account]
GO
ALTER TABLE [account_role]  WITH CHECK ADD  CONSTRAINT [fk_account_role_role] FOREIGN KEY([role_id])
REFERENCES [role] ([id])
GO
ALTER TABLE [account_role] CHECK CONSTRAINT [fk_account_role_role]
GO
ALTER TABLE [claim_definition]  WITH CHECK ADD  CONSTRAINT [FK21ncn2cgn2k5cglv5yi1wt0oa] FOREIGN KEY([is_a_list_of])
REFERENCES [claim_definition] ([id])
GO
ALTER TABLE [claim_definition] CHECK CONSTRAINT [FK21ncn2cgn2k5cglv5yi1wt0oa]
GO
ALTER TABLE [claim_set]  WITH CHECK ADD  CONSTRAINT [fk_claim_set_business] FOREIGN KEY([business_id])
REFERENCES [business] ([id])
GO
ALTER TABLE [claim_set] CHECK CONSTRAINT [fk_claim_set_business]
GO
ALTER TABLE [claim_set_claim_value]  WITH CHECK ADD  CONSTRAINT [fk_claim_set_claim_value_claim_set] FOREIGN KEY([claim_set_id])
REFERENCES [claim_set] ([id])
GO
ALTER TABLE [claim_set_claim_value] CHECK CONSTRAINT [fk_claim_set_claim_value_claim_set]
GO
ALTER TABLE [claim_set_claim_value]  WITH CHECK ADD  CONSTRAINT [fk_claim_value_claim_set_claim_value] FOREIGN KEY([claim_value_id])
REFERENCES [claim_value] ([id])
GO
ALTER TABLE [claim_set_claim_value] CHECK CONSTRAINT [fk_claim_value_claim_set_claim_value]
GO
ALTER TABLE [claim_set_definition_mapping]  WITH CHECK ADD  CONSTRAINT [fk_claim_set_def_mapping_claim_def] FOREIGN KEY([claim_definition_id])
REFERENCES [claim_definition] ([id])
GO
ALTER TABLE [claim_set_definition_mapping] CHECK CONSTRAINT [fk_claim_set_def_mapping_claim_def]
GO
ALTER TABLE [claim_set_definition_mapping]  WITH CHECK ADD  CONSTRAINT [fk_claim_set_def_mapping_claim_set] FOREIGN KEY([claim_set_id])
REFERENCES [claim_set] ([id])
GO
ALTER TABLE [claim_set_definition_mapping] CHECK CONSTRAINT [fk_claim_set_def_mapping_claim_set]
GO
ALTER TABLE [claim_value]  WITH CHECK ADD  CONSTRAINT [fk_claim_value_definition] FOREIGN KEY([claim_definition_id])
REFERENCES [claim_definition] ([id])
GO
ALTER TABLE [claim_value] CHECK CONSTRAINT [fk_claim_value_definition]
GO
ALTER TABLE [claim_value]  WITH CHECK ADD  CONSTRAINT [FKh8keiwr0ro9djgg7y4jaujils] FOREIGN KEY([claim_verification_id])
REFERENCES [claim_verification] ([id])
GO
ALTER TABLE [claim_value] CHECK CONSTRAINT [FKh8keiwr0ro9djgg7y4jaujils]
GO
ALTER TABLE [claim_verification]  WITH CHECK ADD  CONSTRAINT [FK7rx9u0nn8wwubh3sceyoyvnwg] FOREIGN KEY([claim_value_id])
REFERENCES [claim_value] ([id])
GO
ALTER TABLE [claim_verification] CHECK CONSTRAINT [FK7rx9u0nn8wwubh3sceyoyvnwg]
GO
ALTER TABLE [group_membership]  WITH CHECK ADD  CONSTRAINT [fk_group_membership_account] FOREIGN KEY([account_id])
REFERENCES [account] ([id])
GO
ALTER TABLE [group_membership] CHECK CONSTRAINT [fk_group_membership_account]
GO
ALTER TABLE [group_membership]  WITH CHECK ADD  CONSTRAINT [fk_group_membership_group] FOREIGN KEY([group_id])
REFERENCES [account_group] ([id])
GO
ALTER TABLE [group_membership] CHECK CONSTRAINT [fk_group_membership_group]
GO
ALTER TABLE [role_permission]  WITH CHECK ADD  CONSTRAINT [FKa6jx8n8xkesmjmv6jqug6bg68] FOREIGN KEY([role_id])
REFERENCES [role] ([id])
GO
ALTER TABLE [role_permission] CHECK CONSTRAINT [FKa6jx8n8xkesmjmv6jqug6bg68]
GO
ALTER TABLE [role_permission]  WITH CHECK ADD  CONSTRAINT [FKldkc3yoh80x16gv94519otli4] FOREIGN KEY([permission_name])
REFERENCES [permission] ([id])
GO
ALTER TABLE [role_permission] CHECK CONSTRAINT [FKldkc3yoh80x16gv94519otli4]
GO
ALTER TABLE [account]  WITH CHECK ADD CHECK  (([account_type]='SERVICE' OR [account_type]='WORKFORCE' OR [account_type]='CUSTOMER' OR [account_type]='SOCIAL' OR [account_type]='FEDERATED' OR [account_type]='LOCAL' OR [account_type]='NONE'))
GO
ALTER TABLE [account]  WITH CHECK ADD CHECK  (([lifecycle_state]='DELETED' OR [lifecycle_state]='DEACTIVATED' OR [lifecycle_state]='SUSPENDED' OR [lifecycle_state]='ACTIVE' OR [lifecycle_state]='PENDING' OR [lifecycle_state]='PROVISIONED' OR [lifecycle_state]='NONE'))
GO
ALTER TABLE [account]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [account_group]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [business]  WITH CHECK ADD CHECK  (([business_type]='BUSINESS_UNIT' OR [business_type]='LEGAL_ENTITY' OR [business_type]='TENANT'))
GO
ALTER TABLE [business]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [channel]  WITH CHECK ADD CHECK  (([channel_type]='DEFAULT' OR [channel_type]='AUTOMATION' OR [channel_type]='ON_SITE' OR [channel_type]='API' OR [channel_type]='POS' OR [channel_type]='ATM' OR [channel_type]='MOBILE_APP' OR [channel_type]='WEB'))
GO
ALTER TABLE [channel]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [claim_definition]  WITH CHECK ADD CHECK  (([claim_type]='SYSTEM_DEFINED' OR [claim_type]='USER_DEFINED'))
GO
ALTER TABLE [claim_definition]  WITH CHECK ADD CHECK  (([data_type]='DATETIME' OR [data_type]='DATE' OR [data_type]='ARRAY' OR [data_type]='IMAGE' OR [data_type]='BOOL' OR [data_type]='NUMERIC' OR [data_type]='STRING'))
GO
ALTER TABLE [claim_definition]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [claim_set]  WITH CHECK ADD CHECK  (([account_type]='SERVICE' OR [account_type]='WORKFORCE' OR [account_type]='CUSTOMER' OR [account_type]='SOCIAL' OR [account_type]='FEDERATED' OR [account_type]='LOCAL' OR [account_type]='NONE'))
GO
ALTER TABLE [claim_set]  WITH CHECK ADD CHECK  (([lookup_strategy]='ANY_CLAIM_CAN_MATCH' OR [lookup_strategy]='ALL_CLAIMS_MUST_MATCH'))
GO
ALTER TABLE [claim_set]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [claim_set_claim_value]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [claim_set_definition_mapping]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [claim_value]  WITH CHECK ADD CHECK  (([owner_type]='ACCOUNT' OR [owner_type]='IDENTITY'))
GO
ALTER TABLE [claim_value]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [claim_verification]  WITH CHECK ADD CHECK  (([result]='FAIL' OR [result]='OK' OR [result]='PENDING' OR [result]='NONE'))
GO
ALTER TABLE [claim_verification]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [group_membership]  WITH CHECK ADD CHECK  (([membership_role]='MEMBER' OR [membership_role]='OWNER'))
GO
ALTER TABLE [group_membership]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [identities]  WITH CHECK ADD CHECK  (([identity_type]='ORGANIZATION' OR [identity_type]='SERVICE' OR [identity_type]='DEVICE' OR [identity_type]='FEDERATED' OR [identity_type]='PERSON' OR [identity_type]='NONE'))
GO
ALTER TABLE [identities]  WITH CHECK ADD CHECK  (([lifecycle_state]='TERMINATED' OR [lifecycle_state]='DELETED' OR [lifecycle_state]='INACTIVE' OR [lifecycle_state]='DEACTIVATED' OR [lifecycle_state]='BLOCKED' OR [lifecycle_state]='SUSPENDED' OR [lifecycle_state]='ACTIVE' OR [lifecycle_state]='PENDING' OR [lifecycle_state]='REGISTERED' OR [lifecycle_state]='NONE'))
GO
ALTER TABLE [identities]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [identity_provider]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [oauth2_authorization_consent]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [oauth2_registered_client]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [permission]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [role]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
ALTER TABLE [scim_to_claim_mapping]  WITH CHECK ADD CHECK  (([status]='DELETED' OR [status]='INACTIVE' OR [status]='ACTIVE'))
GO
