package com.vusecurity.auth.shared.health;

import com.vusecurity.auth.AppConfigProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.actuate.health.Health;

import static org.junit.jupiter.api.Assertions.*;

class ApplicationHealthIndicatorTest {

    private AppConfigProperties props;

    @BeforeEach
    void setUp() {
        props = new AppConfigProperties();
        props.setVersion("1.2.3");
        props.setBuild("42");
        props.setDate(1700000000000L);
        props.setApikey("test");
    }

    @Test
    void health_should_include_version_and_build_details() {
        // Given
        ApplicationHealthIndicator indicator = new ApplicationHealthIndicator(props);

        // When
        Health health = indicator.health();

        // Then
        assertEquals("UP", health.getStatus().getCode());
        assertEquals("VU One Auth Server", health.getDetails().get("application"));
        assertEquals("1.2.3", health.getDetails().get("version"));
        assertEquals("42", health.getDetails().get("build"));
    }
}

