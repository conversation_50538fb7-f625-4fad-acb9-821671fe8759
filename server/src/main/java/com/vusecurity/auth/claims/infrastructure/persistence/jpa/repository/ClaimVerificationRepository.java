package com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimVerificationJpaEntity;
import com.vusecurity.auth.shared.enums.ResultType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Spring Data JPA repository for ClaimVerificationJpaEntity.
 * Infrastructure-only interface for database operations.
 */
@Repository
public interface ClaimVerificationRepository extends JpaRepository<ClaimVerificationJpaEntity, UUID>, JpaSpecificationExecutor<ClaimVerificationJpaEntity> {

    /**
     * Find claim verification by ID.
     * @param id the claim verification ID
     * @return the claim verification if found
     */
    Optional<ClaimVerificationJpaEntity> findById(UUID id);

    /**
     * Find all verifications for a specific claim value.
     * @param claimValueId the claim value ID
     * @return list of verifications for the claim value
     */
    @Query("""
            SELECT cv
              FROM ClaimVerificationJpaEntity cv
             WHERE cv.claimValue.id = :claimValueId
             ORDER BY cv.verifiedAt DESC
            """)
    List<ClaimVerificationJpaEntity> findByClaimValueId(@Param("claimValueId") UUID claimValueId);

    /**
     * Find the latest verification for a specific claim value.
     * @param claimValueId the claim value ID
     * @return the latest verification if found
     */
    @Query("""
            SELECT cv
              FROM ClaimVerificationJpaEntity cv
             WHERE cv.claimValue.id = :claimValueId
               AND cv.verifiedAt IS NOT NULL
             ORDER BY cv.verifiedAt DESC
             LIMIT 1
            """)
    Optional<ClaimVerificationJpaEntity> findLatestByClaimValueId(@Param("claimValueId") UUID claimValueId);

    /**
     * Find verifications by result type for a specific claim value.
     * @param claimValueId the claim value ID
     * @param result the result type
     * @return list of verifications with the specified result
     */
    @Query("""
            SELECT cv
              FROM ClaimVerificationJpaEntity cv
             WHERE cv.claimValue.id = :claimValueId
               AND cv.result = :result
             ORDER BY cv.verifiedAt DESC
            """)
    List<ClaimVerificationJpaEntity> findByClaimValueIdAndResult(@Param("claimValueId") UUID claimValueId, 
                                                                 @Param("result") ResultType result);

    /**
     * Find verifications by verification type for a specific claim value.
     * @param claimValueId the claim value ID
     * @param verificationType the verification type
     * @return list of verifications with the specified type
     */
    @Query("""
            SELECT cv
              FROM ClaimVerificationJpaEntity cv
             WHERE cv.claimValue.id = :claimValueId
               AND cv.verificationType = :verificationType
             ORDER BY cv.verifiedAt DESC
            """)
    List<ClaimVerificationJpaEntity> findByClaimValueIdAndVerificationType(@Param("claimValueId") UUID claimValueId, 
                                                                           @Param("verificationType") String verificationType);

    /**
     * Check if a claim value has any successful verifications.
     * @param claimValueId the claim value ID
     * @return true if there are successful verifications
     */
    @Query("""
            SELECT COUNT(cv) > 0
              FROM ClaimVerificationJpaEntity cv
             WHERE cv.claimValue.id = :claimValueId
               AND cv.result = com.vusecurity.auth.shared.enums.ResultType.OK
            """)
    boolean hasSuccessfulVerification(@Param("claimValueId") UUID claimValueId);

    /**
     * Find all verifications for claim values owned by a specific account.
     * @param accountId the account ID
     * @return list of verifications for the account's claim values
     */
    @Query("""
            SELECT cv
              FROM ClaimVerificationJpaEntity cv
              JOIN cv.claimValue claimValue
             WHERE claimValue.ownerType = com.vusecurity.auth.contracts.enums.OwnerType.ACCOUNT
               AND claimValue.ownerId = :accountId
             ORDER BY cv.verifiedAt DESC
            """)
    List<ClaimVerificationJpaEntity> findByAccountId(@Param("accountId") UUID accountId);
}
