package com.vusecurity.auth;

import org.springframework.boot.actuate.info.Info;
import org.springframework.boot.actuate.info.InfoContributor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Custom info contributor that adds application-specific information to the /actuator/info endpoint.
 */
@Component
public class AppInfoContributor implements InfoContributor {

    private final AppConfigProperties appConfigProperties;

    public AppInfoContributor(AppConfigProperties appConfigProperties) {
        this.appConfigProperties = appConfigProperties;
    }

    @Override
    public void contribute(Info.Builder builder) {
        builder.withDetail("app", getAppVersion());

        // Add additional runtime information
        builder.withDetail("runtime", buildRuntimeInfo());
    }

    private Map<String, Object> getAppVersion() {
        Map<String, Object> appVersion = new HashMap<>();
        appVersion.put("version", appConfigProperties.getVersion());
        appVersion.put("build", appConfigProperties.getBuild());
        appVersion.put("date", appConfigProperties.getDate());
        return appVersion;
    }
    
    private Object buildRuntimeInfo() {
        return new RuntimeInfo(
                System.getProperty("java.version"),
                Runtime.getRuntime().availableProcessors(),
                Runtime.getRuntime().maxMemory() / (1024 * 1024),
                Runtime.getRuntime().totalMemory() / (1024 * 1024),
                Runtime.getRuntime().freeMemory() / (1024 * 1024)
        );
    }
    
    /**
     * Inner class to represent runtime information.
     */
    private static class RuntimeInfo {
        private final String javaVersion;
        private final int availableProcessors;
        private final long maxMemoryMB;
        private final long totalMemoryMB;
        private final long freeMemoryMB;
        
        public RuntimeInfo(String javaVersion, int availableProcessors, long maxMemoryMB, 
                          long totalMemoryMB, long freeMemoryMB) {
            this.javaVersion = javaVersion;
            this.availableProcessors = availableProcessors;
            this.maxMemoryMB = maxMemoryMB;
            this.totalMemoryMB = totalMemoryMB;
            this.freeMemoryMB = freeMemoryMB;
        }
        
        public String getJavaVersion() {
            return javaVersion;
        }
        
        public int getAvailableProcessors() {
            return availableProcessors;
        }
        
        public long getMaxMemoryMB() {
            return maxMemoryMB;
        }
        
        public long getTotalMemoryMB() {
            return totalMemoryMB;
        }
        
        public long getFreeMemoryMB() {
            return freeMemoryMB;
        }
    }
}
