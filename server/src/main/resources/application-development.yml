# Development profile configuration
# This profile is intended for local development environments only

app:
  dataseed:
    enabled: false

# Flyway configuration for development
flyway:
  locations: classpath:db/migration
  default-schema: dbo
  baseline-on-migrate: true
  baseline-version: 1
  validate-on-migrate: true
  clean-disabled: true
  debug: true

logging:
  level:
    # Enable debug logging for migration services in development
    com.vusecurity.auth.shared.infrastructure.migration: DEBUG
    # Enable trace logging for setup services to see detailed seeding process
    com.vusecurity.auth.shared.infrastructure.migration.initial: TRACE

# Development-specific Hibernate settings
hibernate:
  show_sql: true
  hbm2ddl.auto: validate

spring:
  jpa:
    hibernate:
      ddl-auto: validate
    generate-ddl: false
