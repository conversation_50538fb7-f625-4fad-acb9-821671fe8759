package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.ClaimSetAssignmentCommand;
import com.vusecurity.auth.claims.application.command.PatchClaimDefinitionsOnClaimSetCommand;
import com.vusecurity.auth.claims.application.command.PatchClaimSetsOnClaimDefinitionCommand;
import com.vusecurity.auth.claims.application.exception.ClaimSetNotFoundException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.service.ClaimSetDefinitionMappingService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PatchClaimSetsOnClaimDefinitionHandler {
    private static final Logger logger = LoggerFactory.getLogger(PatchClaimSetsOnClaimDefinitionHandler.class);

    private final ClaimDefinitionRepository claimDefinitionRepository;
    private final ClaimSetRepository claimSetRepository;
    private final ClaimSetDefinitionMappingService mappingService;

    @Transactional
    public void patchClaimSetsOnClaimDefinition(PatchClaimSetsOnClaimDefinitionCommand command) {
        logger.debug("Patching claim sets on claim definition {} with {}", command.claimDefinitionId(), command.claimSetsAssignment());

        // Validate command
        command.validate();

        // Find the claim definition
        ClaimDefinitionJpaEntity claimDefinition = claimDefinitionRepository.getReferenceById(command.claimDefinitionId());

        // If no claim sets provided, nothing to do (valid PATCH operation)
        if (command.claimSetsAssignment().isEmpty()) {
            logger.debug("No claim sets provided in patch request, no changes made");
            return;
        }

        // Extract claim set IDs for validation
        Set<UUID> claimSetIds = command.claimSetsAssignment().stream()
                .map(ClaimSetAssignmentCommand::claimSetId)
                .collect(Collectors.toSet());

        // Validate that all claim sets exist
        long foundCount = claimSetRepository.countByIdIn(claimSetIds);
        if (foundCount != claimSetIds.size()) {
            throw new ClaimSetNotFoundException("One or more claim sets not found");
        }

        // Deduplicate assignments - keep last occurrence of each claimDefinitionId
        List<ClaimSetAssignmentCommand> deduplicatedAssignments = getDeduplicatedAssignments(command);

        // Use batch operation
        mappingService.batchAddClaimSetsToClaimDefinition(claimDefinition, claimSetIds, deduplicatedAssignments);

        logger.debug("Successfully patched claim sets on claim definition: {}", command.claimDefinitionId());
    }

    private static List<ClaimSetAssignmentCommand> getDeduplicatedAssignments(PatchClaimSetsOnClaimDefinitionCommand command) {
        Map<UUID, ClaimSetAssignmentCommand> deduplicatedMap = new HashMap<>();
        for (ClaimSetAssignmentCommand assignment : command.claimSetsAssignment()) {
            deduplicatedMap.put(assignment.claimSetId(), assignment);
        }
        return new ArrayList<>(deduplicatedMap.values());
    }
} 