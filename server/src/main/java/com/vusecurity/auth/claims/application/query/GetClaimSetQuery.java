package com.vusecurity.auth.claims.application.query;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.UUID;

public interface GetClaimSetQuery {
    ClaimSetJpaEntity getClaimSetById(UUID id);
    ClaimSetJpaEntity getClaimSetByClaimSetId(UUID claimSetId);
    List<ClaimSetJpaEntity> getAllClaimSetsByIds(List<UUID> claimSetIdList);
    Page<ClaimSetJpaEntity> getAllClaimSets(GetClaimSetsPagedQuery query);
}
