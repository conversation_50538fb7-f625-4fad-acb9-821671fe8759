package com.vusecurity.auth.claims;

import com.vusecurity.auth.claims.application.command.CreateClaimValueCommand;
import com.vusecurity.auth.claims.application.command.UpdateClaimValueCommand;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify that Object values work correctly in claim value commands
 */
class ObjectValueTest {

    @Test
    void createClaimValueCommand_shouldAcceptStringValue() {
        CreateClaimValueCommand command = new CreateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "<PERSON>",
                true,
                false,
                "USER_INPUT"
        );

        assertDoesNotThrow(command::validate);
        assertEquals("<PERSON>", command.convertValueToString());
    }

    @Test
    void createClaimValueCommand_shouldAcceptComplexObjectValue() {
        Map<String, Object> complexValue = new HashMap<>();
        complexValue.put("claimSetId", "t2t2t2t2-**************-************");
        Map<String, String> values = new HashMap<>();
        values.put("g7g7g7g7-**************-************", "Av. Siempre Viva");
        values.put("h8h8h8h8-**************-************", "742");
        values.put("l3l3l3l3-1313-4313-8313-131313131313", "Springfield");
        complexValue.put("values", values);

        CreateClaimValueCommand command = new CreateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                complexValue,
                true,
                false,
                "USER_INPUT"
        );

        assertDoesNotThrow(command::validate);
        String jsonString = command.convertValueToString();
        assertNotNull(jsonString);
        assertTrue(jsonString.contains("claimSetId"));
        assertTrue(jsonString.contains("values"));
    }

    @Test
    void createClaimValueCommand_shouldAcceptArrayValue() {
        Object arrayValue = Arrays.asList("facturacion", "usuario_premium", "newsletter_semanal");

        CreateClaimValueCommand command = new CreateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                arrayValue,
                true,
                false,
                "USER_INPUT"
        );

        assertDoesNotThrow(command::validate);
        String jsonString = command.convertValueToString();
        assertNotNull(jsonString);
        assertTrue(jsonString.contains("facturacion"));
        assertTrue(jsonString.contains("usuario_premium"));
    }

    @Test
    void updateClaimValueCommand_shouldAcceptObjectValue() {
        Map<String, String> objectValue = new HashMap<>();
        objectValue.put("name", "Carlos Sánchez");
        objectValue.put("relation", "Hermano");
        objectValue.put("phone", "+54 9 11 2222-2222");

        UpdateClaimValueCommand command = new UpdateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                objectValue,
                true,
                false,
                "USER_INPUT",
                Instant.now(),
                UUID.randomUUID()
        );

        assertDoesNotThrow(command::validate);
        String jsonString = command.convertValueToString();
        assertNotNull(jsonString);
        assertTrue(jsonString.contains("Carlos Sánchez"));
    }

    @Test
    void dataTypeEnum_shouldValidateObjectValues() {
        // Test STRING type with Object
        assertTrue(DataTypeEnum.STRING.isValid("simple string"));
        assertTrue(DataTypeEnum.STRING.isValid(123)); // Should convert to string
        assertTrue(DataTypeEnum.STRING.isValid(Arrays.asList("item1", "item2"))); // Should convert to JSON

        // Test ARRAY type with Object
        assertTrue(DataTypeEnum.ARRAY.isValid(Arrays.asList("item1", "item2")));
        assertTrue(DataTypeEnum.ARRAY.isValid("[\"item1\", \"item2\"]"));

        // Test CLAIMSET type with Object
        Map<String, Object> claimSetValue = new HashMap<>();
        claimSetValue.put("property1", "value1");
        claimSetValue.put("property2", "value2");
        assertTrue(DataTypeEnum.CLAIMSET.isValid(claimSetValue));
    }

    @Test
    void dataTypeEnum_shouldNormalizeObjectValues() {
        // Test normalization of complex objects
        Map<String, String> objectValue = new HashMap<>();
        objectValue.put("key", "value");
        
        String normalized = DataTypeEnum.STRING.normalize(objectValue);
        assertNotNull(normalized);
        assertTrue(normalized.contains("key"));
        assertTrue(normalized.contains("value"));
    }
}
