package com.vusecurity.auth.identities.application.query;

import java.util.Map;
import java.util.UUID;

/**
 * Query interface for retrieving claim values and definitions for accounts.
 * Follows the CQRS pattern established in the project architecture.
 */
public interface GetAccountClaimsQuery {

    /**
     * Retrieves all claim values for a specific account.
     *
     * @param accountId the account ID
     * @return map of claim code to claim value
     */
    Map<String, Object> getClaimValuesByAccountId(UUID accountId);

    /**
     * Retrieves claim definition names for a specific account.
     *
     * @param accountId the account ID
     * @return map of claim code to claim definition name
     */
    Map<String, String> getClaimDefinitionNamesByAccountId(UUID accountId);

    /**
     * Checks if an account has a specific claim value.
     *
     * @param accountId the account ID
     * @param claimCode the claim code
     * @param expectedValue the expected value
     * @return true if the account has the claim with the expected value
     */
    boolean hasClaimValue(UUID accountId, String claimCode, String expectedValue);

}
