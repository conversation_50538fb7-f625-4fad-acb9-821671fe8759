package com.vusecurity.auth.authorization.api.handler;

import com.vusecurity.auth.authorization.application.exception.PermissionNameAlreadyExistException;
import com.vusecurity.auth.authorization.application.exception.PermissionNotFoundException;
import com.vusecurity.core.commons.ApiMessage;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@ControllerAdvice(assignableTypes = {com.vusecurity.auth.authorization.api.controller.PermissionsController.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class PermissionErrorControllerHandler {

    @ExceptionHandler(PermissionNameAlreadyExistException.class)
    public ResponseEntity<ApiMessage> handleAlreadyExistsException(PermissionNameAlreadyExistException ex) {

        ApiMessage error = new ApiMessage(4100, ex.getMessage());
        return ResponseEntity.status(HttpStatus.CONFLICT).body(error);
    }

    @ExceptionHandler(PermissionNotFoundException.class)
    public ResponseEntity<ApiMessage> handleNotFoundException(PermissionNotFoundException ex) {

        ApiMessage error = new ApiMessage(4101, ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }
}
