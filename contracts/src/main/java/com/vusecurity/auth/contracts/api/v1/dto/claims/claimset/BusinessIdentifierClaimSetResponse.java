package com.vusecurity.auth.contracts.api.v1.dto.claims.claimset;

import com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition.ClaimDefinitionSummaryResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.LookupStrategy;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.UUID;

/**
 * Response DTO for business identifier claim set with its claim definitions.
 * This represents the complete structure of an identifier claim set for a business,
 * including the claim set metadata and all its associated claim definitions.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "Business identifier claim set with its claim definitions")
public class BusinessIdentifierClaimSetResponse extends BaseResponse {

    @Schema(description = "The identifier claim set information")
    private ClaimSetInfo claimSet;

    @Schema(description = "List of claim definitions that belong to this identifier claim set")
    private List<ClaimDefinitionSummaryResponse> claimDefinitions;

    /**
     * Nested class representing claim set information without circular references to claim definitions.
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "Claim set information without related claim definitions")
    public static class ClaimSetInfo {
        @Schema(description = "Unique identifier of the claim set",
                example = "123e4567-e89b-12d3-a456-************")
        private UUID id;

        @Schema(description = "Unique identifier of the business this claim set belongs to",
                example = "987fcdeb-51a2-43d1-9c4f-123456789abc")
        private UUID businessId;

        @Schema(description = "Account type this claim set applies to",
                example = "WORKFORCE")
        private AccountType accountType;

        @Schema(description = "Whether this claim set is an identifier claim set (part of composite primary key)",
                example = "true")
        private Boolean isIdentifier;

        @Schema(description = "Name of the claim set",
                example = "Employee Basic Claims")
        private String name;

        @Schema(description = "Detailed description of the claim set",
                example = "Basic claims required for all employee accounts")
        private String description;

        @Schema(description = "Strategy for resolving claim handles when multiple claim definitions are involved",
                example = "ALL_CLAIMS_MUST_MATCH")
        private LookupStrategy lookupStrategy;
    }
}
