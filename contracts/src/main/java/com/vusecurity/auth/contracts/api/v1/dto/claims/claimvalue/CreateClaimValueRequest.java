package com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.UUID;
import java.util.List;
import java.util.Map;

@Data
@Schema(description = "Request to create a new claim value")
public class CreateClaimValueRequest {

    @Schema(description = "Unique identifier of the claim set this value belongs to, required if accountId is present",
            example = "********-51a2-43d1-9c4f-abc9987fcdeb")
    private UUID claimSetId;
    
    @NotNull(message = "Claim definition ID is required")
    @Schema(description = "Unique identifier of the claim definition",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID claimDefinitionId;

    @Schema(description = "Unique identifier of the identity this claim value belongs to",
            example = "987fcdeb-51a2-43d1-9c4f-********9abc")
    private UUID identityId;

    @Schema(description = "Unique identifier of the account this claim value belongs to",
            example = "456e7890-a12b-34c5-d678-901234567def")
    private UUID accountId;

    @NotNull(message = "Value is required")
    @Schema(description = "The claim value. Can be a string, array, or complex object structure",
            example = "<EMAIL>",
            oneOf = {String.class, List.class, Map.class})
    private Object value;

    @NotNull(message = "Is primary is required")
    @Schema(description = "Whether this is the primary value for this claim type",
            example = "true")
    private boolean isPrimary;

    @NotNull(message = "Is computed is required")
    @Schema(description = "Whether this value is computed automatically",
            example = "false")
    private boolean isComputed;

    @Schema(description = "Source of the claim value",
            example = "USER_INPUT")
    private String source;

    @Schema(description = "Index indicating the primary order of the claim value",
            example = "1")
    private Integer primaryIndex;
}
