package com.vusecurity.auth.authorization.application.command;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

import org.junit.jupiter.api.Test;

/**
 * Unit tests for CreateRoleCommand validation logic.
 * Tests pure domain logic with no external dependencies.
 */
class CreateRoleCommandTest {

    @Test
    void shouldValidateSuccessfully_WhenAllRequiredFieldsProvided() {
        // Given
        Set<UUID> permissionIds = Set.of(UUID.randomUUID(), UUID.randomUUID());
        CreateRoleCommand command = new CreateRoleCommand(
                "ADMIN",
                "Administrator role",
                UUID.randomUUID(),
                permissionIds
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WhenOptionalFieldsAreNull() {
        // Given
        CreateRoleCommand command = new CreateRoleCommand(
                "USER",
                null, // description is optional
                null, // businessId is optional
                null  // permissionIds is optional
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WhenPermissionIdsIsEmpty() {
        // Given
        CreateRoleCommand command = new CreateRoleCommand(
                "GUEST",
                "Guest role with no permissions",
                UUID.randomUUID(),
                new HashSet<>() // empty set
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldThrowException_WhenNameIsNull() {
        // Given
        CreateRoleCommand command = new CreateRoleCommand(
                null, // name is null
                "Administrator role",
                UUID.randomUUID(),
                Set.of(UUID.randomUUID())
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("name is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenNameIsEmpty() {
        // Given
        CreateRoleCommand command = new CreateRoleCommand(
                "", // name is empty
                "Administrator role",
                UUID.randomUUID(),
                Set.of(UUID.randomUUID())
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("name is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenNameIsWhitespace() {
        // Given
        CreateRoleCommand command = new CreateRoleCommand(
                "   ", // name is whitespace
                "Administrator role",
                UUID.randomUUID(),
                Set.of(UUID.randomUUID())
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("name is required", exception.getMessage());
    }

    @Test
    void shouldValidateSuccessfully_WithValidRoleNames() {
        // Test various valid role name formats
        String[] validNames = {
                "ADMIN",
                "USER",
                "GUEST",
                "SUPER_ADMIN",
                "read-only",
                "Content Manager",
                "API_ACCESS_ROLE",
                "Role123",
                "a", // single character
                "A".repeat(100) // long name
        };

        for (String name : validNames) {
            // Given
            CreateRoleCommand command = new CreateRoleCommand(
                    name,
                    "Test role",
                    UUID.randomUUID(),
                    Set.of(UUID.randomUUID())
            );

            // When & Then
            assertDoesNotThrow(command::validate, 
                    "Validation should pass for role name: " + name);
        }
    }

    @Test
    void shouldValidateSuccessfully_WithDifferentDescriptions() {
        // Test various description formats
        String[] descriptions = {
                null,
                "",
                "Simple description",
                "Multi-line\ndescription\nwith breaks",
                "Description with special chars !@#$%^&*()",
                "Very long description that goes on and on and contains lots of text to test edge cases",
                "   Whitespace padded description   "
        };

        for (String description : descriptions) {
            // Given
            CreateRoleCommand command = new CreateRoleCommand(
                    "TEST_ROLE",
                    description,
                    UUID.randomUUID(),
                    Set.of(UUID.randomUUID())
            );

            // When & Then
            assertDoesNotThrow(command::validate, 
                    "Validation should pass for description: " + description);
        }
    }

    @Test
    void shouldValidateSuccessfully_WithMultiplePermissions() {
        // Given
        Set<UUID> permissionIds = Set.of(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID()
        );
        CreateRoleCommand command = new CreateRoleCommand(
                "MULTI_PERMISSION_ROLE",
                "Role with multiple permissions",
                UUID.randomUUID(),
                permissionIds
        );

        // When & Then
        assertDoesNotThrow(command::validate);
        assertEquals(5, command.permissionIds().size());
    }

    @Test
    void shouldValidateSuccessfully_WithSinglePermission() {
        // Given
        Set<UUID> permissionIds = Set.of(UUID.randomUUID());
        CreateRoleCommand command = new CreateRoleCommand(
                "SINGLE_PERMISSION_ROLE",
                "Role with single permission",
                UUID.randomUUID(),
                permissionIds
        );

        // When & Then
        assertDoesNotThrow(command::validate);
        assertEquals(1, command.permissionIds().size());
    }

    @Test
    void shouldValidateSuccessfully_WithoutBusinessId() {
        // Given
        CreateRoleCommand command = new CreateRoleCommand(
                "GLOBAL_ROLE",
                "Global role not tied to specific business",
                null, // no businessId
                Set.of(UUID.randomUUID())
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithoutPermissions() {
        // Given
        CreateRoleCommand command = new CreateRoleCommand(
                "NO_PERMISSION_ROLE",
                "Role with no permissions initially",
                UUID.randomUUID(),
                null // no permissions
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldPreserveAllFieldsAfterValidation() {
        // Given
        String name = "TEST_ROLE";
        String description = "Test description";
        UUID businessId = UUID.randomUUID();
        Set<UUID> permissionIds = Set.of(UUID.randomUUID(), UUID.randomUUID());
        
        CreateRoleCommand command = new CreateRoleCommand(
                name,
                description,
                businessId,
                permissionIds
        );

        // When
        command.validate();

        // Then - verify all fields are preserved
        assertEquals(name, command.name());
        assertEquals(description, command.description());
        assertEquals(businessId, command.businessId());
        assertEquals(permissionIds, command.permissionIds());
        assertEquals(2, command.permissionIds().size());
    }

    @Test
    void shouldHandleImmutablePermissionIds() {
        // Given
        Set<UUID> originalPermissionIds = Set.of(UUID.randomUUID(), UUID.randomUUID());
        CreateRoleCommand command = new CreateRoleCommand(
                "IMMUTABLE_TEST",
                "Test immutability",
                UUID.randomUUID(),
                originalPermissionIds
        );

        // When
        command.validate();

        // Then - verify the set is the same reference (records are immutable)
        assertSame(originalPermissionIds, command.permissionIds());
    }
}
