package com.vusecurity.auth.shared.infrastructure.migration.initial;

import java.util.UUID;

public class DataSeedConstants {
    // System entities
    public static final UUID SYSTEM_BUSINESS_ID = UUID.fromString("d37ee3d4-22c6-41f7-819d-1464b3b9c454");
    public static final UUID SYSTEM_CHANNEL_ID = UUID.fromString("2701e2f0-8631-4561-a3c6-2b4af8e269ae");
    public static final UUID IDENTITY_PROVIDER_ID = UUID.fromString("b3c1c3b0-303c-4a7a-8c43-7bdeb96ef558");
    public static final UUID SCIM_PROVIDER_ID = UUID.fromString("55896ef7-303c-4a7a-8c43-bdc3b0b3c1eb");
    public static final UUID SYSTEM_IDENTITY_ID = UUID.fromString("509dde79-20d8-4ccb-92f7-f659927b5531");
    public static final UUID SYSTEM_ACCOUNT_ID = UUID.fromString("a1b2c3d4-e5f6-7890-abcd-ef1234567890");
    public static final UUID ADMIN_IDENTITY_ID = UUID.fromString("e1e9580e-6115-4f8d-90dd-349bffcb25ad");
    public static final UUID ADMIN_ACCOUNT_ID = UUID.fromString("a5f5129e-3901-4f65-9c1c-90f176d7d60f");

    // Claim Sets - Identifier Sets
    public static final UUID NONE_IDENTIFIER_CLAIM_SET_ID = UUID.fromString("4307e527-1cf4-4d10-8156-f913916c0e3e");
    public static final UUID CUSTOMER_IDENTIFIER_CLAIM_SET_ID = UUID.fromString("d95bb76c-13aa-4587-b3ef-8dc88f2f03bb");
    public static final UUID FEDERATED_IDENTIFIER_CLAIM_SET_ID = UUID.fromString("6b9810c2-50fb-4b02-8461-19eb90b61ccd");
    public static final UUID WORKFORCE_IDENTIFIER_CLAIM_SET_ID = UUID.fromString("f427be74-df33-451a-82b8-a09f30cec78e");
    public static final UUID SERVICE_IDENTIFIER_CLAIM_SET_ID = UUID.fromString("5e1e40af-3736-4f75-bf53-e0e9ce9adac0");
    public static final UUID SOCIAL_IDENTIFIER_CLAIM_SET_ID = UUID.fromString("9dced845-4d92-4bc7-a6e8-7fec81d50a0d");
    public static final UUID LOCAL_IDENTIFIER_CLAIM_SET_ID = UUID.fromString("b82bae6a-ee4d-4488-8136-dcdb49ef2fef");

    // Claim Sets - Profile Sets
    public static final UUID NONE_PROFILE_CLAIM_SET_ID = UUID.fromString("159c5b08-bdb2-4d49-9256-197ff0d2c761");
    public static final UUID CUSTOMER_PROFILE_CLAIM_SET_ID = UUID.fromString("3c416395-5ac9-40d6-98c7-b347b09ca21b");
    public static final UUID FEDERATED_PROFILE_CLAIM_SET_ID = UUID.fromString("73bbc97a-1733-475b-bd9e-10635a8b5cb6");
    public static final UUID WORKFORCE_PROFILE_CLAIM_SET_ID = UUID.fromString("33379d68-f824-4eb0-b8d4-a713f0b8d79f");
    public static final UUID SERVICE_PROFILE_CLAIM_SET_ID = UUID.fromString("d2d9ba77-177d-486a-a0b4-851e104a20ab");
    public static final UUID SOCIAL_PROFILE_CLAIM_SET_ID = UUID.fromString("1855d37f-c794-466f-9087-913f4dac0e39");
    public static final UUID LOCAL_PROFILE_CLAIM_SET_ID = UUID.fromString("ddb920ef-caa4-4193-ade5-908e8c61c931");

    // Roles
    public static final UUID ADMIN_ROLE_ID = UUID.fromString("9fa7a7f6-b8d4-4db4-a8c7-1642a80e863a");
    public static final UUID OPERATOR_ROLE_ID = UUID.fromString("779f520a-bb5f-45ef-95b8-3f2ea204023d");

    // Permissions - Identity
    public static final UUID PERMISSION_IDENTITY_CREATE_ID = UUID.fromString("0b5a1b36-4eb8-476b-8cfe-0f152822a5cb");
    public static final UUID PERMISSION_IDENTITY_READ_ID = UUID.fromString("5e3e4043-9ca3-44b5-9953-4ac4e1045716");
    public static final UUID PERMISSION_IDENTITY_UPDATE_ID = UUID.fromString("b2ab0b5b-96ba-4e9f-9ae0-9f98b4fae309");
    public static final UUID PERMISSION_IDENTITY_DELETE_ID = UUID.fromString("5a6960d5-a7e7-44ab-bbe9-06ab1e76bf7b");

    // Permissions - Account
    public static final UUID PERMISSION_ACCOUNT_CREATE_ID = UUID.fromString("cdfb0bc3-e6fe-472b-bb03-eaf619a51f96");
    public static final UUID PERMISSION_ACCOUNT_READ_ID = UUID.fromString("02b5b45f-7169-4eb3-9ecf-23a993e36d34");
    public static final UUID PERMISSION_ACCOUNT_UPDATE_ID = UUID.fromString("a8a4eb8f-0beb-4a77-92d6-1d6649a46568");
    public static final UUID PERMISSION_ACCOUNT_DELETE_ID = UUID.fromString("e653f407-2fad-4250-888f-d7465f83e8ce");

    // Permissions - Business
    public static final UUID PERMISSION_BUSINESS_CREATE_ID = UUID.fromString("504719a0-fdc1-448d-b58c-ec45b594893d");
    public static final UUID PERMISSION_BUSINESS_READ_ID = UUID.fromString("5e9b0f7f-8c50-4613-8991-a7b4455ce5f5");
    public static final UUID PERMISSION_BUSINESS_UPDATE_ID = UUID.fromString("4213292d-f965-4d00-9a1d-57a3615c351f");
    public static final UUID PERMISSION_BUSINESS_DELETE_ID = UUID.fromString("b9338fd5-30b8-4a6e-ac51-4a7aeca65301");

    // Permissions - Claim
    public static final UUID PERMISSION_CLAIM_CREATE_ID = UUID.fromString("84510add-1c13-47ab-98bb-7094c23bfc56");
    public static final UUID PERMISSION_CLAIM_READ_ID = UUID.fromString("bec3b3ea-cfe4-4465-b896-ec3047d20232");
    public static final UUID PERMISSION_CLAIM_UPDATE_ID = UUID.fromString("ed4d7659-e9e9-4d29-bbce-166c729f36b9");
    public static final UUID PERMISSION_CLAIM_DELETE_ID = UUID.fromString("316cc403-d3c8-4a19-b115-fe5b64e376fd");

    // Permissions - Consent
    public static final UUID PERMISSION_CONSENT_CREATE_ID = UUID.fromString("b397c3ad-6428-4d7b-8cc9-13b8732ce9e4");
    public static final UUID PERMISSION_CONSENT_READ_ID = UUID.fromString("debdd7a0-a3fc-46f5-8f12-b731bb8421e4");
    public static final UUID PERMISSION_CONSENT_UPDATE_ID = UUID.fromString("dad173eb-4496-4b40-b6c7-693f0ea7f5e4");
    public static final UUID PERMISSION_CONSENT_DELETE_ID = UUID.fromString("5ca9d48f-2097-4bd1-a6a4-05df06805a9a");

    // Permissions - Role
    public static final UUID PERMISSION_ROLE_CREATE_ID = UUID.fromString("79c8e073-4825-4ccb-9621-7b7ae127d03d");
    public static final UUID PERMISSION_ROLE_READ_ID = UUID.fromString("410db433-5ebc-48fa-b0ce-d313ebf3a8c6");
    public static final UUID PERMISSION_ROLE_UPDATE_ID = UUID.fromString("010f7d25-44e2-4602-ba18-9b81096d50ef");
    public static final UUID PERMISSION_ROLE_DELETE_ID = UUID.fromString("f086d3fd-a216-4e1a-8381-049fae470719");

    // Permissions - Permission
    public static final UUID PERMISSION_PERMISSION_CREATE_ID = UUID.fromString("ab4fd567-3d79-4fc1-8fe7-9e55695dfb2a");
    public static final UUID PERMISSION_PERMISSION_READ_ID = UUID.fromString("9494aa9f-f0ac-4098-847c-57086568aab3");
    public static final UUID PERMISSION_PERMISSION_UPDATE_ID = UUID.fromString("3f8965a3-a7b9-4074-b8bf-33824d8e8ad7");
    public static final UUID PERMISSION_PERMISSION_DELETE_ID = UUID.fromString("7372b969-56e7-43fe-bade-8d992630a173");

    // Permissions - Group
    public static final UUID PERMISSION_GROUP_CREATE_ID = UUID.fromString("e4b8c9d1-2f35-46a7-8e90-12c3d4e5f678");
    public static final UUID PERMISSION_GROUP_READ_ID = UUID.fromString("f5c9d0e2-3f46-57b8-9f01-23d4e5f6a789");
    public static final UUID PERMISSION_GROUP_UPDATE_ID = UUID.fromString("a6d0e1f3-4f57-68c9-0f12-34e5f6a7b890");
    public static final UUID PERMISSION_GROUP_DELETE_ID = UUID.fromString("b7e1f2a4-5f68-79d0-1f23-45f6a7b8c901");

    // Permissions - All (aggregate) IDs
    public static final UUID PERMISSION_IDENTITY_ALL_ID = UUID.fromString("ddef4592-cdce-4158-800d-00fb06c24109");
    public static final UUID PERMISSION_ACCOUNT_ALL_ID = UUID.fromString("5e1b9351-2f1d-4f93-871d-a3e27ce8d97d");
    public static final UUID PERMISSION_BUSINESS_ALL_ID = UUID.fromString("83040f1d-fb72-474f-b856-3d6f5270b543");
    public static final UUID PERMISSION_CLAIM_ALL_ID = UUID.fromString("04a610e6-48bc-444f-9ee7-27bef5b26fe9");
    public static final UUID PERMISSION_CONSENT_ALL_ID = UUID.fromString("7f81915c-161c-4eb8-b72f-a02839c04e69");
    public static final UUID PERMISSION_ROLE_ALL_ID = UUID.fromString("b3077820-bc48-4a89-be3c-bf1374b7eb51");
    public static final UUID PERMISSION_PERMISSION_ALL_ID = UUID.fromString("05f9c70b-df37-4dad-8180-c03fdaa31b57");
    public static final UUID PERMISSION_GROUP_ALL_ID = UUID.fromString("63ff405d-100f-4955-b818-454e13d5b171");

    public static final UUID PERMISSION_CHANNEL_CREATE_ID = UUID.fromString("8f52f06e-e52c-47e0-81fd-d8e568ed34cb");
    public static final UUID PERMISSION_CHANNEL_READ_ID = UUID.fromString("359ee229-e09c-46b1-8c55-90ac0ae1a9e8");
    public static final UUID PERMISSION_CHANNEL_UPDATE_ID = UUID.fromString("3b1f7511-bd15-47b8-9053-7d9bf4e99155");
    public static final UUID PERMISSION_CHANNEL_DELETE_ID = UUID.fromString("b55af4fd-f1c0-4a08-9d02-0c70859f0e68");
    public static final UUID PERMISSION_CHANNEL_ALL_ID = UUID.fromString("50835799-dad9-4bf2-8b41-5cd922c0d04d");

    // Permissions - Factor TOTP
    public static final UUID PERMISSION_FACTOR_TOTP_CREATE_ID = UUID.fromString("a29e8a6e-9d50-434a-857e-6c111c8172c9");
    public static final UUID PERMISSION_FACTOR_TOTP_READ_ID = UUID.fromString("46b76be4-a78e-4d94-9f94-53e51c8502d0");
    public static final UUID PERMISSION_FACTOR_TOTP_UPDATE_ID = UUID.fromString("d01ac2ad-120f-4272-96b6-e280f6345165");
    public static final UUID PERMISSION_FACTOR_TOTP_DELETE_ID = UUID.fromString("2b76862c-3d48-497c-8c2e-24fb3d66da00");
    public static final UUID PERMISSION_FACTOR_TOTP_ALL_ID = UUID.fromString("28657e33-e179-4ced-8a92-457fce997fae");

    // Permissions - Factor SMS
    public static final UUID PERMISSION_FACTOR_SMS_CREATE_ID = UUID.fromString("adeb7443-b028-4564-992b-3edaa9a28452");
    public static final UUID PERMISSION_FACTOR_SMS_READ_ID = UUID.fromString("df4a93eb-466a-4217-bef5-0c2aafe3adeb");
    public static final UUID PERMISSION_FACTOR_SMS_UPDATE_ID = UUID.fromString("d80aec72-f433-4259-bbf5-00b587ff90d9");
    public static final UUID PERMISSION_FACTOR_SMS_DELETE_ID = UUID.fromString("38720e22-ea55-4a81-b819-2eca45202db1");
    public static final UUID PERMISSION_FACTOR_SMS_ALL_ID = UUID.fromString("1a36d1d6-7eba-427a-b097-4e6e881fb558");

    // Permissions - Factor Email
    public static final UUID PERMISSION_FACTOR_EMAIL_CREATE_ID = UUID.fromString("365c9b68-5ea9-4912-b6b4-588923143b60");
    public static final UUID PERMISSION_FACTOR_EMAIL_READ_ID = UUID.fromString("3e9da38f-6fb2-431c-9666-a89286ec6e61");
    public static final UUID PERMISSION_FACTOR_EMAIL_UPDATE_ID = UUID.fromString("446bf8b8-3abd-414e-89b0-49fbae82f3d8");
    public static final UUID PERMISSION_FACTOR_EMAIL_DELETE_ID = UUID.fromString("a95d38f1-07ef-4d88-8b8c-36c86d9ad7c2");
    public static final UUID PERMISSION_FACTOR_EMAIL_ALL_ID = UUID.fromString("6cc3d3f8-5cd9-4372-ab55-841e4f047074");

    // Permissions - Factor Password
    public static final UUID PERMISSION_FACTOR_PASSWORD_CREATE_ID = UUID.fromString("83ff93f4-1879-4e5d-8f1a-6ffff7d92800");
    public static final UUID PERMISSION_FACTOR_PASSWORD_READ_ID = UUID.fromString("cdc88324-602e-4461-ba65-79d6f5aa9727");
    public static final UUID PERMISSION_FACTOR_PASSWORD_UPDATE_ID = UUID.fromString("38febaf5-e637-40a9-b42f-631978980b05");
    public static final UUID PERMISSION_FACTOR_PASSWORD_DELETE_ID = UUID.fromString("a5a482be-0142-420e-822a-f4091d22b236");
    public static final UUID PERMISSION_FACTOR_PASSWORD_ALL_ID = UUID.fromString("ac02fd0f-fdbd-4295-bee2-ddd49a2335d2");

    // Permissions - Factor Policies
    public static final UUID PERMISSION_FACTOR_POLICIES_CREATE_ID = UUID.fromString("4de0b8dd-3e0c-4055-9177-c13d901f18f1");
    public static final UUID PERMISSION_FACTOR_POLICIES_READ_ID = UUID.fromString("4212c99e-de64-4e6e-9478-5655c53e9b18");
    public static final UUID PERMISSION_FACTOR_POLICIES_UPDATE_ID = UUID.fromString("d78738b0-50f2-44ec-9c4a-3bf0d712016e");
    public static final UUID PERMISSION_FACTOR_POLICIES_DELETE_ID = UUID.fromString("1c98dfd4-57ca-4045-b5a0-022cb251af87");
    public static final UUID PERMISSION_FACTOR_POLICIES_ALL_ID = UUID.fromString("4bc58115-4c53-43d7-b168-009f75896f5a");

    // Claim Definitions - Core Claims
    public static final UUID CLAIM_DEF_EMAIL_ADDRESSES_ID = UUID.fromString("ac0de313-0d86-49a4-97bf-03da1943671b");
    public static final UUID CLAIM_DEF_EMAIL_ADDRESS_ID = UUID.fromString("beb4da69-6685-481b-a940-87823788b02a");
    public static final UUID CLAIM_DEF_FIRST_NAME_ID = UUID.fromString("fd9c993f-d544-43a1-9fd8-b77d19de1635");
    public static final UUID CLAIM_DEF_LAST_NAME_ID = UUID.fromString("db76198f-9866-41d5-82b3-856a6a86d157");
    public static final UUID CLAIM_DEF_PHONE_NUMBERS_ID = UUID.fromString("6dffb4ad-1300-4770-a5c3-7c0913e7a624");
    public static final UUID CLAIM_DEF_ADDRESSES_ID = UUID.fromString("aa087964-b788-44c4-9078-3e53e50fe853");
    public static final UUID CLAIM_DEF_LAST_LOGIN_ID = UUID.fromString("ed926e0f-4d85-4f3d-b9f6-971d566a8c59");
    public static final UUID CLAIM_DEF_PHONE_NUMBER_ID = UUID.fromString("2784994d-dc95-491b-8843-f5664dece55e");
    public static final UUID CLAIM_DEF_ADDRESS_ID = UUID.fromString("e78db996-7a30-46b0-8a47-6d86c0a91f49");
    public static final UUID CLAIM_DEF_ID_NUMBER_ID = UUID.fromString("d31ff644-6a74-41da-b81c-13ad894a189a");

    // Claim Definitions - Workforce Claims
    public static final UUID CLAIM_DEF_EMPLOYEE_ID_ID = UUID.fromString("f1a2b3c4-d5e6-7f8a-9b0c-1d2e3f4a5b6c");
    public static final UUID CLAIM_DEF_DEPARTMENT_ID = UUID.fromString("a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d");
    public static final UUID CLAIM_DEF_JOB_TITLE_ID = UUID.fromString("b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e");
    public static final UUID CLAIM_DEF_MANAGER_EMAIL_ID = UUID.fromString("c3d4e5f6-a7b8-9c0d-1e2f-3a4b5c6d7e8f");
    public static final UUID CLAIM_DEF_HIRE_DATE_ID = UUID.fromString("d4e5f6a7-b8c9-0d1e-2f3a-4b5c6d7e8f9a");

    // Claim Definition - SCIM Identity Claims
    public static final UUID CLAIM_DEF_SCIM_USER_NAME_ID = UUID.fromString("12afd933-ede5-4ac2-8d2b-e8978642965c");
    public static final UUID CLAIM_DEF_SCIM_EXTERNAL_ID = UUID.fromString("e8978642-8d2b-4ac2-ede5-965c12afd933");
}