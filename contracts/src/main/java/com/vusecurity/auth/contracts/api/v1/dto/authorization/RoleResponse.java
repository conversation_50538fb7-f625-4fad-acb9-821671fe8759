package com.vusecurity.auth.contracts.api.v1.dto.authorization;

import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BusinessBasicInfoResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "Role information response")
public class RoleResponse extends BaseResponse {
    @Schema(description = "Unique identifier of the role",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    @Schema(description = "Name of the role",
            example = "Administrator")
    private String name;

    @Schema(description = "Detailed description of the role",
            example = "Full system administrator with all permissions")
    private String description;

    @Schema(description = "Basic business information")
    private BusinessBasicInfoResponse business;

    @Schema(description = "List of permission names assigned to this role",
            example = "[{\"id\": \"123e4567-e89b-12d3-a456-************\", \"name\": \"READ_USERS\"}, " +
                    "{\"id\": \"123e4567-e89b-12d3-a456-************\", \"name\": \"WRITE_USERS\"}, " +
                    "{\"id\": \"123e4567-e89b-12d3-a456-************\", \"name\": \"DELETE_USERS\"}]")
    private List<PermissionBasicInfoResponse> permissions;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        RoleResponse that = (RoleResponse) o;
        return Objects.equals(id, that.id) &&
               Objects.equals(name, that.name) &&
               Objects.equals(description, that.description) &&
               Objects.equals(business, that.business) &&
               Objects.equals(permissions, that.permissions);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), id, name, description, business, permissions);
    }
}
