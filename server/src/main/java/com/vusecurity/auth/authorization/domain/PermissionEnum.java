package com.vusecurity.auth.authorization.domain;

/**
 * Central enumeration of permission names to avoid string duplication across controllers.
 * Naming convention: DOMAIN_ACTION mapping to permission value "sys_<domain>:<action>".
 */
public enum PermissionEnum {
    // Identity
    IDENTITY_CREATE("sys_identity:create"),
    IDENTITY_READ("sys_identity:read"),
    IDENTITY_UPDATE("sys_identity:update"),
    IDENTITY_DELETE("sys_identity:delete"),
    IDENTITY_ALL("sys_identity:all"),

    // Account
    ACCOUNT_CREATE("sys_account:create"),
    ACCOUNT_READ("sys_account:read"),
    ACCOUNT_UPDATE("sys_account:update"),
    ACCOUNT_DELETE("sys_account:delete"),
    ACCOUNT_ALL("sys_account:all"),

    // Business
    BUSINESS_CREATE("sys_business:create"),
    BUSINESS_READ("sys_business:read"),
    BUSINESS_UPDATE("sys_business:update"),
    BUSINESS_DELETE("sys_business:delete"),
    BUSINESS_ALL("sys_business:all"),

    // Claim
    <PERSON>_CREATE("sys_claim:create"),
    CLAIM_READ("sys_claim:read"),
    CLAIM_UPDATE("sys_claim:update"),
    CLAIM_DELETE("sys_claim:delete"),
    CLAIM_ALL("sys_claim:all"),

    // Consent
    CONSENT_CREATE("sys_consent:create"),
    CONSENT_READ("sys_consent:read"),
    CONSENT_UPDATE("sys_consent:update"),
    CONSENT_DELETE("sys_consent:delete"),
    CONSENT_ALL("sys_consent:all"),

    // Role
    ROLE_CREATE("sys_role:create"),
    ROLE_READ("sys_role:read"),
    ROLE_UPDATE("sys_role:update"),
    ROLE_DELETE("sys_role:delete"),
    ROLE_ALL("sys_role:all"),

    // Permission
    PERMISSION_CREATE("sys_permission:create"),
    PERMISSION_READ("sys_permission:read"),
    PERMISSION_UPDATE("sys_permission:update"),
    PERMISSION_DELETE("sys_permission:delete"),
    PERMISSION_ALL("sys_permission:all"),

    // Group
    GROUP_CREATE("sys_group:create"),
    GROUP_READ("sys_group:read"),
    GROUP_UPDATE("sys_group:update"),
    GROUP_DELETE("sys_group:delete"),
    GROUP_ALL("sys_group:all"),

    // Channel (new)
    CHANNEL_CREATE("sys_channel:create"),
    CHANNEL_READ("sys_channel:read"),
    CHANNEL_UPDATE("sys_channel:update"),
    CHANNEL_DELETE("sys_channel:delete"),
    CHANNEL_ALL("sys_channel:all"),

    // Factor TOTP
    FACTOR_TOTP_CREATE("sys_factor_totp:create"),
    FACTOR_TOTP_READ("sys_factor_totp:read"),
    FACTOR_TOTP_UPDATE("sys_factor_totp:update"),
    FACTOR_TOTP_DELETE("sys_factor_totp:delete"),
    FACTOR_TOTP_ALL("sys_factor_totp:all"),

    // Factor SMS
    FACTOR_SMS_CREATE("sys_factor_sms:create"),
    FACTOR_SMS_READ("sys_factor_sms:read"),
    FACTOR_SMS_UPDATE("sys_factor_sms:update"),
    FACTOR_SMS_DELETE("sys_factor_sms:delete"),
    FACTOR_SMS_ALL("sys_factor_sms:all"),

    // Factor Email
    FACTOR_EMAIL_CREATE("sys_factor_email:create"),
    FACTOR_EMAIL_READ("sys_factor_email:read"),
    FACTOR_EMAIL_UPDATE("sys_factor_email:update"),
    FACTOR_EMAIL_DELETE("sys_factor_email:delete"),
    FACTOR_EMAIL_ALL("sys_factor_email:all"),

    // Factor Password
    FACTOR_PASSWORD_CREATE("sys_factor_password:create"),
    FACTOR_PASSWORD_READ("sys_factor_password:read"),
    FACTOR_PASSWORD_UPDATE("sys_factor_password:update"),
    FACTOR_PASSWORD_DELETE("sys_factor_password:delete"),
    FACTOR_PASSWORD_ALL("sys_factor_password:all"),

    // Factor Policies
    FACTOR_POLICIES_CREATE("sys_factor_policies:create"),
    FACTOR_POLICIES_READ("sys_factor_policies:read"),
    FACTOR_POLICIES_UPDATE("sys_factor_policies:update"),
    FACTOR_POLICIES_DELETE("sys_factor_policies:delete"),
    FACTOR_POLICIES_ALL("sys_factor_policies:all");

    private final String value;

    PermissionEnum(String value) {
        this.value = value;
    }

    public String value() { return value; }

    // Expose compile-time constants for use in annotations.
    public static final String CHANNEL_CREATE_VALUE = CHANNEL_CREATE.value;
    public static final String CHANNEL_READ_VALUE = CHANNEL_READ.value;
    public static final String CHANNEL_UPDATE_VALUE = CHANNEL_UPDATE.value;
    public static final String CHANNEL_DELETE_VALUE = CHANNEL_DELETE.value;
}
