package com.vusecurity.auth.identities.api.handler;


import com.vusecurity.auth.identities.application.exception.IdentityException;
import com.vusecurity.auth.identities.application.exception.IdentityMetadataParseException;
import com.vusecurity.auth.identities.application.exception.IdentityNameAlreadyExistException;
import com.vusecurity.auth.identities.application.exception.IdentityNotFoundException;
import com.vusecurity.auth.identities.application.exception.IdentityValidationException;
import com.vusecurity.core.commons.ApiMessage;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@ControllerAdvice(assignableTypes = {com.vusecurity.auth.identities.api.controller.IdentityController.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class IdentityErrorControllerHandler {

    @ExceptionHandler(IdentityNameAlreadyExistException.class)
    public ResponseEntity<ApiMessage> handleAlreadyExistsException(IdentityNameAlreadyExistException ex) {

        ApiMessage error = new ApiMessage(4400, ex.getMessage());
        return ResponseEntity.status(HttpStatus.CONFLICT).body(error);
    }

    @ExceptionHandler(IdentityMetadataParseException.class)
    public ResponseEntity<ApiMessage> handleMetadataParseException(IdentityMetadataParseException ex) {

        ApiMessage error = new ApiMessage(4401, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }

    @ExceptionHandler(IdentityNotFoundException.class)
    public ResponseEntity<ApiMessage> handleNotFoundException(IdentityNotFoundException ex) {

        ApiMessage error = new ApiMessage(4402, ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }

    @ExceptionHandler(IdentityException.class)
    public ResponseEntity<ApiMessage> handleIdentityException(IdentityException ex) {

        ApiMessage error = new ApiMessage(4403, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }

    @ExceptionHandler(IdentityValidationException.class)
    public ResponseEntity<ApiMessage> handleValidationException(IdentityValidationException ex) {
        ApiMessage error = new ApiMessage(4404, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }


}
