package com.vusecurity.auth.authorization.application.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vusecurity.multitenant.TenantContext;
import org.springframework.jdbc.core.JdbcOperations;
import org.springframework.security.oauth2.server.authorization.JdbcOAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.JdbcOAuth2AuthorizationService.OAuth2AuthorizationParametersMapper;
import org.springframework.security.oauth2.server.authorization.JdbcOAuth2AuthorizationService.OAuth2AuthorizationRowMapper;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class MultitenantJdbcOAuth2AuthorizationService implements OAuth2AuthorizationService {
    private final Map<String, JdbcOAuth2AuthorizationService> authorizationServiceMap;
    private final JdbcOperations jdbcOperations;
    private final RegisteredClientRepository registeredClientRepository;
    private final ObjectMapper securityObjectMapper;

    public MultitenantJdbcOAuth2AuthorizationService(JdbcOperations jdbcOperations,
            RegisteredClientRepository registeredClientRepository, ObjectMapper securityObjectMapper) {
        this.jdbcOperations = jdbcOperations;
        this.registeredClientRepository = registeredClientRepository;
        this.authorizationServiceMap = new ConcurrentHashMap<>();
        this.securityObjectMapper = securityObjectMapper;
    }

    private JdbcOAuth2AuthorizationService getAuthorizationService() {
        JdbcOAuth2AuthorizationService authorizationService = authorizationServiceMap
                .get(TenantContext.getCurrentTenant());
        if (authorizationService == null) {
            authorizationService = new JdbcOAuth2AuthorizationService(jdbcOperations, registeredClientRepository);

            OAuth2AuthorizationRowMapper authorizationRowMapper = new OAuth2AuthorizationRowMapper(
                    registeredClientRepository);
            authorizationRowMapper.setObjectMapper(securityObjectMapper);

            OAuth2AuthorizationParametersMapper authorizationParametersMapper = new OAuth2AuthorizationParametersMapper();
            authorizationParametersMapper.setObjectMapper(securityObjectMapper);

            authorizationService.setAuthorizationRowMapper(authorizationRowMapper);
            authorizationService.setAuthorizationParametersMapper(authorizationParametersMapper);

            authorizationServiceMap.put(TenantContext.getCurrentTenant(), authorizationService);
        }
        return authorizationService;
    }

    @Override
    public void save(OAuth2Authorization authorization) {
        getAuthorizationService().save(authorization);
    }

    @Override
    public void remove(OAuth2Authorization authorization) {
        getAuthorizationService().remove(authorization);
    }

    @Override
    public OAuth2Authorization findById(String id) {
        return getAuthorizationService().findById(id);
    }

    @Override
    public OAuth2Authorization findByToken(String token, OAuth2TokenType tokenType) {
        return getAuthorizationService().findByToken(token, tokenType);
    }
}
