package com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity;

import java.io.Serializable;
import java.time.Instant;

import com.vusecurity.auth.authorization.dto.ClientSettingsDto;
import com.vusecurity.auth.authorization.dto.TokenSettingsDto;
import com.vusecurity.core.commons.Auditable;

import jakarta.persistence.AttributeOverride;
import jakarta.persistence.AttributeOverrides;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Entity
@Table(name = "oauth2_registered_client", indexes = {
        @Index(columnList = "client_id", unique = true)
})
public class Oauth2RegisteredClient extends Auditable implements Serializable {

    @Id
    @Column(length = 100)
    @NotBlank
    private String id;

    @Column(name = "client_id", length = 100, nullable = false)
    @NotBlank
    private String clientId;

    @Column(name = "client_id_issued_at", nullable = false)
    @NotNull
    private Instant clientIdIssuedAt;

    @Column(name = "client_secret", length = 200)
    private String clientSecret;

    @Column(name = "client_secret_expires_at")
    private Instant clientSecretExpiresAt;

    @Column(name = "client_name", length = 200, nullable = false)
    @NotBlank
    private String clientName;

    @Column(name = "client_authentication_methods", length = 1000, nullable = false)
    @NotBlank
    private String clientAuthenticationMethods;

    @Column(name = "authorization_grant_types", length = 1000, nullable = false)
    @NotBlank
    private String authorizationGrantTypes;

    @Column(name = "redirect_uris", length = 1000)
    private String redirectUris;

    @Column(name = "post_logout_redirect_uris", length = 1000)
    private String postLogoutRedirectUris;

    @Column(name = "scopes", length = 1000, nullable = false)
    @NotBlank
    private String scopes;

    @Embedded
    @AttributeOverrides({
        @AttributeOverride(name = "requireAuthorizationConsent", column = @Column(name = "require_authorization_consent")),
        @AttributeOverride(name = "requireProofKey", column = @Column(name = "require_proof_key")),
        @AttributeOverride(name = "jwkSetUrl", column = @Column(name = "jwk_set_url")),
        @AttributeOverride(name = "tokenEndpointAuthenticationSigningAlgorithm", column = @Column(name = "token_endpoint_authentication_signing_algorithm")),
        @AttributeOverride(name = "x509CertificateSubjectDN", column = @Column(name = "x509certificate_subjectdn"))
    })
    private ClientSettingsDto clientSettings;

    @Embedded
    @AttributeOverrides({
        @AttributeOverride(name = "accessTokenTimeToLive", column = @Column(name = "access_token_time_to_live")),
        @AttributeOverride(name = "accessTokenFormat", column = @Column(name = "access_token_format")),
        @AttributeOverride(name = "reuseRefreshTokens", column = @Column(name = "reuse_refresh_tokens")),
        @AttributeOverride(name = "refreshTokenTimeToLive", column = @Column(name = "refresh_token_time_to_live")),
        @AttributeOverride(name = "idTokenSignatureAlgorithm", column = @Column(name = "id_token_signature_algorithm")),
        @AttributeOverride(name = "x509CertificateBoundAccessTokens", column = @Column(name = "x509certificate_bound_access_tokens")),
        @AttributeOverride(name = "authorizationCodeTimeToLive", column = @Column(name = "authorization_code_time_to_live")),
        @AttributeOverride(name = "deviceCodeTimeToLive", column = @Column(name = "device_code_time_to_live"))
    })
    private TokenSettingsDto tokenSettings;

    public Oauth2RegisteredClient() {
        // Default constructor required by JPA
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Instant getClientIdIssuedAt() {
        return clientIdIssuedAt;
    }

    public void setClientIdIssuedAt(Instant clientIdIssuedAt) {
        this.clientIdIssuedAt = clientIdIssuedAt;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public Instant getClientSecretExpiresAt() {
        return clientSecretExpiresAt;
    }

    public void setClientSecretExpiresAt(Instant clientSecretExpiresAt) {
        this.clientSecretExpiresAt = clientSecretExpiresAt;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientAuthenticationMethods() {
        return clientAuthenticationMethods;
    }

    public void setClientAuthenticationMethods(String clientAuthenticationMethods) {
        this.clientAuthenticationMethods = clientAuthenticationMethods;
    }

    public String getAuthorizationGrantTypes() {
        return authorizationGrantTypes;
    }

    public void setAuthorizationGrantTypes(String authorizationGrantTypes) {
        this.authorizationGrantTypes = authorizationGrantTypes;
    }

    public String getRedirectUris() {
        return redirectUris;
    }

    public void setRedirectUris(String redirectUris) {
        this.redirectUris = redirectUris;
    }

    public String getPostLogoutRedirectUris() {
        return postLogoutRedirectUris;
    }

    public void setPostLogoutRedirectUris(String postLogoutRedirectUris) {
        this.postLogoutRedirectUris = postLogoutRedirectUris;
    }

    public String getScopes() {
        return scopes;
    }

    public void setScopes(String scopes) {
        this.scopes = scopes;
    }

    public ClientSettingsDto getClientSettings() {
        return clientSettings;
    }

    public void setClientSettings(ClientSettingsDto clientSettings) {
        this.clientSettings = clientSettings;
    }

    public TokenSettingsDto getTokenSettings() {
        return tokenSettings;
    }

    public void setTokenSettings(TokenSettingsDto tokenSettings) {
        this.tokenSettings = tokenSettings;
    }

}