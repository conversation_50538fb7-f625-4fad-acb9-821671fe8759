server:
  port: ${SERVER_PORT:8443}
  servlet:
    context-path: ${CONTEXT_PATH:}
  ssl:
    key-store: ${SSL_KEY_STORE:/opt/certs/cert.p12}
    key-store-password: ${SSL_KEY_STORE_PASSWORD}
    key-store-type: ${SSL_KEY_STORE_TYPE:PKCS12}
    key-alias: ${SSL_KEY_ALIAS:}
flyway:
  locations: classpath:db/migration
  default-schema: dbo
  baseline-on-migrate: true
  baseline-version: 1
  validate-on-migrate: true
  clean-disabled: true
  debug: true

spring:
  jpa:
    hibernate:
      ddl-auto: validate
  ssl:
    trust-store: ${SPRING_SSL_TRUST_STORE}
    trust-store-password: ${SPRING_SSL_TRUST_STORE_PASSWORD}

