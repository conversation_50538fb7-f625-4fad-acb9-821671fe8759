server:
  port: ${SERVER_PORT:8443}
  servlet:
    context-path: ${CONTEXT_PATH:}
  ssl:
    key-store: ${SSL_KEY_STORE:classpath:cert.p12}
    key-store-password: ${SSL_KEY_STORE_PASSWORD:vupassword}
    key-store-type: PKCS12
    key-alias: ${SSL_KEY_ALIAS:vu}

flyway:
  locations: classpath:db/migration
  default-schema: dbo
  baseline-on-migrate: true
  baseline-version: 1
  validate-on-migrate: true
  clean-disabled: true
  debug: true

spring:
  jpa:
    hibernate:
      ddl-auto: validate