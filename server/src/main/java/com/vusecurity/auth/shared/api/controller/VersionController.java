package com.vusecurity.auth.shared.api.controller;

import com.vusecurity.auth.AppConfigProperties;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller that provides version information about the application.
 * This endpoint can be used to verify which version of the service is running.
 */
@RestController
@Tag(name = "Version", description = "API to retrieve application version information")
public class VersionController {

    private final AppConfigProperties appConfigProperties;

    public VersionController(AppConfigProperties appConfigProperties) {
        this.appConfigProperties = appConfigProperties;
    }

    /**
     * Returns the version information of the application.
     *
     * @return A map containing version, build number, and build date
     */
    @GetMapping(value = "/version", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(
            summary = "Get application version information",
            description = "Returns the version, build number, and build date of the application",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Version information retrieved successfully",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = Map.class))
                    )
            }
    )
    public Map<String, Object> getVersion() {
        Map<String, Object> appVersion = new HashMap<>();
        appVersion.put("version", appConfigProperties.getVersion());
        appVersion.put("build", appConfigProperties.getBuild());
        appVersion.put("date", appConfigProperties.getDate());
        return appVersion;
    }
}
