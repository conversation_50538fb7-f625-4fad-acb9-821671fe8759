package com.vusecurity.auth.identities.application.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vusecurity.auth.claims.application.command.CreateClaimValueCommand;
import com.vusecurity.auth.claims.application.handler.CreateClaimValueHandler;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.*;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.identities.application.command.CreateAccountCommand;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class CreateAccountWithClaimValuesHandler {

    private static final Logger log = LoggerFactory.getLogger(CreateAccountWithClaimValuesHandler.class);

    private final CreateAccountHandler accountHandler;
    private final CreateClaimValueHandler claimValueHandler;
    private final ClaimDefinitionRepository claimDefinitionRepository;
    private final ObjectMapper objectMapper;

    @Transactional
    public AccountJpaEntity handle(CreateAccountCommand cmd) {
        cmd.validate();

        AccountJpaEntity account = accountHandler.createAccount(cmd);

        List<ClaimValueRequest> claims = cmd.claimValueList() == null
                ? Collections.emptyList()
                : cmd.claimValueList();

        addClaimValues(account, claims);

        return account;
    }

    private void addClaimValues(AccountJpaEntity account, List<ClaimValueRequest> requests) {
        requests.forEach(r -> {
            List<CreateClaimValueCommand> commands = toCommands(account.getId(), r);
            commands.forEach(command -> {
                claimValueHandler.handle(command);
                log.debug("Claim value added to account [{}] (definitionId={}, value={})",
                        account.getId(), r.claimDefinitionId(), command.value());
            });
        });
    }

    private List<CreateClaimValueCommand> toCommands(UUID accountId, ClaimValueRequest r) {
        return switch (r) {
            case ScalarClaimValueRequest scalar -> List.of(
                    new CreateClaimValueCommand(
                            scalar.claimSetId(),
                            scalar.claimDefinitionId(),
                            null, // identityId
                            accountId,
                            scalar.value(),
                            true, // isPrimary - scalar values are always primary
                            false, // isComputed – user supplied
                            scalar.source(),
                            null) // primaryIndex - not applicable for scalar values
            );
            case ArrayClaimValueRequest array -> {
                // Skip processing if array is empty
                if (array.values().isEmpty()) {
                    yield List.of(); // Return empty list - no commands to process
                }
                
                // Check if the ClaimDefinition is actually an ARRAY type
                ClaimDefinitionJpaEntity claimDefinition = claimDefinitionRepository.findById(array.claimDefinitionId())
                        .orElseThrow(() -> new IllegalArgumentException("ClaimDefinition not found: " + array.claimDefinitionId()));
                
                if (claimDefinition.getDataType() == DataTypeEnum.ARRAY) {
                    // For ARRAY type ClaimDefinitions, create ONE ClaimValue with JSON serialized array
                    try {
                        String jsonValue = objectMapper.writeValueAsString(array.values());
                        yield List.of(new CreateClaimValueCommand(
                                array.claimSetId(),
                                array.claimDefinitionId(),
                                null, // identityId
                                accountId,
                                jsonValue,
                                true, // isPrimary for array types
                                false, // isComputed – user supplied
                                array.source(),
                                array.primaryIndex())); // primaryIndex for ARRAY type
                    } catch (JsonProcessingException e) {
                        throw new IllegalArgumentException("Failed to serialize array values to JSON", e);
                    }
                } else {
                    // For non-ARRAY type ClaimDefinitions, create individual ClaimValues per element
                    List<CreateClaimValueCommand> commands = new ArrayList<>();
                    for (int i = 0; i < array.values().size(); i++) {
                        boolean isPrimary = i == array.primaryIndex();
                        commands.add(new CreateClaimValueCommand(
                                array.claimSetId(),
                                array.claimDefinitionId(),
                                null, // identityId
                                accountId,
                                array.values().get(i),
                                isPrimary,
                                false, // isComputed – user supplied
                                array.source(),
                                isPrimary ? i : null)); // primaryIndex - set to index if this is the primary element
                    }
                    yield commands;
                }
            }
            case ComplexClaimValueRequest complex -> List.of(
                    new CreateClaimValueCommand(
                            complex.claimSetId(),
                            complex.claimDefinitionId(),
                            null, // identityId
                            accountId,
                            complex.value(),
                            true, // isPrimary - scalar values are always primary
                            false, // isComputed – user supplied
                            complex.source(),
                            null) // primaryIndex - not applicable for scalar values
            );
            case ArrayComplexClaimValueRequest arrayComplex -> {
                // Skip processing if array is empty
                if (arrayComplex.values().isEmpty()) {
                    yield List.of(); // Return empty list - no commands to process
                }

                // Check if the ClaimDefinition is actually an ARRAY type
                ClaimDefinitionJpaEntity claimDefinition = claimDefinitionRepository.findById(arrayComplex.claimDefinitionId())
                        .orElseThrow(() -> new IllegalArgumentException("ClaimDefinition not found: " + arrayComplex.claimDefinitionId()));

                if (claimDefinition.getDataType() == DataTypeEnum.ARRAY) {
                    // For ARRAY type ClaimDefinitions, create ONE ClaimValue with JSON serialized array
                    try {
                        String jsonValue = objectMapper.writeValueAsString(arrayComplex.values());
                        yield List.of(new CreateClaimValueCommand(
                                arrayComplex.claimSetId(),
                                arrayComplex.claimDefinitionId(),
                                null, // identityId
                                accountId,
                                jsonValue,
                                true, // isPrimary for array types
                                false, // isComputed – user supplied
                                arrayComplex.source(),
                                arrayComplex.primaryIndex())); // primaryIndex for ARRAY type
                    } catch (JsonProcessingException e) {
                        throw new IllegalArgumentException("Failed to serialize array values to JSON", e);
                    }
                } else {
                    // For non-ARRAY type ClaimDefinitions, create individual ClaimValues per element
                    List<CreateClaimValueCommand> commands = new ArrayList<>();
                    for (int i = 0; i < arrayComplex.values().size(); i++) {
                        boolean isPrimary = i == arrayComplex.primaryIndex();
                        commands.add(new CreateClaimValueCommand(
                                arrayComplex.claimSetId(),
                                arrayComplex.claimDefinitionId(),
                                null, // identityId
                                accountId,
                                arrayComplex.values().get(i),
                                isPrimary,
                                false, // isComputed – user supplied
                                arrayComplex.source(),
                                isPrimary ? i : null)); // primaryIndex - set to index if this is the primary element
                    }
                    yield commands;
                }
            }
        };
    }
}
