package com.vusecurity.auth.scim.mapper;

import com.vusecurity.auth.contracts.api.v1.dto.scimConfig.ScimTokenResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse.AuditInfo;
import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimTokenJpaEntity;

public class ScimTokenDtoMapper {

    public static ScimTokenResponse toResponse(ScimTokenJpaEntity entity){
        return toResponse(entity, true);
    }

    public static ScimTokenResponse toResponse(ScimTokenJpaEntity entity, boolean includeDetails){
        ScimTokenResponse response = new ScimTokenResponse()
            .setId(entity.getId())
            .setName(entity.getName())
            .setDescription(entity.getDescription())
            .setBusinesName(entity.getBusiness().getName())
            .setBusinessId(entity.getBusiness().getId())
            .setIdentityProviderName(entity.getIdentityProvider().getName())
            .setIdentityProviderId(entity.getIdentityProvider().getId())
            .setStatus(entity.getStatus());
        
        if (includeDetails){
            //Set audit info
            response.setAudit(
                new AuditInfo()
                    .setCreatedAt(entity.getCreatedAt())
                    .setCreatedBy(entity.getCreatedBy())
                    .setUpdatedAt(entity.getUpdatedAt())
                    .setUpdatedBy(entity.getUpdatedBy())
                );
        }
        
        return response;
    }

}
