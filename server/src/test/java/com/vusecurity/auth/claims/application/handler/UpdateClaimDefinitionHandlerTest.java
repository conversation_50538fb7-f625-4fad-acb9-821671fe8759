package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.DeleteClaimDefinitionCommand;
import com.vusecurity.auth.claims.application.command.UpdateClaimDefinitionCommand;
import com.vusecurity.auth.claims.application.exception.DeleteClaimTypeSystemException;
import com.vusecurity.auth.claims.application.exception.UpdateClaimTypeSystemException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.contracts.enums.ClaimType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UpdateClaimDefinitionHandlerTest {

    @Mock
    private ClaimDefinitionRepository claimDefinitionRepository;

    @InjectMocks
    private UpdateClaimDefinitionHandler handler;

    @Test
    void should_not_fail_when_deleting_definition_of_claimType_user() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.NUMERIC, null, ClaimType.USER_DEFINED);

        UpdateClaimDefinitionCommand cmd = new UpdateClaimDefinitionCommand(
            definition.getId(),
            definition.getCode(),
            definition.getName(),
            "New description",
            definition.getDataType(),
            definition.getDataFormat());
        
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        assertDoesNotThrow(() -> handler.updateClaimDefinition(cmd));
    }

    @Test
    void should_fail_when_deleting_definition_of_claimType_system() {
        // Given
        UUID claimDefinitionId = UUID.randomUUID();

        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.NUMERIC, null, ClaimType.SYSTEM_DEFINED);

        UpdateClaimDefinitionCommand cmd = new UpdateClaimDefinitionCommand(
            definition.getId(),
            definition.getCode(),
            definition.getName(),
            "New description",
            definition.getDataType(),
            definition.getDataFormat());
        
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        assertThrows(UpdateClaimTypeSystemException.class, () -> handler.updateClaimDefinition(cmd));
    }

    private ClaimDefinitionJpaEntity createClaimDefinition(UUID id, DataTypeEnum dataType, String dataFormat, ClaimType claimType) {
        ClaimDefinitionJpaEntity definition = new ClaimDefinitionJpaEntity();
        definition.setId(id);
        definition.setCode("test-code");
        definition.setName("Test Claim");
        definition.setDataType(dataType);
        definition.setDataFormat(dataFormat);
        definition.setClaimType(claimType);
        return definition;
    }
}