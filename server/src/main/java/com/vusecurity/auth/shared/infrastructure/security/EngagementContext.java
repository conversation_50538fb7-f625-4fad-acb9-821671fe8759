package com.vusecurity.auth.shared.infrastructure.security;

import java.util.UUID;

import lombok.Data;

public class EngagementContext {
    // Private constructor to prevent instantiation
    private EngagementContext() {
        // No-op
    }

    private static ThreadLocal<Engagement> engagementContextThreadLocal = new ThreadLocal<>();

    // Establecer el EngagementContext para el hilo actual
    public static void setEngagementContext(Engagement context) {
        engagementContextThreadLocal.set(context);
    }

    public static void setBusinessId(UUID businessId) {
        Engagement engagement = getEngagementContext();
        engagement.setBusinessId(businessId);
        setEngagementContext(engagement);
    }

    public static void setChannelId(UUID channelId) {
        Engagement engagement = getEngagementContext();
        engagement.setChannelId(channelId);
        setEngagementContext(engagement);
    }

    // Obtener el EngagementContext del hilo actual
    public static Engagement getEngagementContext() {
        // Si no hay un EngagementContext establecido, devolver un nuevo Engagement por
        // defecto
        if (engagementContextThreadLocal.get() == null) {
            engagementContextThreadLocal.set(new Engagement());
        }
        return engagementContextThreadLocal.get();
    }

    // Limpiar el contexto del hilo actual (importante para evitar fugas de memoria)
    public static void clear() {
        engagementContextThreadLocal.remove();
    }

    public static UUID getBusinessId() {
        return getEngagementContext().getBusinessId();
    }

    public static UUID getChannelId() {
        return getEngagementContext().getChannelId();
    }

    @Data
    public static class Engagement {
        private UUID businessId;
        private UUID channelId;
    }
}
