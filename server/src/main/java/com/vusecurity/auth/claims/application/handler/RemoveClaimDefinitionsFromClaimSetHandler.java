package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.RemoveClaimDefinitionsFromClaimSetCommand;
import com.vusecurity.auth.claims.application.exception.ClaimDefinitionNotFoundException;
import com.vusecurity.auth.claims.application.exception.ClaimSetNotFoundException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.service.ClaimSetDefinitionMappingService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Handler for removing claim definitions from a claim set.
 */
@Service
@RequiredArgsConstructor
public class RemoveClaimDefinitionsFromClaimSetHandler {

    private static final Logger logger = LoggerFactory.getLogger(RemoveClaimDefinitionsFromClaimSetHandler.class);

    private final ClaimSetRepository claimSetRepository;
    private final ClaimDefinitionRepository claimDefinitionRepository;
    private final ClaimSetDefinitionMappingService mappingService;

    @Transactional
    public void removeClaimDefinitionFromClaimSet(RemoveClaimDefinitionsFromClaimSetCommand command) {
        logger.debug("Removing claim definition from claim set with command: {}", command);

        try {
            // Validate command
            command.validate();

            // Find the claim set
            ClaimSetJpaEntity claimSet = claimSetRepository.findById(command.claimSetId())
                    .orElseThrow(() -> new ClaimSetNotFoundException("Claim set not found with ID: " + command.claimSetId()));

            // Find and remove each claim definition from the claim set
            command.claimDefinitionIds().forEach(claimDefinitionId -> {
                ClaimDefinitionJpaEntity claimDefinition = claimDefinitionRepository.findById(claimDefinitionId)
                    .orElseThrow(() -> new ClaimDefinitionNotFoundException("Claim definition not found with ID: " + claimDefinitionId));

                // Remove the mapping between claim set and claim definition
                mappingService.removeClaimDefinitionFromClaimSet(claimSet, claimDefinition);
            });

            logger.debug("Successfully removed claim definition {} from claim set: {}", 
                    command.claimDefinitionIds(), command.claimSetId());

        } catch (Exception e) {
            logger.error("Error removing claim definition {} from claim set {}: {}", 
                    command.claimDefinitionIds(), command.claimSetId(), e.getMessage(), e);
            throw e;
        }
    }
}
