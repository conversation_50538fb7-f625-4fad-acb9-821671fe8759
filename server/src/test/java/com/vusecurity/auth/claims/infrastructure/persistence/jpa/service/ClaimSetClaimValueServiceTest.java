package com.vusecurity.auth.claims.infrastructure.persistence.jpa.service;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.*;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetClaimValuesRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueReferencesRepository;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ClaimSetClaimValueServiceTest {

    @Mock
    private ClaimSetClaimValuesRepository claimSetClaimValuesRepo;
    
    @Mock
    private ClaimValueReferencesRepository claimValueReferencesRepo;
    
    @InjectMocks
    private ClaimSetClaimValueService service;
    
    private ClaimSetJpaEntity testClaimSet;
    private ClaimValueJpaEntity testClaimValue;
    private ClaimDefinitionJpaEntity testClaimDefinition;
    private UUID claimSetId;
    private UUID claimValueId;
    
    @BeforeEach
    void setUp() {
        claimSetId = UUID.randomUUID();
        claimValueId = UUID.randomUUID();
        
        testClaimSet = new ClaimSetJpaEntity();
        testClaimSet.setId(claimSetId);
        testClaimSet.setName("Test ClaimSet");
        
        testClaimDefinition = new ClaimDefinitionJpaEntity();
        testClaimDefinition.setId(UUID.randomUUID());
        testClaimDefinition.setCode("test_claim");
        testClaimDefinition.setDataType(DataTypeEnum.STRING);
        
        testClaimValue = new ClaimValueJpaEntity();
        testClaimValue.setId(claimValueId);
        testClaimValue.setValue("test value");
        testClaimValue.setClaimDefinition(testClaimDefinition);
    }
    
    @Test
    void shouldAssociateClaimValueToClaimSet_WhenNotAlreadyAssociated() {
        // Given
        when(claimSetClaimValuesRepo.existsByClaimSetIdAndClaimValueId(claimSetId, claimValueId))
            .thenReturn(false);
        
        ClaimSetClaimValuesJpaEntity expectedAssociation = new ClaimSetClaimValuesJpaEntity(testClaimSet, testClaimValue);
        when(claimSetClaimValuesRepo.save(any(ClaimSetClaimValuesJpaEntity.class)))
            .thenReturn(expectedAssociation);
        
        // When
        ClaimSetClaimValuesJpaEntity result = service.associateClaimValueToClaimSet(testClaimSet, testClaimValue);
        
        // Then
        assertNotNull(result);
        assertEquals(testClaimSet, result.getClaimSet());
        assertEquals(testClaimValue, result.getClaimValue());
        verify(claimSetClaimValuesRepo).existsByClaimSetIdAndClaimValueId(claimSetId, claimValueId);
        verify(claimSetClaimValuesRepo).save(any(ClaimSetClaimValuesJpaEntity.class));
    }
    
    @Test
    void shouldThrowException_WhenClaimValueAlreadyAssociated() {
        // Given
        when(claimSetClaimValuesRepo.existsByClaimSetIdAndClaimValueId(claimSetId, claimValueId))
            .thenReturn(true);
        
        // When & Then
        assertThrows(IllegalArgumentException.class, 
            () -> service.associateClaimValueToClaimSet(testClaimSet, testClaimValue));
        
        verify(claimSetClaimValuesRepo).existsByClaimSetIdAndClaimValueId(claimSetId, claimValueId);
        verify(claimSetClaimValuesRepo, never()).save(any());
    }
    
    @Test
    void shouldCreateClaimSetReference_Successfully() {
        // Given
        ClaimSetJpaEntity referencedClaimSet = new ClaimSetJpaEntity();
        referencedClaimSet.setId(UUID.randomUUID());
        
        ClaimValueReferencesJpaEntity expectedReference = new ClaimValueReferencesJpaEntity(
            testClaimValue, referencedClaimSet, testClaimDefinition, 1);
        
        when(claimValueReferencesRepo.save(any(ClaimValueReferencesJpaEntity.class)))
            .thenReturn(expectedReference);
        
        // When
        ClaimValueReferencesJpaEntity result = service.createClaimSetReference(
            testClaimValue, referencedClaimSet, testClaimDefinition, 1);
        
        // Then
        assertNotNull(result);
        assertEquals(testClaimValue, result.getSourceClaimValue());
        assertEquals(referencedClaimSet, result.getReferencedClaimSet());
        assertEquals(testClaimDefinition, result.getReferencedClaimDefinition());
        assertEquals(1, result.getSequenceOrder());
        verify(claimValueReferencesRepo).save(any(ClaimValueReferencesJpaEntity.class));
    }
    
    @Test
    void shouldGetClaimValuesForClaimSet_Successfully() {
        // Given
        ClaimSetClaimValuesJpaEntity association1 = new ClaimSetClaimValuesJpaEntity(testClaimSet, testClaimValue);
        
        ClaimValueJpaEntity claimValue2 = new ClaimValueJpaEntity();
        claimValue2.setId(UUID.randomUUID());
        ClaimSetClaimValuesJpaEntity association2 = new ClaimSetClaimValuesJpaEntity(testClaimSet, claimValue2);
        
        when(claimSetClaimValuesRepo.findByClaimSetId(claimSetId))
            .thenReturn(List.of(association1, association2));
        
        // When
        List<ClaimValueJpaEntity> result = service.getClaimValuesForClaimSet(claimSetId);
        
        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(testClaimValue));
        assertTrue(result.contains(claimValue2));
        verify(claimSetClaimValuesRepo).findByClaimSetId(claimSetId);
    }
    
    @Test
    void shouldGetReferencesForClaimValue_Successfully() {
        // Given
        ClaimValueReferencesJpaEntity reference1 = new ClaimValueReferencesJpaEntity();
        ClaimValueReferencesJpaEntity reference2 = new ClaimValueReferencesJpaEntity();
        
        when(claimValueReferencesRepo.findBySourceClaimValueId(claimValueId))
            .thenReturn(List.of(reference1, reference2));
        
        // When
        List<ClaimValueReferencesJpaEntity> result = service.getReferencesForClaimValue(claimValueId);
        
        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(claimValueReferencesRepo).findBySourceClaimValueId(claimValueId);
    }
    
    @Test
    void shouldRemoveAllAssociationsForClaimSet_Successfully() {
        // When
        service.removeAllAssociationsForClaimSet(claimSetId);
        
        // Then
        verify(claimSetClaimValuesRepo).deleteByClaimSetId(claimSetId);
    }
    
    @Test
    void shouldRemoveAllAssociationsForClaimValue_Successfully() {
        // When
        service.removeAllAssociationsForClaimValue(claimValueId);
        
        // Then
        verify(claimSetClaimValuesRepo).deleteByClaimValueId(claimValueId);
        verify(claimValueReferencesRepo).deleteBySourceClaimValueId(claimValueId);
    }
}