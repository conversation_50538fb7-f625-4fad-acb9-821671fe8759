package com.vusecurity.auth.contracts.api.v1.dto.scimConfig;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.UUID;

@Data
@Schema(description = "Request to create a new SCIM authorization token")
public class CreateScimTokenRequest {
    @NotNull(message = "Name is required")
    @Size(max = 255, message = "Name must not exceed 255 characters")
    @Schema(description = "Human-readable name for the token",
            example = "AzureAd")
    private String name;

    @Size(max = 1024, message = "Description must not exceed 1024 characters")
    @Schema(description = "Detailed description of what this token represents",
            example = "SCIM token for MyDomain and My Business")
    private String description;

    @NotNull(message = "Business Id is required")
    @Schema(description = "Id of a business",
            example = "f9fc0a64-56fc-4477-87a7-8e525505abce")
    private UUID businessId;

    @NotNull(message = "Identity Provider Id is required")
    @Schema(description = "Id of a Identity Provider",
            example = "f5250a64-5f6c-4433-84a7-9fc8e505abce")
    private UUID identityProviderId;
}
