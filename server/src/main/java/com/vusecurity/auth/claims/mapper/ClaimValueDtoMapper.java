package com.vusecurity.auth.claims.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetClaimValuesJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetClaimValuesRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueReferencesRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.ClaimValueResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Mapper for converting between ClaimValueJpaEntity and DTOs.
 * Manual mapping implementation following the project's preference over MapStruct.
 */
@Component
@RequiredArgsConstructor
public class ClaimValueDtoMapper {

    private static final Logger log = LoggerFactory.getLogger(ClaimValueDtoMapper.class);

    private final ObjectMapper objectMapper;
    private final ClaimValueRepository claimValueRepository;
    private final ClaimValueReferencesRepository claimValueReferencesRepository;
    private final ClaimSetClaimValuesRepository claimSetClaimValuesRepository;

    /**
     * Convert ClaimValueJpaEntity to ClaimValueResponse.
     * @param entity the JPA entity
     * @return the response DTO
     */
    public ClaimValueResponse toResponse(ClaimValueJpaEntity entity) {
        if (entity == null) {
            return null;
        }

        ClaimValueResponse response = new ClaimValueResponse();
        response.setId(entity.getId());
        response.setClaimDefinitionId(entity.getClaimDefinition() != null ? entity.getClaimDefinition().getId() : null);
        response.setClaimSetId(entity.getClaimSetClaimValue() != null ? entity.getClaimSetClaimValue().getClaimSetId() : null);
        response.setOwnerType(entity.getOwnerType());
        response.setOwnerId(entity.getOwnerId());
        response.setValue(parseValueForResponse(entity));
        response.setPrimary(entity.isPrimary());
        response.setComputed(entity.isComputed());
        response.setSource(entity.getSource());
        response.setPrimaryIndex(entity.getPrimaryIndex());
        
        // Set audit information
        response.setAudit(new BaseResponse.AuditInfo()
                .setCreatedAt(entity.getCreatedAt())
                .setUpdatedAt(entity.getUpdatedAt())
                .setCreatedBy(entity.getCreatedBy())
                .setUpdatedBy(entity.getUpdatedBy()));

        return response;
    }

    /**
     * Parse the claim value based on the ClaimDefinition data type.
     * For ARRAY types, parse JSON string to actual array.
     * For other types, return the string value as-is.
     * 
     * @param entity the ClaimValue entity
     * @return parsed value (Object for arrays, String for others)
     */
    private Object parseValueForResponse(ClaimValueJpaEntity entity) {
        String rawValue = entity.getValue();
        if (rawValue == null) {
            return null;
        }

        ClaimDefinitionJpaEntity claimDefinition = entity.getClaimDefinition();
        if (claimDefinition == null) {
            return rawValue;
        }

        DataTypeEnum dataType = claimDefinition.getDataType();

        return switch (dataType) {
            case ARRAY -> {
                try {
                    yield objectMapper.readValue(rawValue, Object[].class);
                } catch (JsonProcessingException e) {
                    log.warn("Failed to parse ARRAY claim value: {}", e.getMessage());
                    yield rawValue;
                }
            }
            case CLAIMSET -> {
                // Para CLAIMSET, devolver la estructura completa del ClaimSet
                try {
                    UUID claimSetId = UUID.fromString(rawValue);
                    yield buildClaimSetStructure(claimSetId, entity);
                } catch (IllegalArgumentException e) {
                    log.warn("Invalid CLAIMSET UUID: {}", rawValue);
                    yield rawValue;
                }
            }
            case NUMERIC -> {
                try {
                    yield new BigDecimal(rawValue);
                } catch (NumberFormatException e) {
                    yield rawValue;
                }
            }
            case BOOL -> Boolean.parseBoolean(rawValue);
            case DATE -> {
                try {
                    yield LocalDate.parse(rawValue);
                } catch (DateTimeParseException e) {
                    yield rawValue;
                }
            }
            case DATETIME -> {
                try {
                    yield OffsetDateTime.parse(rawValue);
                } catch (DateTimeParseException e) {
                    yield rawValue;
                }
            }
            default -> rawValue; // STRING, IMAGE
        };
    }

    private Object buildClaimSetStructure(UUID claimSetId, ClaimValueJpaEntity parentClaimValue) {
        // Obtener ClaimValues asociados al ClaimSet usando la tabla de referencias
        List<ClaimSetClaimValuesJpaEntity> claimSetAssociations =
            claimSetClaimValuesRepository.findByClaimSetId(claimSetId);

        Map<String, Object> claimSetStructure = new HashMap<>();

        for (ClaimSetClaimValuesJpaEntity association : claimSetAssociations) {
            ClaimValueJpaEntity value = association.getClaimValue();

            // Filtrar por mismo owner
            if (value.getOwnerType().equals(parentClaimValue.getOwnerType()) &&
                value.getOwnerId().equals(parentClaimValue.getOwnerId())) {

                String claimCode = value.getClaimDefinition().getCode();
                Object parsedValue = parseValueForResponse(value);
                claimSetStructure.put(claimCode, parsedValue);
            }
        }

        return claimSetStructure;
    }
}
