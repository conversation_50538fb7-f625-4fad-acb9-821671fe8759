package com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.contracts.enums.IdentityLifecycleState;
import com.vusecurity.auth.contracts.enums.IdentityType;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for IdentityJpaEntity business logic.
 * Tests pure domain logic with no external dependencies.
 */
class IdentityJpaEntityTest {

    @Test
    void shouldCreateIdentityWithValidData() {
        // Given
        IdentityType identityType = IdentityType.PERSON;
        String name = "<PERSON>";

        // When
        IdentityJpaEntity identity = new IdentityJpaEntity(identityType, name);

        // Then
        assertEquals(identityType, identity.getIdentityType());
        assertEquals(name, identity.getName());
        assertEquals(IdentityLifecycleState.NONE, identity.getLifecycleState());
        assertNotNull(identity.getMetadata());
        assertTrue(identity.getMetadata().isEmpty());
        assertNotNull(identity.getAccounts());
        assertTrue(identity.getAccounts().isEmpty());
        assertNull(identity.getMergedTo());
    }

    @Test
    void shouldTrimNameInConstructor() {
        // Given
        IdentityType identityType = IdentityType.PERSON;
        String nameWithWhitespace = "  John Doe  ";

        // When
        IdentityJpaEntity identity = new IdentityJpaEntity(identityType, nameWithWhitespace);

        // Then
        assertEquals("John Doe", identity.getName());
    }

    @Test
    void shouldThrowException_WhenIdentityTypeIsNull() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> new IdentityJpaEntity(null, "John Doe")
        );
        assertEquals("identityType cannot be null", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenNameIsNull() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> new IdentityJpaEntity(IdentityType.PERSON, null)
        );
        assertEquals("name cannot be null or empty", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenNameIsEmpty() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> new IdentityJpaEntity(IdentityType.PERSON, "")
        );
        assertEquals("name cannot be null or empty", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenNameIsWhitespace() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> new IdentityJpaEntity(IdentityType.PERSON, "   ")
        );
        assertEquals("name cannot be null or empty", exception.getMessage());
    }

    @Test
    void shouldActivateIdentity() {
        // Given
        IdentityJpaEntity identity = createTestIdentity();
        Instant beforeActivation = Instant.now();

        // When
        identity.activate();

        // Then
        assertTrue(identity.isActive());
        assertEquals(IdentityLifecycleState.ACTIVE, identity.getLifecycleState());
    }

    @Test
    void shouldSuspendActiveIdentity() {
        // Given
        IdentityJpaEntity identity = createTestIdentity();
        identity.activate();

        // When
        identity.suspend();

        // Then
        assertTrue(identity.isSuspended());
        assertEquals(IdentityLifecycleState.SUSPENDED, identity.getLifecycleState());
    }

    @Test
    void shouldThrowException_WhenSuspendingNonActiveIdentity() {
        // Given
        IdentityJpaEntity identity = createTestIdentity();
        // Identity is not active (default state is NONE)

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, identity::suspend);
        assertEquals("Only active identities can be suspended", exception.getMessage());
    }

//    @Test
//    void shouldDeactivateIdentity() {
//        // Given
//        IdentityJpaEntity identity = createTestIdentity();
//        identity.activate();
//        Instant beforeDeactivation = Instant.now();
//
//        // When
//        identity.deactivate();
//
//        // Then
//        assertTrue(identity.isInactive());
//        assertEquals(IdentityLifecycleState.DEACTIVATED, identity.getLifecycleState());
//        assertNotNull(identity.getUpdatedAt());
//        assertTrue(identity.getUpdatedAt().isAfter(beforeDeactivation) || identity.getUpdatedAt().equals(beforeDeactivation));
//    }

    @Test
    void shouldCheckLifecycleStates() {
        // Given
        IdentityJpaEntity identity = createTestIdentity();

        // Test initial state
        assertFalse(identity.isActive());
        assertFalse(identity.isSuspended());
        assertFalse(identity.isInactive());

        // Test active state
        identity.activate();
        assertTrue(identity.isActive());
        assertFalse(identity.isSuspended());
        assertFalse(identity.isInactive());

        // Test suspended state
        identity.suspend();
        assertFalse(identity.isActive());
        assertTrue(identity.isSuspended());
        assertFalse(identity.isInactive());

        // Test deactivated state
        identity.setLifecycleState(IdentityLifecycleState.ACTIVE); // Reset to active first
        identity.deactivate();
        assertFalse(identity.isActive());
        assertFalse(identity.isSuspended());
        assertTrue(identity.isInactive());
    }

    @Test
    void shouldMergeIdentity() {
        // Given
        IdentityJpaEntity identity = createTestIdentity();
        UUID targetIdentityId = UUID.randomUUID();
        // When
        identity.mergeInto(targetIdentityId);

        // Then
        assertEquals(targetIdentityId, identity.getMergedTo());
        assertEquals(IdentityLifecycleState.INACTIVE, identity.getLifecycleState());
    }

    @Test
    void shouldThrowException_WhenMergingIntoNull() {
        // Given
        IdentityJpaEntity identity = createTestIdentity();

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> identity.mergeInto(null)
        );
        assertEquals("Target identity ID cannot be null", exception.getMessage());
    }

    @Test
    void shouldManageMetadata() {
        // Given
        IdentityJpaEntity identity = createTestIdentity();
        String key = "department";
        String value = "Engineering";
        // When
        identity.addMetadata(key, value);

        // Then
        assertEquals(value, identity.getMetadata().get(key));
    }

    @Test
    void shouldAddAndRemoveMetadata() {
        // Given
        IdentityJpaEntity identity = createTestIdentity();

        // When - Add metadata
        identity.addMetadata("key1", "value1");
        identity.addMetadata("key2", 42);
        identity.addMetadata("key3", true);

        // Then - Verify added
        assertEquals("value1", identity.getMetadata().get("key1"));
        assertEquals(42, identity.getMetadata().get("key2"));
        assertEquals(true, identity.getMetadata().get("key3"));

        // When - Remove metadata
        identity.removeMetadata("key1");

        // Then - Verify removed
        assertFalse(identity.getMetadata().containsKey("key1"));
        assertTrue(identity.getMetadata().containsKey("key2"));
        assertTrue(identity.getMetadata().containsKey("key3"));
    }

    @Test
    void shouldThrowException_WhenAddingMetadataWithNullKey() {
        // Given
        IdentityJpaEntity identity = createTestIdentity();

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> identity.addMetadata(null, "value"));
        assertThrows(IllegalArgumentException.class, () -> identity.addMetadata("", "value"));
        assertThrows(IllegalArgumentException.class, () -> identity.addMetadata("   ", "value"));
    }

    @Test
    void shouldValidateForCreation() {
        // Given
        IdentityJpaEntity validIdentity = createTestIdentity();
        IdentityJpaEntity invalidIdentity1 = new IdentityJpaEntity();
        invalidIdentity1.setIdentityType(null);
        invalidIdentity1.setName("Valid Name");

        IdentityJpaEntity invalidIdentity2 = new IdentityJpaEntity();
        invalidIdentity2.setIdentityType(IdentityType.PERSON);
        invalidIdentity2.setName(null);

        // When & Then
        assertDoesNotThrow(validIdentity::validateForCreation);
        assertThrows(IllegalArgumentException.class, invalidIdentity1::validateForCreation);
        assertThrows(IllegalArgumentException.class, invalidIdentity2::validateForCreation);
    }

    @Test
    void shouldValidateForUpdate() {
        // Given
        IdentityJpaEntity validIdentity = createTestIdentity();
        validIdentity.setId(UUID.randomUUID());

        // When & Then
        assertDoesNotThrow(validIdentity::validateForUpdate);
    }

    @Test
    void shouldReturnDefensiveCopyOfMetadata() {
        // Given
        IdentityJpaEntity identity = createTestIdentity();
        identity.addMetadata("key1", "value1");

        // When
        Map<String, Object> metadata1 = identity.getMetadata();
        Map<String, Object> metadata2 = identity.getMetadata();

        // Then
        assertNotSame(metadata1, metadata2, "Should return different instances");
        assertEquals(metadata1, metadata2, "But content should be the same");

        // Modify returned map should not affect entity
        metadata1.put("newKey", "newValue");
        assertFalse(identity.getMetadata().containsKey("newKey"));
    }

    @Test
    void shouldSetMetadataWithDefensiveCopy() {
        // Given
        IdentityJpaEntity identity = createTestIdentity();
        Map<String, Object> originalMetadata = new HashMap<>();
        originalMetadata.put("key1", "value1");

        // When
        identity.setMetadata(originalMetadata);
        originalMetadata.put("key2", "value2"); // Modify original

        // Then
        assertFalse(identity.getMetadata().containsKey("key2"), 
                "Entity should not be affected by changes to original map");
        assertEquals(1, identity.getMetadata().size());
    }

    @Test
    void shouldCreateWithBuilder() {
        // Given
        IdentityType identityType = IdentityType.ORGANIZATION;
        String name = "Test Organization";

        // When
        IdentityJpaEntity identity = IdentityJpaEntity.builder(identityType, name);

        // Then
        assertNotNull(identity);
        assertEquals(identityType, identity.getIdentityType());
        assertEquals(name, identity.getName());
        assertEquals(IdentityLifecycleState.NONE, identity.getLifecycleState());
        assertNotNull(identity.getMetadata());
        assertTrue(identity.getMetadata().isEmpty());
    }

    @Test
    void shouldCreateWithBuilderAndId() {
        // Given
        UUID id = UUID.randomUUID();
        IdentityType identityType = IdentityType.SERVICE;
        String name = "Test Service";

        // When
        IdentityJpaEntity identity = IdentityJpaEntity.builderWithId(id, identityType, name);

        // Then
        assertNotNull(identity);
        assertEquals(id, identity.getId());
        assertEquals(identityType, identity.getIdentityType());
        assertEquals(name, identity.getName());
    }

    @Test
    void shouldThrowException_InBuilderWithNullParameters() {
        // When & Then
        assertThrows(IllegalArgumentException.class, 
                () -> IdentityJpaEntity.builder(null, "Name"));
        assertThrows(IllegalArgumentException.class, 
                () -> IdentityJpaEntity.builder(IdentityType.PERSON, null));
        assertThrows(IllegalArgumentException.class, 
                () -> IdentityJpaEntity.builder(IdentityType.PERSON, ""));
        assertThrows(IllegalArgumentException.class, 
                () -> IdentityJpaEntity.builderWithId(null, IdentityType.PERSON, "Name"));
    }

    @Test
    void shouldTestAllIdentityTypes() {
        // Test all enum values
        for (IdentityType identityType : IdentityType.values()) {
            if (identityType != IdentityType.NONE) { // Skip NONE as it might have special handling
                // Given & When
                IdentityJpaEntity identity = new IdentityJpaEntity(identityType, "Test " + identityType.name());

                // Then
                assertEquals(identityType, identity.getIdentityType(), 
                        "Should correctly set IdentityType: " + identityType);
            }
        }
    }

    // Helper method
    private IdentityJpaEntity createTestIdentity() {
        return new IdentityJpaEntity(IdentityType.PERSON, "John Doe");
    }
}
