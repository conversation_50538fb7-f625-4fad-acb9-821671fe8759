package com.vusecurity.auth.claims.mapper;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition.ClaimDefinitionResponse;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition.ClaimDefinitionSummaryResponse;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.ClaimSetSummaryResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Mapper for converting between ClaimDefinition entities and DTOs.
 */
@Component
@RequiredArgsConstructor
public class ClaimDefinitionDtoMapper {

    /**
     * Converts a ClaimDefinitionJpaEntity to ClaimDefinitionResponse.
     * @param entity the JPA entity
     * @return the response DTO
     */
    public ClaimDefinitionResponse toResponse(ClaimDefinitionJpaEntity entity) {
        if (entity == null) {
            return null;
        }

        ClaimDefinitionResponse response = new ClaimDefinitionResponse();
        response.setId(entity.getId());
        response.setCode(entity.getCode());
        response.setName(entity.getName());
        response.setDescription(entity.getDescription());
        response.setDataType(entity.getDataType());
        response.setDataFormat(entity.getDataFormat());

        // Map isAListOf field - get the ID from the referenced entity
        if (entity.getIsAListOf() != null) {
            response.setIsAListOf(entity.getIsAListOf().getId());
        }

        if (entity.getClaimType() != null){
            response.setClaimType(entity.getClaimType());
        }

        // Set audit information
        response.setAudit(new BaseResponse.AuditInfo()
                .setCreatedAt(entity.getCreatedAt())
                .setUpdatedAt(entity.getUpdatedAt())
                .setCreatedBy(entity.getCreatedBy())
                .setUpdatedBy(entity.getUpdatedBy()));

        // Map claim sets using the mapping entities
        List<ClaimSetSummaryResponse> claimSets = entity.getClaimSetMappings().stream()
                .map(mapping -> mapping.getClaimSet())
                .filter(Objects::nonNull)
                .map(this::toClaimSetSummaryResponse)
                .collect(Collectors.toList());

        response.setClaimSets(claimSets);

        return response;
    }

    /**
     * Converts a ClaimDefinitionJpaEntity to ClaimDefinitionSummaryResponse.
     * @param entity the JPA entity
     * @return the summary response DTO
     */
    public ClaimDefinitionSummaryResponse toSummaryResponse(ClaimDefinitionJpaEntity entity) {
        if (entity == null) {
            return null;
        }

        ClaimDefinitionSummaryResponse response = new ClaimDefinitionSummaryResponse();
        response.setId(entity.getId());
        response.setCode(entity.getCode());
        response.setName(entity.getName());
        response.setDescription(entity.getDescription());
        response.setClaimType(entity.getClaimType());
        response.setDataType(entity.getDataType());
        response.setDataFormat(entity.getDataFormat());
        return response;
    }

    /**
     * Converts a ClaimSetJpaEntity to ClaimSetSummaryResponse.
     * @param entity the JPA entity
     * @return the summary response DTO
     */
    private ClaimSetSummaryResponse toClaimSetSummaryResponse(ClaimSetJpaEntity entity) {
        if (entity == null) {
            return null;
        }

        ClaimSetSummaryResponse response = new ClaimSetSummaryResponse();
        response.setId(entity.getId());
        response.setBusinessId(entity.getBusinessId());
        response.setAccountType(entity.getAccountType());
        response.setIsIdentifier(entity.getIsIdentifier());
        response.setName(entity.getName());
        response.setDescription(entity.getDescription());
        response.setLookupStrategy(entity.getLookupStrategy());
        return response;
    }
}
