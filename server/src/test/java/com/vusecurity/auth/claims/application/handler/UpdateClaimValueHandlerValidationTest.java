package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.UpdateClaimValueCommand;
import com.vusecurity.auth.claims.application.exception.ClaimValueNotFoundException;
import com.vusecurity.auth.claims.application.exception.ClaimValueValidationException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UpdateClaimValueHandlerValidationTest {

    @Mock
    private ClaimValueRepository claimValueRepository;
    
    @Mock
    private ClaimDefinitionRepository claimDefinitionRepository;

    @InjectMocks
    private UpdateClaimValueHandler handler;

    @Test
    void should_throw_exception_when_claim_value_not_found() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                null,
                null,
                null,
                "test-value",
                null,
                null,
                null,
                null,
                null
        );

        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.empty());

        // When & Then
        ClaimValueNotFoundException exception = assertThrows(
                ClaimValueNotFoundException.class,
                () -> handler.updateClaimValue(cmd)
        );
        assertEquals("Claim value not found: " + claimValueId, exception.getMessage());
    }

    @Test
    void should_validate_numeric_data_type_successfully() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                null,
                UUID.randomUUID(),
                null,
                "123.45",
                null,
                null,
                null,
                null,
                null
        );

        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.NUMERIC, null);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        when(claimValueRepository.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(existingValue);

        // When & Then
        assertDoesNotThrow(() -> handler.updateClaimValue(cmd));
        verify(claimValueRepository).saveAndFlush(any(ClaimValueJpaEntity.class));
    }

    @Test
    void should_reject_invalid_numeric_data_type() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                null,
                UUID.randomUUID(),
                null,
                "not-a-number",
                null,
                null,
                null,
                null,
                null
        );

        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.NUMERIC, null);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        ClaimValueValidationException exception = assertThrows(
                ClaimValueValidationException.class,
                () -> handler.updateClaimValue(cmd)
        );
        assertEquals("Value does not match data type: NUMERIC", exception.getMessage());
    }

    @Test
    void should_validate_boolean_data_type_successfully() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                null,
                UUID.randomUUID(),
                null,
                "false",
                null,
                null,
                null,
                null,
                null
        );

        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.BOOL, null);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        when(claimValueRepository.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(existingValue);

        // When & Then
        assertDoesNotThrow(() -> handler.updateClaimValue(cmd));
        verify(claimValueRepository).saveAndFlush(any(ClaimValueJpaEntity.class));
    }

    @Test
    void should_reject_invalid_boolean_data_type() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                null,
                UUID.randomUUID(),
                null,
                "maybe",
                null,
                null,
                null,
                null,
                null
        );

        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.BOOL, null);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        ClaimValueValidationException exception = assertThrows(
                ClaimValueValidationException.class,
                () -> handler.updateClaimValue(cmd)
        );
        assertEquals("Value does not match data type: BOOL", exception.getMessage());
    }

    @Test
    void should_validate_date_data_type_successfully() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                null,
                UUID.randomUUID(),
                null,
                "2023-12-25",
                null,
                null,
                null,
                null,
                null
        );

        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.DATE, null);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        when(claimValueRepository.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(existingValue);

        // When & Then
        assertDoesNotThrow(() -> handler.updateClaimValue(cmd));
        verify(claimValueRepository).saveAndFlush(any(ClaimValueJpaEntity.class));
    }

    @Test
    void should_reject_invalid_date_data_type() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                null,
                UUID.randomUUID(),
                null,
                "2023-13-01", // Invalid month
                null,
                null,
                null,
                null,
                null
        );

        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.DATE, null);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        ClaimValueValidationException exception = assertThrows(
                ClaimValueValidationException.class,
                () -> handler.updateClaimValue(cmd)
        );
        assertEquals("Value does not match data type: DATE", exception.getMessage());
    }

    @Test
    void should_validate_datetime_data_type_successfully() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                null,
                UUID.randomUUID(),
                null,
                "2023-12-25T10:30:00Z",
                null,
                null,
                null,
                null,
                null
        );

        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.DATETIME, null);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        when(claimValueRepository.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(existingValue);

        // When & Then
        assertDoesNotThrow(() -> handler.updateClaimValue(cmd));
        verify(claimValueRepository).saveAndFlush(any(ClaimValueJpaEntity.class));
    }

    @Test
    void should_reject_invalid_datetime_data_type() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                null,
                UUID.randomUUID(),
                null,
                "2023-12-25 10:30:00", // Invalid format
                null,
                null,
                null,
                null,
                null
        );

        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.DATETIME, null);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        ClaimValueValidationException exception = assertThrows(
                ClaimValueValidationException.class,
                () -> handler.updateClaimValue(cmd)
        );
        assertEquals("Value does not match data type: DATETIME", exception.getMessage());
    }

    @Test
    void should_validate_regex_pattern_successfully() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                null,
                UUID.randomUUID(),
                null,
                "<EMAIL>",
                null,
                null,
                null,
                null,
                null
        );

        String emailRegex = "^[\\w-.]+@([\\w-]+\\.)+[\\w-]{2,4}$";
        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.STRING, emailRegex);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        when(claimValueRepository.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(existingValue);

        // When & Then
        assertDoesNotThrow(() -> handler.updateClaimValue(cmd));
        verify(claimValueRepository).saveAndFlush(any(ClaimValueJpaEntity.class));
    }

    @Test
    void should_reject_invalid_regex_pattern() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                null,
                UUID.randomUUID(),
                null,
                "not-an-email",
                null,
                null,
                null,
                null,
                null
        );

        String emailRegex = "^[\\w-.]+@([\\w-]+\\.)+[\\w-]{2,4}$";
        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.STRING, emailRegex);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> handler.updateClaimValue(cmd)
        );
        assertEquals("value does not match the required pattern", exception.getMessage());
    }

    @Test
    void should_normalize_boolean_values() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                null,
                UUID.randomUUID(),
                null,
                "1", // Should be normalized to "true"
                null,
                null,
                null,
                null,
                null
        );

        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.BOOL, null);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        when(claimValueRepository.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(existingValue);

        // When
        handler.updateClaimValue(cmd);

        // Then
        verify(claimValueRepository).saveAndFlush(argThat(entity -> "true".equals(entity.getValue())));
    }

    @Test
    void should_normalize_numeric_values() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                null,
                UUID.randomUUID(),
                null,
                "456.00", // Should be normalized to "456"
                null,
                null,
                null,
                null,
                null
        );

        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.NUMERIC, null);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        when(claimValueRepository.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(existingValue);

        // When
        handler.updateClaimValue(cmd);

        // Then
        verify(claimValueRepository).saveAndFlush(argThat(entity -> "456".equals(entity.getValue())));
    }

    @Test
    void should_update_with_new_claim_definition() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID oldClaimDefinitionId = UUID.randomUUID();
        UUID newClaimDefinitionId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                newClaimDefinitionId,
                UUID.randomUUID(),
                null,
                "789.99",
                null,
                null,
                null,
                null,
                null
        );

        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, oldClaimDefinitionId);
        ClaimDefinitionJpaEntity newDefinition = createClaimDefinition(newClaimDefinitionId, DataTypeEnum.NUMERIC, null);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));
        when(claimDefinitionRepository.findById(newClaimDefinitionId)).thenReturn(Optional.of(newDefinition));
        when(claimValueRepository.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(existingValue);

        // When
        handler.updateClaimValue(cmd);

        // Then
        verify(claimValueRepository).saveAndFlush(argThat(entity -> 
            entity.getClaimDefinition().getId().equals(newClaimDefinitionId) &&
            "789.99".equals(entity.getValue())
        ));
    }

    @Test
    void should_skip_validation_when_value_is_null() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId,
                null,
                UUID.randomUUID(),
                null,
                null, // null value should skip validation
                true,
                null,
                null,
                null,
                null
        );

        ClaimValueJpaEntity existingValue = createClaimValue(claimValueId, claimDefinitionId);
        ClaimDefinitionJpaEntity definition = createClaimDefinition(claimDefinitionId, DataTypeEnum.NUMERIC, null);
        
        when(claimValueRepository.findById(claimValueId)).thenReturn(Optional.of(existingValue));
        when(claimDefinitionRepository.findById(claimDefinitionId)).thenReturn(Optional.of(definition));
        when(claimValueRepository.saveAndFlush(any(ClaimValueJpaEntity.class))).thenReturn(existingValue);

        // When & Then
        assertDoesNotThrow(() -> handler.updateClaimValue(cmd));
        verify(claimValueRepository).saveAndFlush(any(ClaimValueJpaEntity.class));
    }

    private ClaimValueJpaEntity createClaimValue(UUID id, UUID claimDefinitionId) {
        ClaimValueJpaEntity value = new ClaimValueJpaEntity();
        value.setId(id);
        
        ClaimDefinitionJpaEntity definition = new ClaimDefinitionJpaEntity();
        definition.setId(claimDefinitionId);
        value.setClaimDefinition(definition);
        
        return value;
    }

    private ClaimDefinitionJpaEntity createClaimDefinition(UUID id, DataTypeEnum dataType, String dataFormat) {
        ClaimDefinitionJpaEntity definition = new ClaimDefinitionJpaEntity();
        definition.setId(id);
        definition.setCode("test-code");
        definition.setName("Test Claim");
        definition.setDataType(dataType);
        definition.setDataFormat(dataFormat);
        return definition;
    }
}