package com.vusecurity.auth.contracts.validations;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.UUID;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

public class ValidRegexValidator implements ConstraintValidator<ValidRegex, String> {
    
    private static final Pattern UUID_PATTERN = Pattern.compile(
        "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"
    );
    
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.isEmpty()) return true;

        // Check if it's a UUID format first - UUIDs are valid for array type references
        if (isUuidFormat(value)) {
            return true;
        }

        // Otherwise, validate as regex pattern
        try {
            Pattern.compile(value);
            return true;
        } catch (PatternSyntaxException e) {
            return false;
        }
    }
    
    private boolean isUuidFormat(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        return UUID_PATTERN.matcher(value.trim()).matches();
    }
}
