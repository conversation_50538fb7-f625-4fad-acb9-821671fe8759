package com.vusecurity.auth.contracts.api.v1.dto.authorization;

import com.vusecurity.auth.contracts.enums.AccountType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.UUID;

@Data
@Accessors(chain = true)
@Schema(description = "Summary information about an account")
public class AccountSummary {
    
    @Schema(description = "Unique identifier of the account",
            example = "456e7890-e89b-12d3-a456-************")
    private UUID id;

    @Schema(description = "Account type",
            example = "WORKFORCE")
    private AccountType accountType;

    @Schema(description = "Username or identifier from the identity",
            example = "john.doe")
    private String username;

    @Schema(description = "Display name from the identity",
            example = "John Doe")
    private String displayName;

    @Schema(description = "Account role in the group",
            example = "MEMBER")
    private String groupRole;
} 