package com.vusecurity.auth.identities.application.query;

import com.vusecurity.auth.contracts.api.v1.dto.identities.ClaimIdentifierRequest;
import com.vusecurity.auth.contracts.enums.AccountType;
import lombok.Builder;
import lombok.Value;

import java.util.List;
import java.util.UUID;

@Value
@Builder
public class IdentifyAccountByClaimsQuery {
    UUID businessId;
    AccountType accountType;
    List<ClaimIdentifierRequest> claimIdentifiers;

    public void validate() {
        if (businessId == null) {
            throw new IllegalArgumentException("Business ID is required");
        }
        if (accountType == null) {
            throw new IllegalArgumentException("Account type is required");
        }
        if (claimIdentifiers == null || claimIdentifiers.isEmpty()) {
            throw new IllegalArgumentException("At least one claim identifier is required");
        }
        
        // Validate each claim identifier
        for (ClaimIdentifierRequest claimIdentifier : claimIdentifiers) {
            if (claimIdentifier.getClaimDefinitionId() == null) {
                throw new IllegalArgumentException("Claim definition ID is required for all claim identifiers");
            }
            if (claimIdentifier.getValue() == null || claimIdentifier.getValue().trim().isEmpty()) {
                throw new IllegalArgumentException("Claim value is required and cannot be blank");
            }
        }
    }
}
