package com.vusecurity.auth.contracts.api.v1.dto.shared;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "Admin User information")
public class SetupAdminUserResponse {
    private final String accountId;
    private final String email;
    private final String password;

    public SetupAdminUserResponse(String accountId, String email, String password) {
        this.accountId = accountId;
        this.email = email;
        this.password = password;
    }
}
