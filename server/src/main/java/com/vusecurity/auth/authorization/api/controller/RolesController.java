package com.vusecurity.auth.authorization.api.controller;

import static com.vusecurity.auth.authorization.domain.PermissionNames.ROLE_CREATE;
import static com.vusecurity.auth.authorization.domain.PermissionNames.ROLE_DELETE;
import static com.vusecurity.auth.authorization.domain.PermissionNames.ROLE_READ;
import static com.vusecurity.auth.authorization.domain.PermissionNames.ROLE_UPDATE;

import java.util.Set;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.vusecurity.auth.authorization.application.command.CreateRoleCommand;
import com.vusecurity.auth.authorization.application.command.DeleteRoleCommand;
import com.vusecurity.auth.authorization.application.command.ManageRolePermissionsCommand;
import com.vusecurity.auth.authorization.application.command.UpdateRoleCommand;
import com.vusecurity.auth.authorization.application.handler.CreateRoleHandler;
import com.vusecurity.auth.authorization.application.handler.DeleteRoleHandler;
import com.vusecurity.auth.authorization.application.handler.ManageRolePermissionsHandler;
import com.vusecurity.auth.authorization.application.handler.UpdateRoleHandler;
import com.vusecurity.auth.authorization.application.query.GetRoleQuery;
import com.vusecurity.auth.authorization.application.query.GetRolesPagedQuery;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.mapper.RoleDtoMapper;
import com.vusecurity.auth.contracts.api.v1.dto.authorization.CreateRoleRequest;
import com.vusecurity.auth.contracts.api.v1.dto.authorization.RoleResponse;
import com.vusecurity.auth.contracts.api.v1.dto.authorization.UpdateRoleRequest;
import com.vusecurity.auth.shared.config.aspect.Oauth2Authorize;
import com.vusecurity.auth.shared.util.IncludeParams;
import com.vusecurity.auth.shared.util.UrlParameterUtils;
import com.vusecurity.core.commons.models.PageableResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

@RestController
@SecurityRequirement(name = "oauth2 client credentials")
@Tag(name = "Roles", description = "Role management operations")
public class RolesController {

        private final CreateRoleHandler createRoleHandler;
        private final UpdateRoleHandler updateRoleHandler;
        private final DeleteRoleHandler deleteRoleHandler;
        private final ManageRolePermissionsHandler manageRolePermissionsHandler;
        private final GetRoleQuery getRoleQuery;
        private final RoleDtoMapper roleDtoMapper;

        public RolesController(CreateRoleHandler createRoleHandler,
                        UpdateRoleHandler updateRoleHandler,
                        DeleteRoleHandler deleteRoleHandler,
                        ManageRolePermissionsHandler manageRolePermissionsHandler,
                        GetRoleQuery getRoleQuery,
                        RoleDtoMapper roleDtoMapper) {
                this.createRoleHandler = createRoleHandler;
                this.updateRoleHandler = updateRoleHandler;
                this.deleteRoleHandler = deleteRoleHandler;
                this.manageRolePermissionsHandler = manageRolePermissionsHandler;
                this.getRoleQuery = getRoleQuery;
                this.roleDtoMapper = roleDtoMapper;
        }

        private static final String BUSINESS_INCLUDE = "business";
        private static final String ACCOUNTS_INCLUDE = "accounts";
        private final Set<String> allowedIncludes = Set.of(BUSINESS_INCLUDE, ACCOUNTS_INCLUDE);

        @Operation(summary = "Get all roles", description = "Retrieves a paginated list of roles with optional filtering. Supports filtering by role name, description, and other properties.")
        @ApiResponses({
                        @ApiResponse(responseCode = "200", description = "Successful retrieval", content = @Content(schema = @Schema(implementation = PageableResponse.class))),
                        @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
                        @ApiResponse(responseCode = "401", description = "Authentication required"),
                        @ApiResponse(responseCode = "403", description = "Access denied")
        })
        @GetMapping("${app.authorization.context-path}/roles")
        @Oauth2Authorize(permission = ROLE_READ)
        public PageableResponse<RoleResponse> get(@RequestParam(name = "page", defaultValue = "1") int page,
                        @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                        @RequestParam(name = "filter", required = false) String filter,
                        @RequestParam(name = "include", required = false) String include) {

                IncludeParams includes = new IncludeParams(include, allowedIncludes);

                boolean includeBusiness = includes.contains(BUSINESS_INCLUDE);

                // Decode URL-encoded parameter to handle special characters properly
                String decodedFilter = UrlParameterUtils.decodeAndSanitizeParameter(filter);

                GetRolesPagedQuery query = GetRolesPagedQuery.builder()
                                .page(page)
                                .pageSize(pageSize)
                                .filter(decodedFilter)
                                .build();

                Page<RoleJpaEntity> roles = getRoleQuery.getAllRoles(query);

                return new PageableResponse<>(roles.getNumber() + 1, roles.getSize(), roles.getTotalElements(),
                                roles.get().map(role -> roleDtoMapper.toResponse(role, includeBusiness)).toList());
        }

        @Operation(summary = "Create a new role", description = "Creates a new role with the specified name, description, business association, and permission assignments.")
        @ApiResponses({
                        @ApiResponse(responseCode = "201", description = "Role created successfully", content = @Content(schema = @Schema(implementation = RoleResponse.class))),
                        @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors"),
                        @ApiResponse(responseCode = "401", description = "Authentication required"),
                        @ApiResponse(responseCode = "403", description = "Access denied"),
                        @ApiResponse(responseCode = "409", description = "Role with same name already exists")
        })
        @PostMapping("${app.authorization.context-path}/roles")
        @ResponseStatus(HttpStatus.CREATED)
        @Oauth2Authorize(permission = ROLE_CREATE)
        public RoleResponse create(@RequestBody @Valid CreateRoleRequest request) {

                CreateRoleCommand command = new CreateRoleCommand(
                                request.getName(),
                                request.getDescription(),
                                request.getBusinessId(),
                                request.getPermissionIds());

                RoleJpaEntity role = createRoleHandler.createRole(command);
                return roleDtoMapper.toResponse(role, true);
        }

        @Operation(summary = "Get role by ID", description = "Retrieves a specific role by its unique identifier.")
        @ApiResponses({
                        @ApiResponse(responseCode = "200", description = "Role found", content = @Content(schema = @Schema(implementation = RoleResponse.class))),
                        @ApiResponse(responseCode = "404", description = "Role not found"),
                        @ApiResponse(responseCode = "401", description = "Authentication required"),
                        @ApiResponse(responseCode = "403", description = "Access denied")
        })
        @GetMapping("${app.authorization.context-path}/roles/{id}")
        @Oauth2Authorize(permission = ROLE_READ)
        public RoleResponse get(@PathVariable(value = "id") UUID id,
                        @RequestParam(name = "include", required = false) String include) {

                RoleJpaEntity role = getRoleQuery.getRoleById(id);

                IncludeParams includes = new IncludeParams(include, allowedIncludes);

                boolean includeBusiness = includes.contains(BUSINESS_INCLUDE);

                return roleDtoMapper.toResponse(role, includeBusiness);
        }

        @Operation(summary = "Delete a role", description = "Deletes a role by its ID.")
        @ApiResponses({
                        @ApiResponse(responseCode = "204", description = "Role deleted successfully"),
                        @ApiResponse(responseCode = "404", description = "Role not found"),
                        @ApiResponse(responseCode = "401", description = "Client is not authenticated or the credentials provided are incorrect"),
                        @ApiResponse(responseCode = "403", description = "Client authenticated, but does not have permission to access the resource, even if it has valid credentials")
        })
        @DeleteMapping(path = "${app.authorization.context-path}/roles/{id}")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        @Oauth2Authorize(permission = ROLE_DELETE)
        public void delete(@PathVariable(value = "id") UUID id) {
                DeleteRoleCommand command = new DeleteRoleCommand(id);
                deleteRoleHandler.deleteRole(command);
        }

        @Operation(summary = "Update a role", description = "Updates a role's name and description.")
        @ApiResponses({
                        @ApiResponse(responseCode = "204", description = "Role updated successfully"),
                        @ApiResponse(responseCode = "404", description = "Role not found"),
                        @ApiResponse(responseCode = "401", description = "Client is not authenticated or the credentials provided are incorrect"),
                        @ApiResponse(responseCode = "403", description = "Client authenticated, but does not have permission to access the resource, even if it has valid credentials")
        })
        @PatchMapping(path = "${app.authorization.context-path}/roles/{id}")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        @Oauth2Authorize(permission = ROLE_UPDATE)
        public void update(@PathVariable(value = "id") UUID id, @RequestBody @Valid UpdateRoleRequest request) {
                UpdateRoleCommand command = new UpdateRoleCommand(
                                id,
                                request.getName(),
                                request.getDescription(),
                                request.getBusinessId(),
                                request.getStatus(),
                                request.getPermissionIds());
                updateRoleHandler.updateRole(command);
        }

        @Operation(summary = "Add permissions to a role", description = "Adds a list of permissions to a role.")
        @ApiResponses({
                        @ApiResponse(responseCode = "200", description = "Permissions added successfully"),
                        @ApiResponse(responseCode = "404", description = "Role not found"),
                        @ApiResponse(responseCode = "401", description = "Client is not authenticated or the credentials provided are incorrect"),
                        @ApiResponse(responseCode = "403", description = "Client authenticated, but does not have permission to access the resource, even if it has valid credentials")
        })
        @PostMapping(path = "${app.authorization.context-path}/roles/{id}/permissions")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        @Oauth2Authorize(permission = ROLE_UPDATE)
        public void addPermissions(@PathVariable(value = "id") UUID id, @RequestBody UUID[] permissionIds) {
                ManageRolePermissionsCommand command = new ManageRolePermissionsCommand(
                                id,
                                Set.of(permissionIds),
                                ManageRolePermissionsCommand.PermissionOperation.ADD);
                manageRolePermissionsHandler.managePermissions(command);
        }

        @Operation(summary = "Remove permissions from a role", description = "Removes a list of permissions from a role.")
        @ApiResponses({
                        @ApiResponse(responseCode = "200", description = "Permissions removed successfully"),
                        @ApiResponse(responseCode = "404", description = "Role not found"),
                        @ApiResponse(responseCode = "401", description = "Client is not authenticated or the credentials provided are incorrect"),
                        @ApiResponse(responseCode = "403", description = "Client authenticated, but does not have permission to access the resource, even if it has valid credentials")
        })
        @DeleteMapping(path = "${app.authorization.context-path}/roles/{id}/permissions")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        @Oauth2Authorize(permission = ROLE_UPDATE)
        public void deletePermissions(@PathVariable(value = "id") UUID id, @RequestBody UUID[] permissionIds) {
                ManageRolePermissionsCommand command = new ManageRolePermissionsCommand(
                                id,
                                Set.of(permissionIds),
                                ManageRolePermissionsCommand.PermissionOperation.REMOVE);
                manageRolePermissionsHandler.managePermissions(command);
        }
}
