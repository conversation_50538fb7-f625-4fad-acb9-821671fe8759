package com.vusecurity.auth.identities.application.handler;

import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.identities.application.command.UpdateAccountCommand;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;
import com.vusecurity.auth.shared.util.DatabaseConstraintUtils;
import com.vusecurity.auth.identities.application.exception.AccountNotFoundException;
import com.vusecurity.auth.identities.application.exception.AccountAlreadyExistsException;

@Service
@RequiredArgsConstructor
public class UpdateAccountHandler {

    private static final Logger logger = LoggerFactory.getLogger(UpdateAccountHandler.class);

    private final AccountRepository accountRepository;

    @Transactional
    public void updateAccount(UpdateAccountCommand command) {
        logger.debug("Updating account: {}", command.accountId());

        // Validate command
        command.validate();

        AccountJpaEntity account = accountRepository.findById(command.accountId())
                .orElseThrow(() -> new AccountNotFoundException(command.accountId().toString()));

        // Update lifecycle state if provided
        if (command.lifecycleState() != null) {
            account.setLifecycleState(command.lifecycleState());
        }

        // Update metadata if provided
        if (command.metadata() != null) {
            account.setMetadata(command.metadata());
        }

        // Save account with DB constraint handling for unique account per identity/business/type/identityProvider
        DatabaseConstraintUtils.executeWithConstraintHandling(
            () -> accountRepository.saveAndFlush(account),
            "uk_account_type_business_identity_provider_identity", // Unique constraint on account per identity/business/type/identityProvider
            () -> new AccountAlreadyExistsException("Account already exists for this identity, business, identityProvider and type")
        );
        logger.debug("Account updated successfully: {}", command.accountId());
    }
}
