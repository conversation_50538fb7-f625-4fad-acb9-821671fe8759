package com.vusecurity.auth.claims.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.ClaimValueResponse;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.contracts.enums.OwnerType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class ClaimValueDtoMapperTest {

    private ObjectMapper objectMapper;
    private ClaimValueDtoMapper mapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        mapper = new ClaimValueDtoMapper(objectMapper);
    }

    @Test
    void should_return_string_value_for_non_array_claim_type() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UUID ownerId = UUID.randomUUID();

        ClaimDefinitionJpaEntity stringClaimDefinition = new ClaimDefinitionJpaEntity(
                claimDefinitionId, "email", "Email", "Email address",
                DataTypeEnum.STRING, "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

        ClaimValueJpaEntity claimValue = new ClaimValueJpaEntity(stringClaimDefinition, OwnerType.ACCOUNT, ownerId);
        claimValue.setId(claimValueId);
        claimValue.setValue("<EMAIL>");
        claimValue.setPrimary(true);
        claimValue.setSource("USER_INPUT");
        claimValue.setCreatedAt(Instant.now());
        claimValue.setUpdatedAt(Instant.now());
        claimValue.setCreatedBy("test");
        claimValue.setUpdatedBy("test");

        // When
        ClaimValueResponse response = mapper.toResponse(claimValue);

        // Then
        assertNotNull(response);
        assertEquals(claimValueId, response.getId());
        assertEquals(claimDefinitionId, response.getClaimDefinitionId());
        assertEquals(OwnerType.ACCOUNT, response.getOwnerType());
        assertEquals(ownerId, response.getOwnerId());
        assertEquals("<EMAIL>", response.getValue()); // Should remain as string
        assertTrue(response.isPrimary());
        assertEquals("USER_INPUT", response.getSource());
    }

    @Test
    void should_return_parsed_array_for_array_claim_type() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UUID ownerId = UUID.randomUUID();

        ClaimDefinitionJpaEntity arrayClaimDefinition = new ClaimDefinitionJpaEntity(
                claimDefinitionId, "phone_numbers", "Phone Numbers", "List of phone numbers",
                DataTypeEnum.ARRAY, "some-uuid-reference");

        ClaimValueJpaEntity claimValue = new ClaimValueJpaEntity(arrayClaimDefinition, OwnerType.ACCOUNT, ownerId);
        claimValue.setId(claimValueId);
        claimValue.setValue("[\"+***********\",\"+***********\"]"); // JSON string
        claimValue.setPrimary(true);
        claimValue.setSource("USER_INPUT");
        claimValue.setCreatedAt(Instant.now());
        claimValue.setUpdatedAt(Instant.now());
        claimValue.setCreatedBy("test");
        claimValue.setUpdatedBy("test");

        // When
        ClaimValueResponse response = mapper.toResponse(claimValue);

        // Then
        assertNotNull(response);
        assertEquals(claimValueId, response.getId());
        assertEquals(claimDefinitionId, response.getClaimDefinitionId());
        assertEquals(OwnerType.ACCOUNT, response.getOwnerType());
        assertEquals(ownerId, response.getOwnerId());
        
        // Value should be parsed as actual array
        assertInstanceOf(Object[].class, response.getValue());
        Object[] phoneNumbers = (Object[]) response.getValue();
        assertEquals(2, phoneNumbers.length);
        assertEquals("+***********", phoneNumbers[0]);
        assertEquals("+***********", phoneNumbers[1]);
        
        assertTrue(response.isPrimary());
        assertEquals("USER_INPUT", response.getSource());
    }

    @Test
    void should_fallback_to_string_when_json_parsing_fails() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UUID ownerId = UUID.randomUUID();

        ClaimDefinitionJpaEntity arrayClaimDefinition = new ClaimDefinitionJpaEntity(
                claimDefinitionId, "phone_numbers", "Phone Numbers", "List of phone numbers",
                DataTypeEnum.ARRAY, "some-uuid-reference");

        ClaimValueJpaEntity claimValue = new ClaimValueJpaEntity(arrayClaimDefinition, OwnerType.ACCOUNT, ownerId);
        claimValue.setId(claimValueId);
        claimValue.setValue("invalid-json-format"); // Invalid JSON
        claimValue.setPrimary(true);
        claimValue.setSource("USER_INPUT");
        claimValue.setCreatedAt(Instant.now());
        claimValue.setUpdatedAt(Instant.now());
        claimValue.setCreatedBy("test");
        claimValue.setUpdatedBy("test");

        // When
        ClaimValueResponse response = mapper.toResponse(claimValue);

        // Then
        assertNotNull(response);
        assertEquals("invalid-json-format", response.getValue()); // Should fallback to string
        assertInstanceOf(String.class, response.getValue());
    }

    @Test
    void should_handle_null_claim_value() {
        // Given & When
        ClaimValueResponse response = mapper.toResponse(null);

        // Then
        assertNull(response);
    }

    @Test
    void should_handle_null_claim_value_content() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UUID ownerId = UUID.randomUUID();

        ClaimDefinitionJpaEntity arrayClaimDefinition = new ClaimDefinitionJpaEntity(
                claimDefinitionId, "phone_numbers", "Phone Numbers", "List of phone numbers",
                DataTypeEnum.ARRAY, "some-uuid-reference");

        ClaimValueJpaEntity claimValue = new ClaimValueJpaEntity(arrayClaimDefinition, OwnerType.ACCOUNT, ownerId);
        claimValue.setId(claimValueId);
        claimValue.setValue(null); // Null value
        claimValue.setCreatedAt(Instant.now());
        claimValue.setUpdatedAt(Instant.now());
        claimValue.setCreatedBy("test");
        claimValue.setUpdatedBy("test");

        // When
        ClaimValueResponse response = mapper.toResponse(claimValue);

        // Then
        assertNotNull(response);
        assertNull(response.getValue());
    }

    @Test
    void should_handle_claim_definition_without_relationship() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID ownerId = UUID.randomUUID();

        ClaimValueJpaEntity claimValue = new ClaimValueJpaEntity();
        claimValue.setId(claimValueId);
        claimValue.setClaimDefinition(null); // No relationship loaded
        claimValue.setOwnerType(OwnerType.ACCOUNT);
        claimValue.setOwnerId(ownerId);
        claimValue.setValue("[\"+***********\",\"+***********\"]");
        claimValue.setPrimary(true);
        claimValue.setSource("USER_INPUT");
        claimValue.setCreatedAt(Instant.now());
        claimValue.setUpdatedAt(Instant.now());
        claimValue.setCreatedBy("test");
        claimValue.setUpdatedBy("test");

        // When
        ClaimValueResponse response = mapper.toResponse(claimValue);

        // Then
        assertNotNull(response);
        // Should return as string since ClaimDefinition is not available
        assertEquals("[\"+***********\",\"+***********\"]", response.getValue());
        assertInstanceOf(String.class, response.getValue());
    }

    @Test
    void should_parse_empty_array() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UUID ownerId = UUID.randomUUID();

        ClaimDefinitionJpaEntity arrayClaimDefinition = new ClaimDefinitionJpaEntity(
                claimDefinitionId, "tags", "Tags", "List of tags",
                DataTypeEnum.ARRAY, "some-uuid-reference");

        ClaimValueJpaEntity claimValue = new ClaimValueJpaEntity(arrayClaimDefinition, OwnerType.ACCOUNT, ownerId);
        claimValue.setId(claimValueId);
        claimValue.setValue("[]"); // Empty JSON array
        claimValue.setPrimary(true);
        claimValue.setSource("USER_INPUT");
        claimValue.setCreatedAt(Instant.now());
        claimValue.setUpdatedAt(Instant.now());
        claimValue.setCreatedBy("test");
        claimValue.setUpdatedBy("test");

        // When
        ClaimValueResponse response = mapper.toResponse(claimValue);

        // Then
        assertNotNull(response);
        assertInstanceOf(Object[].class, response.getValue());
        Object[] tags = (Object[]) response.getValue();
        assertEquals(0, tags.length);
    }

    @Test
    void should_parse_complex_array_values() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID claimDefinitionId = UUID.randomUUID();
        UUID ownerId = UUID.randomUUID();

        ClaimDefinitionJpaEntity arrayClaimDefinition = new ClaimDefinitionJpaEntity(
                claimDefinitionId, "addresses", "Addresses", "List of addresses",
                DataTypeEnum.ARRAY, "some-uuid-reference");

        ClaimValueJpaEntity claimValue = new ClaimValueJpaEntity(arrayClaimDefinition, OwnerType.ACCOUNT, ownerId);
        claimValue.setId(claimValueId);
        claimValue.setValue("[\"123 Main St, City, State 12345\",\"456 Oak Ave, Town, State 67890\"]");
        claimValue.setPrimary(true);
        claimValue.setSource("USER_INPUT");
        claimValue.setCreatedAt(Instant.now());
        claimValue.setUpdatedAt(Instant.now());
        claimValue.setCreatedBy("test");
        claimValue.setUpdatedBy("test");

        // When
        ClaimValueResponse response = mapper.toResponse(claimValue);

        // Then
        assertNotNull(response);
        assertInstanceOf(Object[].class, response.getValue());
        Object[] addresses = (Object[]) response.getValue();
        assertEquals(2, addresses.length);
        assertEquals("123 Main St, City, State 12345", addresses[0]);
        assertEquals("456 Oak Ave, Town, State 67890", addresses[1]);
    }
}