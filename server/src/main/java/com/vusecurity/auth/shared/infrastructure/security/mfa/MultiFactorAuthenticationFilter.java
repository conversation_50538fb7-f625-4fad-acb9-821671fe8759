package com.vusecurity.auth.shared.infrastructure.security.mfa;

import java.io.IOException;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.log.LogMessage;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.context.SecurityContextHolderStrategy;
import org.springframework.security.web.context.RequestAttributeSecurityContextRepository;
import org.springframework.security.web.context.SecurityContextRepository;
import org.springframework.web.filter.OncePerRequestFilter;

import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * Authentication filter for multi-factor authentication.
 * This filter intercepts requests to the multi-factor authentication endpoint,
 * extracts credentials from the request body, creates a
 * MultiFactorAuthenticationToken,
 * and delegates authentication to the MultiFactorAuthenticationProvider.
 *
 * Similar to BasicAuthenticationFilter, this filter allows the chain to
 * continue
 * after processing authentication.
 */
public class MultiFactorAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(MultiFactorAuthenticationFilter.class);

    private SecurityContextHolderStrategy securityContextHolderStrategy = SecurityContextHolder
            .getContextHolderStrategy();
    private final AuthenticationManager authenticationManager;
    private final ObjectMapper objectMapper;
    private SecurityContextRepository securityContextRepository = new RequestAttributeSecurityContextRepository();

    /**
     * Constructor that sets up the filter
     */
    public MultiFactorAuthenticationFilter(AuthenticationManager authenticationManager) {
        this.authenticationManager = authenticationManager;
        this.objectMapper = new ObjectMapper();
    }

    public MultiFactorAuthenticationFilter(AuthenticationManager authenticationManager, ObjectMapper objectMapper) {
        this.authenticationManager = authenticationManager;
        this.objectMapper = objectMapper;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // Only process POST requests to /api/v1/auth/multifactor
        if (!isMultiFactorAuthRequest(request)) {
            filterChain.doFilter(request, response);
            return;
        }

        // Skip if already authenticated
        if (SecurityContextHolder.getContext().getAuthentication() != null &&
                SecurityContextHolder.getContext().getAuthentication().isAuthenticated()) {
            filterChain.doFilter(request, response);
            return;
        }

        logger.debug("Processing multi-factor authentication request");

        try {
            Authentication authResult = attemptAuthentication(request);

            SecurityContext context = this.securityContextHolderStrategy.createEmptyContext();
            context.setAuthentication(authResult);
            this.securityContextHolderStrategy.setContext(context);
            logger.debug("Set SecurityContextHolder to {}", authResult);

            this.securityContextRepository.saveContext(context, request, response);

        } catch (AuthenticationException e) {
            this.securityContextHolderStrategy.clearContext();
            logger.debug("Multi-factor authentication failed: {}", e.getMessage());
        }

        // Continue filter chain for non-auth requests
        filterChain.doFilter(request, response);
    }

    /**
     * Check if this is a multi-factor authentication request
     */
    private boolean isMultiFactorAuthRequest(HttpServletRequest request) {
        return ("POST".equals(request.getMethod()) || "GET".equals(request.getMethod())) &&
                request.getRequestURI().contains("/oauth2/authorize");
    }

    /**
     * Attempt authentication similar to the original method
     */
    private Authentication attemptAuthentication(HttpServletRequest request)
            throws AuthenticationException, IOException {
        logger.debug("Attempting multi-factor authentication from filter");

        try {
            // Read and parse the request body
            MultiFactorAuthRequest authRequest = objectMapper.readValue(request.getInputStream(),
                    MultiFactorAuthRequest.class);

            // Validate required fields
            if (authRequest.getAccountId() == null) {
                throw new BadCredentialsException("Account ID is required");
            }
            

            // Create the authentication token
            MultiFactorAuthenticationToken authToken = new MultiFactorAuthenticationToken(
                    authRequest.getAccountId(),
                    authRequest.getTotp(),
                    authRequest.getPassword());

            logger.debug("Created MultiFactorAuthenticationToken for account: {}", authRequest.getAccountId());

            // Delegate to the authentication manager
            return authenticationManager.authenticate(authToken);

        } catch (IOException e) {
            logger.debug("Failed to parse authentication request: {}", e.getMessage());
            throw new BadCredentialsException("Invalid request format", e);
        }
    }

    /**
     * DTO for multi-factor authentication requests
     */
    public static class MultiFactorAuthRequest {
        private UUID accountId;
        private String totp;
        private String password;

        // Getters and setters
        public UUID getAccountId() {
            return accountId;
        }

        public void setAccountId(UUID accountId) {
            this.accountId = accountId;
        }

        public String getTotp() {
            return totp;
        }

        public void setTotp(String totp) {
            this.totp = totp;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }

}
