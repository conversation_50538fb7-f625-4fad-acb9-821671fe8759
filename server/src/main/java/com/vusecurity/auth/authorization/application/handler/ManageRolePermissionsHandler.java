package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.command.ManageRolePermissionsCommand;
import com.vusecurity.auth.authorization.application.exception.PermissionNotExistsException;
import com.vusecurity.auth.authorization.application.exception.RoleNotFoundException;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.PermissionJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.PermissionRepository;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class ManageRolePermissionsHandler {

    private static final Logger logger = LoggerFactory.getLogger(ManageRolePermissionsHandler.class);
    
    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;

    @Transactional
    public void managePermissions(ManageRolePermissionsCommand command) {
        logger.debug("Managing permissions for role: {} with operation: {}", command.roleId(), command.operation());

        try {
            // Validate command
            command.validate();

            RoleJpaEntity role = roleRepository.findById(command.roleId())
                    .orElseThrow(() -> new RoleNotFoundException("Role not found with ID: " + command.roleId()));

            // Get permission entities
            Set<PermissionJpaEntity> permissions = new HashSet<>();
            for (UUID permissionId : command.permissionIds()) {
                PermissionJpaEntity permission = permissionRepository.findById(permissionId)
                        .orElseThrow(() -> new PermissionNotExistsException("Permission not found with ID: " + permissionId));
                permissions.add(permission);
            }

            // Apply operation
            switch (command.operation()) {
                case ADD -> {
                    for (PermissionJpaEntity permission : permissions) {
                        role.addPermission(permission);
                    }
                }
                case REMOVE -> {
                    for (PermissionJpaEntity permission : permissions) {
                        role.removePermission(permission);
                    }
                }
                case REPLACE -> {
                    // Clear existing permissions and add new ones
                    role.getPermissions().clear();
                    role.setPermissions(permissions);
                }
            }

            roleRepository.save(role);
            logger.debug("Permissions managed successfully for role: {}", command.roleId());

        } catch (Exception e) {
            logger.error("Error managing permissions for role: {}", e.getMessage());
            throw e;
        }
    }
}
