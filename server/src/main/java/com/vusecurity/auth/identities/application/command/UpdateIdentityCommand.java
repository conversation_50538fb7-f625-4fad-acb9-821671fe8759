package com.vusecurity.auth.identities.application.command;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * Command for updating an existing identity.
 * Contains the identity ID and the fields to update.
 */
@Data
@Builder
public class UpdateIdentityCommand {
    private String identityId;
    private String name;
    private String identityType;
    private Map<String, Object> metadata;
    private String lifecycleState;
}
