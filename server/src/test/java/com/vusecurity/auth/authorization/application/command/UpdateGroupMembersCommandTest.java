package com.vusecurity.auth.authorization.application.command;

import com.vusecurity.auth.authorization.application.exception.InvalidGroupRequestException;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for UpdateGroupMembersCommand validation logic.
 */
class UpdateGroupMembersCommandTest {

    @Test
    void shouldValidateSuccessfully_WhenAllFieldsAreValid() {
        // Given
        UUID ownerId = UUID.randomUUID();
        UpdateGroupMembersCommand command = new UpdateGroupMembersCommand(
                UUID.randomUUID(),
                List.of(UUID.randomUUID(), ownerId),
                List.of(ownerId)
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldThrowException_WhenGroupIdIsNull() {
        // Given
        UpdateGroupMembersCommand command = new UpdateGroupMembersCommand(null, List.of(), List.of());

        // When & Then
        var exception = assertThrows(InvalidGroupRequestException.class, command::validate);
        assertEquals("Group ID is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenOwnerIdIsNotInAccountIds() {
        // Given
        UpdateGroupMembersCommand command = new UpdateGroupMembersCommand(
                UUID.randomUUID(),
                List.of(UUID.randomUUID()),
                List.of(UUID.randomUUID()) // Different ID
        );

        // When & Then
        var exception = assertThrows(InvalidGroupRequestException.class, command::validate);
        assertEquals("All ownerAccountIds must also be present in accountIds", exception.getMessage());
    }

    @Test
    void shouldValidateSuccessfully_WhenListsAreNull() {
        // Given
        UpdateGroupMembersCommand command = new UpdateGroupMembersCommand(
                UUID.randomUUID(),
                null,
                null
        );

        // When & Then
        assertDoesNotThrow(command::validate);
        assertNotNull(command.accountIds());
        assertNotNull(command.ownerAccountIds());
    }
} 