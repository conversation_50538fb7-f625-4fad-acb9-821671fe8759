/* Ensure required identity providers exist for tests and runtime */

-- Ensure LOCAL provider exists
IF NOT EXISTS (SELECT 1 FROM dbo.identity_provider WHERE id = 'b3c1c3b0-303c-4a7a-8c43-7bdeb96ef558')
BEGIN
    INSERT INTO dbo.identity_provider (id, name, description, created_by, status, created_at, updated_at)
    VALUES ('b3c1c3b0-303c-4a7a-8c43-7bdeb96ef558', 'LOCAL', 'Local identity provider', 'SYSTEM', 'ACTIVE', SYSUTCDATETIME(), SYSUTCDATETIME());
END
GO

-- Ensure SCIM provider exists
IF NOT EXISTS (SELECT 1 FROM dbo.identity_provider WHERE id = '55896ef7-303c-4a7a-8c43-bdc3b0b3c1eb')
BEGIN
    INSERT INTO dbo.identity_provider (id, name, description, created_by, status, created_at, updated_at)
    VALUES ('55896ef7-303c-4a7a-8c43-bdc3b0b3c1eb', 'SCIM', 'SCIM identity provider', 'SYSTEM', 'ACTIVE', SYSUTCDATETIME(), SYSUTCDATETIME());
END
GO

