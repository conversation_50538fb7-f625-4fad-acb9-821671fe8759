package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.query.GetPermissionQuery;
import com.vusecurity.auth.authorization.application.query.GetPermissionsPagedQuery;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.PermissionJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.PermissionRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class GetPermissionHandler implements GetPermissionQuery {

    private static final Logger logger = LoggerFactory.getLogger(GetPermissionHandler.class);
    
    private final PermissionRepository permissionRepository;

    @Override
    public PermissionJpaEntity getPermissionByName(String name) {
        logger.debug("Getting permission by name: {}", name);
        
        return permissionRepository.findByName(name)
                .orElseThrow(() -> new IllegalArgumentException("Permission not found with name: " + name));
    }

    @Override
    public Page<PermissionJpaEntity> getAllPermissions(GetPermissionsPagedQuery query) {
        logger.debug("Getting all permissionsIds with query: {}", query);
        
        query.validate();
        
        // Convert 1-based page to 0-based for Spring Data
        int pageNumber = Math.max(0, query.getPage() - 1);
        Pageable pageable = PageRequest.of(pageNumber, query.getPageSize());
        
        Page<PermissionJpaEntity> permissions;
        
        if (query.getFilter() != null && !query.getFilter().trim().isEmpty()) {
            // Apply filter if provided
            Specification<PermissionJpaEntity> spec = createFilterSpecification(query.getFilter());
            permissions = permissionRepository.findAll(spec, pageable);
        } else {
            // Return all permissionsIds if no filter
            permissions = permissionRepository.findAll(pageable);
        }
        
        logger.debug("Found {} permissionsIds", permissions.getTotalElements());
        return permissions;
    }
    
    private Specification<PermissionJpaEntity> createFilterSpecification(String filter) {
        return (root, query, criteriaBuilder) -> {
            String filterPattern = "%" + filter.toLowerCase() + "%";
            return criteriaBuilder.or(
                criteriaBuilder.like(criteriaBuilder.lower(root.get("name")), filterPattern),
                criteriaBuilder.like(criteriaBuilder.lower(root.get("description")), filterPattern)
            );
        };
    }
}
