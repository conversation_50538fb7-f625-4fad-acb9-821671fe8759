//package com.vusecurity.auth.claims.api.controller;
//
//import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.*;
//import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.*;
//import com.vusecurity.auth.contracts.api.v1.dto.claims.ClaimSetStructureResponse;
//import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.ClaimValueResponse;
//import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.CreateClaimValueRequest;
//import com.vusecurity.auth.contracts.enums.DataTypeEnum;
//import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
//import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
//import com.vusecurity.auth.shared.TestDataBuilder;
//import com.vusecurity.business.domain.Business;
//import com.vusecurity.business.domain.repositories.BusinessRepository;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.boot.test.web.client.TestRestTemplate;
//import org.springframework.test.context.TestPropertySource;
//import org.springframework.transaction.annotation.Transactional;
//import org.testcontainers.shaded.com.fasterxml.jackson.core.type.TypeReference;
//import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;
//
//import java.time.LocalDateTime;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.UUID;
//import java.util.stream.Collectors;
//
//import static org.junit.jupiter.api.Assertions.*;
//
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@TestPropertySource(locations = "classpath:application-test.properties")
//@Transactional
//class HierarchicalClaimValueControllerIT {
//
//    @Autowired
//    private TestRestTemplate restTemplate;
//
//    @Autowired
//    private ClaimSetRepository claimSetRepo;
//
//    @Autowired
//    private ClaimDefinitionRepository claimDefinitionRepo;
//
//    @Autowired
//    private ClaimValueRepository claimValueRepo;
//
//    @Autowired
//    private ClaimSetClaimValuesRepository claimSetClaimValuesRepo;
//
//    @Autowired
//    private ClaimValueReferencesRepository claimValueReferencesRepo;
//
//    @Autowired
//    private BusinessRepository businessRepo;
//
//    @Autowired
//    private AccountRepository accountRepo;
//
//    @Autowired
//    private ObjectMapper objectMapper;
//
//    private Business testBusiness;
//    private AccountJpaEntity testAccount;
//    private ClaimSetJpaEntity personaClaimSet;
//    private ClaimSetJpaEntity direccionClaimSet;
//    private ClaimDefinitionJpaEntity nombreDef;
//    private ClaimDefinitionJpaEntity apellidoDef;
//    private ClaimDefinitionJpaEntity direccionPrincipalDef;
//    private ClaimDefinitionJpaEntity calleDef;
//    private ClaimDefinitionJpaEntity numeroDef;
//    private ClaimDefinitionJpaEntity ciudadDef;
//
//    @BeforeEach
//    void setUp() {
//        setupTestData();
//    }
//
//    private void setupTestData() {
//        // Crear business y account
//        testBusiness = TestDataBuilder.validBusiness().build();
//        testBusiness = businessRepo.save(testBusiness);
//
//        testAccount = TestDataBuilder.validAccount()
//            .business(testBusiness)
//            .build();
//        testAccount = accountRepo.save(testAccount);
//
//        // Crear ClaimSets
//        personaClaimSet = TestDataBuilder.validClaimSet()
//            .business(testBusiness)
//            .name("persona_template")
//            .description("Template para persona")
//            .build();
//        personaClaimSet = claimSetRepo.save(personaClaimSet);
//
//        direccionClaimSet = TestDataBuilder.validClaimSet()
//            .business(testBusiness)
//            .name("direccion_template")
//            .description("Template para dirección")
//            .build();
//        direccionClaimSet = claimSetRepo.save(direccionClaimSet);
//
//        // Crear ClaimDefinitions para persona
//        nombreDef = TestDataBuilder.validClaimDefinition()
//            .code("nombre")
//            .name("Nombre")
//            .dataType(DataTypeEnum.STRING)
//            .build();
//        nombreDef = claimDefinitionRepo.save(nombreDef);
//
//        apellidoDef = TestDataBuilder.validClaimDefinition()
//            .code("apellido")
//            .name("Apellido")
//            .dataType(DataTypeEnum.STRING)
//            .build();
//        apellidoDef = claimDefinitionRepo.save(apellidoDef);
//
//        direccionPrincipalDef = TestDataBuilder.validClaimDefinition()
//            .code("direccion_principal")
//            .name("Dirección Principal")
//            .dataType(DataTypeEnum.CLAIMSET)
//            .build();
//        direccionPrincipalDef = claimDefinitionRepo.save(direccionPrincipalDef);
//
//        // Crear ClaimDefinitions para dirección
//        calleDef = TestDataBuilder.validClaimDefinition()
//            .code("calle")
//            .name("Calle")
//            .dataType(DataTypeEnum.STRING)
//            .build();
//        calleDef = claimDefinitionRepo.save(calleDef);
//
//        numeroDef = TestDataBuilder.validClaimDefinition()
//            .code("numero")
//            .name("Número")
//            .dataType(DataTypeEnum.NUMERIC)
//            .build();
//        numeroDef = claimDefinitionRepo.save(numeroDef);
//
//        ciudadDef = TestDataBuilder.validClaimDefinition()
//            .code("ciudad")
//            .name("Ciudad")
//            .dataType(DataTypeEnum.STRING)
//            .build();
//        ciudadDef = claimDefinitionRepo.save(ciudadDef);
//    }
//
//    @Test
//    void shouldCreateHierarchicalStructure_PersonaWithDireccion() throws Exception {
//        // 1. Crear ClaimValues para dirección
//        ClaimValueJpaEntity calleValue = createClaimValue(direccionClaimSet, calleDef, "Av. Siempre Viva");
//        ClaimValueJpaEntity numeroValue = createClaimValue(direccionClaimSet, numeroDef, "742");
//        ClaimValueJpaEntity ciudadValue = createClaimValue(direccionClaimSet, ciudadDef, "Springfield");
//
//        // 2. Crear ClaimValues para persona (primitivos)
//        ClaimValueJpaEntity nombreValue = createClaimValue(personaClaimSet, nombreDef, "Juan");
//        ClaimValueJpaEntity apellidoValue = createClaimValue(personaClaimSet, apellidoDef, "Pérez");
//
//        // 3. Crear ClaimValue tipo CLAIMSET que referencia la dirección
//        CreateClaimValueRequest direccionRequest = new CreateClaimValueRequest();
//        direccionRequest.setClaimSetId(personaClaimSet.getId());
//        direccionRequest.setClaimDefinitionId(direccionPrincipalDef.getId());
//        direccionRequest.setAccountId(testAccount.getId());
//        direccionRequest.setValue(direccionClaimSet.getId().toString());
//        direccionRequest.setPrimary(true);
//        direccionRequest.setComputed(false);
//        direccionRequest.setSource("USER_INPUT");
//
//        String response = restTemplate.postForObject(
//            "/api/v1/claim-values",
//            direccionRequest,
//            String.class
//        );
//
//        ClaimValueResponse direccionClaimValue = objectMapper.readValue(response, ClaimValueResponse.class);
//
//        // 4. Verificar que se crearon las asociaciones correctas
//        List<ClaimSetClaimValuesJpaEntity> personaAssociations =
//            claimSetClaimValuesRepo.findByClaimSetId(personaClaimSet.getId());
//
//        List<ClaimSetClaimValuesJpaEntity> direccionAssociations =
//            claimSetClaimValuesRepo.findByClaimSetId(direccionClaimSet.getId());
//
//        // Persona debe tener 3 ClaimValues: nombre, apellido, direccion_principal
//        assertEquals(3, personaAssociations.size());
//
//        // Dirección debe tener 3 ClaimValues: calle, numero, ciudad
//        assertEquals(3, direccionAssociations.size());
//
//        // 5. Verificar que se creó la referencia jerárquica
//        List<ClaimValueReferencesJpaEntity> references =
//            claimValueReferencesRepo.findBySourceClaimValueId(direccionClaimValue.getId());
//
//        assertEquals(1, references.size());
//        assertEquals(direccionClaimSet.getId(), references.getFirst().getReferencedClaimSet().getId());
//        assertEquals(direccionPrincipalDef.getId(), references.getFirst().getReferencedClaimDefinition().getId());
//    }
//
//    @Test
//    void shouldQueryHierarchicalStructure_BySearchValue() throws Exception {
//        // Given - Crear estructura jerárquica completa
//        setupCompleteHierarchy();
//
//        // When - Buscar por valor en estructura anidada
//        String searchResponse = restTemplate.getForObject(
//            "/api/v1/claim-values/related?ownerType=ACCOUNT&ownerId={ownerId}&searchValue={searchValue}",
//            String.class,
//            testAccount.getId(),
//            "Springfield"
//        );
//
//        // Then
//        List<ClaimValueResponse> relatedValues = objectMapper.readValue(
//            searchResponse,
//            new TypeReference<List<ClaimValueResponse>>() {}
//        );
//
//        assertNotNull(relatedValues);
//        assertTrue(relatedValues.size() >= 5); // Al menos: nombre, apellido, calle, numero, ciudad
//
//        // Verificar que incluye valores de ambos ClaimSets
//        Set<String> claimCodes = relatedValues.stream()
//            .map(cv -> cv.getClaimDefinition().getCode())
//            .collect(Collectors.toSet());
//
//        assertTrue(claimCodes.contains("nombre"));
//        assertTrue(claimCodes.contains("apellido"));
//        assertTrue(claimCodes.contains("calle"));
//        assertTrue(claimCodes.contains("numero"));
//        assertTrue(claimCodes.contains("ciudad"));
//    }
//
//    @Test
//    void shouldGetClaimSetStructure_WithNestedReferences() throws Exception {
//        // Given
//        setupCompleteHierarchy();
//
//        // When
//        String structureResponse = restTemplate.getForObject(
//            "/api/v1/claim-sets/{claimSetId}/structure?ownerType=ACCOUNT&ownerId={ownerId}",
//            String.class,
//            personaClaimSet.getId(),
//            testAccount.getId()
//        );
//
//        // Then
//        ClaimSetStructureResponse structure = objectMapper.readValue(
//            structureResponse, ClaimSetStructureResponse.class
//        );
//
//        assertNotNull(structure);
//        assertEquals(personaClaimSet.getId(), structure.claimSetId());
//        assertEquals("persona_template", structure.name());
//
//        Map<String, Object> structureMap = structure.structure();
//        assertTrue(structureMap.containsKey("nombre"));
//        assertTrue(structureMap.containsKey("apellido"));
//        assertTrue(structureMap.containsKey("direccion_principal"));
//
//        // La dirección principal debe ser una estructura anidada
//        Object direccionPrincipal = structureMap.get("direccion_principal");
//        assertNotNull(direccionPrincipal);
//        assertTrue(direccionPrincipal instanceof ClaimSetStructureResponse);
//
//        ClaimSetStructureResponse direccionStructure = (ClaimSetStructureResponse) direccionPrincipal;
//        assertTrue(direccionStructure.structure().containsKey("calle"));
//        assertTrue(direccionStructure.structure().containsKey("numero"));
//        assertTrue(direccionStructure.structure().containsKey("ciudad"));
//    }
//
//    @Test
//    void shouldHandleArrayOfClaimSets_Successfully() throws Exception {
//        // Given - Crear ClaimDefinition tipo ARRAY con elementos CLAIMSET
//        ClaimDefinitionJpaEntity direccionesDef = TestDataBuilder.validClaimDefinition()
//            .code("direcciones")
//            .name("Direcciones")
//            .dataType(DataTypeEnum.ARRAY)
//            .isAListOf(direccionPrincipalDef) // Array de direcciones
//            .build();
//        direccionesDef = claimDefinitionRepo.save(direccionesDef);
//
//        // Crear múltiples ClaimSets de dirección
//        ClaimSetJpaEntity direccionCasa = createDireccionClaimSet("casa", "Calle Casa 123", "456", "Madrid");
//        ClaimSetJpaEntity direccionTrabajo = createDireccionClaimSet("trabajo", "Av. Trabajo 789", "101", "Barcelona");
//
//        // Crear ClaimValue tipo ARRAY que referencia múltiples ClaimSets
//        String arrayValue = String.format("[%s, %s]",
//            direccionCasa.getId().toString(),
//            direccionTrabajo.getId().toString());
//
//        CreateClaimValueRequest arrayRequest = new CreateClaimValueRequest();
//        arrayRequest.setClaimSetId(personaClaimSet.getId());
//        arrayRequest.setClaimDefinitionId(direccionesDef.getId());
//        arrayRequest.setAccountId(testAccount.getId());
//        arrayRequest.setValue(arrayValue);
//        arrayRequest.setPrimary(true);
//        arrayRequest.setComputed(false);
//        arrayRequest.setSource("USER_INPUT");
//
//        // When
//        String response = restTemplate.postForObject(
//            "/api/v1/claim-values",
//            arrayRequest,
//            String.class
//        );
//
//        // Then
//        ClaimValueResponse arrayClaimValue = objectMapper.readValue(response, ClaimValueResponse.class);
//        assertNotNull(arrayClaimValue);
//
//        // Verificar que se crearon las referencias para ambos elementos del array
//        List<ClaimValueReferencesJpaEntity> references =
//            claimValueReferencesRepo.findBySourceClaimValueId(arrayClaimValue.getId());
//
//        assertEquals(2, references.size());
//
//        Set<UUID> referencedClaimSetIds = references.stream()
//            .map(ref -> ref.getReferencedClaimSet().getId())
//            .collect(Collectors.toSet());
//
//        assertTrue(referencedClaimSetIds.contains(direccionCasa.getId()));
//        assertTrue(referencedClaimSetIds.contains(direccionTrabajo.getId()));
//    }
//
//    private void setupCompleteHierarchy() {
//        // Crear ClaimValues para dirección
//        createClaimValue(direccionClaimSet, calleDef, "Av. Siempre Viva");
//        createClaimValue(direccionClaimSet, numeroDef, "742");
//        createClaimValue(direccionClaimSet, ciudadDef, "Springfield");
//
//        // Crear ClaimValues para persona
//        createClaimValue(personaClaimSet, nombreDef, "Juan");
//        createClaimValue(personaClaimSet, apellidoDef, "Pérez");
//
//        // Crear referencia jerárquica
//        ClaimValueJpaEntity direccionRef = createClaimValue(
//            personaClaimSet, direccionPrincipalDef, direccionClaimSet.getId().toString());
//
//        // Crear la referencia explícita
//        ClaimValueReferencesJpaEntity reference = new ClaimValueReferencesJpaEntity(
//            direccionRef, direccionClaimSet, direccionPrincipalDef, null);
//        claimValueReferencesRepo.save(reference);
//    }
//
//    private ClaimValueJpaEntity createClaimValue(ClaimSetJpaEntity claimSet,
//                                               ClaimDefinitionJpaEntity definition,
//                                               String value) {
//        ClaimValueJpaEntity claimValue = new ClaimValueJpaEntity();
//        claimValue.setId(UUID.randomUUID());
//        claimValue.setClaimDefinition(definition);
//        claimValue.setOwnerId(testAccount.getId());
//        claimValue.setValue(value);
//        claimValue.setPrimary(true);
//        claimValue.setComputed(false);
//        claimValue.setSource("USER_INPUT");
//        claimValue.setCreatedAt(LocalDateTime.now());
//
//        claimValue = claimValueRepo.save(claimValue);
//
//        // Crear asociación con ClaimSet
//        ClaimSetClaimValuesJpaEntity association = new ClaimSetClaimValuesJpaEntity(claimSet, claimValue);
//        claimSetClaimValuesRepo.save(association);
//
//        return claimValue;
//    }
//
//    private ClaimSetJpaEntity createDireccionClaimSet(String suffix, String calle, String numero, String ciudad) {
//        ClaimSetJpaEntity direccion = TestDataBuilder.validClaimSet()
//            .business(testBusiness)
//            .name("direccion_" + suffix)
//            .description("Dirección " + suffix)
//            .build();
//        direccion = claimSetRepo.save(direccion);
//
//        createClaimValue(direccion, calleDef, calle);
//        createClaimValue(direccion, numeroDef, numero);
//        createClaimValue(direccion, ciudadDef, ciudad);
//
//        return direccion;
//    }
//}