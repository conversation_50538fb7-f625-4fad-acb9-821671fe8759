package com.vusecurity.auth.scim.application.command;

import com.vusecurity.auth.contracts.api.v1.dto.scimConfig.ScimFilterMapperRequest;
import com.vusecurity.auth.scim.application.exception.InvalidScimMapperRequest;

import java.util.*;

public record UpdateScimMapperCommand(
        UUID businessId,
        List<ScimFilterMapperRequest> filters
) {
    
    public void validate() {
        //userName and externalId must be mapped.
        boolean userNameFilterPresent = false;
        boolean externalIdFilterPresent = false;

        if (businessId == null) {
            throw new InvalidScimMapperRequest("businessId is required.");
        }

        if (filters == null || filters.isEmpty()) {
            throw new InvalidScimMapperRequest("At least one filter is required.");
        }

        for (ScimFilterMapperRequest filter : filters) {
            if (filter.getClaimDefinitionId() == null) {
                throw new InvalidScimMapperRequest("claimDefinitionCode is required for all filters.");
            }

            if (filter.getFilter() == null || filter.getFilter().trim().isEmpty()) {
                throw new InvalidScimMapperRequest("filter is required for all filters.");
            }

            if (filter.getClaimSetIdOrigin() == null) {
                throw new InvalidScimMapperRequest("claimSetIdOrigin is required for all filters.");
            }

            if (!userNameFilterPresent){
                userNameFilterPresent = filter.getFilter().equals("userName");
            }

            if (!externalIdFilterPresent){
                externalIdFilterPresent = filter.getFilter().equals("externalId");
            }
        }
        
        //If there's no mapping throw an error.
        if (!userNameFilterPresent){
            throw new InvalidScimMapperRequest("`userName` filter is required to update the mapper.");
        }

        if (!externalIdFilterPresent){
            throw new InvalidScimMapperRequest("`externalId` filter is required to update the mapper.");
        }
    }
} 