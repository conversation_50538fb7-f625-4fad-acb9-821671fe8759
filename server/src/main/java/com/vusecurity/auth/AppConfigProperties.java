package com.vusecurity.auth;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

@Data
@Validated
@Component
@ConfigurationProperties(prefix = "app")
public class AppConfigProperties {

    @NotBlank
    private String version;

    @NotBlank
    private String build;

    private Long date;

    @NotBlank
    private String apikey;

    private MultitenantConfig multitenant;

    private DataSeedConfig dataseed;

    private AuthorizationConfig authorization;

    private ClaimsConfig claims;

    private BusinessConfig business;

    private FactorsServerConfig factorsServer;

    @Data
    @Validated
    public static class MultitenantConfig {
        @NotBlank
        private String applicationName;

        @NotBlank
        private String key;

        @NotBlank
        private String url;

        @NotBlank
        private String transitKey;

        private String idHeader;

        private JpaConfig jpa;

        @Data
        @Validated
        public static class JpaConfig {
            private HikariConfig hikari;
            private String mode;

            @Data
            @Validated
            public static class HikariConfig {
                private Integer maximumPoolSize;
                private Long idleTimeout;
                private Integer minimumIdle;
                private Long connectionTimeout;
                private Long validationTimeout;
            }
        }
    }

    @Data
    @Validated
    public static class DataSeedConfig {
        private Boolean enabled;
    }

    @Data
    @Validated
    public static class AuthorizationConfig {
        private String contextPath;
        private AutoConfigureConfig autoconfigure;

        @Data
        @Validated
        public static class AutoConfigureConfig {
            private Boolean service;
        }
    }

    @Data
    @Validated
    public static class ClaimsConfig {
        private String contextPath;
        private AutoConfigureConfig autoconfigure;

        @Data
        @Validated
        public static class AutoConfigureConfig {
            private Boolean service;
        }
    }

    @Data
    @Validated
    public static class BusinessConfig {
        private String contextPath;
        private AutoConfigureConfig autoconfigure;

        @Data
        @Validated
        public static class AutoConfigureConfig {
            private Boolean services;
        }
    }

    @Data
    @Validated
    public static class FactorsServerConfig {
        @NotBlank
        private String url;

        @NotBlank
        private String apikey;
    }

}