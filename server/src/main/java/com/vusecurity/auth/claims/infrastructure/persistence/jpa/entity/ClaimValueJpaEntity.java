package com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import jakarta.persistence.*;

import java.util.Objects;
import java.util.UUID;

@Entity
@Table(
        name = "claim_value",
        indexes = {
                // speeds up look-ups by claim definition
                @Index(
                        name = "idx_claim_value_definition",
                        columnList = "claim_definition_id"),

                // speeds up look-ups by owner
                @Index(
                        name = "idx_claim_value_owner",
                        columnList = "owner_type, owner_id")
        }
)
@SuppressWarnings("LombokGetterMayBeUsed, LombokSetterMayBeUsed")
public class ClaimValueJpaEntity extends AbstractEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "claim_definition_id", referencedColumnName = "id",
            foreignKey = @ForeignKey(name = "fk_claim_value_definition"))
    private ClaimDefinitionJpaEntity claimDefinition;

    @Enumerated(EnumType.STRING)
    @Column(name = "owner_type", nullable = false)
    private OwnerType ownerType;

    @Column(name = "owner_id", nullable = false)
    private UUID ownerId;

    @Column(length = 1024)
    private String value;

    @Column(nullable = false)
    private boolean isPrimary = false;

    @Column(nullable = false)
    private boolean isComputed = false;

    private String source;

    private Integer primaryIndex;

    /**
     * One-to-one relationship to the primary claim verification.
     * This allows direct access to the verification data for this claim value.
     */
    @OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "claim_verification_id", referencedColumnName = "id")
    private ClaimVerificationJpaEntity verification;

    /**
     * One-to-one relationship to the claim set claim value entity.
     * This allows direct access to the binded claim set.
     */
    @OneToOne(mappedBy = "claimValue", cascade = CascadeType.ALL)
    private ClaimSetClaimValueJpaEntity claimSetClaimValue;

    /**
     * Transient property to indicate if verification data has been loaded.
     */
    @Transient
    private boolean verificationLoaded = false;

    // Constructors
    public ClaimValueJpaEntity() {
    }

    public ClaimValueJpaEntity(ClaimDefinitionJpaEntity claimDefinition, OwnerType ownerType, UUID ownerId) {
        if (claimDefinition == null) {
            throw new IllegalArgumentException("claimDefinition cannot be null");
        }
        if (ownerType == null) {
            throw new IllegalArgumentException("ownerType cannot be null");
        }
        if (ownerId == null) {
            throw new IllegalArgumentException("ownerId cannot be null");
        }

        this.claimDefinition = claimDefinition;
        this.ownerType = ownerType;
        this.ownerId = ownerId;
        this.isPrimary = false;
        this.isComputed = false;
    }

    public ClaimValueJpaEntity(UUID id, ClaimDefinitionJpaEntity claimDefinition, OwnerType ownerType, UUID ownerId) {
        this(claimDefinition, ownerType, ownerId);
        this.setId(id);
    }

    // Getters
    public ClaimDefinitionJpaEntity getClaimDefinition() {
        return claimDefinition;
    }

    public OwnerType getOwnerType() {
        return ownerType;
    }

    public UUID getOwnerId() {
        return ownerId;
    }

    public String getValue() {
        return value;
    }

    public boolean isPrimary() {
        return isPrimary;
    }

    public boolean isComputed() {
        return isComputed;
    }

    public String getSource() {
        return source;
    }

    public Integer getPrimaryIndex() { return primaryIndex; }

    // Setters
    public void setClaimDefinition(ClaimDefinitionJpaEntity claimDefinition) {
        this.claimDefinition = claimDefinition;
    }

    public void setOwnerType(OwnerType ownerType) {
        this.ownerType = ownerType;
    }

    public void setOwnerId(UUID ownerId) {
        this.ownerId = ownerId;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public void setPrimary(boolean primary) {
        isPrimary = primary;
    }

    public void setComputed(boolean computed) {
        isComputed = computed;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public void setPrimaryIndex(Integer primaryIndex) { this.primaryIndex = primaryIndex; }

    public ClaimSetClaimValueJpaEntity getClaimSetClaimValue() {
        return claimSetClaimValue;
    }

    public void setClaimSetClaimValue(ClaimSetClaimValueJpaEntity claimSetClaimValue) {
        this.claimSetClaimValue = claimSetClaimValue;
    }

    /**
     * Gets the verification for this claim value.
     * @return the verification or null if not set
     */
    public ClaimVerificationJpaEntity getVerification() {
        return verification;
    }

    /**
     * Sets the verification for this claim value.
     * @param verification the verification
     */
    public void setVerification(ClaimVerificationJpaEntity verification) {
        this.verification = verification;
        this.verificationLoaded = true;
    }

    /**
     * Checks if verification data has been loaded.
     * @return true if verification has been loaded
     */
    public boolean isVerificationLoaded() {
        return verificationLoaded;
    }

    /**
     * Marks verification data as loaded.
     * @param verificationLoaded true if verification is loaded
     */
    public void setVerificationLoaded(boolean verificationLoaded) {
        this.verificationLoaded = verificationLoaded;
    }

    /**
     * Convenience method to check if this claim value has a verification.
     * @return true if there is a verification
     */
    public boolean hasVerification() {
        return verification != null;
    }

    /**
     * Convenience method to check if this claim value is verified (has successful verification).
     * @return true if the verification result is OK
     */
    public boolean isVerified() {
        return verification != null &&
               verification.getResult() == com.vusecurity.auth.shared.enums.ResultType.OK;
    }

    /**
     * Convenience method to get the verification result.
     * @return the verification result or NONE if no verification exists
     */
    public com.vusecurity.auth.shared.enums.ResultType getVerificationResult() {
        return verification != null ? verification.getResult() : com.vusecurity.auth.shared.enums.ResultType.NONE;
    }

    /**
     * Convenience method to get the verification type.
     * @return the verification type or null if no verification exists
     */
    public String getVerificationType() {
        return verification != null ? verification.getVerificationType() : null;
    }

    /**
     * Convenience method to get when the claim was verified.
     * @return the verification timestamp or null if no verification exists
     */
    public java.time.Instant getVerifiedAt() {
        return verification != null ? verification.getVerifiedAt() : null;
    }

    // equals() and hashCode() using id (fallback since no unique business key)
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        ClaimValueJpaEntity that = (ClaimValueJpaEntity) o;
        return Objects.equals(getId(), that.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getId());
    }

    @Override
    public String toString() {
        return "ClaimValueJpaEntity{" +
                "id=" + getId() +
                ", ownerType=" + ownerType +
                ", ownerId=" + ownerId +
                ", value='" + value + '\'' +
                ", isPrimary=" + isPrimary +
                ", isComputed=" + isComputed +
                ", source='" + source + '\'' +
                ", primaryIndex=" + primaryIndex +
                '}';
    }
}
