package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.exception.GroupNotFoundException;
import com.vusecurity.auth.authorization.application.query.GetGroupsPagedQuery;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountGroupRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Unit tests for GetGroupHandler.
 * Tests business logic with a mocked repository dependency.
 */
@ExtendWith(MockitoExtension.class)
class GetGroupHandlerTest {

    @Mock
    private AccountGroupRepository accountGroupRepository;

    @InjectMocks
    private GetGroupHandler getGroupHandler;

    private UUID groupId;
    private AccountGroupJpaEntity testGroup;
    private GetGroupsPagedQuery defaultQuery;

    @BeforeEach
    void setUp() {
        groupId = UUID.randomUUID();
        
        testGroup = new AccountGroupJpaEntity();
        testGroup.setId(groupId);
        testGroup.setName("Test Group");
        testGroup.setDescription("A description for testing.");

        defaultQuery = GetGroupsPagedQuery.builder()
                .page(1)
                .pageSize(10)
                .sortDirection("asc")
                .build();
    }

    // --- Tests for getGroupById ---

    @Test
    void shouldReturnGroup_whenGroupIdExists() {
        // Given
        when(accountGroupRepository.findById(groupId)).thenReturn(Optional.of(testGroup));

        // When
        AccountGroupJpaEntity result = getGroupHandler.getGroupById(groupId);

        // Then
        assertNotNull(result);
        assertEquals(groupId, result.getId());
        assertEquals("Test Group", result.getName());
        verify(accountGroupRepository).findById(groupId);
    }

    @Test
    void shouldThrowGroupNotFoundException_whenGroupIdDoesNotExist() {
        // Given
        when(accountGroupRepository.findById(groupId)).thenReturn(Optional.empty());

        // When & Then
        GroupNotFoundException exception = assertThrows(
                GroupNotFoundException.class,
                () -> getGroupHandler.getGroupById(groupId)
        );

        assertEquals("Group not found with ID: " + groupId, exception.getMessage());
        verify(accountGroupRepository).findById(groupId);
    }

    // --- Tests for getAllGroups ---

    @Test
    void shouldReturnPagedGroups_withDefaultSorting() {
        // Given
        Page<AccountGroupJpaEntity> expectedPage = new PageImpl<>(List.of(testGroup));
        Sort expectedSort = Sort.by(Sort.Direction.ASC, "name");
        Pageable expectedPageable = PageRequest.of(0, 10, expectedSort);
        when(accountGroupRepository.findAll(expectedPageable)).thenReturn(expectedPage);

        // When
        Page<AccountGroupJpaEntity> result = getGroupHandler.getAllGroups(defaultQuery);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(testGroup.getName(), result.getContent().get(0).getName());
        verify(accountGroupRepository).findAll(expectedPageable);
    }
    
    @Test
    void shouldReturnPagedGroups_withCustomSorting() {
        // Given
        GetGroupsPagedQuery customQuery = GetGroupsPagedQuery.builder()
                .page(1)
                .pageSize(10)
                .sortBy("description")
                .sortDirection("desc")
                .build();
        Page<AccountGroupJpaEntity> expectedPage = new PageImpl<>(List.of(testGroup));
        Sort expectedSort = Sort.by(Sort.Direction.DESC, "description");
        Pageable expectedPageable = PageRequest.of(0, 10, expectedSort);
        when(accountGroupRepository.findAll(expectedPageable)).thenReturn(expectedPage);

        // When
        Page<AccountGroupJpaEntity> result = getGroupHandler.getAllGroups(customQuery);

        // Then
        assertNotNull(result);
        verify(accountGroupRepository).findAll(expectedPageable);
    }

    @Test
    @SuppressWarnings("unchecked")
    void shouldReturnFilteredGroups_whenFilterIsProvided() {
        // Given
        GetGroupsPagedQuery filteredQuery = GetGroupsPagedQuery.builder()
                .page(1)
                .pageSize(10)
                .sortDirection("asc")
                .filter("Test")
                .build();
        Page<AccountGroupJpaEntity> expectedPage = new PageImpl<>(List.of(testGroup));
        Sort expectedSort = Sort.by(Sort.Direction.ASC, "name");
        Pageable expectedPageable = PageRequest.of(0, 10, expectedSort);
        when(accountGroupRepository.findAll(any(Specification.class), eq(expectedPageable)))
                .thenReturn(expectedPage);

        // When
        Page<AccountGroupJpaEntity> result = getGroupHandler.getAllGroups(filteredQuery);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        verify(accountGroupRepository).findAll(any(Specification.class), eq(expectedPageable));
    }

    @Test
    void shouldReturnEmptyPage_whenNoGroupsExist() {
        // Given
        Page<AccountGroupJpaEntity> emptyPage = new PageImpl<>(Collections.emptyList());
        Sort expectedSort = Sort.by(Sort.Direction.ASC, "name");
        Pageable expectedPageable = PageRequest.of(0, 10, expectedSort);
        when(accountGroupRepository.findAll(expectedPageable)).thenReturn(emptyPage);

        // When
        Page<AccountGroupJpaEntity> result = getGroupHandler.getAllGroups(defaultQuery);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(accountGroupRepository).findAll(expectedPageable);
    }

    @Test
    void shouldThrowException_whenRepositoryCallFails() {
        // Given
        Sort expectedSort = Sort.by(Sort.Direction.ASC, "name");
        Pageable expectedPageable = PageRequest.of(0, 10, expectedSort);
        when(accountGroupRepository.findAll(expectedPageable))
                .thenThrow(new RuntimeException("Database connection failed"));

        // When & Then
        RuntimeException exception = assertThrows(
                RuntimeException.class,
                () -> getGroupHandler.getAllGroups(defaultQuery)
        );

        assertEquals("Database connection failed", exception.getMessage());
        verify(accountGroupRepository).findAll(expectedPageable);
    }
} 