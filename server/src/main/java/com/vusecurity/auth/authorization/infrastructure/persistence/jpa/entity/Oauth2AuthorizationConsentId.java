package com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity;

import java.io.Serializable;
import java.util.Objects;

public class Oauth2AuthorizationConsentId implements Serializable {
    private String registeredClientId;
    private String principalName;

    // equals y hashCode
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Oauth2AuthorizationConsentId that)) return false;
        return Objects.equals(registeredClientId, that.registeredClientId) &&
                Objects.equals(principalName, that.principalName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(registeredClientId, principalName);
    }
}