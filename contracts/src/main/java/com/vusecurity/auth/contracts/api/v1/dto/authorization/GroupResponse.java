package com.vusecurity.auth.contracts.api.v1.dto.authorization;

import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "Account group information response")
public class GroupResponse extends BaseResponse {
    
    @Schema(description = "Unique identifier of the account group",
            example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    @Schema(description = "Name of the account group",
            example = "Engineering Team")
    private String name;

    @Schema(description = "Description of the account group",
            example = "Engineering team members in Argentina office")
    private String description;

    @Schema(description = "Additional metadata for the group as key-value pairs",
            example = "{\"department\": \"engineering\", \"location\": \"argentina\"}")
    private Map<String, Object> metadata;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        GroupResponse that = (GroupResponse) o;
        return Objects.equals(id, that.id) &&
               Objects.equals(name, that.name) &&
               Objects.equals(description, that.description) &&
               Objects.equals(metadata, that.metadata);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), id, name, description, metadata);
    }

    @Schema(description = "List of roles assigned to this group")
    private List<RoleSummary> roles;

    @Schema(description = "List of accounts that are members of this group")
    private List<AccountSummary> accounts;

    @Schema(description = "Number of accounts that are members of this group",
            example = "5")
    private Long accountCount;
} 