<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.vusecurity</groupId>
        <artifactId>auth-server-parent</artifactId>
        <version>1.1.0-SNAPSHOT</version>
    </parent>
    <artifactId>auth-server</artifactId>
    <version>1.1.0-SNAPSHOT</version>
    <name>auth-server</name>
    <description>The VU One Auth Server is the central authentication and authorization service for the VU One platform.</description>

    <properties>
        <app.version>1.1.0-SNAPSHOT</app.version>
        <build.version>0</build.version>
        <build.date>0</build.date>
        <java.version>21</java.version>
        <vu-crypto.version>1.0.0</vu-crypto.version>
        <vu-multitenant.version>1.1.0-SNAPSHOT</vu-multitenant.version>
        <vu-core-commons.version>1.1.0-SNAPSHOT</vu-core-commons.version>
        <vu-business-core.version>1.1.0-SNAPSHOT</vu-business-core.version>
        <vu-auth-contracts.version>1.1.0-SNAPSHOT</vu-auth-contracts.version>
        <logstash-logback-encoder.version>8.0</logstash-logback-encoder.version>
        <spring-doc.version>2.8.9</spring-doc.version>
        <micrometer-tracing.version>1.4.3</micrometer-tracing.version>
        <lombok.version>1.18.36</lombok.version>
        <!-- Test configuration -->
        <skipITs>false</skipITs>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers-bom</artifactId>
                <version>1.21.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.vusecurity</groupId>
            <artifactId>auth-contracts</artifactId>
            <version>${vu-auth-contracts.version}</version>
        </dependency>
        <dependency>
            <groupId>com.vusecurity.core</groupId>
            <artifactId>core-commons</artifactId>
            <version>${vu-core-commons.version}</version>
        </dependency>
        <dependency>
            <groupId>com.vusecurity</groupId>
            <artifactId>business-core</artifactId>
            <version>${vu-business-core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.vusecurity</groupId>
            <artifactId>crypto</artifactId>
            <version>${vu-crypto.version}</version>
        </dependency>
        <dependency>
            <groupId>com.vusecurity</groupId>
            <artifactId>multitenant-commons</artifactId>
            <version>${vu-multitenant.version}</version>
        </dependency>
        <dependency>
            <groupId>com.vusecurity</groupId>
            <artifactId>multitenant-datasource</artifactId>
            <version>${vu-multitenant.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>${spring-doc.version}</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>${logstash-logback-encoder.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-websocket</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Override tomcat-embed-websocket version -->
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-websocket</artifactId>
            <version>10.1.44</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-authorization-server</artifactId>
            <version>1.4.5</version>
        </dependency>
        <!-- Security vulnerability overrides -->
        <dependency>
            <groupId>com.nimbusds</groupId>
            <artifactId>nimbus-jose-jwt</artifactId>
            <version>10.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.18.0</version>
        </dependency>
        <!-- End of Security vulnerability overrides -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>net.datafaker</groupId>
            <artifactId>datafaker</artifactId>
            <version>2.4.3</version>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>testcontainers-bom</artifactId>
            <version>1.21.1</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-testcontainers</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- JUnit-Jupiter integration -->
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Database container we need -->
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>mssqlserver</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- ====== Start SCIM Dependencies ===== -->
        <dependency>
            <groupId>org.apache.directory.scimple</groupId>
            <artifactId>scim-spring-boot-starter</artifactId>
            <version>1.0.0-M1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.directory.scimple</groupId>
            <artifactId>scim-server</artifactId>
            <version>1.0.0-M1</version>
        </dependency>
        <!-- ====== End SCIM Dependencies ===== -->
        <!-- Database migrations -->
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-sqlserver</artifactId>
            <version>11.11.2</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.vusecurity.auth.AuthServerApplication</mainClass>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <parameters>true</parameters>
                    <source>21</source>
                    <target>21</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <!-- Unit tests run here -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.5.3</version>
            </plugin>
            <!-- *IT.java, *ITCase.java executed in 'verify' phase -->
            <!-- Integration tests enabled -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>3.5.3</version>
                <configuration>
                    <skipITs>false</skipITs>
                    <forkedProcessTimeoutInSeconds>600</forkedProcessTimeoutInSeconds>
                    <additionalClasspathElements>
                        <additionalClasspathElement>${project.build.outputDirectory}</additionalClasspathElement>
                        <additionalClasspathElement>${project.build.testOutputDirectory}</additionalClasspathElement>
                    </additionalClasspathElements>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>integration-test</goal>   <!-- phase 'integration-test' -->
                            <goal>verify</goal>             <!-- phase 'verify' -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
