package com.vusecurity.auth.scim.application.resource;

import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.directory.scim.core.repository.PatchHandler;
import org.apache.directory.scim.core.repository.Repository;
import org.apache.directory.scim.server.exception.UnableToCreateResourceException;
import org.apache.directory.scim.server.exception.UnableToRetrieveResourceException;
import org.apache.directory.scim.spec.exception.ResourceException;
import org.apache.directory.scim.spec.filter.*;
import org.apache.directory.scim.spec.filter.attribute.AttributeReference;
import org.apache.directory.scim.spec.patch.PatchOperation;
import org.apache.directory.scim.spec.resources.ScimExtension;
import org.apache.directory.scim.spec.resources.ScimGroup;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.context.annotation.Profile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.vusecurity.auth.authorization.application.command.CreateGroupCommand;
import com.vusecurity.auth.authorization.application.command.DeleteGroupCommand;
import com.vusecurity.auth.authorization.application.command.UpdateGroupCommand;
import com.vusecurity.auth.authorization.application.exception.GroupNotFoundException;
import com.vusecurity.auth.authorization.application.handler.CreateGroupHandler;
import com.vusecurity.auth.authorization.application.handler.DeleteGroupHandler;
import com.vusecurity.auth.authorization.application.handler.UpdateGroupHandler;
import com.vusecurity.auth.authorization.application.query.GetGroupQuery;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import com.vusecurity.auth.identities.application.query.GetAccountQuery;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountGroupRepository;
import com.vusecurity.auth.scim.mapper.FilterToSpecificationBuilder;
import com.vusecurity.auth.scim.mapper.GroupMapper;
import com.vusecurity.auth.scim.util.SchemaValidation;
import com.vusecurity.auth.scim.util.SchemaValidation.ValidationResult;
import com.vusecurity.auth.shared.infrastructure.security.scim.TokenDetails;

@Component
@Slf4j
@RequiredArgsConstructor
public class GroupService implements Repository<ScimGroup> {

  private final PatchHandler patchHandler;

  private final GetGroupQuery getGroupQuery;

  private final CreateGroupHandler createGroupHandler;

  private final GetAccountQuery getAccountQuery;

  private final AccountGroupRepository accountGroupRepository;

  private final DeleteGroupHandler deleteGroupHandler;

  private final UpdateGroupHandler updateGroupHandler;

  private final SchemaValidation schemaValidation;

  private UUID businessID = null;

  @Override
  public Class<ScimGroup> getResourceClass() {
    return ScimGroup.class;
  }

  @Override
  @Transactional(rollbackFor = { UnableToCreateResourceException.class, WebApplicationException.class })
  public ScimGroup create(ScimGroup resource) throws UnableToCreateResourceException, WebApplicationException {
    ValidationResult validation = new ValidationResult();
    try{
        validation = schemaValidation.validateResource(resource);
        setBusinesAndProvider();
    } catch (WebApplicationException e){
        throw e;
    } catch (Exception e){
        log.error("Error validating the schema " + e.getMessage(), e);
        validation.addError(new SchemaValidation.ValidationError(null, e.getMessage(), resource.getBaseUrn()));
    }
    if (Boolean.FALSE.equals(validation.isValid())){
        //For now show the first error.
        throw new UnableToCreateResourceException(Status.BAD_REQUEST, "Validation for schema failed: " + validation.getErrors().getFirst().getMessage());
    }
    // if the external ID is not set, use the displayName instead
    if (!StringUtils.hasText(resource.getExternalId())) {
      resource.setExternalId(resource.getDisplayName());
    }

    AccountGroupJpaEntity newGroup = GroupMapper.fromGroupWithoutMembers(resource);

    if (getGroupQuery.findGroupByName(newGroup.getName()).isPresent()){
        throw new UnableToCreateResourceException(Status.CONFLICT, "Group '" + newGroup.getName() + "' already exists.");
    }

    List<UUID> directMemberList;
    try {
        directMemberList = GroupMapper.groupMembershipToUUIDs(resource.getMembers());
    } catch (Exception e){
        throw new UnableToCreateResourceException(Status.BAD_REQUEST, "Unable to parse members ref to UUIDs - " + e.getMessage());
    }


    //TODO: Get the correct Business
    //Get the IDs that belong to the correct business even if the createGroupHandler already check for them to exists.
    directMemberList = getAccountQuery.filterUUIDsByBusiness(businessID, directMemberList);

    CreateGroupCommand createGroupCommand = new CreateGroupCommand(newGroup.getName(), newGroup.getDescription(), null, directMemberList, null, newGroup.getMetadata());

    try {
        newGroup = createGroupHandler.createGroup(createGroupCommand);
    } catch (Exception e){
        log.error("GroupsService - create - createGroupError - {}", e.getMessage());
        throw new UnableToCreateResourceException(Response.Status.INTERNAL_SERVER_ERROR, "Failed to create group '" + resource.getExternalId() + "' cause: " + e.getMessage());
    }

    return GroupMapper.toGroup(newGroup);
  }

  @Override
  @Transactional(rollbackFor = ResourceException.class)
  public ScimGroup update(String id, String version, ScimGroup resource, Set<AttributeReference> includedAttributeReferences, Set<AttributeReference> excludedAttributeReferences) throws ResourceException {
    setBusinesAndProvider();
    AccountGroupJpaEntity group;
    UUID groupId;
    try {
        groupId = UUID.fromString(id);
        getGroupQuery.getActiveGroupById(groupId);
    } catch (GroupNotFoundException e){
        throw new UnableToRetrieveResourceException(Status.NOT_FOUND, e.getMessage());
    } catch (Exception e){
        throw new ResourceException(500, e.getMessage());
    }
    group = GroupMapper.fromGroupWithoutMembers(resource);
    List<UUID> directMembers = GroupMapper.groupMembershipToUUIDs(resource.getMembers());
    //Remove any duplicated values that may exist.
    directMembers = directMembers.stream().collect(Collectors.toSet()).stream().toList();

    UpdateGroupCommand updateGroupCommand = new UpdateGroupCommand(groupId, group.getName(), group.getDescription(), null, directMembers, null, group.getMetadata());
    AccountGroupJpaEntity updatedGroup;
    try{
        updatedGroup = updateGroupHandler.updateGroup(updateGroupCommand);
    } catch (Exception e){
        throw new ResourceException(500, e.getMessage());
    }

    return GroupMapper.toGroup(updatedGroup);
  }

  @Override
  @Transactional(rollbackFor = ResourceException.class)
  public ScimGroup patch(String id, String version, List<PatchOperation> patchOperations, Set<AttributeReference> includedAttributeReferences, Set<AttributeReference> excludedAttributeReferences) throws ResourceException {
    setBusinesAndProvider();
    ScimGroup originalGroup = get(id);
    ScimGroup patchedGroup = patchHandler.apply(originalGroup, patchOperations);

    return this.update(id, version, patchedGroup, includedAttributeReferences, excludedAttributeReferences);
  }

  @Override
  @Transactional(readOnly = true, noRollbackFor = { UnableToCreateResourceException.class, ResourceException.class })
  public ScimGroup get(String id) throws ResourceException {
    setBusinesAndProvider();
    AccountGroupJpaEntity group;
    try {
        group = getGroupQuery.getActiveGroupById(UUID.fromString(id));
    } catch (GroupNotFoundException e){
        throw new UnableToRetrieveResourceException(Status.NOT_FOUND, e.getMessage());
    } catch (Exception e){
        throw new ResourceException(500, e.getMessage());
    }

    return GroupMapper.toGroup(group);
  }

  @Override
  @Transactional(rollbackFor = ResourceException.class)
  public void delete(String id) throws ResourceException {
    setBusinesAndProvider();
    try {
        DeleteGroupCommand deleteCommand = new DeleteGroupCommand(UUID.fromString(id));
        deleteGroupHandler.deleteGroup(deleteCommand);
    } catch (GroupNotFoundException e){
        //Taken from the ms scim validation tool, future version may required to return NOT_FOUND
        throw new UnableToRetrieveResourceException(Status.NOT_FOUND, e.getMessage());
    } catch (Exception e){
        throw new ResourceException(500, e.getMessage());
    }
  }

  @Override
  @Transactional(readOnly = true)
  public FilterResponse<ScimGroup> find(Filter filter, PageRequest pageRequest, SortRequest sortRequest) throws ResourceException {
    setBusinesAndProvider();
    int count = pageRequest.getCount() != null ? pageRequest.getCount() : 100;
    int startIndex = pageRequest.getStartIndex() != null
      ? pageRequest.getStartIndex() - 1
      : 0;

    Specification<AccountGroupJpaEntity> spec = null;
    String sortAttribute = "id";
    Sort.Direction direction = Sort.Direction.ASC;
    if (sortRequest.getSortBy() != null){
        direction = sortRequest.getSortOrder().equals(SortOrder.ASCENDING) ? Sort.Direction.ASC : Sort.Direction.DESC;
    }
    if (sortRequest.getSortOrder() != null){
        sortAttribute = GroupMapper.groupMapping.getOrDefault(sortRequest.getSortBy().getFullAttributeName(), List.of("id")).get(0);
    }
    Sort sort = Sort.by(direction, sortAttribute);

    org.springframework.data.domain.PageRequest pageable = org.springframework.data.domain.PageRequest.of(startIndex, count, sort);
    
    if (filter != null){
        spec = new FilterToSpecificationBuilder<AccountGroupJpaEntity>(GroupMapper.groupMapping).build(filter);
    }

    List<ScimGroup> result = new ArrayList<>();

    Page<AccountGroupJpaEntity> pagedFilter = this.accountGroupRepository.findAll(spec, pageable);
    if (!pagedFilter.isEmpty()){
        result.addAll(pagedFilter.stream().map(GroupMapper::toGroup).toList());
    }

    return new FilterResponse<>(result, pageRequest, (int)pagedFilter.getTotalElements());
  }

  @Override
  public List<Class<? extends ScimExtension>> getExtensionList() {
    return Collections.emptyList();
  }

  private void setBusinesAndProvider() throws WebApplicationException {
        try{
             Object authenticationDetails = SecurityContextHolder.getContext().getAuthentication().getDetails();
            if (!(authenticationDetails instanceof TokenDetails)){
                throw new WebApplicationException("Invalid authentication credentials type");
            }
            businessID = ((TokenDetails)authenticationDetails).getBusinessId();
        } catch (Exception ex){
            log.error("setBusinesAndProvider - error - {}", ex);
            throw new WebApplicationException("Invalid authentication credentials");
        }

    }

}
