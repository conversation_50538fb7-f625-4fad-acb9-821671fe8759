package com.vusecurity.auth.shared.api.handler;

import com.vusecurity.core.commons.ApiMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

/**
 * Exception handler for shared controllers (Main<PERSON><PERSON><PERSON><PERSON>, StatusController, VersionController).
 * Handles generic exceptions that might occur in system information endpoints.
 */
@ControllerAdvice(assignableTypes = {
    com.vusecurity.auth.shared.api.controller.MainController.class,
    com.vusecurity.auth.shared.api.controller.StatusController.class,
    com.vusecurity.auth.shared.api.controller.VersionController.class
})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class SharedErrorControllerHandler {

    private static final Logger logger = LoggerFactory.getLogger(SharedErrorControllerHandler.class);

    /**
     * Handle generic RuntimeException thrown by shared controllers.
     * This is a fallback for any unexpected runtime exceptions.
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiMessage> handleRuntimeException(RuntimeException ex) {
        logger.error("Unexpected runtime exception in shared controller: {}", ex.getMessage(), ex);
        
        ApiMessage error = new ApiMessage(6000, "Internal server error");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
    }

    /**
     * Handle generic Exception thrown by shared controllers.
     * This is a fallback for any unexpected checked exceptions.
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiMessage> handleGenericException(Exception ex) {
        logger.error("Unexpected exception in shared controller: {}", ex.getMessage(), ex);
        
        ApiMessage error = new ApiMessage(6001, "Internal server error");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
    }
}
